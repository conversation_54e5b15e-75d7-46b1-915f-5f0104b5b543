{"permissions": {"allow": ["<PERSON><PERSON>(python:*)", "Bash(grep:*)", "<PERSON><PERSON>(taskkill:*)", "Bash(start python main.py --env .env --events-ini events.ini)", "Bash(rm:*)", "Bash(find:*)", "<PERSON><PERSON>(timeout:*)", "<PERSON><PERSON>(pkill:*)", "<PERSON><PERSON>(true)", "Bash(del test_multi_event_fix.py verify_fix.py)", "Bash(dotnet build:*)", "<PERSON><PERSON>(sed:*)", "<PERSON><PERSON>(mkdir:*)", "Bash(cp:*)", "Bash(dotnet test:*)", "Bash(dotnet run:*)", "Bash(ls:*)", "Bash(./EventProcessor.Host.exe --help)", "<PERSON><PERSON>(powershell:*)"], "deny": []}}