# EPConfigToolV2 产品需求文档

## 1. 产品概述

EPConfigToolV2是一个基于WinForms的轻量级YAML配置编辑器，专门用于编辑EventProcessor V4.1的统一配置文件。该工具基于实际使用场景（打开→修改→另存为）进行重构，将原有复杂的50个文件简化为10个核心文件，实现启动速度提升80%以上，维护成本降低90%。

产品主要解决EventProcessor配置文件编辑复杂、验证困难、用户体验差的问题，为运维人员和开发人员提供简洁高效的配置管理工具。

目标市场价值：通过大幅简化配置编辑流程，提高运维效率，降低配置错误率，为EventProcessor系统的稳定运行提供有力支撑。

## 2. 核心功能

### 2.1 用户角色

| 角色 | 使用方式 | 核心权限 |
|------|----------|----------|
| 运维人员 | 直接启动应用程序 | 可编辑所有配置项，保存配置文件 |
| 开发人员 | 直接启动应用程序 | 可编辑所有配置项，保存配置文件，查看验证详情 |

### 2.2 功能模块

我们的EPConfigToolV2包含以下主要页面：
1. **主编辑窗口**：文件菜单、配置编辑区域、验证状态显示
2. **文件操作对话框**：打开文件对话框、另存为对话框
3. **验证错误显示**：错误详情窗口、验证结果提示

### 2.3 页面详情

| 页面名称 | 模块名称 | 功能描述 |
|----------|----------|----------|
| 主编辑窗口 | 文件菜单 | 提供打开、另存为、退出功能，支持快捷键Ctrl+O、Ctrl+S |
| 主编辑窗口 | 配置编辑区域 | 使用TabControl组织EventProcessor、MQTT、ErrorHandling、Logging配置节，基于数据模型自动生成表单控件 |
| 主编辑窗口 | 验证状态显示 | 实时显示配置验证结果，标识必填字段，显示错误和警告信息 |
| 文件操作对话框 | 打开文件对话框 | 过滤.yaml和.yml文件，支持最近文件列表，显示文件预览 |
| 文件操作对话框 | 另存为对话框 | 默认.yaml扩展名，支持文件名验证，提供保存确认 |
| 验证错误显示 | 错误详情窗口 | 显示详细的验证错误信息，提供字段定位功能，支持错误修复建议 |

## 3. 核心流程

### 主要用户操作流程

**配置编辑流程**：
1. 用户启动EPConfigToolV2应用程序
2. 通过文件菜单选择"打开"或使用Ctrl+O快捷键
3. 在文件对话框中选择YAML配置文件
4. 系统加载配置文件并在各个Tab页中显示配置项
5. 用户在相应Tab页中修改配置值
6. 系统实时验证输入并显示验证结果
7. 用户通过"另存为"菜单或Ctrl+S保存配置
8. 系统执行完整性检查并保存文件

**验证和错误处理流程**：
1. 用户修改配置项时触发实时验证
2. 系统基于DataAnnotations进行字段验证
3. 验证失败时在控件旁显示错误提示
4. 用户可点击错误详情查看具体问题
5. 系统提供修复建议和字段定位
6. 保存前执行完整性检查，阻止无效配置保存

```mermaid
graph TD
    A[启动应用] --> B[主编辑窗口]
    B --> C[打开文件]
    C --> D[配置编辑]
    D --> E[实时验证]
    E --> F[保存文件]
    F --> G[完成编辑]
    
    D --> H[验证错误]
    H --> I[错误详情]
    I --> D
```

## 4. 用户界面设计

### 4.1 设计风格

- **主色调**：#2E86AB（蓝色）作为主色，#F24236（红色）用于错误提示
- **辅助色**：#A23B72（紫色）用于警告，#F18F01（橙色）用于高亮
- **按钮样式**：现代扁平化设计，圆角矩形，悬停效果
- **字体**：主要使用微软雅黑12pt，代码区域使用Consolas 10pt
- **布局风格**：基于Tab页的卡片式布局，顶部菜单栏，底部状态栏
- **图标风格**：使用简洁的线性图标，支持文件、保存、错误等常用操作

### 4.2 页面设计概览

| 页面名称 | 模块名称 | UI元素 |
|----------|----------|--------|
| 主编辑窗口 | 文件菜单 | 菜单栏使用标准Windows样式，包含文件、编辑、帮助菜单，支持图标和快捷键显示 |
| 主编辑窗口 | 配置编辑区域 | TabControl使用卡片样式，每个Tab页包含分组框，表单控件使用统一间距和对齐 |
| 主编辑窗口 | 验证状态显示 | 底部状态栏显示验证摘要，错误控件旁显示红色感叹号图标，工具提示显示详细信息 |
| 文件操作对话框 | 打开/保存对话框 | 使用标准Windows文件对话框，自定义过滤器，添加预览面板显示YAML内容摘要 |
| 验证错误显示 | 错误详情窗口 | 模态对话框，列表视图显示错误项，双击定位到对应字段，包含修复建议面板 |

### 4.3 响应式设计

产品采用桌面优先设计，主窗口支持最小化到800x600像素，各个Tab页内容支持垂直滚动。表单控件采用相对布局，支持窗口大小调整时的自适应缩放。不考虑触摸交互优化，专注于键盘和鼠标操作的用户体验。