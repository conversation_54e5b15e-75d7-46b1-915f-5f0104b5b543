# EPConfigToolV2 技术架构文档

## 1. 架构设计

```mermaid
graph TD
    A[用户界面层] --> B[业务服务层]
    B --> C[数据访问层]
    C --> D[配置文件系统]
    
    subgraph "用户界面层"
        A1[MainForm.cs]
        A2[FileDialogs]
        A3[ValidationUI]
    end
    
    subgraph "业务服务层"
        B1[ConfigurationService.cs]
        B2[ValidationHelper.cs]
        B3[FileOperationService.cs]
    end
    
    subgraph "数据访问层"
        C1[YamlDotNet序列化]
        C2[DataAnnotations验证]
        C3[文件系统访问]
    end
    
    subgraph "外部依赖"
        D1[EventProcessor.Core模型]
        D2[YAML配置文件]
    end
```

## 2. 技术描述

* 前端：WinForms + .NET 8.0

* 配置处理：YamlDotNet\@15.1.2

* 验证框架：System.ComponentModel.DataAnnotations

* 项目引用：EventProcessor.Core（本地项目引用）

## 3. 路由定义

| 窗口/对话框              | 用途                         |
| ------------------- | -------------------------- |
| MainForm            | 主编辑窗口，包含菜单栏、Tab页配置编辑区域、状态栏 |
| OpenFileDialog      | 文件打开对话框，过滤.yaml/.yml文件     |
| SaveFileDialog      | 文件保存对话框，默认.yaml扩展名         |
| ValidationErrorForm | 验证错误详情窗口，显示详细错误信息          |
| AboutForm           | 关于对话框，显示版本信息和帮助            |

## 4. API定义

### 4.1 核心服务接口

**配置服务相关**

```csharp
// ConfigurationService.cs
public class ConfigurationService
{
    public Task<ConfigurationModel> LoadConfigurationAsync(string filePath);
    public Task SaveConfigurationAsync(ConfigurationModel config, string filePath);
    public ValidationResult ValidateConfiguration(ConfigurationModel config);
    public ValidationResult ValidateAlarmConfiguration(AlarmConfiguration alarmConfig);
    public ValidationResult ValidateFieldMapping(FieldMapping fieldMapping);
}
```

**参数说明**：

| 参数名称         | 参数类型               | 是否必需 | 描述            |
| ------------ | ------------------ | ---- | ------------- |
| filePath     | string             | true | YAML配置文件的完整路径 |
| config       | ConfigurationModel | true | 配置数据模型对象      |
| alarmConfig  | AlarmConfiguration | true | 告警配置对象        |
| fieldMapping | FieldMapping       | true | 字段映射配置对象      |

**返回值说明**：

| 参数名称               | 参数类型   | 描述             |
| ------------------ | ------ | -------------- |
| ConfigurationModel | object | 包含所有配置节的统一配置模型 |
| ValidationResult   | object | 验证结果，包含错误和警告信息 |

**示例**

```json
{
  "EventProcessor": {
    "EventId": "EV001001",
    "EventName": "月租车未过期超时滞留出口",
    "EvaluationStrategy": "BusinessOnly",
    "RuleConfiguration": {
      "AlarmConfig": {
        "CustomAlarmTopic": "hq/101013/P002LfyBmOut/event",
        "CustomAlarmCancellationTopic": "hq/101013/P002LfyBmOut/cancellation",
        "Fields": [
          {
            "AlarmFieldName": "详情",
            "SourceRuleType": "BusinessRules",
            "SourceFieldName": "CardType,log_car_no,log_user_name,log_end_time",
            "FormatTemplate": "[{CardType}][{log_car_no}][{log_user_name}][{log_end_time}]"
          }
        ]
      }
    }
  },
  "Mqtt": {
    "BrokerHost": "mq.bangdouni.com",
    "BrokerPort": 1883
  }
}
```

## 5. 数据模型

### 5.1 数据模型定义

```mermaid
erDiagram
    ConfigurationModel ||--|| EventProcessorConfig : contains
    ConfigurationModel ||--|| MqttConfig : contains
    ConfigurationModel ||--|| ErrorHandlingConfig : contains
    ConfigurationModel ||--|| LoggingConfig : contains
    EventProcessorConfig ||--|| RuleConfiguration : contains
    RuleConfiguration ||--|| AlarmConfiguration : contains
    AlarmConfiguration ||--|| FieldMapping : contains
    
    EventProcessorConfig {
        string EventId PK
        string EventName
        string EvaluationStrategy
        string Priority
        string CommId
        string PositionId
        string CompanyName
        int AlarmGracePeriodSeconds
        bool EnableAlarmCancellation
        string CustomTimeWindowMinutes
    }
    
    RuleConfiguration {
        ExclusionRuleGroup[] ExclusionRules
        BusinessRuleGroup[] BusinessRules
        AIResultRuleGroup[] AIResultRules
        AlarmConfiguration AlarmConfig
    }
    
    AlarmConfiguration {
        FieldMapping[] Fields
        string CustomTemplate
        string CustomAlarmTopic
        string CustomAlarmCancellationTopic
    }
    
    FieldMapping {
        string AlarmFieldName PK
        string SourceRuleType
        string SourceFieldName
        string DefaultValue
        string FormatTemplate
    }
    
    MqttConfig {
        string BrokerHost
        int BrokerPort
        string ClientId
        string Username
        string Password
    }
    
    ErrorHandlingConfig {
        string ToleranceLevel
        RetryPolicy RetryPolicy
        FallbackStrategy FallbackStrategy
    }
    
    LoggingConfig {
        LogLevel LogLevel
        SerilogConfig Serilog
    }
```

### 5.2 数据定义语言

**配置模型类定义**

```csharp
// 统一配置模型
public class ConfigurationModel
{
    [Required(ErrorMessage = "EventProcessor配置不能为空")]
    public EventConfiguration EventProcessor { get; set; }
    
    [Required(ErrorMessage = "MQTT配置不能为空")]
    public MqttConfiguration Mqtt { get; set; }
    
    public ErrorHandlingConfiguration ErrorHandling { get; set; }
    
    public LoggingConfiguration Logging { get; set; }
    
    public SerilogConfiguration Serilog { get; set; }
}

// 告警配置模型
public class AlarmConfiguration
{
    /// <summary>
    /// 字段映射列表
    /// </summary>
    public FieldMapping[]? Fields { get; set; }

    /// <summary>
    /// 自定义告警模板
    /// </summary>
    public string? CustomTemplate { get; set; }

    /// <summary>
    /// 自定义告警主题（可选）
    /// 如果指定，则使用此主题发送告警消息
    /// 如果未指定，则使用默认格式：{CompanyName}/{CommId}/{PositionId}/event
    /// </summary>
    public string? CustomAlarmTopic { get; set; }

    /// <summary>
    /// 自定义告警撤销主题（可选）
    /// 如果指定，则使用此主题发送告警撤销消息
    /// 如果未指定，则使用默认格式：{CompanyName}/{CommId}/{PositionId}/cancellation
    /// </summary>
    public string? CustomAlarmCancellationTopic { get; set; }
}

// 字段映射配置 - 支持动态字段引用
public class FieldMapping
{
    /// <summary>
    /// 告警字段名称
    /// </summary>
    [Required(ErrorMessage = "告警字段名称不能为空")]
    public required string AlarmFieldName { get; set; }

    /// <summary>
    /// 源规则类型：ExclusionRules、BusinessRules、AIResultRules、DeviceSignal
    /// </summary>
    [Required(ErrorMessage = "源规则类型不能为空")]
    [RegularExpression("^(ExclusionRules|BusinessRules|AIResultRules|DeviceSignal)$", 
        ErrorMessage = "源规则类型必须是ExclusionRules、BusinessRules、AIResultRules或DeviceSignal")]
    public required string SourceRuleType { get; set; }

    /// <summary>
    /// 源字段名称（支持多字段用逗号分隔）
    /// </summary>
    [Required(ErrorMessage = "源字段名称不能为空")]
    public required string SourceFieldName { get; set; }

    /// <summary>
    /// 默认值（字段不存在时使用）
    /// </summary>
    public string? DefaultValue { get; set; }

    /// <summary>
    /// 格式化模板（支持{fieldName}占位符）
    /// </summary>
    public string? FormatTemplate { get; set; }
}

// 验证结果模型
public class ValidationResult
{
    public bool IsValid { get; set; }
    public List<ValidationError> Errors { get; set; } = new();
    public List<ValidationWarning> Warnings { get; set; } = new();
}

// 验证错误模型
public class ValidationError
{
    public string PropertyName { get; set; }
    public string ErrorMessage { get; set; }
    public string TabName { get; set; }
    public object AttemptedValue { get; set; }
}
```

**表单控件绑定配置**

```csharp
// 表单控件映射
public class FormControlMapping
{
    public string PropertyName { get; set; }
    public Control FormControl { get; set; }
    public string TabPageName { get; set; }
    public bool IsRequired { get; set; }
    public string ValidationGroup { get; set; }
}

// Tab页配置
public class TabPageConfiguration
{
    public string TabName { get; set; }
    public string DisplayName { get; set; }
    public List<FormControlMapping> Controls { get; set; }
    public int DisplayOrder { get; set; }
}

// AlarmConfig Tab页配置
public class AlarmConfigTabConfiguration : TabPageConfiguration
{
    public DataGridView FieldsDataGridView { get; set; }
    public Button AddFieldButton { get; set; }
    public Button RemoveFieldButton { get; set; }
    public TextBox CustomAlarmTopicTextBox { get; set; }
    public TextBox CustomAlarmCancellationTopicTextBox { get; set; }
    public TextBox CustomTemplateTextBox { get; set; }
}
```

**初始化数据**

```csharp
// 默认配置模板
public static ConfigurationModel GetDefaultConfiguration()
{
    return new ConfigurationModel
    {
        EventProcessor = new EventConfiguration
        {
            EventId = "EV000000",
            EventName = "新建事件",
            EvaluationStrategy = "BusinessOnly",
            Priority = "P3",
            CommId = "",
            PositionId = "",
            CompanyName = "hq",
            AlarmGracePeriodSeconds = 3,
            EnableAlarmCancellation = true,
            CustomTimeWindowMinutes = 5,
            RuleConfiguration = new RuleConfiguration
            {
                AlarmConfig = new AlarmConfiguration
                {
                    CustomAlarmTopic = "",
                    CustomAlarmCancellationTopic = "",
                    CustomTemplate = "",
                    Fields = new FieldMapping[]
                    {
                        new FieldMapping
                        {
                            AlarmFieldName = "详情",
                            SourceRuleType = "BusinessRules",
                            SourceFieldName = "CardType,log_car_no,log_user_name,log_end_time",
                            FormatTemplate = "[{CardType}][{log_car_no}][{log_user_name}][{log_end_time}]"
                        },
                        new FieldMapping
                        {
                            AlarmFieldName = "事件",
                            SourceRuleType = "DeviceSignal",
                            SourceFieldName = "duration",
                            FormatTemplate = "停留时间{duration}秒"
                        },
                        new FieldMapping
                        {
                            AlarmFieldName = "设备",
                            SourceRuleType = "DeviceSignal",
                            SourceFieldName = "",
                            DefaultValue = "帮豆你门岗智能监测"
                        },
                        new FieldMapping
                        {
                            AlarmFieldName = "名称",
                            SourceRuleType = "DeviceSignal",
                            SourceFieldName = "",
                            DefaultValue = "月租车超时滞留出口（卡未过期）"
                        },
                        new FieldMapping
                        {
                            AlarmFieldName = "等级",
                            SourceRuleType = "DeviceSignal",
                            SourceFieldName = "",
                            DefaultValue = "通知"
                        }
                    }
                }
            }
        },
        Mqtt = new MqttConfiguration
        {
            BrokerHost = "localhost",
            BrokerPort = 1883,
            ClientId = "EPConfigTool",
            KeepAliveInterval = 60,
            ReconnectDelay = 5,
            QualityOfServiceLevel = 1
        }
    };
}
```

### 5.3 验证逻辑

**AlarmConfig验证方法**

```csharp
public ValidationResult ValidateAlarmConfiguration(AlarmConfiguration alarmConfig)
{
    var result = new ValidationResult { IsValid = true };
    
    // 验证字段映射
    if (alarmConfig.Fields != null)
    {
        foreach (var field in alarmConfig.Fields)
        {
            var fieldResult = ValidateFieldMapping(field);
            if (!fieldResult.IsValid)
            {
                result.IsValid = false;
                result.Errors.AddRange(fieldResult.Errors);
            }
        }
    }
    
    // 验证自定义主题格式
    if (!string.IsNullOrEmpty(alarmConfig.CustomAlarmTopic))
    {
        if (!IsValidMqttTopic(alarmConfig.CustomAlarmTopic))
        {
            result.IsValid = false;
            result.Errors.Add(new ValidationError
            {
                PropertyName = "CustomAlarmTopic",
                ErrorMessage = "自定义告警主题格式无效",
                TabName = "AlarmConfig"
            });
        }
    }
    
    if (!string.IsNullOrEmpty(alarmConfig.CustomAlarmCancellationTopic))
    {
        if (!IsValidMqttTopic(alarmConfig.CustomAlarmCancellationTopic))
        {
            result.IsValid = false;
            result.Errors.Add(new ValidationError
            {
                PropertyName = "CustomAlarmCancellationTopic",
                ErrorMessage = "自定义告警撤销主题格式无效",
                TabName = "AlarmConfig"
            });
        }
    }
    
    return result;
}

public ValidationResult ValidateFieldMapping(FieldMapping fieldMapping)
{
    var result = new ValidationResult { IsValid = true };
    
    // 验证源规则类型
    if (!Regex.IsMatch(fieldMapping.SourceRuleType, "^(ExclusionRules|BusinessRules|AIResultRules|DeviceSignal)$"))
    {
        result.IsValid = false;
        result.Errors.Add(new ValidationError
        {
            PropertyName = "SourceRuleType",
            ErrorMessage = "源规则类型必须是ExclusionRules、BusinessRules、AIResultRules或DeviceSignal",
            TabName = "AlarmConfig"
        });
    }
    
    // 验证格式化模板语法
    if (!string.IsNullOrEmpty(fieldMapping.FormatTemplate))
    {
        if (!IsValidFormatTemplate(fieldMapping.FormatTemplate))
        {
            result.IsValid = false;
            result.Errors.Add(new ValidationError
            {
                PropertyName = "FormatTemplate",
                ErrorMessage = "格式化模板语法无效",
                TabName = "AlarmConfig"
            });
        }
    }
    
    return result;
}
```

### 5.4 完整配置示例

```yaml
# Event Processor V4.1 统一配置文件
EventProcessor:
  EventId: "EV001001"
  EventName: "月租车未过期超时滞留出口"
  EvaluationStrategy: "BusinessOnly"
  Priority: "P3"
  CommId: "101013"
  PositionId: "P002LfyBmOut"
  CompanyName: "hq"
  AlarmGracePeriodSeconds: 3
  EnableAlarmCancellation: true
  CustomTimeWindowMinutes: 5
  
  DeviceSignal:
    Topics:
      - "device/BDN888/event"
    TriggerField: "I2"
    TriggerValues:
      "true": "0"
      "false": "1"
    HoldingTimeoutSec: 20
  
  RuleConfiguration:
    BusinessRules:
      - SourceTopic: "ajb/101013/out/P088LfyBmOut/time_log"
        LogicOperator: "AND"
        ConditionGroups:
          - LogicOperator: "OR"
            Conditions:
              - FieldName: "CardType"
                DataType: "string"
                Operator: "In"
                Value: "月租卡|万全卡|贵宾卡|储值卡"
                Description: "有效卡类型"
          - LogicOperator: "OR"
            Conditions:
              - FieldName: "log_remain_days"
                DataType: "number"
                Operator: "NotEqual"
                Value: "0"
                Description: "卡未过期 (剩余天数不为0)"
    
    AlarmConfig:
      CustomAlarmTopic: "hq/101013/P002LfyBmOut/event"
      CustomAlarmCancellationTopic: "hq/101013/P002LfyBmOut/cancellation"
      Fields:
        - AlarmFieldName: "详情"
          SourceRuleType: "BusinessRules"
          SourceFieldName: "CardType,log_car_no,log_user_name,log_end_time"
          FormatTemplate: "[{CardType}][{log_car_no}][{log_user_name}][{log_end_time}]"
        - AlarmFieldName: "事件"
          SourceRuleType: "DeviceSignal"
          SourceFieldName: "duration"
          FormatTemplate: "停留时间{duration}秒"
        - AlarmFieldName: "设备"
          SourceRuleType: "DeviceSignal"
          SourceFieldName: ""
          DefaultValue: "帮豆你门岗智能监测"
        - AlarmFieldName: "名称"
          SourceRuleType: "DeviceSignal"
          SourceFieldName: ""
          DefaultValue: "月租车超时滞留出口（卡未过期）"
        - AlarmFieldName: "等级"
          SourceRuleType: "DeviceSignal"
          SourceFieldName: ""
          DefaultValue: "通知"

Mqtt:
  BrokerHost: "mq.bangdouni.com"
  BrokerPort: 1883
  ClientId: "EP_V4.1_EV001001_TEST"
  Username: "bdn_ai_process"
  Password: "Bdn@2024"
  KeepAliveInterval: 60
  ReconnectDelay: 5
  QualityOfServiceLevel: 1

ErrorHandling:
  ToleranceLevel: "Normal"
  RetryPolicy:
    MaxRetries: 3
    RetryDelay: 1000
    BackoffMultiplier: 2.0
    MaxRetryDelay: 30000
  FallbackStrategy:
    OnRuleFailure: "ContinueProcessing"
    OnAIFailure: "UseBusinessOnly"
    OnTimerFailure: "ImmediateAlarm"
    OnMqttFailure: "Retry"

Logging:
  LogLevel:
    Default: "Information"
    Microsoft: "Warning"
    Microsoft.Hosting.Lifetime: "Information"
    EventProcessor: "Debug"

Serilog:
  Using:
    - "Serilog.Sinks.Console"
    - "Serilog.Sinks.File"
  MinimumLevel:
    Default: "Information"
    Override:
      Microsoft: "Warning"
      System: "Warning"
      EventProcessor: "Debug"
  WriteTo:
    - Name: "Console"
      Args:
        outputTemplate: "[{Timestamp:HH:mm:ss.fff} {Level:u3}] {Message:lj}{NewLine}{Exception}"
    - Name: "File"
      Args:
        path: "logs/eventprocessor-.log"
        rollingInterval: "Day"
        retainedFileCountLimit: 30
        outputTemplate: "[{Timestamp:HH:mm:ss.fff} {Level:u3}] {Message:lj}{NewLine}{Exception}"
  Enrich:
    - "FromLogContext"
    - "WithMachineName"
    - "WithThreadId"
```

