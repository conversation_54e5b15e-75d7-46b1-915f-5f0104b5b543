## 1. Product Overview
EPConfigTool校验功能修复项目旨在解决当前配置验证体系中的关键缺陷，建立完整、一致的配置验证标准。
- 核心问题：MainViewModel.ValidateCurrentConfigurationAsync方法在内存验证路径中仅进行序列化循环测试，直接返回IsValid=true，未执行实际验证，导致新建配置与文件配置使用不同的验证标准。
- 目标：确保所有配置无论是从文件加载还是内存创建，都使用相同的验证逻辑和标准，提升配置质量和用户体验。

## 2. Core Features

### 2.1 User Roles
本项目不涉及用户角色区分，所有功能面向EPConfigTool的使用者。

### 2.2 Feature Module
我们的校验功能修复需求包含以下主要页面：
1. **配置验证页面**：核心验证逻辑重构，内存配置验证实现。
2. **验证结果显示页面**：增强的验证结果展示，错误分类和修复建议。
3. **实时验证反馈页面**：输入字段实时验证，视觉反馈优化。

### 2.3 Page Details

| Page Name | Module Name | Feature description |
|-----------|-------------|---------------------|
| 配置验证页面 | 核心验证逻辑 | 重构ValidateCurrentConfigurationAsync方法，新增ValidateInMemoryConfigurationAsync方法实现完整的内存配置验证，包括DataAnnotations基础验证、业务逻辑验证、配置一致性验证和深度结构验证 |
| 配置验证页面 | 基础字段验证 | 实现EventId格式验证(^EV\d{6}$)、EventName长度验证(≤100字符)、EvaluationStrategy枚举验证、Priority格式验证(^P[1-5]$)、CommId和PositionId必填验证等基础字段验证规则 |
| 配置验证页面 | 业务逻辑验证 | 实现AI策略验证(AI策略必须配置AI提示词)、时间窗口验证(自定义时间窗口必须指定大小)、AI提示词长度检查(10-1000字符)等业务逻辑一致性验证 |
| 配置验证页面 | 设备信号配置验证 | 验证MQTT主题格式、触发字段名格式、触发值映射完整性、保持超时时间有效性等设备信号相关配置 |
| 配置验证页面 | 规则配置验证 | 验证排除规则、业务规则、AI结果规则的完整性，包括逻辑操作符、条件列表、字段名格式、数据类型、操作符兼容性等 |
| 验证结果显示页面 | 验证结果对话框 | 创建ValidationResultDialog显示详细验证结果，包括错误分类显示(基础验证、业务逻辑、一致性检查)、修复建议、警告信息重要性级别 |
| 验证结果显示页面 | 错误信息增强 | 提供清晰的错误消息和具体的修复建议，支持错误快速定位和问题解决指导 |
| 实时验证反馈页面 | 输入实时验证 | 在EventViewModel中添加属性验证，实现EventId、EvaluationStrategy等关键字段的实时格式验证和错误提示 |
| 实时验证反馈页面 | 视觉反馈优化 | 创建ValidatedTextBoxStyle样式，添加验证状态显示、ToolTip增强、错误状态视觉反馈等UI改进 |
| 实时验证反馈页面 | 验证样式系统 | 创建ValidationStyles.xaml包含验证错误显示模板、验证提示样式等统一的验证UI组件 |

## 3. Core Process

**主要验证流程**：
1. 用户触发配置验证 → 系统判断是文件验证还是内存验证
2. 文件验证：调用_configService.ValidateYamlFileAsync进行完整验证
3. 内存验证：调用新增的ValidateInMemoryConfigurationAsync方法
4. 执行四层验证：DataAnnotations基础验证 → 业务逻辑验证 → 配置一致性验证 → 深度结构验证
5. 收集错误和警告信息 → 生成YamlValidationResult
6. 显示增强的验证结果对话框，提供错误分类和修复建议

**实时验证流程**：
1. 用户输入配置字段 → 触发属性变更事件
2. 执行对应字段的实时验证逻辑
3. 更新UI验证状态和错误提示
4. 提供即时的视觉反馈和修复建议

```mermaid
graph TD
    A[用户触发验证] --> B{验证类型判断}
    B -->|文件验证| C[ValidateYamlFileAsync]
    B -->|内存验证| D[ValidateInMemoryConfigurationAsync]
    D --> E[DataAnnotations验证]
    E --> F[业务逻辑验证]
    F --> G[配置一致性验证]
    G --> H[深度结构验证]
    H --> I[生成验证结果]
    C --> I
    I --> J[显示验证结果对话框]
    
    K[用户输入字段] --> L[实时验证]
    L --> M[更新UI状态]
    M --> N[显示错误提示]
```

## 4. User Interface Design

### 4.1 Design Style
- **主色调**：保持现有EPConfigTool的蓝色主题(#0078D4)，错误状态使用红色(#D13438)，警告使用橙色(#FF8C00)，成功状态使用绿色(#107C10)
- **按钮样式**：现代化扁平设计，圆角按钮，支持悬停和点击状态变化
- **字体**：Microsoft YaHei UI，标题14px，正文12px，错误提示11px
- **布局风格**：卡片式布局，清晰的分组和层次结构，充分利用空白空间
- **图标样式**：使用Segoe MDL2 Assets字体图标，错误❌、警告⚠️、成功✅、信息ℹ️

### 4.2 Page Design Overview

| Page Name | Module Name | UI Elements |
|-----------|-------------|-------------|
| 配置验证页面 | 验证按钮区域 | 重新设计验证按钮，添加加载状态指示器，按钮文字"验证配置"，支持快捷键Ctrl+T |
| 验证结果显示页面 | 验证结果对话框 | 模态对话框设计，600x400px，包含标题栏、错误分类树形视图、详细信息面板、操作按钮区域，支持错误项展开/折叠 |
| 验证结果显示页面 | 错误分类显示 | 使用TreeView控件，根节点为验证类型(基础验证、业务逻辑等)，子节点为具体错误，不同类型使用不同图标和颜色 |
| 实时验证反馈页面 | 验证状态文本框 | TextBox边框颜色动态变化(正常#CCCCCC，错误#D13438，警告#FF8C00)，右侧显示验证状态图标 |
| 实时验证反馈页面 | 错误提示ToolTip | 增强的ToolTip设计，包含错误描述、修复建议、示例格式，背景色#FFFBF0，边框#FF8C00 |
| 实时验证反馈页面 | 验证摘要面板 | 页面底部显示验证摘要，实时更新错误和警告数量，提供"查看详情"链接 |

### 4.3 Responsiveness
本项目为桌面WPF应用，主要考虑不同屏幕分辨率的适配，最小支持1024x768分辨率，验证对话框支持窗口大小调整和内容自适应布局。