# Event Processor V3 配置

COMM_ID=101013
POSITION_ID=P001LfyBmIn
EVENT_IDS=["EV001008", "EV001009", "EV001003", "EV001005"]

# 日志配置
LOG_LEVEL=DEBUG

# MQTT配置
MQTT_BROKER_HOST=mq.bangdouni.com
MQTT_BROKER_PORT=1883
MQTT_USERNAME=bdn_ai_process
MQTT_PASSWORD=Bdn@2024

# 位置信息
EP_POSITION_NAME=来福园北门车辆入口

# FTP配置
FTP_HOST=api.bangdouni.com
FTP_PORT=22221
FTP_USERNAME=bdn_ftp_client
FTP_PASSWORD=Bdn@2024
FTP_REMOTE_DIR=fcdn
FTP_IS_ENABLED=true
FTP_URL_PREFIX=http://api.bangdouni.com

# 统一时间参数
EP_AI_ANALYSIS_DELAY_SEC=6                     # AI分析延迟启动时间（秒）

# 统一图片配置（本实例专注入口车辆监控）
EP_PV_BIND_IPC_IMAGE_DIR=Y:\LFY-IN01
EP_PV_IMAGE_CROP_COORDINATES=[751, 652, 2533, 1343]

# ============ 事件配置（EV001008 - 三轮车快递车滞留） ============
EVENT_ID_EV001008_EP_START_DEVICE_EVENT_TOPIC=["device/BDN861290073715232/event"]
EVENT_ID_EV001008_EP_START_FIELD_FROM_DEVICE_EVENT=I1
EVENT_ID_EV001008_EP_POSITION_START_VALUE_FROM_DEVICE_EVENT={"true": "0", "false": "1"}
EVENT_ID_EV001008_EP_PV_HOLDING_TIMEOUT=11
# EV001008排除匹配配置（车牌识别排除三轮车）
EVENT_ID_EV001008_EP_MQTT_EXCLUDE_INFO_SOURCE=ajb/101013/in/P001LfyBmIn/time_log
EVENT_ID_EV001008_EP_MQTT_EXCLUDE_INFO_FIELD=log_car_no
EVENT_ID_EV001008_EP_MQTT_EXCLUDE_LOGIC=exists
# EV001008 AI提示词配置
EVENT_ID_EV001008_EP_AI_PROMPT=图片是否存在以下类型的车辆[三轮车]或[快递车], 请用JSON格式返回是或否. 返回格式: {"detected": true/false, "vehicle_types": ["检测到的车辆类型"], "confidence": 0.95}
# EV001008告警配置
EVENT_ID_EV001008_EP_PV_ALARM_TOPIC=hq/101013/P001LfyBmIn/event
EVENT_ID_EV001008_EP_PV_DETAIL_INFO_TOPIC=ajb/101013/in/P001LfyBmIn/time_log
EVENT_ID_EV001008_EP_PV_DETAIL_INFO_FIELDS=[{"field_name": "设备", "field_keyword": "", "field_default":"帮豆你智能门岗监测"}, {"field_name": "名称", "field_keyword": "", "field_default":"三轮车快递车超时滞留入口"}, {"field_name": "等级", "field_keyword": "", "field_default":"通知"}]

# ============ 事件配置（EV001009 - 特种车超时滞留入口） ============
EVENT_ID_EV001009_EP_START_DEVICE_EVENT_TOPIC=["device/BDN861290073715232/event"]
EVENT_ID_EV001009_EP_START_FIELD_FROM_DEVICE_EVENT=I1
EVENT_ID_EV001009_EP_POSITION_START_VALUE_FROM_DEVICE_EVENT={"true": "0", "false": "1"}
EVENT_ID_EV001009_EP_PV_HOLDING_TIMEOUT=10
# EV001009排除匹配配置（卡类型(CardType)为月租卡或万全卡或贵宾卡或储值卡排除特种车）
EVENT_ID_EV001009_EP_MQTT_EXCLUDE_INFO_SOURCE=ajb/101013/in/P001LfyBmIn/time_log
EVENT_ID_EV001009_EP_MQTT_EXCLUDE_INFO_FIELD=CardType
EVENT_ID_EV001009_EP_MQTT_EXCLUDE_LOGIC=exists
# EV001009 AI提示词配置
EVENT_ID_EV001009_EP_AI_PROMPT=图片是否存在以下类型的车辆:[消防车、救护车、垃圾清运车、警车、工程车], 请用JSON格式返回检测结果. 返回格式: {"detected": true/false, "vehicle_types": ["检测到的特种车辆类型"], "emergency_level": "high/medium/low"}
# EV001009告警配置
EVENT_ID_EV001009_EP_PV_ALARM_TOPIC=hq/101013/P001LfyBmIn/event
EVENT_ID_EV001009_EP_PV_DETAIL_INFO_TOPIC=ajb/101013/in/P001LfyBmIn/time_log
EVENT_ID_EV001009_EP_PV_DETAIL_INFO_FIELDS=[{"field_name": "设备", "field_keyword": "", "field_default":"帮豆你智能门岗监测"}, {"field_name": "名称", "field_keyword": "", "field_default":"特种车超时滞留入口"},{"field_name": "车牌", "field_keyword": "log_car_no", "field_default":""}, {"field_name": "等级", "field_keyword": "", "field_default":"通知"}]

# ============ 事件配置（EV001003 - 月租车未过期超时滞留入口） ============
EVENT_ID_EV001003_EP_START_DEVICE_EVENT_TOPIC=["device/BDN861290073715232/event"]
EVENT_ID_EV001003_EP_START_FIELD_FROM_DEVICE_EVENT=I1
EVENT_ID_EV001003_EP_POSITION_START_VALUE_FROM_DEVICE_EVENT={"true": "0", "false": "1"}
EVENT_ID_EV001003_EP_PV_HOLDING_TIMEOUT=20
# 业务逻辑判断配置：基于CardType字段判断储值卡
EVENT_ID_EV001003_EP_BusinessLogic_JUDGE_SOURCE=ajb/101013/in/P001LfyBmIn/time_log
EVENT_ID_EV001003_EP_BusinessLogic_JUDGE_FIELD=CardType
EVENT_ID_EV001003_EP_BusinessLogic_JUDGE_LOGIC=contains
EVENT_ID_EV001003_EP_BusinessLogic_JUDGE_VALUE=月租卡|万全卡|贵宾卡|储值卡
# EV001003排除匹配配置（为业务逻辑事件添加排除匹配支持）
EVENT_ID_EV001003_EP_MQTT_EXCLUDE_INFO_SOURCE=ajb/101013/in/P001LfyBmIn/time_log
EVENT_ID_EV001003_EP_MQTT_EXCLUDE_INFO_FIELD=CardType
EVENT_ID_EV001003_EP_MQTT_EXCLUDE_LOGIC=exists
# EV001001告警配置
EVENT_ID_EV001003_EP_PV_ALARM_TOPIC=hq/101013/P001LfyBmIn/event
EVENT_ID_EV001003_EP_PV_DETAIL_INFO_TOPIC=ajb/101013/in/P001LfyBmIn/time_log
EVENT_ID_EV001003_EP_PV_DETAIL_INFO_FIELDS=[{"field_name": "详情", "field_keyword": "CardType,log_car_no,log_user_name,log_end_time", "field_format": "[{CardType}][{log_car_no}][{log_user_name}][{log_end_time}]"}, {"field_name": "事件", "field_keyword": "duration", "field_format": "停留时间{duration}秒"}, {"field_name": "设备", "field_keyword": "", "field_default":"帮豆你门岗智能监测"}, {"field_name": "等级", "field_keyword": "", "field_default":"通知"}]

# ============ 事件配置（EV001005 - 临时车超时滞留入口） ============
EVENT_ID_EV001005_EP_START_DEVICE_EVENT_TOPIC=["device/BDN861290073715232/event"]
EVENT_ID_EV001005_EP_START_FIELD_FROM_DEVICE_EVENT=I1
EVENT_ID_EV001005_EP_POSITION_START_VALUE_FROM_DEVICE_EVENT={"true": "0", "false": "1"}
EVENT_ID_EV001005_EP_PV_HOLDING_TIMEOUT=20
# 业务逻辑判断配置：基于CardType字段判断储值卡
EVENT_ID_EV001005_EP_BusinessLogic_JUDGE_SOURCE=ajb/101013/in/P001LfyBmIn/time_log
EVENT_ID_EV001005_EP_BusinessLogic_JUDGE_FIELD=CardType
EVENT_ID_EV001005_EP_BusinessLogic_JUDGE_LOGIC=contains
EVENT_ID_EV001005_EP_BusinessLogic_JUDGE_VALUE=临保卡
# EV001005排除匹配配置（为业务逻辑事件添加排除匹配支持）
EVENT_ID_EV001005_EP_MQTT_EXCLUDE_INFO_SOURCE=ajb/101013/in/P001LfyBmIn/time_log
EVENT_ID_EV001005_EP_MQTT_EXCLUDE_INFO_FIELD=CardType
EVENT_ID_EV001005_EP_MQTT_EXCLUDE_LOGIC=exists
# EV001001告警配置
EVENT_ID_EV001005_EP_PV_ALARM_TOPIC=hq/101013/P001LfyBmIn/event
EVENT_ID_EV001005_EP_PV_DETAIL_INFO_TOPIC=ajb/101013/in/P001LfyBmIn/time_log
EVENT_ID_EV001005_EP_PV_DETAIL_INFO_FIELDS=[{"field_name": "详情", "field_keyword": "CardType,log_car_no,log_user_name,log_end_time", "field_format": "[{CardType}][{log_car_no}]"}, {"field_name": "事件", "field_keyword": "duration", "field_format": "停留时间{duration}秒"}, {"field_name": "设备", "field_keyword": "", "field_default":"帮豆你门岗智能监测"}, {"field_name": "等级", "field_keyword": "", "field_default":"通知"}]
