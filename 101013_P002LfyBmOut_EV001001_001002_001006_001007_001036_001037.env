# Event Processor V3 配置

COMM_ID=101013
POSITION_ID=P002LfyBmOut
EVENT_IDS=["EV001001", "EV001002", "EV001006", "EV001036", "EV001037"]

# 日志配置
LOG_LEVEL=DEBUG

# MQTT配置
MQTT_BROKER_HOST=mq.bangdouni.com
MQTT_BROKER_PORT=1883
MQTT_USERNAME=bdn_ai_process
MQTT_PASSWORD=Bdn@2024

# 位置信息
EP_POSITION_NAME=来福园北门车辆出口

# FTP配置
FTP_HOST=api.bangdouni.com
FTP_PORT=22221
FTP_USERNAME=bdn_ftp_client
FTP_PASSWORD=Bdn@2024
FTP_REMOTE_DIR=fcdn
FTP_IS_ENABLED=true
FTP_URL_PREFIX=http://api.bangdouni.com

# AI模型配置
# 必需参数
EDGE_AI_API_KEY=sk-12dc12c0d5854d3e9ac0ce7d6c6527f7
EDGE_AI_MODEL_NAME=qwen-vl-plus-latest
EDGE_AI_MODEL_URL=https://dashscope.aliyuncs.com/compatible-mode/v1

# 可选参数
EDGE_AI_CONFIDENCE_THRESHOLD=0.5
EDGE_AI_TIMEOUT=30
EDGE_AI_MAX_RETRIES=3

# 统一时间参数
EP_STAGE1_DURATION=6                           # 阶段1时长（秒），用于AI触发时机
EP_AGGREGATION_TIME_WINDOW=1000   # 聚合时间窗口（毫秒）

# 统一图片配置（本实例处理出口车辆监控）
EP_PV_BIND_IPC_IMAGE_DIR=E:\FTP-IPC\IPC-LFY-OUT01
EP_PV_IMAGE_CROP_COORDINATES=[1102,129,1672,849]

# 性能参数
EP_MERGED_PROMPT_MAX_LENGTH=1000 # 合并提示词最大长度
EP_AI_RESULT_TIMEOUT=60                        # AI结果等待超时（秒）

# ============ 事件配置说明 ============
# EP_PV_EXCLUDE_INFO_FIELDS中的排除条件：exclude_condition 说明
# 可选4种值: "exists", "equals", "contains", "regex"
#     - exists条件: 无需填写exclude_value或留空
#     - equals条件: 填写完全匹配的值，支持多值用|分隔
#     - contains条件: 填写包含的字符串，支持多值用|分隔  
#     - regex条件: 填写正则表达式，支持多表达式用|分隔
# 示例: "exclude_condition": "contains", "exclude_value": "月租卡|万全卡|贵宾卡"
# 示例: "exclude_condition": "exists", "exclude_value": ""
# 示例: "exclude_condition": "equals", "exclude_value": "粤A12345" 

# ============ 事件配置（EV001001 - 月租车未过期超时滞留出口） ============
EVENT_ID_EV001001_EP_START_DEVICE_EVENT_TOPIC=["device/BDN861290073715232/event"]
EVENT_ID_EV001001_EP_START_FIELD_FROM_DEVICE_EVENT=I2
EVENT_ID_EV001001_EP_POSITION_START_VALUE_FROM_DEVICE_EVENT={"true": "0", "false": "1"}
EVENT_ID_EV001001_EP_PV_HOLDING_TIMEOUT=20
EVENT_ID_EV001001_EP_EV_PRIORITY=P3
# 业务逻辑判断配置：基于CardType字段判断储值卡
EVENT_ID_EV001001_EP_BL_JUDGE_FIELD=CardType
EVENT_ID_EV001001_EP_BL_JUDGE_CONDITION=contains
EVENT_ID_EV001001_EP_BL_JUDGE_VALUE=月租卡|万全卡|贵宾卡|储值卡
# EV001001排除匹配配置（为业务逻辑事件添加排除匹配支持）
EVENT_ID_EV001001_EP_PV_EXCLUDE_INFO_SOURCE_TYPE=MQTT
EVENT_ID_EV001001_EP_PV_EXCLUDE_INFO_SOURCE=ajb/101013/out/P002LfyBmOut/time_log
EVENT_ID_EV001001_EP_PV_EXCLUDE_INFO_FIELDS=[{"field_name": "业务", "field_keyword": "log_remain_days", "exclude_condition": "contains", "exclude_value": "0"}]
EVENT_ID_EV001001_EP_PV_EXCLUDE_LOGIC=ANY
# EV001001告警配置
EVENT_ID_EV001001_EP_PV_ALARM_TOPIC=hq/101013/P002LfyBmOut/event
EVENT_ID_EV001001_EP_PV_DETAIL_INFO_TOPIC=ajb/101013/out/P002LfyBmOut/time_log
EVENT_ID_EV001001_EP_PV_DETAIL_INFO_FIELDS=[{"field_name": "详情", "field_keyword": "CardType,log_car_no,log_user_name,log_end_time", "field_format": "[{CardType}][{log_car_no}][{log_user_name}][{log_end_time}]"}, {"field_name": "事件", "field_keyword": "duration", "field_format": "停留时间{duration}秒"}, {"field_name": "设备", "field_keyword": "", "field_default":"帮豆你门岗智能监测"}, {"field_name": "名称", "field_keyword": "", "field_default":"月租车超时滞留出口（卡未过期）"},{"field_name": "等级", "field_keyword": "", "field_default":"通知"}]

# ============ 事件配置（EV001002 - 月租车过期超时滞留出口） ============
EVENT_ID_EV001002_EP_START_DEVICE_EVENT_TOPIC=["device/BDN861290073715232/event"]
EVENT_ID_EV001002_EP_START_FIELD_FROM_DEVICE_EVENT=I2
EVENT_ID_EV001002_EP_POSITION_START_VALUE_FROM_DEVICE_EVENT={"true": "0", "false": "1"}
EVENT_ID_EV001002_EP_PV_HOLDING_TIMEOUT=20
EVENT_ID_EV001002_EP_EV_PRIORITY=P3
# 业务逻辑判断配置：基于CardType字段判断储值卡
EVENT_ID_EV001002_EP_BL_JUDGE_FIELD=CardType
EVENT_ID_EV001002_EP_BL_JUDGE_CONDITION=contains
EVENT_ID_EV001002_EP_BL_JUDGE_VALUE=月租卡|万全卡|贵宾卡|储值卡
# EV001002排除匹配配置（为业务逻辑事件添加排除匹配支持）
EVENT_ID_EV001002_EP_PV_EXCLUDE_INFO_SOURCE_TYPE=None
EVENT_ID_EV001002_EP_PV_EXCLUDE_INFO_SOURCE=None
EVENT_ID_EV001002_EP_PV_EXCLUDE_INFO_FIELDS=None
EVENT_ID_EV001002_EP_PV_EXCLUDE_LOGIC=None
# EV001002告警配置
EVENT_ID_EV001002_EP_PV_ALARM_TOPIC=hq/101013/P002LfyBmOut/event
EVENT_ID_EV001002_EP_PV_DETAIL_INFO_TOPIC=ajb/101013/out/P002LfyBmOut/time_log
EVENT_ID_EV001002_EP_PV_DETAIL_INFO_FIELDS=[{"field_name": "详情", "field_keyword": "CardType,log_car_no,log_user_name,log_end_time", "field_format": "[{CardType}][{log_car_no}][{log_user_name}][{log_end_time}]"}, {"field_name": "事件", "field_keyword": "duration", "field_format": "停留时间{duration}秒"}, {"field_name": "设备", "field_keyword": "", "field_default":"帮豆你门岗智能监测"},{"field_name": "名称", "field_keyword": "", "field_default":"月租车超时滞留出口（卡已过期）"}, {"field_name": "等级", "field_keyword": "", "field_default":"通知"}]

# ============ 事件配置（EV001006 - 临时车超时滞留入口（未缴费）） ============
EVENT_ID_EV001006_EP_START_DEVICE_EVENT_TOPIC=["device/BDN861290073715232/event"]
EVENT_ID_EV001006_EP_START_FIELD_FROM_DEVICE_EVENT=I2
EVENT_ID_EV001006_EP_POSITION_START_VALUE_FROM_DEVICE_EVENT={"true": "0", "false": "1"}
EVENT_ID_EV001006_EP_PV_HOLDING_TIMEOUT=20
EVENT_ID_EV001006_EP_EV_PRIORITY=P1
# 业务逻辑判断配置：基于CardType字段判断储值卡
EVENT_ID_EV001006_EP_BL_JUDGE_FIELD=CardType
EVENT_ID_EV001006_EP_BL_JUDGE_CONDITION=contains
EVENT_ID_EV001006_EP_BL_JUDGE_VALUE=临保卡
# EV001001排除匹配配置（为业务逻辑事件添加排除匹配支持）
EVENT_ID_EV001006_EP_PV_EXCLUDE_INFO_SOURCE_TYPE=MQTT
EVENT_ID_EV001006_EP_PV_EXCLUDE_INFO_SOURCE=ajb/101013/out/P002LfyBmOut/time_log
EVENT_ID_EV001006_EP_PV_EXCLUDE_INFO_FIELDS=[{"field_name": "业务", "field_keyword": "CardType", "exclude_condition": "contains", "exclude_value": "月租卡|万全卡|贵宾卡|储值卡"}]
EVENT_ID_EV001006_EP_PV_EXCLUDE_LOGIC=ANY
# EV001001告警配置
EVENT_ID_EV001006_EP_PV_ALARM_TOPIC=hq/101013/P002LfyBmOut/event
EVENT_ID_EV001006_EP_PV_DETAIL_INFO_TOPIC=ajb/101013/out/P002LfyBmOut/time_log
EVENT_ID_EV001006_EP_PV_DETAIL_INFO_FIELDS=[{"field_name": "详情", "field_keyword": "CardType,log_car_no,log_user_name,log_end_time", "field_format": "[{CardType}][{log_car_no}]"}, {"field_name": "事件", "field_keyword": "duration", "field_format": "停留时间{duration}秒"}, {"field_name": "设备", "field_keyword": "", "field_default":"帮豆你门岗智能监测"}, {"field_name": "名称", "field_keyword": "", "field_default":"临时车超时滞留出口"},{"field_name": "等级", "field_keyword": "", "field_default":"通知"}]

# ============ 事件配置（EV001036 - 三轮车快递车滞留出口） ============
EVENT_ID_EV001036_EP_START_DEVICE_EVENT_TOPIC=["device/BDN861290073715232/event"]
EVENT_ID_EV001036_EP_START_FIELD_FROM_DEVICE_EVENT=I2
EVENT_ID_EV001036_EP_POSITION_START_VALUE_FROM_DEVICE_EVENT={"true": "0", "false": "1"}
EVENT_ID_EV001036_EP_PV_HOLDING_TIMEOUT=11
EVENT_ID_EV001036_EP_EV_PRIORITY=P3
# EV001036排除匹配配置（车牌识别排除三轮车）
EVENT_ID_EV001036_EP_PV_EXCLUDE_INFO_SOURCE_TYPE=MQTT
EVENT_ID_EV001036_EP_PV_EXCLUDE_INFO_SOURCE=ajb/101013/out/P002LfyBmOut/time_log
EVENT_ID_EV001036_EP_PV_EXCLUDE_INFO_FIELDS=[{"field_name": "车牌", "field_keyword": "log_car_no", "exclude_condition": "exists"}]
EVENT_ID_EV001036_EP_PV_EXCLUDE_LOGIC=ANY
# EV001036 AI提示词配置
EVENT_ID_EV001036_EP_AI_PROMPT=图片是否存在以下类型的车辆[三轮车]或[快递车], 请用JSON格式返回是或否. 返回格式: {"detected": true/false, "vehicle_types": ["检测到的车辆类型"], "confidence": 0.95}
# EV001036告警配置
EVENT_ID_EV001036_EP_PV_ALARM_TOPIC=hq/101013/P002LfyBmOut/event
EVENT_ID_EV001036_EP_PV_DETAIL_INFO_TOPIC=ajb/101013/out/P002LfyBmOut/time_log
EVENT_ID_EV001036_EP_PV_DETAIL_INFO_FIELDS=[{"field_name": "设备", "field_keyword": "", "field_default":"帮豆你智能门岗监测"}, {"field_name": "名称", "field_keyword": "", "field_default":"三轮车快递车超时滞留出口"}, {"field_name": "等级", "field_keyword": "", "field_default":"通知"}]

# ============ 事件配置（EV001037 - 特种车超时滞留出口） ============
EVENT_ID_EV001037_EP_START_DEVICE_EVENT_TOPIC=["device/BDN861290073715232/event"]
EVENT_ID_EV001037_EP_START_FIELD_FROM_DEVICE_EVENT=I2
EVENT_ID_EV001037_EP_POSITION_START_VALUE_FROM_DEVICE_EVENT={"true": "0", "false": "1"}
EVENT_ID_EV001037_EP_PV_HOLDING_TIMEOUT=10
EVENT_ID_EV001037_EP_EV_PRIORITY=P2
# EV001037排除匹配配置（卡类型(CardType)为月租卡或万全卡或贵宾卡或储值卡排除特种车）
EVENT_ID_EV001037_EP_PV_EXCLUDE_INFO_SOURCE_TYPE=MQTT
EVENT_ID_EV001037_EP_PV_EXCLUDE_INFO_SOURCE=ajb/101013/out/P002LfyBmOut/time_log
EVENT_ID_EV001037_EP_PV_EXCLUDE_INFO_FIELDS=[{"field_name": "CardType", "field_keyword":"CardType", "exclude_condition": "contains", "exclude_value": "月租卡|万全卡|贵宾卡|储值卡"}]
EVENT_ID_EV001037_EP_PV_EXCLUDE_LOGIC=ANY
# EV001037 AI提示词配置
EVENT_ID_EV001037_EP_AI_PROMPT=图片是否存在以下类型的车辆:[消防车、救护车、垃圾清运车、警车、工程车], 请用JSON格式返回检测结果. 返回格式: {"detected": true/false, "vehicle_types": ["检测到的特种车辆类型"], "emergency_level": "high/medium/low"}
# EV001037告警配置
EVENT_ID_EV001037_EP_PV_ALARM_TOPIC=hq/101013/P002LfyBmOut/event
EVENT_ID_EV001037_EP_PV_DETAIL_INFO_TOPIC=ajb/101013/out/P002LfyBmOut/time_log
EVENT_ID_EV001037_EP_PV_DETAIL_INFO_FIELDS=[{"field_name": "设备", "field_keyword": "", "field_default":"帮豆你智能门岗监测"}, {"field_name": "名称", "field_keyword": "", "field_default":"特种车超时滞留出口"},{"field_name": "车牌", "field_keyword": "log_car_no", "field_default":""}, {"field_name": "等级", "field_keyword": "", "field_default":"通知"}]

