# ================================================================
# EP_V3 统一配置 - 单文件配置（合并AI提示词）
# Event Processor V3 架构统一配置格式
# ================================================================

# ================================================================
# 核心配置区域
# ================================================================

# 基础配置
COMM_ID=101013
POSITION_ID=P002LfyBmOut
EVENT_IDS=["EV001001"]

# 位置信息
EP_POSITION_NAME=来福园北门车辆出口

# 日志配置
LOG_LEVEL=DEBUG

# ================================================================
# 外部服务配置
# ================================================================

# MQTT配置
MQTT_BROKER_HOST=mq.bangdouni.com
MQTT_BROKER_PORT=1883
MQTT_USERNAME=bdn_ai_process
MQTT_PASSWORD=Bdn@2024

# FTP配置
FTP_HOST=api.bangdouni.com
FTP_PORT=22221
FTP_USERNAME=bdn_ftp_client
FTP_PASSWORD=Bdn@2024
FTP_REMOTE_DIR=fcdn
FTP_IS_ENABLED=true
FTP_URL_PREFIX=http://api.bangdouni.com

# ================================================================
# V3架构配置参数
# ================================================================

# V3 AI配置（MQTT外部服务）
EP_AI_ANALYSIS_DELAY_SEC=6
EP_AI_RESULT_TIMEOUT=60
EP_AI_DETECT_TIME_BEFORE_ALARM=15

# V3 统一图片配置（移除per-event重复配置）
EP_PV_BIND_IPC_IMAGE_DIR=E:\FTP-IPC\IPC-LFY-OUT01
EP_PV_IMAGE_CROP_COORDINATES=[1102,129,1672,849]

# V3 通用配置
EP_PV_AI_RESULT_WAIT_TIMEOUT=60
EP_PV_DETAIL_INFO_SOURCE_TYPE=MQTT
EP_PV_DETAIL_INFO_SOURCE=
EP_PV_DETAIL_INFO_FIELDS=[]

# ================================================================
# 事件配置区域
# ================================================================

# ============ 事件配置说明 ============
# EP_PV_EXCLUDE_INFO_FIELDS中的排除条件：exclude_condition 说明
# 可选4种值: "exists", "equals", "contains", "regex"
#     - exists条件: 无需填写exclude_value或留空
#     - equals条件: 填写完全匹配的值，支持多值用|分隔
#     - contains条件: 填写包含的字符串，支持多值用|分隔  
#     - regex条件: 填写正则表达式，支持多表达式用|分隔
# 示例: "exclude_condition": "contains", "exclude_value": "月租卡|万全卡|贵宾卡"
# 示例: "exclude_condition": "exists", "exclude_value": ""
# 示例: "exclude_condition": "equals", "exclude_value": "粤A12345" 

# ================================================================
# EV001001 - 月租车未过期超时滞留出口
# ================================================================
# ==== 基础配置 ====
EVENT_ID_EV001001_EP_START_DEVICE_EVENT_TOPIC=["device/BDN861290073715232/event"]
EVENT_ID_EV001001_EP_START_FIELD_FROM_DEVICE_EVENT=I2
EVENT_ID_EV001001_EP_POSITION_START_VALUE_FROM_DEVICE_EVENT={"true": "0", "false": "1"}
EVENT_ID_EV001001_EP_PV_HOLDING_TIMEOUT=20
# ==== 告警配置  ====
EVENT_ID_EV001001_EP_PV_ALARM_TOPIC=hq/101013/P002LfyBmOut/event
EVENT_ID_EV001001_EP_PV_DETAIL_INFO_TOPIC=ajb/101013/out/P002LfyBmOut/time_log
EVENT_ID_EV001001_EP_PV_DETAIL_INFO_FIELDS=[{"field_name": "详情", "field_keyword": "CardType,log_car_no,log_user_name,log_end_time", "field_format": "[{CardType}][{log_car_no}][{log_user_name}][{log_end_time}]"}, {"field_name": "事件", "field_keyword": "duration", "field_format": "停留时间{duration}秒"}, {"field_name": "设备", "field_keyword": "", "field_default":"帮豆你门岗智能监测"}, {"field_name": "名称", "field_keyword": "", "field_default":"月租车超时滞留出口（卡未过期）"},{"field_name": "等级", "field_keyword": "", "field_default":"通知"}]
# ==== 业务逻辑判断配置 ====
# 基于CardType字段判断储值卡
EVENT_ID_EV001001_EP_BusinessLogic_JUDGE_SOURCE=ajb/101013/in/P001LfyBmIn/time_log
EVENT_ID_EV001001_EP_BusinessLogic_JUDGE_FIELD=CardType
EVENT_ID_EV001001_EP_BusinessLogic_JUDGE_CONDITION=contains
EVENT_ID_EV001001_EP_BusinessLogic_JUDGE_VALUE=月租卡|万全卡|贵宾卡|储值卡
# ====AI提示词配置 ====
# EV001001靠地感触发，靠安居宝日志内容判断是否为月租车，不需要AI识别，因此业务逻辑事件设为None）
EVENT_ID_EV001001_EP_AI_PROMPT=None
# ====排除匹配配置 ====
EVENT_ID_EV001001_EP_MQTT_EXCLUDE_INFO_SOURCE=ajb/101013/out/P002LfyBmOut/time_log
EVENT_ID_EV001001_EP_MQTT_EXCLUDE_INFO_FIELDS=[{"field_name": "业务", "field_keyword": "CardType", "exclude_condition": "contains", "exclude_value": "临保卡"}]
EVENT_ID_EV001001_EP_MQTT_EXCLUDE_LOGIC=ANY


