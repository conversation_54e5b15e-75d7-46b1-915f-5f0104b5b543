using Microsoft.Extensions.Configuration;
using FluentAssertions;
using AIProcessor.Models;
using System.ComponentModel.DataAnnotations;

namespace AIProcessor.Tests.Configuration;

/// <summary>
/// 配置模型绑定和验证测试
/// </summary>
public class AppSettingsTests
{
    /// <summary>
    /// 测试完整有效的JSON配置绑定
    /// </summary>
    [Fact]
    public void LoadConfiguration_WithValidJson_ShouldBindCorrectly()
    {
        // Arrange
        var jsonConfig = @"{
          ""Mqtt"": {
            ""BrokerHost"": ""localhost"",
            ""BrokerPort"": 1883,
            ""ClientId"": ""AIProcessor_001"",
            ""Username"": ""ai_processor"",
            ""Password"": ""password123"",
            ""KeepAliveInterval"": 60,
            ""ReconnectDelay"": 5
          },
          ""AI"": {
            ""ApiKey"": ""sk-888888888888883e9ac0ce7d6c6527f7"",
            ""ModelName"": ""qwen-vl-plus-latest"",
            ""ApiUrl"": ""https://dashscope.aliyuncs.com/compatible-mode/v1"",
            ""Timeout"": 30
          },
          ""Processing"": {
            ""MaxConcurrentRequests"": 100
          },
          ""Logging"": {
            ""LogLevel"": {
              ""Default"": ""Information"",
              ""Microsoft"": ""Warning""
            },
            ""LogFilePath"": ""logs\\\\ai_processor.log""
          }
        }";

        var configuration = BuildConfiguration(jsonConfig);

        // Act
        var appSettings = new AppSettings();
        configuration.Bind(appSettings);

        // Assert
        appSettings.Should().NotBeNull();
        
        // MQTT配置验证
        appSettings.Mqtt.Should().NotBeNull();
        appSettings.Mqtt.BrokerHost.Should().Be("localhost");
        appSettings.Mqtt.BrokerPort.Should().Be(1883);
        appSettings.Mqtt.ClientId.Should().Be("AIProcessor_001");
        appSettings.Mqtt.Username.Should().Be("ai_processor");
        appSettings.Mqtt.Password.Should().Be("password123");
        appSettings.Mqtt.KeepAliveInterval.Should().Be(60);
        appSettings.Mqtt.ReconnectDelay.Should().Be(5);

        // AI配置验证
        appSettings.AI.Should().NotBeNull();
        appSettings.AI.ApiKey.Should().Be("sk-888888888888883e9ac0ce7d6c6527f7");
        appSettings.AI.ModelName.Should().Be("qwen-vl-plus-latest");
        appSettings.AI.ApiUrl.Should().Be("https://dashscope.aliyuncs.com/compatible-mode/v1");
        appSettings.AI.Timeout.Should().Be(30);

        // Processing配置验证
        appSettings.Processing.Should().NotBeNull();
        appSettings.Processing.MaxConcurrentRequests.Should().Be(100);

        // Logging配置验证
        appSettings.Logging.Should().NotBeNull();
        appSettings.Logging.LogLevel.Should().NotBeNull();
        appSettings.Logging.LogLevel.Default.Should().Be("Information");
        appSettings.Logging.LogLevel.Microsoft.Should().Be("Warning");
        appSettings.Logging.LogFilePath.Should().Be("logs\\\\ai_processor.log");
    }



    /// <summary>
    /// 测试可选字段的默认值
    /// </summary>
    [Fact]
    public void LoadConfiguration_WithOptionalFieldsMissing_ShouldUseDefaultValues()
    {
        // Arrange
        var jsonConfig = @"{
          ""Mqtt"": {
            ""BrokerHost"": ""localhost"",
            ""BrokerPort"": 1883,
            ""ClientId"": ""AIProcessor_001""
            // 缺少可选字段 Username, Password, KeepAliveInterval, ReconnectDelay
          },
          ""AI"": {
            ""ApiKey"": ""sk-test"",
            ""ModelName"": ""test-model"",
            ""ApiUrl"": ""https://api.test.com""
            // 缺少可选字段 Timeout
          }
          // 缺少可选的 Processing 和 Logging 配置
        }";

        var configuration = BuildConfiguration(jsonConfig);

        // Act
        var appSettings = new AppSettings();
        configuration.Bind(appSettings);

        // Assert
        // MQTT可选字段默认值
        appSettings.Mqtt.Username.Should().BeNull();
        appSettings.Mqtt.Password.Should().BeNull();
        appSettings.Mqtt.KeepAliveInterval.Should().Be(60); // 默认值
        appSettings.Mqtt.ReconnectDelay.Should().Be(5); // 默认值

        // AI可选字段默认值
        appSettings.AI.Timeout.Should().Be(5); // 默认值

        // Processing默认值
        appSettings.Processing.MaxConcurrentRequests.Should().Be(100); // 默认值

        // Logging默认值
        appSettings.Logging.LogLevel.Default.Should().Be("Information"); // 默认值
        appSettings.Logging.LogLevel.Microsoft.Should().Be("Warning"); // 默认值
        appSettings.Logging.LogFilePath.Should().BeNull(); // 可选字段
    }

    /// <summary>
    /// 构建配置对象
    /// </summary>
    private static IConfiguration BuildConfiguration(string jsonContent)
    {
        var stream = new MemoryStream(System.Text.Encoding.UTF8.GetBytes(jsonContent));
        return new ConfigurationBuilder()
            .AddJsonStream(stream)
            .Build();
    }
}