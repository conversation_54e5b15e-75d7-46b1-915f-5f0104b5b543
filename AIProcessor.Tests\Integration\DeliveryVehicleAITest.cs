using Microsoft.Extensions.Logging;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Configuration;
using Xunit;
using Xunit.Abstractions;
using AIProcessor.Services;
using AIProcessor.Models;
using System.IO;
using System.Threading.Tasks;
using System.Text.Json;

namespace AIProcessor.Tests.Integration
{
    /// <summary>
    /// 快递车AI检测专项测试
    /// 测试AIProcessor对快递车图片的识别能力
    /// </summary>
    public class DeliveryVehicleAITest : IDisposable
    {
        private readonly ITestOutputHelper _output;
        private readonly ServiceProvider _serviceProvider;
        private readonly IAIService _aiService;
        private readonly string _testImagePath;

        public DeliveryVehicleAITest(ITestOutputHelper output)
        {
            _output = output;
            
            var configuration = new ConfigurationBuilder()
                .AddJsonFile("appsettings.json", optional: false)
                .Build();

            var services = new ServiceCollection();
            services.AddSingleton<IConfiguration>(configuration);
            services.AddLogging(builder => builder.AddConsole().SetMinimumLevel(LogLevel.Information));
            services.AddScoped<IAIService, AIService>();

            _serviceProvider = services.BuildServiceProvider();
            _aiService = _serviceProvider.GetRequiredService<IAIService>();
            
            _testImagePath = GetTestImagePath();
        }

        [Fact]
        public async Task AnalyzeDeliveryVehicleImage_ShouldDetectViolation()
        {
            // Arrange
            _output.WriteLine($"开始快递车AI检测测试");
            _output.WriteLine($"测试图片: {_testImagePath}");
            
            Assert.True(File.Exists(_testImagePath), "测试图片文件不存在");

            var request = new AIRequest
            {
                RequestId = Guid.NewGuid().ToString(),
                ImageUrl = $"file://{_testImagePath}",
                Prompt = @"请仔细分析这张停车场监控图片。检查是否有以下情况：
1. 快递车、货车、面包车等商用车辆
2. 车辆是否停放在住宅小区停车位
3. 是否存在违规停放行为

如果发现商用车辆违规停放，请回复'违规'并详细描述；如果没有发现违规行为，请回复'正常'。
请重点关注蓝白色的快递车辆。",
                EventId = "EV001010_AI_TEST",
                Timestamp = DateTime.UtcNow
            };

            // Act
            _output.WriteLine("开始AI图片分析...");
            var response = await _aiService.AnalyzeImageAsync(request);

            // Assert
            Assert.NotNull(response);
            Assert.True(response.IsSuccess, $"AI分析失败: {response.ErrorMessage}");
            Assert.False(string.IsNullOrEmpty(response.Result), "AI分析结果为空");

            _output.WriteLine($"AI分析结果: {response.Result}");
            _output.WriteLine($"置信度: {response.Confidence}");

            // 验证AI能够识别出违规情况
            var result = response.Result.ToLower();
            var hasViolationKeywords = result.Contains("违规") || 
                                     result.Contains("快递") || 
                                     result.Contains("商用") ||
                                     result.Contains("货车") ||
                                     result.Contains("配送") ||
                                     result.Contains("delivery");

            Assert.True(hasViolationKeywords, 
                $"AI未能识别出快递车违规行为。分析结果: {response.Result}");

            // 验证置信度
            Assert.True(response.Confidence > 0.5, 
                $"AI分析置信度过低: {response.Confidence}");

            _output.WriteLine("✅ 快递车AI检测测试通过");
        }

        [Fact]
        public async Task AnalyzeDeliveryVehicleWithSpecificPrompt_ShouldProvideDetailedAnalysis()
        {
            // Arrange
            var request = new AIRequest
            {
                RequestId = Guid.NewGuid().ToString(),
                ImageUrl = $"file://{_testImagePath}",
                Prompt = @"这是一张停车场监控图片，时间戳显示为2020-06-20。请详细分析：

1. 图片中有什么类型的车辆？
2. 车辆的颜色和特征是什么？
3. 车辆停放的位置是否合规？
4. 是否是快递车、货车等商用车辆？
5. 如果是商用车辆，在住宅小区停放是否违规？

请提供详细的分析结果，包括车辆类型判断依据。",
                EventId = "EV001010_DETAILED_TEST",
                Timestamp = DateTime.UtcNow
            };

            // Act
            _output.WriteLine("开始详细AI分析...");
            var response = await _aiService.AnalyzeImageAsync(request);

            // Assert
            Assert.NotNull(response);
            Assert.True(response.IsSuccess, $"详细AI分析失败: {response.ErrorMessage}");
            Assert.False(string.IsNullOrEmpty(response.Result), "详细AI分析结果为空");

            _output.WriteLine($"详细分析结果:");
            _output.WriteLine(response.Result);

            // 验证详细分析包含关键信息
            var result = response.Result.ToLower();
            var hasColorInfo = result.Contains("蓝") || result.Contains("白") || result.Contains("blue");
            var hasVehicleType = result.Contains("快递") || result.Contains("货") || result.Contains("商用") || result.Contains("delivery");
            var hasLocationInfo = result.Contains("停车") || result.Contains("小区") || result.Contains("residential");

            Assert.True(hasColorInfo, "AI分析结果缺少车辆颜色信息");
            Assert.True(hasVehicleType, "AI分析结果缺少车辆类型信息");  
            Assert.True(hasLocationInfo, "AI分析结果缺少停放位置信息");

            _output.WriteLine("✅ 详细AI分析测试通过");
        }

        [Fact]
        public async Task GenerateAlarmMessage_BasedOnAIResult_ShouldCreateValidAlarm()
        {
            // Arrange - 模拟AI分析结果
            var aiResult = new AIResponse
            {
                RequestId = Guid.NewGuid().ToString(),
                IsSuccess = true,
                Result = "违规：检测到一辆蓝白色快递车停放在住宅小区停车位。该车辆属于商用车辆，根据小区管理规定不应在此停放。",
                Confidence = 0.85,
                ProcessingTime = TimeSpan.FromSeconds(3.2),
                Timestamp = DateTime.UtcNow
            };

            // Act - 基于AI结果生成告警消息
            var alarmMessage = CreateAlarmMessage(aiResult);

            // Assert
            Assert.NotNull(alarmMessage);
            Assert.Equal("EV001010", alarmMessage.EventId);
            Assert.Equal("MEDIUM", alarmMessage.Severity);
            Assert.Contains("快递车", alarmMessage.Description);
            Assert.Contains("违规", alarmMessage.Description);
            
            _output.WriteLine("生成的告警消息:");
            _output.WriteLine(JsonSerializer.Serialize(alarmMessage, new JsonSerializerOptions { WriteIndented = true }));
            
            _output.WriteLine("✅ 告警消息生成测试通过");
        }

        private AlarmMessage CreateAlarmMessage(AIResponse aiResult)
        {
            return new AlarmMessage
            {
                AlarmId = Guid.NewGuid().ToString(),
                EventId = "EV001010",
                Severity = "MEDIUM",
                Location = "P001LfyBmIn",
                Description = $"AI检测结果: {aiResult.Result}",
                VehicleType = "快递车",
                ViolationType = "商用车违规停放",
                DetectionConfidence = aiResult.Confidence,
                Timestamp = DateTime.UtcNow,
                ImagePath = _testImagePath,
                ProcessingTime = aiResult.ProcessingTime
            };
        }

        private static string GetTestImagePath()
        {
            // 查找test_image.jpg文件
            var currentDir = Directory.GetCurrentDirectory();
            var testImage = "test_image.jpg";
            
            // 向上查找项目根目录
            var searchDir = new DirectoryInfo(currentDir);
            while (searchDir != null)
            {
                var imagePath = Path.Combine(searchDir.FullName, testImage);
                if (File.Exists(imagePath))
                {
                    return imagePath;
                }
                searchDir = searchDir.Parent;
            }
            
            throw new FileNotFoundException($"无法找到测试图片: {testImage}");
        }

        public void Dispose()
        {
            _serviceProvider?.Dispose();
        }
    }

    /// <summary>
    /// 告警消息模型
    /// </summary>
    public class AlarmMessage
    {
        public string AlarmId { get; set; } = "";
        public string EventId { get; set; } = "";
        public string Severity { get; set; } = "";
        public string Location { get; set; } = "";
        public string Description { get; set; } = "";
        public string VehicleType { get; set; } = "";
        public string ViolationType { get; set; } = "";
        public double DetectionConfidence { get; set; }
        public DateTime Timestamp { get; set; }
        public string ImagePath { get; set; } = "";
        public TimeSpan ProcessingTime { get; set; }
    }
}