using FluentAssertions;
using Xunit;
using Moq;
using MQTTnet;
using MQTTnet.Protocol;
using DotNet.Testcontainers.Builders;
using DotNet.Testcontainers.Containers;
using AIProcessor.Services;
using AIProcessor.Models;
using AIProcessor.Validation;
using AIProcessor.Processing;
using System;
using System.Collections.Generic;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using SixLabors.ImageSharp;
using SixLabors.ImageSharp.PixelFormats;
using Microsoft.Extensions.Logging;

namespace AIProcessor.Tests.Integration
{
    public class ProcessingIntegrationTests : IAsyncLifetime
    {
        private readonly IContainer _mqttContainer;
        private readonly Mock<IControlMessageValidator> _mockControlValidator;
        private readonly Mock<IImageFileValidator> _mockImageFileValidator;
        private readonly Mock<ICoordinateValidator> _mockCoordinateValidator;
        private readonly Mock<IImageProcessor> _mockImageProcessor;
        private readonly Mock<IAIService> _mockAIService;
        private readonly Mock<IAIResultParser> _mockAIResultParser;
        private readonly Mock<ILogger<ProcessingIntegrationTests>> _mockLogger;
        
        // private MqttFactory _mqttFactory;
        private IMqttClient _testPublisher;
        private IMqttClient _testSubscriber;
        private string _mqttBrokerEndpoint;

        public ProcessingIntegrationTests()
        {
            // 创建MQTT容器（使用eclipse-mosquitto镜像）
            _mqttContainer = new ContainerBuilder()
                .WithImage("eclipse-mosquitto:2.0")
                .WithPortBinding(1883, true)
                .WithWaitStrategy(Wait.ForUnixContainer().UntilPortIsAvailable(1883))
                .Build();

            // 初始化所有模拟对象
            _mockControlValidator = new Mock<IControlMessageValidator>();
            _mockImageFileValidator = new Mock<IImageFileValidator>();
            _mockCoordinateValidator = new Mock<ICoordinateValidator>();
            _mockImageProcessor = new Mock<IImageProcessor>();
            _mockAIService = new Mock<IAIService>();
            _mockAIResultParser = new Mock<IAIResultParser>();
            _mockLogger = new Mock<ILogger<ProcessingIntegrationTests>>();
        }

        public async Task InitializeAsync()
        {
            await _mqttContainer.StartAsync();
            
            // 获取MQTT Broker端点
            var host = _mqttContainer.Hostname;
            var port = _mqttContainer.GetMappedPublicPort(1883);
            _mqttBrokerEndpoint = $"{host}:{port}";

            // 初始化MQTT客户端
            // _mqttFactory = new MqttFactory();
            
            // 创建发布者客户端
            // _testPublisher = _mqttFactory.CreateMqttClient();
            var publisherOptions = new MqttClientOptionsBuilder()
                .WithTcpServer(host, port)
                .WithClientId("test-publisher")
                .Build();
            await _testPublisher.ConnectAsync(publisherOptions);

            // 创建订阅者客户端
            // _testSubscriber = _mqttFactory.CreateMqttClient();
            var subscriberOptions = new MqttClientOptionsBuilder()
                .WithTcpServer(host, port)
                .WithClientId("test-subscriber")
                .Build();
            await _testSubscriber.ConnectAsync(subscriberOptions);
        }

        [Fact]
        public async Task FullProcess_WhenValidMessageReceived_ShouldProcessAndPublishSuccessEvent()
        {
            // --- Arrange ---
            // 1. Setup all mocks to return SUCCESS
            var validControlMessage = new ControlMessage
            {
                EventId = "test-event-001",
                ImagePath = "test-image.jpg",
                Coordinates = new Coordinates { X1 = 10, Y1 = 10, X2 = 100, Y2 = 100 },
                Prompt = "检测三轮车"
            };

            _mockControlValidator.Setup(v => v.Validate(It.IsAny<ControlMessage>()))
                .Returns(ValidationResult.Success());
            
            _mockImageFileValidator.Setup(v => v.Validate(It.IsAny<string>()))
                .Returns(ValidationResult.Success());
            
            _mockCoordinateValidator.Setup(v => v.Validate(It.IsAny<Coordinates>(), It.IsAny<int>(), It.IsAny<int>()))
                .Returns(CoordinateValidationResult.Success());

            var mockProcessedImage = new Mock<Image<Rgba32>>();
            _mockImageProcessor.Setup(p => p.ProcessImageAsync(It.IsAny<Image<Rgba32>>(), It.IsAny<Coordinates>()))
                .ReturnsAsync(mockProcessedImage.Object);

            var mockAIResponse = new AIResponse
            {
                Choices = new List<AIChoice>
                {
                    new AIChoice
                    {
                        Message = new AIResponseMessage
                        {
                            Content = "{\"三轮车\": true, \"快递车\": false}"
                        }
                    }
                }
            };
            _mockAIService.Setup(s => s.AnalyzeImageAsync(It.IsAny<Image<Rgba32>>(), It.IsAny<string>()))
                .ReturnsAsync(mockAIResponse);

            var expectedResult = new Dictionary<string, bool>
            {
                { "三轮车", true },
                { "快递车", false }
            };
            _mockAIResultParser.Setup(p => p.Parse(It.IsAny<AIResponse>()))
                .Returns(expectedResult);

            // 2. Setup a test subscriber to listen for the final "event" message
            var messageReceivedTcs = new TaskCompletionSource<MqttApplicationMessage>();
            _testSubscriber.ApplicationMessageReceivedAsync += e =>
            {
                if (e.ApplicationMessage.Topic == "ai/test_comm/test_pos/event")
                {
                    messageReceivedTcs.SetResult(e.ApplicationMessage);
                }
                return Task.CompletedTask;
            };
            await _testSubscriber.SubscribeAsync("ai/test_comm/test_pos/event");

            // --- Act ---
            // 3. Publish a "control" message to trigger the process
            var controlMessageJson = JsonSerializer.Serialize(validControlMessage);
            var controlMessage = new MqttApplicationMessageBuilder()
                .WithTopic("ai/test_comm/test_pos/control")
                .WithPayload(controlMessageJson)
                .WithQualityOfServiceLevel(MqttQualityOfServiceLevel.AtLeastOnce)
                .Build();
            await _testPublisher.PublishAsync(controlMessage);

            // --- Assert ---
            // 4. Wait for the final "event" message and assert its content
            var receivedMessage = await messageReceivedTcs.Task.WaitAsync(TimeSpan.FromSeconds(10));
            receivedMessage.Should().NotBeNull();
            
            var payloadString = Encoding.UTF8.GetString(receivedMessage.PayloadSegment);
            var payload = JsonSerializer.Deserialize<EventMessage>(payloadString);
            
            payload.Should().NotBeNull();
            payload.Success.Should().BeTrue();
            payload.Result.Should().ContainKey("三轮车").WhoseValue.Should().BeTrue();
            payload.Result.Should().ContainKey("快递车").WhoseValue.Should().BeFalse();

            // 5. Verify that all our services were called in order
            _mockControlValidator.Verify(v => v.Validate(It.IsAny<ControlMessage>()), Times.Once);
            _mockImageFileValidator.Verify(v => v.Validate(It.IsAny<string>()), Times.Once);
            _mockCoordinateValidator.Verify(v => v.Validate(It.IsAny<Coordinates>(), It.IsAny<int>(), It.IsAny<int>()), Times.Once);
            _mockImageProcessor.Verify(p => p.ProcessImageAsync(It.IsAny<Image<Rgba32>>(), It.IsAny<Coordinates>()), Times.Once);
            _mockAIService.Verify(s => s.AnalyzeImageAsync(It.IsAny<Image<Rgba32>>(), It.IsAny<string>()), Times.Once);
            _mockAIResultParser.Verify(p => p.Parse(It.IsAny<AIResponse>()), Times.Once);
        }

        [Fact]
        public async Task FullProcess_WhenValidationFails_ShouldPublishFailureEvent()
        {
            // --- Arrange ---
            // Setup validation to fail
            _mockControlValidator.Setup(v => v.Validate(It.IsAny<ControlMessage>()))
                .Returns(ValidationResult.Failure("Invalid control message"));

            // Setup subscriber for failure event
            var messageReceivedTcs = new TaskCompletionSource<MqttApplicationMessage>();
            _testSubscriber.ApplicationMessageReceivedAsync += e =>
            {
                if (e.ApplicationMessage.Topic == "ai/test_comm/test_pos/event")
                {
                    messageReceivedTcs.SetResult(e.ApplicationMessage);
                }
                return Task.CompletedTask;
            };
            await _testSubscriber.SubscribeAsync("ai/test_comm/test_pos/event");

            // --- Act ---
            var invalidControlMessage = new ControlMessage { EventId = "" }; // Invalid message
            var controlMessageJson = JsonSerializer.Serialize(invalidControlMessage);
            var controlMessage = new MqttApplicationMessageBuilder()
                .WithTopic("ai/test_comm/test_pos/control")
                .WithPayload(controlMessageJson)
                .WithQualityOfServiceLevel(MqttQualityOfServiceLevel.AtLeastOnce)
                .Build();
            await _testPublisher.PublishAsync(controlMessage);

            // --- Assert ---
            var receivedMessage = await messageReceivedTcs.Task.WaitAsync(TimeSpan.FromSeconds(10));
            receivedMessage.Should().NotBeNull();
            
            var payloadString = Encoding.UTF8.GetString(receivedMessage.PayloadSegment);
            var payload = JsonSerializer.Deserialize<EventMessage>(payloadString);
            
            payload.Should().NotBeNull();
            payload.Success.Should().BeFalse();
            payload.ErrorMessage.Should().Contain("Invalid control message");

            // Verify only control validator was called
            _mockControlValidator.Verify(v => v.Validate(It.IsAny<ControlMessage>()), Times.Once);
            _mockImageFileValidator.Verify(v => v.Validate(It.IsAny<string>()), Times.Never);
        }

        public async Task DisposeAsync()
        {
            if (_testPublisher?.IsConnected == true)
                await _testPublisher.DisconnectAsync();
            if (_testSubscriber?.IsConnected == true)
                await _testSubscriber.DisconnectAsync();
            
            _testPublisher?.Dispose();
            _testSubscriber?.Dispose();
            
            await _mqttContainer.StopAsync();
            await _mqttContainer.DisposeAsync();
        }
    }
}