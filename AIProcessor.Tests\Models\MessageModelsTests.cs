using System.Text.Json;
using System.Text.Json.Serialization;
using FluentAssertions;
using Xunit;
// 你需要在 AIProcessor 项目中创建这些模型
using AIProcessor.Models;

namespace AIProcessor.Tests.Models;

public class MessageModelsTests
{
    [Fact]
    public void Deserialize_ControlMessage_WithCorrectJson_ShouldMapAllProperties()
    {
        // Arrange: 使用 IR-001 中的 JSON 字段名 (snake_case)
        var json = @"{
            ""event_id"": ""EV001008"",
            ""image_path"": ""E:\\FTP-IPC\\LFY-IN01-TEST\\P001.jpg"",
            ""image_crop_coordinates"": ""0,0,0,0"",
            ""prompt"": ""图片是否存在车辆"",
            ""timestamp"": ""20250627031700738"",
            ""request_id"": ""uuid-12345-6789-abcdef""
        }";

        // Act: 尝试反序列化
        var message = JsonSerializer.Deserialize<ControlMessage>(json);

        // Assert: 验证 C# 属性 (PascalCase) 被正确赋值
        message.Should().NotBeNull();
        message.EventId.Should().Be("EV001008");
        message.ImagePath.Should().Be("E:\\FTP-IPC\\LFY-IN01-TEST\\P001.jpg");
        message.ImageCropCoordinates.Should().Be("0,0,0,0");
        message.Prompt.Should().Be("图片是否存在车辆");
        message.Timestamp.Should().Be("20250627031700738");
        message.RequestId.Should().Be("uuid-12345-6789-abcdef");
    }

    [Fact]
    public void Serialize_EventMessage_SuccessScenario_ShouldProduceCorrectJson()
    {
        // Arrange: 创建一个成功的 EventMessage C# 对象实例
        var message = new EventMessage
        {
            EventId = "EV001008",
            RequestId = "uuid-12345-6789-abcdef",
            Result = new Dictionary<string, bool>
            {
                { "三轮车", true },
                { "快递车", false }
            },
            Timestamp = "20250627031700738",
            ProcessingTime = 2.3,
            Success = true,
            ErrorMessage = null // 成功时 error_message 不应序列化
        };

        // Act: 序列化为 JSON 字符串
        var jsonOptions = new JsonSerializerOptions { PropertyNamingPolicy = JsonNamingPolicy.SnakeCaseLower, DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull };
        var json = JsonSerializer.Serialize(message, jsonOptions);

        // Assert: 验证生成的 JSON 字符串结构和内容是否符合 IR-001 输出规范
        using var jsonDoc = JsonDocument.Parse(json);
        var root = jsonDoc.RootElement;

        root.GetProperty("event_id").GetString().Should().Be("EV001008");
        root.GetProperty("request_id").GetString().Should().Be("uuid-12345-6789-abcdef");
        root.GetProperty("result").GetProperty("三轮车").GetBoolean().Should().BeTrue();
        root.GetProperty("result").GetProperty("快递车").GetBoolean().Should().BeFalse();
        root.GetProperty("timestamp").GetString().Should().Be("20250627031700738");
        root.GetProperty("processing_time").GetDouble().Should().Be(2.3);
        root.GetProperty("success").GetBoolean().Should().BeTrue();
        root.TryGetProperty("error_message", out _).Should().BeFalse(); // 确认 error_message 字段不存在
    }
}