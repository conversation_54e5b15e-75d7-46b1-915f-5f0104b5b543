using FluentAssertions;
using Xunit;
using SixLabors.ImageSharp;
using SixLabors.ImageSharp.PixelFormats;
using AIProcessor.Processing;
using AIProcessor.Validation; // Coordinates

namespace AIProcessor.Tests.Processing;

public class ImageProcessorTests
{
    private readonly IImageProcessor _processor = new ImageProcessor();

    // --- 裁剪测试 ---
    [Fact]
    public void ProcessImage_WithValidCropCoordinates_ReturnsCroppedImage()
    {
        // Arrange
        using var originalImage = new Image<Rgba32>(200, 150); // 200x150 像素
        var cropRect = new Coordinates(10, 20, 110, 120); // 裁剪区域 100x100

        // Act
        using var resultImage = _processor.ProcessImage(originalImage, cropRect);

        // Assert
        resultImage.Width.Should().Be(100); // 110 - 10
        resultImage.Height.Should().Be(100); // 120 - 20
    }

    // --- 智能缩放测试 ---
    [Fact]
    public void ProcessImage_WithSmartScalingCoords_AndImageExceedsBothDims_ReturnsResizedImage()
    {
        // Arrange: 图片尺寸 (1600x600) 远超限制 (800x400)
        using var largeImage = new Image<Rgba32>(1600, 600);
        var smartScaleCoords = new Coordinates(0, 0, 0, 0);
        // 宽度比例: 800/1600 = 0.5
        // 高度比例: 400/600 = 0.66
        // 应选择较小的比例 0.5 进行缩放
        var expectedWidth = 1600 * 0.5; // = 800
        var expectedHeight = 600 * 0.5; // = 300

        // Act
        using var resultImage = _processor.ProcessImage(largeImage, smartScaleCoords);

        // Assert
        resultImage.Width.Should().Be((int)expectedWidth);
        resultImage.Height.Should().Be((int)expectedHeight);
    }

    [Fact]
    public void ProcessImage_WithSmartScalingCoords_AndImageExceedsOnlyWidth_ReturnsOriginalImage()
    {
        // Arrange: 图片宽度超限，但高度未超限
        using var wideImage = new Image<Rgba32>(1000, 300); // 限制 800x400
        var smartScaleCoords = new Coordinates(0, 0, 0, 0);

        // Act
        using var resultImage = _processor.ProcessImage(wideImage, smartScaleCoords);

        // Assert
        // "仅当图片宽度和高度都超过限制时才进行缩放"
        resultImage.Width.Should().Be(wideImage.Width);
        resultImage.Height.Should().Be(wideImage.Height);
    }

    [Fact]
    public void ProcessImage_WithSmartScalingCoords_AndImageWithinLimits_ReturnsOriginalImage()
    {
        // Arrange: 图片尺寸在限制内
        using var smallImage = new Image<Rgba32>(500, 300); // 限制 800x400
        var smartScaleCoords = new Coordinates(0, 0, 0, 0);

        // Act
        using var resultImage = _processor.ProcessImage(smallImage, smartScaleCoords);

        // Assert
        resultImage.Width.Should().Be(smallImage.Width);
        resultImage.Height.Should().Be(smallImage.Height);
    }
}