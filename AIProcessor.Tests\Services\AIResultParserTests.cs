using FluentAssertions;
using Xunit;
using AIProcessor.Services;
using AIProcessor.Models;
using System.Collections.Generic;
using System;

namespace AIProcessor.Tests.Services
{
    public class AIResultParserTests
    {
        private readonly IAIResultParser _parser = new AIResultParser();

        [Fact]
        public void Parse_WithValidJsonResponse_ReturnsCorrectDictionary()
        {
            // Arrange
            var aiResponse = new AIResponse
            {
                Choices = new List<AIChoice>
                {
                    new AIChoice { Message = new AIResponseMessage { Content = "{\"三轮车\": true, \"快递车\": false}" } }
                }
            };

            // Act
            var result = _parser.Parse(aiResponse);

            // Assert
            result.Should().NotBeNull();
            result.Should().HaveCount(2);
            result["三轮车"].Should().BeTrue();
            result["快递车"].Should().BeFalse();
        }

        [Fact]
        public void Parse_WhenResponseHasNoChoices_ReturnsEmptyDictionary()
        {
            // Arrange
            var aiResponse = new AIResponse { Choices = new List<AIChoice>() };

            // Act
            var result = _parser.Parse(aiResponse);

            // Assert
            result.Should().NotBeNull().And.BeEmpty();
        }

        [Theory]
        [InlineData("this is not json")]       // 非JSON字符串
        [InlineData("{\"value\": 123}")]          // JSON但值不是布尔值
        [InlineData("[1, 2, 3]")]             // JSON但不是对象
        [InlineData(null)]
        public void Parse_WithInvalidContent_ThrowsFormatException(string invalidContent)
        {
            // Arrange
            var aiResponse = new AIResponse
            {
                Choices = new List<AIChoice>
                {
                    new AIChoice { Message = new AIResponseMessage { Content = invalidContent } }
                }
            };

            // Act & Assert
            Assert.Throws<FormatException>(() => _parser.Parse(aiResponse));
        }

        [Fact]
        public void Parse_WithNullResponse_ThrowsArgumentNullException()
        {
            // Act & Assert
            Assert.Throws<ArgumentNullException>(() => _parser.Parse(null));
        }
    }
}