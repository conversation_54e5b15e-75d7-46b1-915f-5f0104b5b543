using FluentAssertions;
using Xunit;
using WireMock.RequestBuilders;
using WireMock.ResponseBuilders;
using WireMock.Server;
using System;
using System.Net;
using System.Net.Http;
using AIProcessor.Services;
using AIProcessor.Models;
using SixLabors.ImageSharp;
using SixLabors.ImageSharp.PixelFormats;

namespace AIProcessor.Tests.Services;

public class AIServiceTests : IDisposable
{
    private readonly WireMockServer _server;
    private readonly IAIService _aiService;
    private readonly AiSettings _aiSettings;

    public AIServiceTests()
    {
        _server = WireMockServer.Start();
        _aiSettings = new AiSettings
        {
            ApiUrl = _server.Urls[0] + "/v1/chat/completions",
            ApiKey = "test_api_key",
            ModelName = "test-model",
            Timeout = 5
        };
        _aiService = new AIService(new HttpClient(), _aiSettings);
    }

    [Fact]
    public async Task AnalyzeImageAsync_WithValidRequest_ReturnsSuccessResponse()
    {
        // Arrange
        var prompt = "Is there a car?";
        using var image = new Image<Rgba32>(10, 10);
        var expectedResponseJson = "{\"id\":\"chatcmpl-123\",\"object\":\"chat.completion\",\"created\":1677652288,\"model\":\"test-model\",\"choices\":[{\"index\":0,\"message\":{\"role\":\"assistant\",\"content\":\"{\\\"car\\\":true}\"},\"finish_reason\":\"stop\"}],\"usage\":{\"prompt_tokens\":9,\"completion_tokens\":12,\"total_tokens\":21}}";

        _server
            .Given(Request.Create().WithPath("/v1/chat/completions").UsingPost())
            .RespondWith(Response.Create()
                .WithStatusCode(HttpStatusCode.OK)
                .WithHeader("Content-Type", "application/json")
                .WithBody(expectedResponseJson));

        // Act
        var response = await _aiService.AnalyzeImageAsync(image, prompt);

        // Assert
        response.Should().NotBeNull();
        response.Choices.Should().NotBeEmpty();
        response.Choices[0].Message.Content.Should().Be("{\"car\":true}");
    }

    [Fact]
    public async Task AnalyzeImageAsync_WhenApiReturnsError_ThrowsHttpRequestException()
    {
        // Arrange
        var prompt = "Any prompt";
        using var image = new Image<Rgba32>(10, 10);

        _server
            .Given(Request.Create().WithPath("/v1/chat/completions").UsingPost())
            .RespondWith(Response.Create().WithStatusCode(HttpStatusCode.InternalServerError));

        // Act & Assert
        await Assert.ThrowsAsync<HttpRequestException>(() =>
            _aiService.AnalyzeImageAsync(image, prompt));
    }

    public void Dispose()
    {
        _server.Stop();
    }
}