using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Moq;
using FluentAssertions;
using AIProcessor.Services;
using AIProcessor.Models;
using System.ComponentModel.DataAnnotations;

namespace AIProcessor.Tests.Services;

/// <summary>
/// 配置服务测试
/// </summary>
public class ConfigurationServiceTests
{
    private readonly Mock<ILogger<ConfigurationService>> _mockLogger;

    public ConfigurationServiceTests()
    {
        _mockLogger = new Mock<ILogger<ConfigurationService>>();
    }
    /// <summary>
    /// 测试有效配置的加载
    /// </summary>
    [Fact]
    public void AppSettings_WithValidConfiguration_ShouldReturnCorrectSettings()
    {
        // Arrange
        var jsonConfig = @"{
          ""Mqtt"": {
            ""BrokerHost"": ""test.broker.com"",
            ""BrokerPort"": 1883,
            ""ClientId"": ""TestClient_001"",
            ""Username"": ""testuser"",
            ""Password"": ""testpass""
          },
          ""AI"": {
            ""ApiKey"": ""sk-test-key"",
            ""ModelName"": ""test-model"",
            ""ApiUrl"": ""https://api.test.com""
          }
        }";

        var configuration = BuildConfiguration(jsonConfig);
        var configService = new ConfigurationService(configuration, _mockLogger.Object);

        // Act
        var appSettings = configService.AppSettings;

        // Assert
        appSettings.Should().NotBeNull();
        appSettings.Mqtt.BrokerHost.Should().Be("test.broker.com");
        appSettings.Mqtt.BrokerPort.Should().Be(1883);
        appSettings.Mqtt.ClientId.Should().Be("TestClient_001");
        appSettings.AI.ApiKey.Should().Be("sk-test-key");
        appSettings.AI.ModelName.Should().Be("test-model");
        appSettings.AI.ApiUrl.Should().Be("https://api.test.com");
    }

    /// <summary>
    /// 测试无效配置抛出异常
    /// </summary>
    [Fact]
    public void AppSettings_WithInvalidConfiguration_ShouldThrowConfigurationException()
    {
        // Arrange
        var jsonConfig = @"{
          ""Mqtt"": {
            ""BrokerHost"": ""test.broker.com""
            // 缺少必填字段 BrokerPort 和 ClientId
          },
          ""AI"": {
            ""ApiKey"": ""sk-test-key""
            // 缺少必填字段 ModelName 和 ApiUrl
          }
        }";

        var configuration = BuildConfiguration(jsonConfig);
        var configService = new ConfigurationService(configuration, _mockLogger.Object);

        // Act & Assert
        var action = () => _ = configService.AppSettings;
        action.Should().Throw<ConfigurationException>()
            .WithMessage("*Configuration validation failed*")
            .And.InnerException.Should().BeOfType<ValidationException>();
    }

    /// <summary>
    /// 测试必填字段缺失时抛出验证异常
    /// </summary>
    [Fact]
    public void AppSettings_WithMissingRequiredFields_ShouldThrowConfigurationException()
    {
        // Arrange
        var jsonConfig = @"{
          ""Mqtt"": {
            ""BrokerHost"": ""localhost""
            // 缺少 BrokerPort 和 ClientId
          },
          ""AI"": {
            ""ApiKey"": ""sk-test""
            // 缺少 ModelName 和 ApiUrl
          }
        }";

        var configuration = BuildConfiguration(jsonConfig);
        var configService = new ConfigurationService(configuration, _mockLogger.Object);

        // Act & Assert
        var action = () => _ = configService.AppSettings;
        action.Should().Throw<ConfigurationException>()
            .WithMessage("*Configuration validation failed*")
            .And.InnerException.Should().BeOfType<ValidationException>();
    }

    /// <summary>
    /// 测试配置缓存功能
    /// </summary>
    [Fact]
    public void AppSettings_CalledMultipleTimes_ShouldReturnSameInstance()
    {
        // Arrange
        var jsonConfig = @"{
          ""Mqtt"": {
            ""BrokerHost"": ""test.broker.com"",
            ""BrokerPort"": 1883,
            ""ClientId"": ""TestClient_001""
          },
          ""AI"": {
            ""ApiKey"": ""sk-test-key"",
            ""ModelName"": ""test-model"",
            ""ApiUrl"": ""https://api.test.com""
          }
        }";

        var configuration = BuildConfiguration(jsonConfig);
        var configService = new ConfigurationService(configuration, _mockLogger.Object);

        // Act
        var appSettings1 = configService.AppSettings;
        var appSettings2 = configService.AppSettings;

        // Assert
        appSettings1.Should().BeSameAs(appSettings2);
    }

    /// <summary>
    /// 测试构造函数参数验证
    /// </summary>
    [Fact]
    public void Constructor_WithNullConfiguration_ShouldThrowArgumentNullException()
    {
        // Act & Assert
        var action = () => new ConfigurationService(null!, _mockLogger.Object);
        action.Should().Throw<ArgumentNullException>()
            .WithParameterName("configuration");
    }

    /// <summary>
    /// 测试构造函数参数验证 - null logger
    /// </summary>
    [Fact]
    public void Constructor_WithNullLogger_ShouldThrowArgumentNullException()
    {
        // Arrange
        var configuration = BuildConfiguration("{}");

        // Act & Assert
        var action = () => new ConfigurationService(configuration, null!);
        action.Should().Throw<ArgumentNullException>()
            .WithParameterName("logger");
    }

    /// <summary>
    /// 测试默认值设置
    /// </summary>
    [Fact]
    public void AppSettings_WithMinimalConfiguration_ShouldUseDefaultValues()
    {
        // Arrange
        var jsonConfig = @"{
          ""Mqtt"": {
            ""BrokerHost"": ""test.broker.com"",
            ""BrokerPort"": 1883,
            ""ClientId"": ""TestClient_001""
          },
          ""AI"": {
            ""ApiKey"": ""sk-test-key"",
            ""ModelName"": ""test-model"",
            ""ApiUrl"": ""https://api.test.com""
          }
        }";

        var configuration = BuildConfiguration(jsonConfig);
        var configService = new ConfigurationService(configuration, _mockLogger.Object);

        // Act
        var appSettings = configService.AppSettings;

        // Assert
        appSettings.Mqtt.KeepAliveInterval.Should().Be(60);
        appSettings.Mqtt.ReconnectDelay.Should().Be(5);
        appSettings.AI.Timeout.Should().Be(5);
        appSettings.Processing.MaxConcurrentRequests.Should().Be(100);
        appSettings.Logging.LogLevel.Default.Should().Be("Information");
        appSettings.Logging.LogLevel.Microsoft.Should().Be("Warning");
    }

    /// <summary>
    /// 测试MQTT端口号验证
    /// </summary>
    [Fact]
    public void AppSettings_WithInvalidMqttPort_ShouldThrowConfigurationException()
    {
        // Arrange
        var jsonConfig = @"{
          ""Mqtt"": {
            ""BrokerHost"": ""test.broker.com"",
            ""BrokerPort"": 0,
            ""ClientId"": ""TestClient_001"",
            ""Username"": ""testuser"",
            ""Password"": ""testpass""
          },
          ""AI"": {
            ""ApiKey"": ""sk-test-key"",
            ""ModelName"": ""test-model"",
            ""ApiUrl"": ""https://api.test.com""
          }
        }";

        var configuration = BuildConfiguration(jsonConfig);
        var configService = new ConfigurationService(configuration, _mockLogger.Object);

        // Act & Assert
        var action = () => _ = configService.AppSettings;
        action.Should().Throw<ConfigurationException>()
            .WithMessage("*MQTT端口号无效*");
    }

    /// <summary>
    /// 测试AI超时时间验证
    /// </summary>
    [Fact]
    public void AppSettings_WithInvalidAiTimeout_ShouldThrowConfigurationException()
    {
        // Arrange
        var jsonConfig = @"{
          ""Mqtt"": {
            ""BrokerHost"": ""test.broker.com"",
            ""BrokerPort"": 1883,
            ""ClientId"": ""TestClient_001"",
            ""Username"": ""testuser"",
            ""Password"": ""testpass""
          },
          ""AI"": {
            ""ApiKey"": ""sk-test-key"",
            ""ModelName"": ""test-model"",
            ""ApiUrl"": ""https://api.test.com"",
            ""Timeout"": -1
          }
        }";

        var configuration = BuildConfiguration(jsonConfig);
        var configService = new ConfigurationService(configuration, _mockLogger.Object);

        // Act & Assert
        var action = () => _ = configService.AppSettings;
        action.Should().Throw<ConfigurationException>()
            .WithMessage("*AI超时时间无效*");
    }

    /// <summary>
    /// 构建配置对象
    /// </summary>
    private static IConfiguration BuildConfiguration(string jsonContent)
    {
        var stream = new MemoryStream(System.Text.Encoding.UTF8.GetBytes(jsonContent));
        return new ConfigurationBuilder()
            .AddJsonStream(stream)
            .Build();
    }
}