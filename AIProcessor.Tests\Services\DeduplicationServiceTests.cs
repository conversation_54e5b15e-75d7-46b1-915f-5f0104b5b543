using FluentAssertions;
using Xunit;
using AIProcessor.Services;

namespace AIProcessor.Tests.Services
{
    public class DeduplicationServiceTests
    {
        [Fact]
        public void IsDuplicate_ForNewId_ReturnsFalseAndStoresId()
        {
            // Arrange
            var service = new DeduplicationService();
            var messageId = "new-id-123";

            // Act
            var isDuplicate = service.IsDuplicate(messageId);

            // Assert
            isDuplicate.Should().BeFalse();
        }

        [Fact]
        public void IsDuplicate_ForExistingId_ReturnsTrue()
        {
            // Arrange
            var service = new DeduplicationService();
            var messageId = "existing-id-456";
            service.IsDuplicate(messageId); // First call

            // Act
            var isDuplicate = service.IsDuplicate(messageId); // Second call

            // Assert
            isDuplicate.Should().BeTrue();
        }
    }
}