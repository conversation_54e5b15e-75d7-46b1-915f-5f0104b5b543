using Microsoft.Extensions.Logging;
using Moq;
using FluentAssertions;
using AIProcessor.Services;
using System.IO;

namespace AIProcessor.Tests.Services;

/// <summary>
/// FileLoggingService的单元测试
/// </summary>
public class FileLoggingServiceTests : IDisposable
{
    private readonly Mock<ILogger<FileLoggingService>> _mockLogger;
    private readonly string _testDirectory;
    private readonly List<string> _createdFiles;
    private readonly List<string> _createdDirectories;

    public FileLoggingServiceTests()
    {
        _mockLogger = new Mock<ILogger<FileLoggingService>>();
        _testDirectory = Path.Combine(Path.GetTempPath(), "FileLoggingServiceTests", Guid.NewGuid().ToString());
        _createdFiles = new List<string>();
        _createdDirectories = new List<string>();
    }

    public void Dispose()
    {
        // 清理测试文件和目录
        foreach (var file in _createdFiles)
        {
            try
            {
                if (File.Exists(file))
                    File.Delete(file);
            }
            catch { /* 忽略清理错误 */ }
        }

        foreach (var directory in _createdDirectories.OrderByDescending(d => d.Length))
        {
            try
            {
                if (Directory.Exists(directory))
                    Directory.Delete(directory, true);
            }
            catch { /* 忽略清理错误 */ }
        }
    }

    [Fact]
    public void Constructor_WithNullLogFilePath_ShouldNotThrow()
    {
        // Act & Assert
        var action = () => new FileLoggingService(null, _mockLogger.Object);
        action.Should().NotThrow();
    }

    [Fact]
    public void Constructor_WithEmptyLogFilePath_ShouldNotThrow()
    {
        // Act & Assert
        var action = () => new FileLoggingService("", _mockLogger.Object);
        action.Should().NotThrow();
    }

    [Fact]
    public void LogFilePath_ShouldReturnConfiguredPath()
    {
        // Arrange
        var expectedPath = "logs/test.log";
        var service = new FileLoggingService(expectedPath, _mockLogger.Object);

        // Act
        var actualPath = service.LogFilePath;

        // Assert
        actualPath.Should().Be(expectedPath);
    }

    [Fact]
    public void EnsureLogDirectoryExists_WithNullPath_ShouldReturnFalse()
    {
        // Arrange
        var service = new FileLoggingService(null, _mockLogger.Object);

        // Act
        var result = service.EnsureLogDirectoryExists();

        // Assert
        result.Should().BeFalse();
        VerifyLogWarning("日志文件路径未配置，将仅使用控制台日志");
    }

    [Fact]
    public void EnsureLogDirectoryExists_WithValidPath_ShouldCreateDirectoryAndReturnTrue()
    {
        // Arrange
        var logFilePath = Path.Combine(_testDirectory, "logs", "test.log");
        var service = new FileLoggingService(logFilePath, _mockLogger.Object);
        _createdDirectories.Add(_testDirectory);

        // Act
        var result = service.EnsureLogDirectoryExists();

        // Assert
        result.Should().BeTrue();
        Directory.Exists(Path.GetDirectoryName(logFilePath)).Should().BeTrue();
        VerifyLogInformation($"已创建日志目录: {Path.GetDirectoryName(logFilePath)}");
    }

    [Fact]
    public void EnsureLogDirectoryExists_WithExistingDirectory_ShouldReturnTrue()
    {
        // Arrange
        var logDirectory = Path.Combine(_testDirectory, "logs");
        Directory.CreateDirectory(logDirectory);
        _createdDirectories.Add(_testDirectory);

        var logFilePath = Path.Combine(logDirectory, "test.log");
        var service = new FileLoggingService(logFilePath, _mockLogger.Object);

        // Act
        var result = service.EnsureLogDirectoryExists();

        // Assert
        result.Should().BeTrue();
    }

    [Fact]
    public void ValidateLogFilePath_WithNullPath_ShouldReturnFalse()
    {
        // Arrange
        var service = new FileLoggingService(null, _mockLogger.Object);

        // Act
        var result = service.ValidateLogFilePath();

        // Assert
        result.Should().BeFalse();
    }

    [Fact]
    public void ValidateLogFilePath_WithEmptyPath_ShouldReturnFalse()
    {
        // Arrange
        var service = new FileLoggingService("", _mockLogger.Object);

        // Act
        var result = service.ValidateLogFilePath();

        // Assert
        result.Should().BeFalse();
    }

    [Fact]
    public void ValidateLogFilePath_WithValidPath_ShouldReturnTrue()
    {
        // Arrange
        var logFilePath = Path.Combine(_testDirectory, "logs", "test.log");
        var service = new FileLoggingService(logFilePath, _mockLogger.Object);

        // Act
        var result = service.ValidateLogFilePath();

        // Assert
        result.Should().BeTrue();
        VerifyLogDebug($"日志文件路径验证通过: {logFilePath}");
    }

    [Fact]
    public void ValidateLogFilePath_WithInvalidFileName_ShouldReturnFalse()
    {
        // Arrange
        var invalidPath = Path.Combine(_testDirectory, "logs", "test<>|.log");
        var service = new FileLoggingService(invalidPath, _mockLogger.Object);

        // Act
        var result = service.ValidateLogFilePath();

        // Assert
        result.Should().BeFalse();
        VerifyLogError("日志文件名包含无效字符: test<>|.log");
    }

    [Fact]
    public void ValidateLogFilePath_WithDirectoryOnly_ShouldReturnFalse()
    {
        // Arrange
        var directoryPath = Path.Combine(_testDirectory, "logs") + Path.DirectorySeparatorChar;
        var service = new FileLoggingService(directoryPath, _mockLogger.Object);

        // Act
        var result = service.ValidateLogFilePath();

        // Assert
        result.Should().BeFalse();
        VerifyLogError($"日志文件路径无效，缺少文件名: {directoryPath}");
    }

    [Fact]
    public void TestLogFileWritePermission_WithNullPath_ShouldReturnFalse()
    {
        // Arrange
        var service = new FileLoggingService(null, _mockLogger.Object);

        // Act
        var result = service.TestLogFileWritePermission();

        // Assert
        result.Should().BeFalse();
    }

    [Fact]
    public void TestLogFileWritePermission_WithValidPath_ShouldCreateFileAndReturnTrue()
    {
        // Arrange
        var logFilePath = Path.Combine(_testDirectory, "logs", "test.log");
        var service = new FileLoggingService(logFilePath, _mockLogger.Object);
        _createdDirectories.Add(_testDirectory);
        _createdFiles.Add(logFilePath);

        // Act
        var result = service.TestLogFileWritePermission();

        // Assert
        result.Should().BeTrue();
        File.Exists(logFilePath).Should().BeTrue();
        VerifyLogDebug($"日志文件写入权限测试通过: {logFilePath}");
    }

    private void VerifyLogInformation(string message)
    {
        _mockLogger.Verify(
            x => x.Log(
                LogLevel.Information,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains(message)),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
            Times.Once);
    }

    private void VerifyLogWarning(string message)
    {
        _mockLogger.Verify(
            x => x.Log(
                LogLevel.Warning,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains(message)),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
            Times.Once);
    }

    private void VerifyLogError(string message)
    {
        _mockLogger.Verify(
            x => x.Log(
                LogLevel.Error,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains(message)),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
            Times.Once);
    }

    private void VerifyLogDebug(string message)
    {
        _mockLogger.Verify(
            x => x.Log(
                LogLevel.Debug,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains(message)),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
            Times.Once);
    }
}
