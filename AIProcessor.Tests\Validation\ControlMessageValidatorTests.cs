using FluentAssertions;
using Xunit;
using AIProcessor.Models;
using AIProcessor.Validation;

namespace AIProcessor.Tests.Validation;

public class ControlMessageValidatorTests
{
    private readonly IControlMessageValidator _validator = new ControlMessageValidator();

    [Fact]
    public void Validate_WithValidMessage_ReturnsSuccess()
    {
        // Arrange
        var message = new ControlMessage
        {
            EventId = "event1",
            ImagePath = "path/to/image.jpg",
            ImageCropCoordinates = "0,0,100,100",
            Prompt = "a prompt",
            Timestamp = "20250101000000000",
            RequestId = "request1"
        };

        // Act
        var result = _validator.Validate(message);

        // Assert
        result.IsValid.Should().BeTrue();
        result.Errors.Should().BeEmpty();
    }

    [Theory]
    [InlineData(null, "path", "coords", "prompt", "time", "reqId", "EventId")]
    [InlineData("", "path", "coords", "prompt", "time", "reqId", "EventId")]
    [InlineData(" ", "path", "coords", "prompt", "time", "reqId", "EventId")]
    [InlineData("eid", null, "coords", "prompt", "time", "reqId", "ImagePath")]
    [InlineData("eid", "", "coords", "prompt", "time", "reqId", "ImagePath")]
    [InlineData("eid", " ", "coords", "prompt", "time", "reqId", "ImagePath")]
    [InlineData("eid", "path", null, "prompt", "time", "reqId", "ImageCropCoordinates")]
    [InlineData("eid", "path", "", "prompt", "time", "reqId", "ImageCropCoordinates")]
    [InlineData("eid", "path", " ", "prompt", "time", "reqId", "ImageCropCoordinates")]
    [InlineData("eid", "path", "coords", null, "time", "reqId", "Prompt")]
    [InlineData("eid", "path", "coords", "", "time", "reqId", "Prompt")]
    [InlineData("eid", "path", "coords", " ", "time", "reqId", "Prompt")]
    [InlineData("eid", "path", "coords", "prompt", null, "reqId", "Timestamp")]
    [InlineData("eid", "path", "coords", "prompt", "", "reqId", "Timestamp")]
    [InlineData("eid", "path", "coords", "prompt", " ", "reqId", "Timestamp")]
    [InlineData("eid", "path", "coords", "prompt", "time", null, "RequestId")]
    [InlineData("eid", "path", "coords", "prompt", "time", "", "RequestId")]
    [InlineData("eid", "path", "coords", "prompt", "time", " ", "RequestId")]
    public void Validate_WithMissingOrEmptyField_ReturnsFailure(string eventId, string imagePath, string coords, string prompt, string timestamp, string requestId, string invalidFieldName)
    {
        // Arrange
        var message = new ControlMessage { EventId = eventId, ImagePath = imagePath, ImageCropCoordinates = coords, Prompt = prompt, Timestamp = timestamp, RequestId = requestId };
        
        // Act
        var result = _validator.Validate(message);

        // Assert
        result.IsValid.Should().BeFalse();
        result.Errors.Should().ContainSingle(error => error.Contains(invalidFieldName));
    }

    [Fact]
    public void Validate_WithMultipleInvalidFields_ReturnsAllErrors()
    {
        // Arrange
        var message = new ControlMessage
        {
            EventId = null, // Invalid
            ImagePath = "path",
            ImageCropCoordinates = "coords",
            Prompt = "", // Invalid
            Timestamp = "time",
            RequestId = " " // Invalid
        };

        // Act
        var result = _validator.Validate(message);

        // Assert
        result.IsValid.Should().BeFalse();
        result.Errors.Should().HaveCount(3);
        result.Errors.Should().Contain(e => e.Contains("EventId"));
        result.Errors.Should().Contain(e => e.Contains("Prompt"));
        result.Errors.Should().Contain(e => e.Contains("RequestId"));
    }
}