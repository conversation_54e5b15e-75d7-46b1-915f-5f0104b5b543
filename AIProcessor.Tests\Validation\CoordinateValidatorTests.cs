using FluentAssertions;
using Xunit;
using AIProcessor.Validation;

namespace AIProcessor.Tests.Validation
{
    /// <summary>
    /// 坐标验证器的单元测试
    /// </summary>
    public class CoordinateValidatorTests
    {
        private readonly ICoordinateValidator _validator = new CoordinateValidator();

        [Theory]
        [InlineData("10,20,100,200")] // 标准有效坐标
        [InlineData("0,0,0,0")]       // 特殊有效坐标 (智能缩放)
        [InlineData(" 0, 0 ,800,400 ")] // 带空格的有效坐标
        public void Validate_WithValidCoordinates_ReturnsSuccess(string coordinateString)
        {
            // Act
            var result = _validator.Validate(coordinateString);

            // Assert
            result.IsValid.Should().BeTrue();
            result.Errors.Should().BeEmpty();
            result.Coordinates.Should().NotBeNull();
        }

        [Fact]
        public void Validate_WithValidCoordinates_ParsesCorrectValues()
        {
            // Arrange
            var coordinateString = "10,20,100,200";

            // Act
            var result = _validator.Validate(coordinateString);

            // Assert
            result.Coordinates!.X1.Should().Be(10);
            result.Coordinates.Y1.Should().Be(20);
            result.Coordinates.X2.Should().Be(100);
            result.Coordinates.Y2.Should().Be(200);
        }

        [Theory]
        [InlineData("10,20,10,200", "x2必须大于x1")] // x2 not > x1
        [InlineData("10,20,100,20", "y2必须大于y1")] // y2 not > y1
        [InlineData("-10,20,100,200", "坐标值不能为负数")] // Negative value
        [InlineData("10,20,100,abc", "必须是有效的整数")]   // Non-integer value
        [InlineData("10,20,100", "必须包含四个部分")]     // Wrong number of parts
        [InlineData("10,20,100,200,300", "必须包含四个部分")] // Wrong number of parts
        [InlineData("10;20;100;200", "必须由逗号分隔")]  // Wrong separator
        [InlineData("", "坐标字符串不能为空")]
        [InlineData(null, "坐标字符串不能为空")]
        [InlineData("  ", "坐标字符串不能为空")]
        public void Validate_WithInvalidCoordinates_ReturnsFailure(string invalidString, string expectedError)
        {
            // Act
            var result = _validator.Validate(invalidString);

            // Assert
            result.IsValid.Should().BeFalse();
            result.Coordinates.Should().BeNull();
            result.Errors.Should().Contain(e => e.Contains(expectedError));
        }

        [Fact]
        public void Validate_WithBothInvalidCoordinates_ReturnsMultipleErrors()
        {
            // Arrange
            var coordinateString = "10,20,5,15"; // Both x2 <= x1 and y2 <= y1

            // Act
            var result = _validator.Validate(coordinateString);

            // Assert
            result.IsValid.Should().BeFalse();
            result.Coordinates.Should().BeNull();
            result.Errors.Should().HaveCount(2);
            result.Errors.Should().Contain(e => e.Contains("x2必须大于x1"));
            result.Errors.Should().Contain(e => e.Contains("y2必须大于y1"));
        }
    }
}