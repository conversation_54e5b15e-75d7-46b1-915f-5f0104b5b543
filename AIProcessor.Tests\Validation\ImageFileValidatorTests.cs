using FluentAssertions;
using Xunit;
using Moq;
using AIProcessor.Abstractions;
using AIProcessor.Validation;

namespace AIProcessor.Tests.Validation
{
    /// <summary>
    /// 图片文件验证器的单元测试
    /// </summary>
    public class ImageFileValidatorTests
    {
        private readonly Mock<IFileSystem> _mockFileSystem;
        private readonly IImageFileValidator _validator;

        public ImageFileValidatorTests()
        {
            _mockFileSystem = new Mock<IFileSystem>();
            // 将模拟对象注入到验证器中
            _validator = new ImageFileValidator(_mockFileSystem.Object);
        }

        [Fact]
        public void Validate_WhenFileExists_ReturnsSuccess()
        {
            // Arrange
            var filePath = "C:\\valid\\path\\image.jpg";
            _mockFileSystem.Setup(fs => fs.FileExists(filePath)).Returns(true);

            // Act
            var result = _validator.Validate(filePath);

            // Assert
            result.IsValid.Should().BeTrue();
            result.Errors.Should().BeEmpty();
        }

        [Fact]
        public void Validate_WhenFileDoesNotExist_ReturnsFailure()
        {
            // Arrange
            var filePath = "C:\\invalid\\path\\non_existent_image.jpg";
            _mockFileSystem.Setup(fs => fs.FileExists(filePath)).Returns(false);
            
            // Act
            var result = _validator.Validate(filePath);

            // Assert
            result.IsValid.Should().BeFalse();
            result.Errors.Should().ContainSingle()
                  .And.Contain(e => e.Contains("图片文件不存在") && e.Contains(filePath));
        }

        [Theory]
        [InlineData(null)]
        [InlineData("")]
        [InlineData("  ")]
        public void Validate_WhenPathIsInvalid_ReturnsFailure(string invalidPath)
        {
            // Act
            var result = _validator.Validate(invalidPath);

            // Assert
            result.IsValid.Should().BeFalse();
            result.Errors.Should().ContainSingle()
                  .And.Contain("图片路径不能为空");
        }
    }
}