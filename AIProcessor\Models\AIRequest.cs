using System.Text.Json.Serialization;

namespace AIProcessor.Models;

/// <summary>
/// AI服务请求数据模型
/// </summary>
public class AIRequest
{
    /// <summary>
    /// 使用的AI模型名称
    /// </summary>
    [JsonPropertyName("model")]
    public string Model { get; set; } = string.Empty;

    /// <summary>
    /// 消息数组，包含用户提示和图片数据
    /// </summary>
    [JsonPropertyName("messages")]
    public List<AIMessage> Messages { get; set; } = new();

    /// <summary>
    /// 最大生成token数量
    /// </summary>
    [JsonPropertyName("max_tokens")]
    public int MaxTokens { get; set; } = 300;
}

/// <summary>
/// AI消息模型
/// </summary>
public class AIMessage
{
    /// <summary>
    /// 消息角色（user/assistant/system）
    /// </summary>
    [JsonPropertyName("role")]
    public string Role { get; set; } = string.Empty;

    /// <summary>
    /// 消息内容数组，支持文本和图片
    /// </summary>
    [JsonPropertyName("content")]
    public List<AIContent> Content { get; set; } = new();
}

/// <summary>
/// AI消息内容模型
/// </summary>
public class AIContent
{
    /// <summary>
    /// 内容类型（text/image_url）
    /// </summary>
    [JsonPropertyName("type")]
    public string Type { get; set; } = string.Empty;

    /// <summary>
    /// 文本内容（当Type为text时使用）
    /// </summary>
    [JsonPropertyName("text")]
    public string? Text { get; set; }

    /// <summary>
    /// 图片URL信息（当Type为image_url时使用）
    /// </summary>
    [JsonPropertyName("image_url")]
    public AIImageUrl? ImageUrl { get; set; }
}

/// <summary>
/// AI图片URL模型
/// </summary>
public class AIImageUrl
{
    /// <summary>
    /// 图片的Base64编码URL
    /// </summary>
    [JsonPropertyName("url")]
    public string Url { get; set; } = string.Empty;
}