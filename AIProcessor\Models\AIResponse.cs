using System.Text.Json.Serialization;

namespace AIProcessor.Models;

/// <summary>
/// AI服务响应数据模型
/// </summary>
public class AIResponse
{
    /// <summary>
    /// 响应ID
    /// </summary>
    [JsonPropertyName("id")]
    public string Id { get; set; } = string.Empty;

    /// <summary>
    /// 对象类型
    /// </summary>
    [JsonPropertyName("object")]
    public string Object { get; set; } = string.Empty;

    /// <summary>
    /// 创建时间戳
    /// </summary>
    [JsonPropertyName("created")]
    public long Created { get; set; }

    /// <summary>
    /// 使用的模型名称
    /// </summary>
    [JsonPropertyName("model")]
    public string Model { get; set; } = string.Empty;

    /// <summary>
    /// 选择结果数组
    /// </summary>
    [JsonPropertyName("choices")]
    public List<AIChoice> Choices { get; set; } = new();

    /// <summary>
    /// 使用情况统计
    /// </summary>
    [JsonPropertyName("usage")]
    public AIUsage? Usage { get; set; }
}

/// <summary>
/// AI选择结果模型
/// </summary>
public class AIChoice
{
    /// <summary>
    /// 选择索引
    /// </summary>
    [JsonPropertyName("index")]
    public int Index { get; set; }

    /// <summary>
    /// 消息内容
    /// </summary>
    [JsonPropertyName("message")]
    public AIResponseMessage Message { get; set; } = new();

    /// <summary>
    /// 完成原因
    /// </summary>
    [JsonPropertyName("finish_reason")]
    public string FinishReason { get; set; } = string.Empty;
}

/// <summary>
/// AI响应消息模型
/// </summary>
public class AIResponseMessage
{
    /// <summary>
    /// 消息角色
    /// </summary>
    [JsonPropertyName("role")]
    public string Role { get; set; } = string.Empty;

    /// <summary>
    /// 消息内容
    /// </summary>
    [JsonPropertyName("content")]
    public string Content { get; set; } = string.Empty;
}

/// <summary>
/// AI使用情况统计模型
/// </summary>
public class AIUsage
{
    /// <summary>
    /// 提示token数量
    /// </summary>
    [JsonPropertyName("prompt_tokens")]
    public int PromptTokens { get; set; }

    /// <summary>
    /// 完成token数量
    /// </summary>
    [JsonPropertyName("completion_tokens")]
    public int CompletionTokens { get; set; }

    /// <summary>
    /// 总token数量
    /// </summary>
    [JsonPropertyName("total_tokens")]
    public int TotalTokens { get; set; }
}