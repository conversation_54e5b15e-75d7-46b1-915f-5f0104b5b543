using System.ComponentModel.DataAnnotations;

namespace AIProcessor.Models;

/// <summary>
/// 应用程序配置根对象
/// </summary>
public class AppSettings
{
    /// <summary>
    /// MQTT配置
    /// </summary>
    [Required]
    public MqttSettings Mqtt { get; set; } = new();

    /// <summary>
    /// AI服务配置
    /// </summary>
    [Required]
    public AiSettings AI { get; set; } = new();

    /// <summary>
    /// 处理配置
    /// </summary>
    public ProcessingSettings Processing { get; set; } = new();

    /// <summary>
    /// 日志配置
    /// </summary>
    public LoggingSettings Logging { get; set; } = new();
}

/// <summary>
/// MQTT配置
/// </summary>
public class MqttSettings
{
    /// <summary>
    /// MQTT Broker地址，必填
    /// </summary>
    [Required(ErrorMessage = "CFG001: BrokerHost is required")]
    public string BrokerHost { get; set; } = string.Empty;

    /// <summary>
    /// MQTT Broker端口，必填
    /// </summary>
    [Required(ErrorMessage = "CFG001: BrokerPort is required")]
    [Range(1, 65535, ErrorMessage = "CFG001: BrokerPort must be between 1 and 65535")]
    public int BrokerPort { get; set; }

    /// <summary>
    /// MQTT客户端ID，必填
    /// </summary>
    [Required(ErrorMessage = "CFG001: ClientId is required")]
    public string ClientId { get; set; } = string.Empty;

    /// <summary>
    /// MQTT用户名，可选
    /// </summary>
    public string? Username { get; set; }

    /// <summary>
    /// MQTT密码，可选
    /// </summary>
    public string? Password { get; set; }

    /// <summary>
    /// 保活间隔，可选，默认60秒
    /// </summary>
    public int KeepAliveInterval { get; set; } = 60;

    /// <summary>
    /// 重连延迟，可选，默认5秒
    /// </summary>
    public int ReconnectDelay { get; set; } = 5;
}

/// <summary>
/// AI服务配置
/// </summary>
public class AiSettings
{
    /// <summary>
    /// AI服务API密钥，必填
    /// </summary>
    [Required(ErrorMessage = "CFG001: ApiKey is required")]
    public string ApiKey { get; set; } = string.Empty;

    /// <summary>
    /// AI模型名称，必填
    /// </summary>
    [Required(ErrorMessage = "CFG001: ModelName is required")]
    public string ModelName { get; set; } = string.Empty;

    /// <summary>
    /// AI服务API地址，必填
    /// </summary>
    [Required(ErrorMessage = "CFG001: ApiUrl is required")]
    public string ApiUrl { get; set; } = string.Empty;

    /// <summary>
    /// 请求超时时间，可选，默认5秒
    /// </summary>
    public int Timeout { get; set; } = 5;
}

/// <summary>
/// 处理配置
/// </summary>
public class ProcessingSettings
{
    /// <summary>
    /// 最大并发请求数，可选，默认100
    /// </summary>
    public int MaxConcurrentRequests { get; set; } = 100;

    /// <summary>
    /// 合并窗口时间（秒），可选，默认3秒
    /// </summary>
    public int MergeWindowSeconds { get; set; } = 3;
}

/// <summary>
/// 日志配置
/// </summary>
public class LoggingSettings
{
    /// <summary>
    /// 日志级别配置
    /// </summary>
    public LogLevelSettings LogLevel { get; set; } = new();

    /// <summary>
    /// 日志文件路径，可选
    /// </summary>
    public string? LogFilePath { get; set; }
}

/// <summary>
/// 日志级别配置
/// </summary>
public class LogLevelSettings
{
    /// <summary>
    /// 默认日志级别，可选，默认Information
    /// </summary>
    public string Default { get; set; } = "Information";

    /// <summary>
    /// Microsoft组件日志级别，可选，默认Warning
    /// </summary>
    public string Microsoft { get; set; } = "Warning";
}