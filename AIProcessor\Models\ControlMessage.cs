using System.Text.Json.Serialization;

namespace AIProcessor.Models;

/// <summary>
/// 表示从MQTT接收的控制消息模型
/// 对应IR-001规范中的输入消息结构
/// </summary>
public class ControlMessage
{
    /// <summary>
    /// 事件ID
    /// </summary>
    [JsonPropertyName("event_id")]
    public string EventId { get; set; } = string.Empty;

    /// <summary>
    /// 图片路径
    /// </summary>
    [JsonPropertyName("image_path")]
    public string ImagePath { get; set; } = string.Empty;

    /// <summary>
    /// 图片裁剪坐标
    /// </summary>
    [JsonPropertyName("image_crop_coordinates")]
    public string ImageCropCoordinates { get; set; } = string.Empty;

    /// <summary>
    /// AI分析提示词
    /// </summary>
    [JsonPropertyName("prompt")]
    public string Prompt { get; set; } = string.Empty;

    /// <summary>
    /// 时间戳
    /// </summary>
    [JsonPropertyName("timestamp")]
    public string Timestamp { get; set; } = string.Empty;

    /// <summary>
    /// 请求ID
    /// </summary>
    [JsonPropertyName("request_id")]
    public string RequestId { get; set; } = string.Empty;
}