using System.Text.Json.Serialization;

namespace AIProcessor.Models;

/// <summary>
/// 表示发送到MQTT的事件消息模型
/// 对应IR-001规范中的输出消息结构
/// </summary>
public class EventMessage
{
    /// <summary>
    /// 事件ID
    /// </summary>
    [JsonPropertyName("event_id")]
    public string EventId { get; set; } = string.Empty;

    /// <summary>
    /// 请求ID
    /// </summary>
    [JsonPropertyName("request_id")]
    public string RequestId { get; set; } = string.Empty;

    /// <summary>
    /// AI分析结果，字段名到布尔值的映射
    /// </summary>
    [JsonPropertyName("result")]
    public Dictionary<string, bool> Result { get; set; } = new();

    /// <summary>
    /// 时间戳
    /// </summary>
    [JsonPropertyName("timestamp")]
    public string Timestamp { get; set; } = string.Empty;

    /// <summary>
    /// 处理时间（秒）
    /// </summary>
    [JsonPropertyName("processing_time")]
    public double ProcessingTime { get; set; }

    /// <summary>
    /// 处理是否成功
    /// </summary>
    [JsonPropertyName("success")]
    public bool Success { get; set; }

    /// <summary>
    /// 错误消息，仅在失败时包含
    /// </summary>
    [JsonPropertyName("error_message")]
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    public string? ErrorMessage { get; set; }
}