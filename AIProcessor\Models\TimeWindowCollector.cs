using AIProcessor.Validation;

namespace AIProcessor.Models
{
    /// <summary>
    /// 时间窗口收集器，用于收集相同条件的请求并进行合并处理
    /// </summary>
    public class TimeWindowCollector
    {
        /// <summary>
        /// 图片路径
        /// </summary>
        public string ImagePath { get; set; } = string.Empty;

        /// <summary>
        /// 裁剪坐标
        /// </summary>
        public Coordinates Coordinates { get; set; } = new(0, 0, 0, 0);

        /// <summary>
        /// 提示词列表（非线程安全，需要在锁内操作）
        /// </summary>
        public List<string> Prompts { get; } = new();

        /// <summary>
        /// 请求ID列表（非线程安全，需要在锁内操作）
        /// </summary>
        public List<string> RequestIds { get; } = new();

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedTime { get; } = DateTime.UtcNow;

        /// <summary>
        /// 过期定时器
        /// </summary>
        public Timer? ExpirationTimer { get; set; }

        /// <summary>
        /// 完成源，用于异步等待合并结果
        /// </summary>
        public TaskCompletionSource<Dictionary<string, Dictionary<string, bool>>> CompletionSource { get; } = new();

        /// <summary>
        /// 处理状态标记，用于防止竞态条件
        /// </summary>
        public bool IsProcessing { get; set; } = false;

        /// <summary>
        /// 线程安全锁对象，确保对内部集合的操作是原子的
        /// </summary>
        public readonly object LockObject = new object();
    }
}
