# FR-007 增加相同内容合并处理的修改方案

## 1. 修改概述

### 1.1 目标
为AI处理器添加相同内容合并处理功能，实现时间窗口内相同点位、相同裁剪坐标的请求合并，优化AI API调用效率。

### 1.2 核心功能
- 3秒时间窗口收集相同条件的请求
- 多种提示词直接合并发送给AI大模型
- 复合结果按request_id分发到对应EP
- 容错处理确保系统稳定性

## 2. 修改文件清单

### 2.1 新增文件

#### 2.1.1 Services/IMergeService.cs
**功能**：合并服务接口定义
**内容**：
- 定义 `CollectAndWaitAsync` 方法
- 返回类型为 `Task<Dictionary<string, bool>>`
- 参数包括：imagePath, coordinates, prompt, requestId

#### 2.1.2 Services/MergeService.cs
**功能**：合并服务实现类
**内容**：
- 实现 `IMergeService` 接口
- 时间窗口收集器管理
- 提示词合并逻辑
- 复合结果解析和分发
- 详细的日志记录

#### 2.1.3 Models/TimeWindowCollector.cs
**功能**：时间窗口收集器模型
**内容**：
- 收集器属性定义
- 提示词和请求ID列表
- 创建时间和过期定时器
- 完成源用于异步等待

### 2.2 修改现有文件

#### 2.2.1 Models/AppSettings.cs
**修改位置**：`ProcessingSettings` 类
**修改内容**：
- 添加 `MergeWindowSeconds` 属性
- 设置默认值为3
- 添加数据验证注解

#### 2.2.2 Services/MqttHostedService.cs
**修改位置**：
- 构造函数：添加 `IMergeService` 参数
- 私有字段：添加 `_mergeService`
- `ProcessMessageAsync` 方法：添加合并逻辑
- 移除原有的图片处理和AI调用逻辑

**具体修改**：
```csharp
// 构造函数添加参数
public MqttHostedService(
    // ... 现有参数 ...
    IMergeService mergeService)

// 添加私有字段
private readonly IMergeService _mergeService;

// ProcessMessageAsync方法修改
// 在坐标验证后添加：
var mergeResult = await _mergeService.CollectAndWaitAsync(
    controlMessage.ImagePath!,
    coordinateValidationResult.Coordinates!,
    controlMessage.Prompt!,
    controlMessage.RequestId!);

await PublishSuccessEventAsync(topic, controlMessage, mergeResult);
return;
```

#### 2.2.3 Program.cs
**修改位置**：`ConfigureServices` 方法
**修改内容**：
- 注册 `IMergeService` 服务
- 配置为单例服务
- 确保依赖注入正确

#### 2.2.4 appsettings.json
**修改位置**：`Processing` 节点
**修改内容**：
- 添加 `MergeWindowSeconds` 配置项
- 设置默认值为3

#### 2.2.5 REQ_AI_PROCESSOR_V1.md
**修改位置**：
- FR-007 功能需求：添加相同内容合并处理说明
- IR-002 配置接口：添加 `MergeWindowSeconds` 参数
- 配置示例：添加合并窗口时间配置

## 3. 详细修改内容

### 3.1 新增接口定义

#### 3.1.1 IMergeService.cs
```csharp
using AIProcessor.Models;
using AIProcessor.Validation;

namespace AIProcessor.Services
{
    public interface IMergeService
    {
        Task<Dictionary<string, bool>> CollectAndWaitAsync(
            string imagePath, Coordinates coordinates, string prompt, string requestId);
    }
}
```

#### 3.1.2 TimeWindowCollector.cs
```csharp
using AIProcessor.Validation;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace AIProcessor.Models
{
    public class TimeWindowCollector
    {
        public string ImagePath { get; set; } = string.Empty;
        public Coordinates Coordinates { get; set; } = new(0, 0, 0, 0);
        public List<string> Prompts { get; } = new();
        public List<string> RequestIds { get; } = new();
        public DateTime CreatedTime { get; } = DateTime.UtcNow;
        public Timer? ExpirationTimer { get; set; }
        public TaskCompletionSource<Dictionary<string, Dictionary<string, bool>>> CompletionSource { get; } = new();
        
        // 新增状态字段，用于处理竞态条件
        public bool IsProcessing { get; set; } = false; 
        // 用于确保线程安全的锁对象
        public readonly object LockObject = new object();
    }
}
```

### 3.2 核心实现逻辑

#### 3.2.1 合并服务核心方法
- `CollectAndWaitAsync`：主要入口方法
- `CreateNewCollector`：创建新的收集器
- `StartExpirationTimer`：启动过期定时器
- `ExecuteMergedRequest`：执行合并请求
- `ParseCompositeResults`：解析复合结果
- `ParseCompositeAIResponse`：解析AI复合响应
- `ParseAlternativeFormats`：解析备用格式
- `CleanJsonContent`：清理JSON内容

#### 3.2.2 时间窗口管理
- 使用 `ConcurrentDictionary` 管理收集器
- 基于图片路径和坐标生成唯一键
- 3秒时间窗口控制合并范围
- 定时器自动触发合并请求

#### 3.2.3 提示词合并策略
- 直接字符串拼接：`prompt1 + "\n\n" + prompt2 + "\n\n" + prompt3`
- 不进行任何内容修改
- 让AI大模型一次性处理多个识别目标

#### 3.2.4 结果分发机制
- 解析AI返回的复合结果
- 按原始request_id顺序分发
- 容错处理：解析失败时使用整个结果
- **注意**: 依赖AI返回结果顺序的可靠性问题将在EP_V4版本中进行审查和优化。

### 3.3 健壮性与风险控制 (审查补充)

#### 3.3.1 并发安全 (Concurrency Safety)
- **问题**: `TimeWindowCollector` 类中的 `List<string>` 集合不是线程安全的，高并发下直接访问会导致数据竞争。
- **解决方案**: 在 `MergeService` 中，所有对 `TimeWindowCollector` 实例内部属性（如 `Prompts`, `RequestIds`）的读写操作，都必须在 `lock (collector.LockObject)` 语句块内执行，确保原子性和线程安全。

#### 3.3.2 竞态条件 (Race Condition)
- **问题**: 当一个收集器的定时器到期，开始执行合并请求时，新的符合条件的请求可能同时到达，导致数据不一致。
- **解决方案**:
    1. 为 `TimeWindowCollector` 增加 `IsProcessing` 状态标记。
    2. 在 `ExecuteMergedRequest` 方法开始时，立即在 `lock` 块中将 `IsProcessing` 设置为 `true`。
    3. 当新请求到达 `CollectAndWaitAsync` 方法时，如果发现其目标收集器的 `IsProcessing` 为 `true`，则不加入当前收集器，而是为其创建一个全新的收集器。

#### 3.3.3 错误处理 (Error Handling)
- **问题**: 如果合并后的AI API调用失败（如超时、服务器错误），所有等待 `CompletionSource.Task` 的请求将被永久阻塞。
- **解决方案**: 在 `ExecuteMergedRequest` 方法中，必须将AI调用及后续处理放在 `try...catch` 块中。在 `catch` 块里，必须调用 `collector.CompletionSource.SetException(ex)` 来通知所有等待的线程任务已失败，从而释放它们并传播异常。

#### 3.3.4 资源管理 (Resource Management)
- **问题**: `System.Threading.Timer` 和 `TimeWindowCollector` 实例需要被正确管理和销毁，以防内存泄漏。
- **解决方案**: 在 `ExecuteMergedRequest` 方法的 `finally` 块中，确保执行以下清理操作：
    1. 调用 `collector.ExpirationTimer?.Dispose()` 来释放定时器资源。
    2. 从全局的 `ConcurrentDictionary` 中移除当前收集器实例。

### 3.4 配置更新

#### 3.4.1 AppSettings.cs 修改
```csharp
public class ProcessingSettings
{
    public int MaxConcurrentRequests { get; set; } = 100;
    public int MergeWindowSeconds { get; set; } = 3; // 新增
}
```

#### 3.4.2 appsettings.json 修改
```json
{
  "Processing": {
    "MaxConcurrentRequests": 100,
    "MergeWindowSeconds": 3
  }
}
```

### 3.5 处理流程修改

#### 3.5.1 新的处理流水线
```
消息接收 → 去重检查 → 消息验证 → 图片验证 → 坐标验证 → 
合并检查与等待 → [ (如果需要) 图片处理 → AI调用(合并) → 结果解析 ] → 发布结果
```

#### 3.5.2 合并检查逻辑
- 在坐标验证后，调用 `MergeService.CollectAndWaitAsync`。
- 服务内部处理收集、等待、触发合并的逻辑。
- 如果是第一个请求，则创建新收集器并启动定时器。
- 如果是后续请求，则加入现有收集器。
- `await` 等待 `CompletionSource.Task` 返回最终分发给自己的结果。

## 4. 测试验证

### 4.1 单元测试
- 测试时间窗口收集功能
- 测试提示词合并逻辑
- 测试复合结果解析
- 测试结果分发机制
- **新增**: 测试并发场景下 `TimeWindowCollector` 的线程安全性。
- **新增**: 测试AI调用失败时，`SetException` 是否被正确调用。

### 4.2 集成测试
- 测试完整的合并处理流程
- 测试并发请求的合并效果
- 测试异常情况的容错处理
- **新增**: 模拟高并发请求，验证没有发生竞态条件和数据不一致。

### 4.3 性能测试
- 验证合并率是否符合预期（30-60%）
- 验证AI调用次数减少效果
- 验证响应时间优化效果

## 5. 部署和验证

### 5.1 编译验证
- 执行 `dotnet build` 检查编译错误
- 修复所有语法和依赖问题

### 5.2 功能验证
- 测试时间窗口收集功能
- 验证合并请求的正确性
- 确认结果分发的准确性

### 5.3 性能验证
- 监控合并率指标
- 验证AI调用次数减少
- 确认系统稳定性

## 6. 预期效果

### 6.1 性能提升
- 合并率：30-60%的请求会被合并处理
- AI调用减少：减少30-60%的AI API调用次数
- 响应时间：合并请求的处理时间减少50%+

### 6.2 资源节省
- 网络带宽：减少重复AI API调用
- CPU使用：减少重复图片处理
- 内存使用：通过合并减少重复计算

### 6.3 业务价值
- 实时性：3秒窗口不影响业务实时性要求
- 准确性：相同时间点的相同场景，结果一致
- 效率：减少重复计算，提高系统整体效率 