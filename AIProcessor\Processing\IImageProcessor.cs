using SixLabors.ImageSharp;
using SixLabors.ImageSharp.PixelFormats;
using AIProcessor.Validation;

namespace AIProcessor.Processing;

/// <summary>
/// 图片处理器接口，提供图片裁剪和智能缩放功能
/// </summary>
public interface IImageProcessor
{
    /// <summary>
    /// 根据给定坐标处理图片，执行裁剪或智能缩放操作
    /// </summary>
    /// <param name="originalImage">原始图片</param>
    /// <param name="coords">处理坐标，(0,0,0,0)时执行智能缩放，其他坐标执行裁剪</param>
    /// <returns>处理后的图片</returns>
    Image<Rgba32> ProcessImage(Image<Rgba32> originalImage, Coordinates coords);
}