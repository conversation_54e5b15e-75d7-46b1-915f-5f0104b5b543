using SixLabors.ImageSharp;
using SixLabors.ImageSharp.PixelFormats;
using SixLabors.ImageSharp.Processing;
using AIProcessor.Validation;

namespace AIProcessor.Processing;

/// <summary>
/// 图片处理器实现，提供图片裁剪和智能缩放功能
/// </summary>
public class ImageProcessor : IImageProcessor
{
    private const int MaxWidth = 800;
    private const int MaxHeight = 400;

    /// <summary>
    /// 根据给定坐标处理图片，执行裁剪或智能缩放操作
    /// </summary>
    /// <param name="originalImage">原始图片</param>
    /// <param name="coords">处理坐标，(0,0,0,0)时执行智能缩放，其他坐标执行裁剪</param>
    /// <returns>处理后的图片</returns>
    public Image<Rgba32> ProcessImage(Image<Rgba32> originalImage, Coordinates coords)
    {
        if (coords.IsSmartScaling)
        {
            return ProcessSmartScaling(originalImage);
        }
        else
        {
            return ProcessCropping(originalImage, coords);
        }
    }

    /// <summary>
    /// 执行智能缩放处理
    /// </summary>
    /// <param name="originalImage">原始图片</param>
    /// <returns>缩放后的图片或原图克隆</returns>
    private Image<Rgba32> ProcessSmartScaling(Image<Rgba32> originalImage)
    {
        // 仅当图片宽度和高度都超过限制时才进行缩放
        if (originalImage.Width > MaxWidth && originalImage.Height > MaxHeight)
        {
            // 计算缩放比例，选择较小者以保持比例
            var widthRatio = (double)MaxWidth / originalImage.Width;
            var heightRatio = (double)MaxHeight / originalImage.Height;
            var scaleFactor = Math.Min(widthRatio, heightRatio);

            var newWidth = (int)(originalImage.Width * scaleFactor);
            var newHeight = (int)(originalImage.Height * scaleFactor);

            var resizedImage = originalImage.Clone();
            resizedImage.Mutate(x => x.Resize(new ResizeOptions
            {
                Size = new Size(newWidth, newHeight),
                Sampler = KnownResamplers.Lanczos3
            }));

            return resizedImage;
        }
        else
        {
            // 不满足缩放条件，返回原图克隆
            return originalImage.Clone();
        }
    }

    /// <summary>
    /// 执行裁剪处理
    /// </summary>
    /// <param name="originalImage">原始图片</param>
    /// <param name="coords">裁剪坐标</param>
    /// <returns>裁剪后的图片</returns>
    private Image<Rgba32> ProcessCropping(Image<Rgba32> originalImage, Coordinates coords)
    {
        var cropRect = new Rectangle(coords.X1, coords.Y1, coords.X2 - coords.X1, coords.Y2 - coords.Y1);
        return originalImage.Clone(x => x.Crop(cropRect));
    }
}