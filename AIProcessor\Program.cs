using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using MQTTnet;
using Serilog;
using System.IO;
using AIProcessor.Services;
using AIProcessor.Models;
using AIProcessor.Validation;
using AIProcessor.Processing;
using AIProcessor.Abstractions;

namespace AIProcessor;

class Program
{
    static async Task Main(string[] args)
    {
        // 检查是否是测试模式
        if (args.Length > 0 && args[0] == "--test")
        {
            Environment.Exit(await TestConfigAndLogging.TestAsync());
            return;
        }

        try
        {
            // 创建主机构建器
            var hostBuilder = Host.CreateDefaultBuilder(args)
                .UseWindowsService()
                .ConfigureAppConfiguration((context, config) =>
                {
                    config.AddJsonFile("appsettings.json", optional: false, reloadOnChange: true);
                    config.AddEnvironmentVariables();
                    config.AddCommandLine(args);
                })
                .ConfigureServices((context, services) =>
                {
                    // 注册配置服务
                    services.AddSingleton<ConfigurationService>();

                    // 注册文件日志服务
                    services.AddSingleton<FileLoggingService>(serviceProvider =>
                    {
                        var configuration = serviceProvider.GetRequiredService<IConfiguration>();
                        var logger = serviceProvider.GetRequiredService<ILogger<FileLoggingService>>();
                        var logFilePath = configuration.GetSection("Logging:LogFilePath").Value;
                        return new FileLoggingService(logFilePath, logger);
                    });
                    
                    // 注册验证器
                    services.AddSingleton<IControlMessageValidator, ControlMessageValidator>();
                    services.AddSingleton<IImageFileValidator, ImageFileValidator>();
                    services.AddSingleton<ICoordinateValidator, CoordinateValidator>();
                    
                    // 注册处理器
                    services.AddSingleton<IImageProcessor, ImageProcessor>();
                    
                    // 注册AI服务
                services.AddHttpClient<IAIService, AIService>();
                services.AddSingleton<IAIService>(serviceProvider =>
                {
                    var httpClientFactory = serviceProvider.GetRequiredService<IHttpClientFactory>();
                    var httpClient = httpClientFactory.CreateClient(nameof(AIService));
                    var configService = serviceProvider.GetRequiredService<ConfigurationService>();
                    return new AIService(httpClient, configService.AppSettings.AI);
                });
                    
                    // 注册结果解析器
                    services.AddSingleton<IAIResultParser, AIResultParser>();
                    
                    // 注册文件系统抽象
                    services.AddSingleton<IFileSystem, WindowsFileSystem>();
                    
                    // 注册去重服务
                    services.AddSingleton<IDeduplicationService, DeduplicationService>();

                    // 注册合并服务
                    services.AddSingleton<IMergeService, MergeService>();

                    // 注册MQTT后台服务
                    services.AddHostedService<MqttHostedService>();
                })
                .UseSerilog((context, services, loggerConfiguration) =>
                {
                    // 从配置中获取日志文件路径和日志级别
                    var logFilePath = context.Configuration.GetSection("Logging:LogFilePath").Value;
                    var defaultLogLevel = context.Configuration.GetSection("Logging:LogLevel:Default").Value ?? "Information";

                    // 解析日志级别
                    var logLevel = Enum.TryParse<Serilog.Events.LogEventLevel>(defaultLogLevel, true, out var level)
                        ? level
                        : Serilog.Events.LogEventLevel.Information;

                    loggerConfiguration
                        .MinimumLevel.Is(logLevel)
                        .MinimumLevel.Override("Microsoft", Serilog.Events.LogEventLevel.Warning)
                        .MinimumLevel.Override("System", Serilog.Events.LogEventLevel.Warning)
                        .Enrich.FromLogContext()
                        .WriteTo.Console();

                    if (!string.IsNullOrWhiteSpace(logFilePath))
                    {
                        // 确保日志目录存在
                        var directory = Path.GetDirectoryName(logFilePath);
                        if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
                        {
                            Directory.CreateDirectory(directory);
                        }

                        // 添加文件日志
                        loggerConfiguration.WriteTo.File(logFilePath,
                            rollingInterval: RollingInterval.Day,
                            retainedFileCountLimit: 30,
                            shared: true,
                            flushToDiskInterval: TimeSpan.FromSeconds(1));
                    }
                });

            // 构建并运行主机
            using var host = hostBuilder.Build();
            
            // 获取配置服务并验证配置
            var configService = host.Services.GetRequiredService<ConfigurationService>();
            var logger = host.Services.GetRequiredService<ILogger<Program>>();

            logger.LogInformation("开始加载应用程序配置...");

            // 加载并验证配置
            AppSettings appSettings;
            try
            {
                appSettings = configService.AppSettings;
                logger.LogInformation("配置加载成功");
            }
            catch (ConfigurationException ex)
            {
                logger.LogError(ex, "配置加载失败: {Message}", ex.Message);
                throw;
            }

            // 记录关键配置信息
            logger.LogInformation("AI处理器启动中...");

            // 显示当前日志级别和日志文件路径
            var currentLogLevel = appSettings.Logging.LogLevel.Default;
            var currentLogFilePath = appSettings.Logging.LogFilePath ?? "未配置";
            // 获取完整的日志文件路径
            if (!string.IsNullOrWhiteSpace(appSettings.Logging.LogFilePath) && !Path.IsPathRooted(appSettings.Logging.LogFilePath))
            {
                currentLogFilePath = Path.GetFullPath(appSettings.Logging.LogFilePath);
            }
            logger.LogInformation("当前Log Level: {LogLevel} | 日志文件路径: {LogFilePath}", currentLogLevel, currentLogFilePath);

            logger.LogInformation("MQTT Broker: {Host}:{Port}",
                appSettings.Mqtt.BrokerHost, appSettings.Mqtt.BrokerPort);
            logger.LogInformation("AI模型: {Model}", appSettings.AI.ModelName);
            logger.LogInformation("最大并发请求数: {MaxConcurrent}",
                appSettings.Processing.MaxConcurrentRequests);

            // 验证和记录日志配置信息
            var fileLoggingService = host.Services.GetRequiredService<FileLoggingService>();
            if (!string.IsNullOrWhiteSpace(appSettings.Logging.LogFilePath))
            {
                logger.LogInformation("日志文件路径: {LogFilePath}", appSettings.Logging.LogFilePath);

                // 验证日志文件路径
                if (fileLoggingService.ValidateLogFilePath())
                {
                    logger.LogInformation("日志文件路径验证通过");

                    // 确保日志目录存在
                    if (fileLoggingService.EnsureLogDirectoryExists())
                    {
                        logger.LogInformation("日志目录已准备就绪");

                        // 测试写入权限
                        if (fileLoggingService.TestLogFileWritePermission())
                        {
                            logger.LogInformation("日志文件写入权限测试通过");
                        }
                        else
                        {
                            logger.LogWarning("日志文件写入权限测试失败，可能影响日志记录");
                        }
                    }
                    else
                    {
                        logger.LogWarning("无法创建日志目录，将仅使用控制台日志");
                    }
                }
                else
                {
                    logger.LogWarning("日志文件路径验证失败，将仅使用控制台日志");
                }
            }
            else
            {
                logger.LogWarning("未配置日志文件路径，仅使用控制台日志");
            }
            
            // 启动主机服务
            await host.RunAsync();
        }
        catch (ConfigurationException ex)
        {
            Console.WriteLine($"配置错误: {ex.Message}");
            Environment.Exit(1);
        }
        catch (Exception ex)
        {
            Console.WriteLine($"应用程序启动失败: {ex.Message}");
            Environment.Exit(1);
        }
    }
}
