# AI处理器需求定义文档 V1.0

## 1. 项目概述

### 1.1 目的
AI处理器是 Event Processor 系统的独立AI分析模块，负责接收MQTT控制消息，执行图片AI分析，并返回分析结果。本模块采用C# .NET开发，旨在实现一个轻量、高效、易于维护的AI处理服务，初期部署为Windows Console应用，未来可作为Windows Service运行。

### 1.2 范围
- MQTT消息接收和处理
- 图片AI分析执行
- 结果返回和错误处理
- Windows Service部署和运行（MVP版本为Windows Console）

### 1.3 核心设计原则
- **直接处理模式**：只有一个消息队列，收到消息后入队前去重（使用内存字典存储已处理消息ID），放入队列，队头消息直接提交到线程池。
- **独立处理**：每个消息独立处理，无复杂依赖，单个消息处理失败不影响整体流程。
- **并发控制**：使用 `SemaphoreSlim` 限制最大并发处理数（由参数 `AI_MULTI_MAX_NUMBER` 决定，默认100）。
- **流水线验证**：格式验证（JSON格式）→ 内容验证（字段齐全且非空）→ 图片处理 → 调用AI API → 分析 → 验证结果 → 发送MQTT结果消息。

---

## 2. 功能需求（FR）

### FR-001 MQTT消息订阅与处理
系统应订阅MQTT主题 `ai/{COMM_ID}/+/control`，并采用轻量化方式处理消息。

**详细要求**：
- **消息接收**：使用 `MQTTnet` 库创建客户端，保持长连接并自动重连。
- **订阅逻辑**：支持通配符订阅，不区分主题内容，不做消息路由。
- **处理模式**：采用异步回调接收消息，收到后立即提交到.NET默认线程池处理，避免阻塞。
- **消息去重**：在处理前，使用内存字典对消息ID进行去重。
- **QoS**：支持QoS 1消息质量保证。

### FR-002 控制消息验证
系统应对接收到的MQTT消息进行严格验证。

**详细要求**：
- **格式验证**：检查消息长度是否在合理范围，并验证其是否为有效的JSON格式。
- **内容验证**：确保所有必填字段（如 `event_id`, `image_path`, `prompt`, `request_id`）存在且值不为空。
- **豁免验证**：为便于测试，不验证时间戳的有效性；同时，系统不做任何权限验证。
- **错误处理**：对无效消息进行错误处理和日志记录，日志需包含唯一的异常编号和代码位置。

### FR-003 图片文件验证和处理
系统应验证控制消息中指定的图片文件是否存在且可访问，并根据裁剪坐标进行相应的图片处理。

**详细要求**：
- 检查图片文件路径的有效性
- 验证文件是否存在且可读
- 支持常见图片格式：JPG、PNG、BMP
- 对无效文件路径进行错误处理和日志记录

#### 图片裁剪坐标处理逻辑

##### 特殊坐标处理
**需求描述**：
- 当裁剪坐标为 `(0,0,0,0)` 时，系统应执行智能缩放而非裁剪操作
- 智能缩放应按比例调整图片尺寸以适应预设的最大尺寸限制

**技术规格**：
- 最大宽度限制：800像素
- 最大高度限制：400像素
- 重采样算法：使用 LANCZOS 算法确保高质量缩放
- 缩放条件：仅当图片宽度和高度都超过限制时才进行缩放

**实现要点**：
```
智能缩放流程：
1. 检测到特殊坐标 (0,0,0,0) 时触发智能缩放
2. 获取原始图片的宽度和高度
3. 计算宽度和高度的缩放比例（目标尺寸/原始尺寸）
4. 选择较小的缩放比例以保持图片比例
5. 仅当缩放比例小于1.0时才执行缩放操作
6. 使用PIL的LANCZOS重采样算法进行高质量缩放
7. 返回缩放后的图片对象
```

##### 坐标解析和验证机制
**需求描述**：
- 系统应解析配置中的裁剪坐标字符串
- 验证坐标格式的有效性和数值范围
- 处理坐标解析异常情况

**技术规格**：
- 坐标格式：支持逗号分隔的四个整数值 "x1,y1,x2,y2"
- 数值验证：确保坐标值为非负整数
- 逻辑验证：确保 x2 > x1 且 y2 > y1（除特殊坐标外）
- 异常处理：坐标解析失败时记录错误日志

**实现要点**：
```
坐标解析流程：
1. 从配置获取坐标字符串
2. 按逗号分割并转换为整数
3. 验证数值有效性和逻辑关系
4. 特殊坐标 (0,0,0,0) 触发智能缩放路径
5. 有效坐标执行标准裁剪操作
```

### FR-004 AI分析执行
系统应调用AI服务执行图片分析，根据提示词进行智能识别。

**详细要求**：
- 调用OpenAI兼容的AI API服务
- 按配置文件调用AI模型（qwen-vl-plus-latest等）
- **异常处理**：处理AI服务调用异常和超时，但不进行重试。
- **无重试机制**：除MQTT客户端的自动重连外，应用程序级别的其他操作（如AI API调用）失败后均不重试。

#### API调用实现细节

##### HTTP客户端配置
**需求描述**：
- 系统应配置专用的HTTP客户端用于AI API调用
- 设置合适的超时参数和连接配置
- 支持基本的错误状态码处理

**技术规格**：
- 请求超时：可配置的超时时间（默认5秒）
- 连接超时：建立连接的超时限制
- User-Agent：设置标识应用程序的用户代理字符串
- Content-Type：application/json

##### 请求数据构建
**需求描述**：
- 系统应将图片编码为Base64格式
- 构建符合AI模型要求的请求数据结构
- 包含必要的模型参数和配置
- 发送到AI服务API的HTTP请求体（JSON Payload）， 严禁 包含 event_id 或任何AI模型分析图片和提示词所 非必需 的字段。
- AI API的请求载荷应仅包含 model, messages (含 prompt 和 base64编码的图片) 等AI模型直接需要的参数。
- event_id 和 request_id 等上下文信息，必须由应用程序在发起AI调用的处理流程中自行持有和管理。当AI API调用返回结果后，应用程序负责将返回的AI分析结果与之前持有的上下文信息（event_id, request_id）重新组合，以构建最终的 EventMessage。

**技术规格**：
- 图片编码：Base64编码处理
- 请求格式：JSON格式的请求体
- 必需字段：model、messages、max_tokens等
- 图片数据：嵌入在messages中的base64格式

##### 响应解析处理
**需求描述**：
- 系统应解析AI模型返回的JSON响应
- 提取关键信息如分析结果和置信度
- 处理响应格式异常情况

**技术规格**：
- 响应格式：标准JSON格式
- 关键字段：choices、content、usage等
- 内容解析：从content字段提取JSON格式的分析结果
- 异常处理：响应格式错误时记录详细日志

**实现要点**：
```
AI调用流程：
1. 读取图片文件并编码为Base64
2. 构建包含图片数据的JSON请求
3. 发送HTTP POST请求到AI模型端点
4. 解析响应JSON获取choices和content
5. 从content中提取分析结果JSON
6. 验证结果格式并提取置信度
7. 返回结构化的分析结果
```

### FR-005 结果解析和格式化
系统应解析AI服务返回的结果，格式化为标准的结果消息格式。

**详细要求**：
- 解析AI返回的JSON格式结果
- 提取识别结果中的关键字段
- 转换为布尔值格式（true/false）
- 处理AI返回的各种格式和异常情况

### FR-006 结果消息发布
系统应将AI分析结果通过MQTT消息发布到指定主题。

**详细要求**：
- 发布到主题 `ai/{COMM_ID}/{POSITION_ID}/event`
- 包含完整的分析结果和处理时间
- 支持QoS 1消息质量保证
- 确保消息的可靠传递

### FR-007 并发请求处理和相同内容合并
系统应支持同时处理多个AI分析请求，并实现相同内容的智能合并处理，避免阻塞并优化性能。

**详细要求**：
- **并发控制**：使用 `SemaphoreSlim`（轻量级信号量）控制并发处理的请求数量，最大并发数由配置文件中的 `MaxConcurrentRequests` 参数（旧称 `AI_MULTI_MAX_NUMBER`）决定，默认值为100。
- **线程管理**：利用.NET的默认线程池高效处理并发任务，减少上下文切换开销。
- **独立性**：每个消息的处理流程完全独立，不存在复杂的依赖关系。
- **容错性**：单个消息在处理过程中失败（如图片不存在、AI分析超时等）不会影响其他消息的处理流程。

#### 相同内容合并处理
**需求描述**：
- 系统应捕获同一个点位同一时间点（允许3秒时间误差）同一裁剪坐标的请求
- 将时间窗口内收集到的多种不同提示词直接合并，在一次请求中一次性向AI大模型API发起请求
- AI大模型返回复合结果，系统按request_id分发到对应的EP（AIProcessor的请求者）
- 每个时间窗口参数值的时间段发送一次请求（默认3秒）

**技术规格**：
- 合并窗口时间：可配置的时间窗口（默认3秒），由配置文件中的 `MergeWindowSeconds` 参数决定
- 合并条件：相同图片路径 + 相同裁剪坐标 + 时间窗口内
- 提示词合并：直接字符串拼接，格式为 `prompt1 + "\n\n" + prompt2 + "\n\n" + prompt3`
- 结果分发：根据原始request_id列表，将AI返回的复合结果按顺序分发到对应的EP
- 容错处理：合并解析失败时，将整个AI结果分配给所有参与合并的请求

**实现要点**：
```
合并处理流程：
1. 接收请求后检查是否存在相同条件的合并组
2. 如果存在且在时间窗口内，加入现有合并组
3. 如果是新请求或时间窗口过期，创建新的合并组
4. 启动定时器，时间窗口结束时触发合并请求
5. 将多个提示词直接拼接，发送给AI大模型
6. 解析AI返回的复合结果，按request_id顺序分发
7. 为每个原始请求发布对应的事件消息
```

**性能优化**：
- 预期合并率：30-60%的请求会被合并处理
- AI调用减少：合并请求可减少30-60%的AI API调用次数
- 响应时间：合并请求的处理时间减少50%+
- 资源节省：减少网络带宽和CPU使用

### FR-008 错误处理和日志记录
系统应对各种异常情况进行处理和记录。

**详细要求**：
- 在每个可能出现异常的位置添加try-catch
- **异常捕获**：在每个可能出现异常的关键位置（如文件访问、网络请求、消息解析）添加 `try-catch` 块。
- **日志记录**：记录详细的异常信息，每种可预见的异常都应分配一个唯一编号，日志内容需包含异常发生的代码位置和触发异常的原始消息内容，便于快速定位问题。
- **简化运维**：系统不做错误统计、监控、告警或通知。

### FR-009 Windows Service运行
系统应作为Windows Service运行，提供稳定的服务能力。

**详细要求**：
- 支持Windows Service的启动、停止、重启
- 自动重启机制，服务异常时自动恢复
- 服务状态监控和报告
- 服务配置在启动时加载，不支持运行时热更新。

---

## 3. 非功能需求（NFR）

### NFR-001 响应时间
- AI分析请求的处理时间不超过30秒
- MQTT消息的接收和解析时间不超过1秒
- 结果消息的发布时间不超过1秒

### NFR-002 并发能力
- 根据 `MaxConcurrentRequests` 配置，支持最多100个并发AI分析请求。
- 单个请求的处理不应影响其他请求。

### NFR-003 可靠性
- MQTT连接断开时，客户端应能自动重连。
- 应用程序层面不实现任何重试逻辑。

### NFR-004 可维护性
- 提供详细的日志记录，便于问题排查。
- 配置文件在启动时加载，不支持运行时更新。
- 不提供服务状态监控接口。

### NFR-005 可扩展性
- 支持多种AI模型和API的配置
- 支持不同格式的图片文件处理
- 支持自定义的结果格式转换

---

## 4. 接口定义（IR）

### IR-001 MQTT控制消息接口

#### 输入规格

**消息主题**：`ai/{COMM_ID}/{POSITION_ID}/control`

**消息格式**：JSON

**消息结构**：
```json
{
  "event_id": "string",           // 事件ID，必填
  "image_path": "string",         // 图片文件路径，必填
  "image_crop_coordinates": "string", // 图片裁剪坐标，必填
  "prompt": "string",             // AI分析提示词，必填
  "timestamp": "string",          // 时间戳，必填，格式：YYYYMMDDHHmmssSSS
  "request_id": "string"          // 请求ID，必填，UUID格式
}
```

**字段说明**：
- `event_id`：事件标识符，用于关联分析结果
- `image_path`：图片文件的完整路径，支持Windows路径格式
- `prompt`：AI分析的提示词，描述需要识别的内容
- `timestamp`：17位时间戳，格式为YYYYMMDDHHmmssSSS
- `request_id`：唯一请求标识符，用于结果关联

**示例**：
```json
{
  "image_path": "E:\\FTP-IPC\\LFY-IN01-TEST\\P001LfyBmIn_20250627031700738.jpg",
  "prompt": "图片是否存在以下类型的车辆[三轮车]或[快递车]",
  "timestamp": "20250627031700738",
  "request_id": "uuid-12345-6789-abcdef"
}
```

#### 输出规格

**消息主题**：`ai/{COMM_ID}/{POSITION_ID}/event`

**消息格式**：JSON

**消息结构**：
```json
{
  "event_id": "string",                    // 事件ID，必填
  "request_id": "string",                  // 请求ID，必填
  "result": {                              // 分析结果，必填
    "field_name": "boolean"                // 字段名: 布尔值
  },
  "timestamp": "string",                   // 时间戳，必填
  "processing_time": "number",             // 处理时间，必填，单位：秒
  "success": "boolean",                    // 处理成功标志，必填
  "error_message": "string"                // 错误信息，可选，仅在success为false时提供
}
```

**字段说明**：
- `event_id`：对应输入消息的事件ID
- `request_id`：对应输入消息的请求ID
- `result`：AI分析结果，键为字段名，值为布尔值
- `timestamp`：结果生成时间戳，格式与输入相同
- `processing_time`：AI分析处理时间，精确到毫秒
- `success`：处理是否成功，true表示成功，false表示失败
- `error_message`：错误描述信息，仅在处理失败时提供

**成功示例**：
```json
{
  "event_id": "EV001008",
  "request_id": "uuid-12345-6789-abcdef",
  "result": {
    "三轮车": true,
    "快递车": false
  },
  "timestamp": "20250627031700738",
  "processing_time": 2.3,
  "success": true
}
```

**失败示例**：
```json
{
  "event_id": "EV001008",
  "request_id": "uuid-12345-6789-abcdef",
  "result": {},
  "timestamp": "20250627031700738",
  "processing_time": 0.0,
  "success": false,
  "error_message": "图片文件不存在: E:\\FTP-IPC\\LFY-IN01-TEST\\P001LfyBmIn_20250627031700738.jpg"
}
```

### IR-002 配置文件接口

#### 配置文件格式

**文件路径**：`appsettings.json`

**配置结构**：
```json
{
  "Mqtt": {
    "BrokerHost": "string",        // MQTT Broker地址，必填
    "BrokerPort": "number",        // MQTT Broker端口，必填
    "ClientId": "string",          // MQTT客户端ID，必填
    "Username": "string",          // MQTT用户名，可选
    "Password": "string",          // MQTT密码，可选
    "KeepAliveInterval": "number", // 保活间隔，可选，默认60秒
    "ReconnectDelay": "number"     // 重连延迟，可选，默认5秒
  },
  "AI": {
    "ApiKey": "string",            // AI服务API密钥，必填
    "ModelName": "string",         // AI模型名称，必填
    "ApiUrl": "string",            // AI服务API地址，必填
    "Timeout": "number",           // 请求超时时间，可选，默认5秒

  },
  "Processing": {
    "MaxConcurrentRequests": "number", // 最大并发请求数，可选，默认100
    "MergeWindowSeconds": "number"     // 合并窗口时间，可选，默认3秒
  },
  "Logging": {
    "LogLevel": {
      "Default": "string",         // 默认日志级别，可选，默认Information
      "Microsoft": "string"        // Microsoft组件日志级别，可选，默认Warning
    },
    "LogFilePath": "string"        // 日志文件路径，可选
  }
}
```

**配置示例**：
```json
{
  "Mqtt": {
    "BrokerHost": "localhost",
    "BrokerPort": 1883,
    "ClientId": "AIProcessor_001",
    "Username": "ai_processor",
    "Password": "password123",
    "KeepAliveInterval": 60,
    "ReconnectDelay": 5
  },
  "AI": {
    "ApiKey": "sk-888888888888883e9ac0ce7d6c6527f7",
    "ModelName": "qwen-vl-plus",
    "ApiUrl": "https://dashscope.aliyuncs.com/compatible-mode/v1",
    "Timeout": 5,

  },
  "Processing": {
    "MaxConcurrentRequests": 100,
    "MergeWindowSeconds": 3
  },
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft": "Warning"
    },
    "LogFilePath": "logs\\log_ai_processor.log"
  }
}
```

**配置说明**：
- 开发和测试中用"qwen-vl-plus"模型，生产环境用"qwen-vl-plus-latest"模型
- `MergeWindowSeconds`：合并窗口时间，用于控制相同内容合并的时间范围，建议值2-5秒


### IR-003 日志接口

#### 日志格式

**日志级别**：Debug、Information、Warning、Error、Critical

**日志内容**：
- 时间戳：ISO 8601格式
- 日志级别：字符串标识
- 消息内容：详细描述信息
- 异常信息：异常堆栈跟踪（如有）
- 上下文信息：请求ID、事件ID等

**日志示例**：
```
2025-06-27T10:30:15.123Z [Information] AI处理器服务启动成功
2025-06-27T10:30:16.456Z [Information] 收到AI分析请求: event_id=EV001008, request_id=uuid-12345-6789-abcdef
2025-06-27T10:30:18.789Z [Information] AI分析完成: event_id=EV001008, processing_time=2.3s
2025-06-27T10:30:19.012Z [Error] AI服务调用失败: event_id=EV001008, error=Network timeout
```



---

## 5. 约束条件

### 5.1 技术约束
- 必须使用C# .NET 6.0或更高版本
- 必须支持Windows 10/11和Windows Server 2019/2022
- 必须使用MQTT协议进行消息小区
- 必须支持OpenAI兼容的AI API接口

### 5.2 环境约束
- 必须能够访问Event Processor的MQTT Broker
- 必须能够访问AI服务的API接口
- 必须能够访问图片文件存储路径
- 必须具有Windows Service运行权限

### 5.3 资源约束
- 内存使用不超过1GB
- CPU使用率在正常负载下不超过50%
- 磁盘空间需求不超过100MB
- 网络带宽需求根据并发请求数确定

---

## 6. 依赖关系

### 6.1 外部依赖
- Event Processor系统：提供MQTT控制消息
- AI服务提供商：提供图片分析能力
- Windows操作系统：提供Service运行环境
- MQTT Broker：提供消息小区服务

### 6.2 内部依赖
- .NET Runtime：提供运行环境
- MQTTnet：提供MQTT小区能力
- HTTP客户端：提供AI API调用能力
- 日志框架：提供日志记录能力

---

## 7. 验收标准

### 7.1 功能验收
- 能够正确接收和解析MQTT控制消息
- 能够成功调用AI服务进行图片分析
- 能够正确发布AI分析结果消息
- 能够处理各种异常情况

### 7.2 性能验收
- 单个AI分析请求处理时间不超过30秒。
- 支持配置的最大并发请求数（默认100）。
- 服务启动时间不超过30秒。

### 7.3 可靠性验收
- 服务能够稳定运行24小时以上。
- MQTT连接断开时能够自动重连。

### 7.4 可维护性验收
- 提供完整的日志记录。
- 错误信息清晰明确。