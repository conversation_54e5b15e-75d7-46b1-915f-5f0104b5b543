using AIProcessor.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.Json;

namespace AIProcessor.Services
{
    /// <summary>
    /// AI结果解析器实现，用于将AI服务返回的结果解析为标准格式
    /// </summary>
    public class AIResultParser : IAIResultParser
    {
        /// <summary>
        /// 解析AI响应，将其转换为Dictionary<string, bool>格式
        /// </summary>
        /// <param name="aiResponse">AI服务返回的响应</param>
        /// <returns>解析后的结果字典</returns>
        /// <exception cref="ArgumentNullException">当aiResponse为null时抛出</exception>
        /// <exception cref="FormatException">当AI返回的内容格式不符合预期时抛出</exception>
        public Dictionary<string, bool> Parse(AIResponse aiResponse)
        {
            if (aiResponse == null)
            {
                throw new ArgumentNullException(nameof(aiResponse), "AI响应不能为null");
            }

            // 检查Choices是否存在且有内容
            if (aiResponse.Choices == null || !aiResponse.Choices.Any())
            {
                return new Dictionary<string, bool>();
            }

            // 提取第一个Choice的Message.Content
            var content = aiResponse.Choices.First().Message?.Content;
            
            if (string.IsNullOrEmpty(content))
            {
                throw new FormatException("AI返回的内容为空或null，无法解析为有效的JSON格式");
            }

            try
            {
                // 清理内容，移除可能的Markdown代码块标记
                var cleanedContent = CleanJsonContent(content);
                
                // 尝试反序列化为Dictionary<string, bool>
                var result = JsonSerializer.Deserialize<Dictionary<string, bool>>(cleanedContent);
                return result ?? new Dictionary<string, bool>();
            }
            catch (Exception ex) when (ex is JsonException || ex is NotSupportedException || ex is ArgumentException)
            {
                throw new FormatException($"AI返回的内容格式不符合预期。期望JSON格式的字符串到布尔值的映射，但收到: {content}", ex);
            }
        }

        /// <summary>
        /// 清理AI返回的内容，移除Markdown代码块标记并提取JSON
        /// </summary>
        /// <param name="content">原始内容</param>
        /// <returns>清理后的JSON字符串</returns>
        private string CleanJsonContent(string content)
        {
            if (string.IsNullOrEmpty(content))
                return content;

            // 移除前后空白字符
            content = content.Trim();

            // 如果内容以```json或```开头，移除代码块标记
            if (content.StartsWith("```json"))
            {
                content = content.Substring(7); // 移除"```json"
            }
            else if (content.StartsWith("```"))
            {
                content = content.Substring(3); // 移除"```"
            }

            // 如果内容以```结尾，移除结尾标记
            if (content.EndsWith("```"))
            {
                content = content.Substring(0, content.Length - 3);
            }

            // 再次移除前后空白字符
            return content.Trim();
        }
    }
}