using System.Net.Http.Headers;
using System.Text;
using System.Text.Json;
using SixLabors.ImageSharp;
using SixLabors.ImageSharp.PixelFormats;
using SixLabors.ImageSharp.Formats.Png;
using AIProcessor.Models;

namespace AIProcessor.Services;

/// <summary>
/// AI服务实现类，提供图片分析功能
/// </summary>
public class AIService : IAIService
{
    private readonly HttpClient _httpClient;
    private readonly AiSettings _aiSettings;
    private readonly JsonSerializerOptions _jsonOptions;

    /// <summary>
    /// 初始化AI服务
    /// </summary>
    /// <param name="httpClient">HTTP客户端</param>
    /// <param name="aiSettings">AI服务配置</param>
    public AIService(HttpClient httpClient, AiSettings aiSettings)
    {
        _httpClient = httpClient;
        _aiSettings = aiSettings;
        _jsonOptions = new JsonSerializerOptions();

        // 配置HttpClient
        ConfigureHttpClient();
    }

    /// <summary>
    /// 异步分析图片
    /// </summary>
    /// <param name="image">待分析的图片</param>
    /// <param name="prompt">分析提示词</param>
    /// <returns>AI分析响应结果</returns>
    public async Task<AIResponse> AnalyzeImageAsync(Image<Rgba32> image, string prompt)
    {
        // 将图片编码为Base64
        var base64Image = EncodeImageToBase64(image);

        // 构建请求数据
        var request = BuildAIRequest(prompt, base64Image);

        // 序列化请求数据
        var jsonContent = JsonSerializer.Serialize(request, _jsonOptions);
        var content = new StringContent(jsonContent, Encoding.UTF8, "application/json");

        // 发送请求
        var response = await _httpClient.PostAsync(_aiSettings.ApiUrl, content);

        // 检查响应状态
        if (!response.IsSuccessStatusCode)
        {
            throw new HttpRequestException($"AI服务请求失败，状态码: {response.StatusCode}");
        }

        // 解析响应
        var responseContent = await response.Content.ReadAsStringAsync();
        var aiResponse = JsonSerializer.Deserialize<AIResponse>(responseContent, _jsonOptions);

        return aiResponse ?? throw new InvalidOperationException("AI服务响应解析失败");
    }

    /// <summary>
    /// 配置HttpClient
    /// </summary>
    private void ConfigureHttpClient()
    {
        // 设置超时时间
        _httpClient.Timeout = TimeSpan.FromSeconds(_aiSettings.Timeout);

        // 设置Authorization头
        _httpClient.DefaultRequestHeaders.Authorization = 
            new AuthenticationHeaderValue("Bearer", _aiSettings.ApiKey);

        // 设置User-Agent
        _httpClient.DefaultRequestHeaders.UserAgent.ParseAdd("AIProcessor/1.0");
    }

    /// <summary>
    /// 将图片编码为Base64字符串
    /// </summary>
    /// <param name="image">图片对象</param>
    /// <returns>Base64编码的图片数据</returns>
    private string EncodeImageToBase64(Image<Rgba32> image)
    {
        using var memoryStream = new MemoryStream();
        image.Save(memoryStream, new PngEncoder());
        var imageBytes = memoryStream.ToArray();
        return Convert.ToBase64String(imageBytes);
    }

    /// <summary>
    /// 构建AI请求对象
    /// </summary>
    /// <param name="prompt">提示词</param>
    /// <param name="base64Image">Base64编码的图片</param>
    /// <returns>AI请求对象</returns>
    private AIRequest BuildAIRequest(string prompt, string base64Image)
    {
        return new AIRequest
        {
            Model = _aiSettings.ModelName,
            MaxTokens = 300,
            Messages = new List<AIMessage>
            {
                new AIMessage
                {
                    Role = "user",
                    Content = new List<AIContent>
                    {
                        new AIContent
                        {
                            Type = "text",
                            Text = prompt
                        },
                        new AIContent
                        {
                            Type = "image_url",
                            ImageUrl = new AIImageUrl
                            {
                                Url = $"data:image/png;base64,{base64Image}"
                            }
                        }
                    }
                }
            }
        };
    }
}