using System.ComponentModel.DataAnnotations;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using AIProcessor.Models;

namespace AIProcessor.Services;

/// <summary>
/// 配置服务，负责加载和验证应用程序配置
/// </summary>
public class ConfigurationService
{
    private readonly IConfiguration _configuration;
    private readonly ILogger<ConfigurationService> _logger;
    private AppSettings? _appSettings;

    public ConfigurationService(IConfiguration configuration, ILogger<ConfigurationService> logger)
    {
        _configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    /// <summary>
    /// 获取应用程序配置
    /// </summary>
    public AppSettings AppSettings
    {
        get
        {
            if (_appSettings == null)
            {
                LoadAndValidateConfiguration();
            }
            return _appSettings!;
        }
    }

    /// <summary>
    /// 加载并验证配置
    /// </summary>
    /// <exception cref="ConfigurationException">当配置验证失败时抛出</exception>
    private void LoadAndValidateConfiguration()
    {
        _logger.LogDebug("开始加载应用程序配置");

        try
        {
            _appSettings = new AppSettings();
            _logger.LogDebug("正在绑定配置数据到AppSettings对象");

            _configuration.Bind(_appSettings);
            _logger.LogDebug("配置数据绑定完成");

            // 记录关键配置信息（不包含敏感信息）
            _logger.LogDebug("MQTT配置 - Host: {Host}, Port: {Port}, ClientId: {ClientId}",
                _appSettings.Mqtt.BrokerHost, _appSettings.Mqtt.BrokerPort, _appSettings.Mqtt.ClientId);
            _logger.LogDebug("AI配置 - ModelName: {ModelName}, ApiUrl: {ApiUrl}, Timeout: {Timeout}s",
                _appSettings.AI.ModelName, _appSettings.AI.ApiUrl, _appSettings.AI.Timeout);
            _logger.LogDebug("处理配置 - MaxConcurrentRequests: {MaxConcurrent}",
                _appSettings.Processing.MaxConcurrentRequests);
            _logger.LogDebug("日志配置 - LogFilePath: {LogFilePath}, DefaultLevel: {DefaultLevel}",
                _appSettings.Logging.LogFilePath ?? "未配置", _appSettings.Logging.LogLevel.Default);

            // 验证配置
            _logger.LogDebug("开始验证配置数据");
            ValidateConfiguration(_appSettings);
            _logger.LogInformation("配置加载和验证成功完成");
        }
        catch (ConfigurationException)
        {
            _logger.LogError("配置验证失败");
            throw;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "加载配置时发生未预期的错误");
            throw new ConfigurationException("Failed to load configuration", ex);
        }
    }

    /// <summary>
    /// 验证配置对象
    /// </summary>
    /// <param name="appSettings">要验证的配置对象</param>
    /// <exception cref="ConfigurationException">当验证失败时抛出</exception>
    private void ValidateConfiguration(AppSettings appSettings)
    {
        try
        {
            _logger.LogDebug("开始验证配置对象的完整性");

            // 验证根对象以及所有嵌套的属性
            var validationResults = new List<ValidationResult>();
            var validationContext = new ValidationContext(appSettings);

            bool isValid = Validator.TryValidateObject(appSettings, validationContext, validationResults, validateAllProperties: true);

            if (!isValid)
            {
                var errorMessages = validationResults.Select(vr => vr.ErrorMessage).ToArray();
                _logger.LogError("配置验证失败，发现 {ErrorCount} 个错误: {Errors}",
                    errorMessages.Length, string.Join("; ", errorMessages));

                // 抛出第一个验证错误
                throw new ValidationException(errorMessages.FirstOrDefault() ?? "配置验证失败");
            }

            _logger.LogDebug("配置对象验证通过");

            // 额外的业务逻辑验证
            ValidateBusinessRules(appSettings);
        }
        catch (ValidationException ex)
        {
            _logger.LogError("配置验证失败: {Message}", ex.Message);
            throw new ConfigurationException($"Configuration validation failed: {ex.Message}", ex);
        }
    }

    /// <summary>
    /// 验证业务规则
    /// </summary>
    /// <param name="appSettings">配置对象</param>
    private void ValidateBusinessRules(AppSettings appSettings)
    {
        _logger.LogDebug("开始验证业务规则");

        // 验证MQTT端口范围
        if (appSettings.Mqtt.BrokerPort <= 0 || appSettings.Mqtt.BrokerPort > 65535)
        {
            var message = $"MQTT端口号无效: {appSettings.Mqtt.BrokerPort}，必须在1-65535范围内";
            _logger.LogError(message);
            throw new ValidationException(message);
        }

        // 验证AI超时时间
        if (appSettings.AI.Timeout <= 0)
        {
            var message = $"AI超时时间无效: {appSettings.AI.Timeout}，必须大于0";
            _logger.LogError(message);
            throw new ValidationException(message);
        }

        // 验证最大并发请求数
        if (appSettings.Processing.MaxConcurrentRequests <= 0)
        {
            var message = $"最大并发请求数无效: {appSettings.Processing.MaxConcurrentRequests}，必须大于0";
            _logger.LogError(message);
            throw new ValidationException(message);
        }

        _logger.LogDebug("业务规则验证通过");
    }
}

/// <summary>
/// 配置异常类
/// </summary>
public class ConfigurationException : Exception
{
    public ConfigurationException(string message) : base(message)
    {
    }

    public ConfigurationException(string message, Exception innerException) : base(message, innerException)
    {
    }
}