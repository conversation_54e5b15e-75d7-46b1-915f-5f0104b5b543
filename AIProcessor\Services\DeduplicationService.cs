using System.Collections.Concurrent;

namespace AIProcessor.Services
{
    /// <summary>
    /// 消息去重服务实现
    /// 使用内存字典存储已处理的消息ID
    /// </summary>
    public class DeduplicationService : IDeduplicationService
    {
        private readonly ConcurrentDictionary<string, byte> _processedMessages;

        public DeduplicationService()
        {
            _processedMessages = new ConcurrentDictionary<string, byte>();
        }

        /// <summary>
        /// 检查消息ID是否为重复消息
        /// </summary>
        /// <param name="messageId">消息ID</param>
        /// <returns>如果是重复消息返回true，否则返回false</returns>
        public bool IsDuplicate(string messageId)
        {
            // 尝试添加消息ID到字典中
            // 如果添加成功（返回true），说明是新消息，方法返回false
            // 如果添加失败（返回false），说明ID已存在，方法返回true
            return !_processedMessages.TryAdd(messageId, 0);
        }
    }
}