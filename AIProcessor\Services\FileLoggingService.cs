using Microsoft.Extensions.Logging;
using System.IO;

namespace AIProcessor.Services;

/// <summary>
/// 文件日志服务，负责配置和管理文件日志记录
/// </summary>
public class FileLoggingService
{
    private readonly string? _logFilePath;
    private readonly ILogger<FileLoggingService> _logger;

    public FileLoggingService(string? logFilePath, ILogger<FileLoggingService> logger)
    {
        _logFilePath = logFilePath;
        _logger = logger;
    }

    /// <summary>
    /// 确保日志目录存在
    /// </summary>
    /// <returns>如果目录创建成功或已存在返回true，否则返回false</returns>
    public bool EnsureLogDirectoryExists()
    {
        if (string.IsNullOrWhiteSpace(_logFilePath))
        {
            _logger.LogWarning("日志文件路径未配置，将仅使用控制台日志");
            return false;
        }

        try
        {
            var directory = Path.GetDirectoryName(_logFilePath);
            if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
            {
                Directory.CreateDirectory(directory);
                _logger.LogInformation("已创建日志目录: {Directory}", directory);
            }
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "创建日志目录失败: {LogFilePath}", _logFilePath);
            return false;
        }
    }

    /// <summary>
    /// 验证日志文件路径的有效性
    /// </summary>
    /// <returns>如果路径有效返回true，否则返回false</returns>
    public bool ValidateLogFilePath()
    {
        if (string.IsNullOrWhiteSpace(_logFilePath))
        {
            return false;
        }

        try
        {
            // 检查路径格式是否有效
            var fullPath = Path.GetFullPath(_logFilePath);
            var directory = Path.GetDirectoryName(fullPath);
            var fileName = Path.GetFileName(fullPath);

            if (string.IsNullOrEmpty(fileName))
            {
                _logger.LogError("日志文件路径无效，缺少文件名: {LogFilePath}", _logFilePath);
                return false;
            }

            // 检查文件名是否包含无效字符
            var invalidChars = Path.GetInvalidFileNameChars();
            if (fileName.IndexOfAny(invalidChars) >= 0)
            {
                _logger.LogError("日志文件名包含无效字符: {FileName}", fileName);
                return false;
            }

            _logger.LogDebug("日志文件路径验证通过: {LogFilePath}", _logFilePath);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "验证日志文件路径时发生错误: {LogFilePath}", _logFilePath);
            return false;
        }
    }

    /// <summary>
    /// 获取日志文件路径
    /// </summary>
    public string? LogFilePath => _logFilePath;

    /// <summary>
    /// 测试日志文件写入权限
    /// </summary>
    /// <returns>如果可以写入返回true，否则返回false</returns>
    public bool TestLogFileWritePermission()
    {
        if (string.IsNullOrWhiteSpace(_logFilePath))
        {
            return false;
        }

        try
        {
            // 确保目录存在
            if (!EnsureLogDirectoryExists())
            {
                return false;
            }

            // 尝试写入测试内容
            var testMessage = $"日志文件写入测试 - {DateTime.Now:yyyy-MM-dd HH:mm:ss}";
            File.AppendAllText(_logFilePath, testMessage + Environment.NewLine);
            
            _logger.LogDebug("日志文件写入权限测试通过: {LogFilePath}", _logFilePath);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "测试日志文件写入权限失败: {LogFilePath}", _logFilePath);
            return false;
        }
    }
}
