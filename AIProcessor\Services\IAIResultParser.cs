using AIProcessor.Models;
using System.Collections.Generic;

namespace AIProcessor.Services
{
    /// <summary>
    /// AI结果解析器接口，用于将AI服务返回的结果解析为标准格式
    /// </summary>
    public interface IAIResultParser
    {
        /// <summary>
        /// 解析AI响应，将其转换为Dictionary<string, bool>格式
        /// </summary>
        /// <param name="aiResponse">AI服务返回的响应</param>
        /// <returns>解析后的结果字典</returns>
        /// <exception cref="ArgumentNullException">当aiResponse为null时抛出</exception>
        /// <exception cref="FormatException">当AI返回的内容格式不符合预期时抛出</exception>
        Dictionary<string, bool> Parse(AIResponse aiResponse);
    }
}