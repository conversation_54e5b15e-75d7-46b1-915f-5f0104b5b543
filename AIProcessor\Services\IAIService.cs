using SixLabors.ImageSharp;
using SixLabors.ImageSharp.PixelFormats;
using AIProcessor.Models;

namespace AIProcessor.Services;

/// <summary>
/// AI服务接口，提供图片分析功能
/// </summary>
public interface IAIService
{
    /// <summary>
    /// 异步分析图片
    /// </summary>
    /// <param name="image">待分析的图片</param>
    /// <param name="prompt">分析提示词</param>
    /// <returns>AI分析响应结果</returns>
    Task<AIResponse> AnalyzeImageAsync(Image<Rgba32> image, string prompt);
}