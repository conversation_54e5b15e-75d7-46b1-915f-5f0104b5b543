using AIProcessor.Validation;

namespace AIProcessor.Services
{
    /// <summary>
    /// 合并服务接口，负责处理相同内容的合并请求
    /// </summary>
    public interface IMergeService
    {
        /// <summary>
        /// 收集并等待合并处理结果
        /// </summary>
        /// <param name="imagePath">图片路径</param>
        /// <param name="coordinates">裁剪坐标</param>
        /// <param name="prompt">提示词</param>
        /// <param name="requestId">请求ID</param>
        /// <returns>分析结果字典</returns>
        Task<Dictionary<string, bool>> CollectAndWaitAsync(
            string imagePath, 
            Coordinates coordinates, 
            string prompt, 
            string requestId);
    }
}
