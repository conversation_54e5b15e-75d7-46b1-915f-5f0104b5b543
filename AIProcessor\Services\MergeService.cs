using System.Collections.Concurrent;
using System.Text.Json;
using System.Text.RegularExpressions;
using Microsoft.Extensions.Logging;
using SixLabors.ImageSharp;
using SixLabors.ImageSharp.PixelFormats;
using AIProcessor.Models;
using AIProcessor.Services;
using AIProcessor.Validation;
using AIProcessor.Processing;

namespace AIProcessor.Services
{
    /// <summary>
    /// 合并服务实现，负责处理相同内容的合并请求
    /// </summary>
    public class MergeService : IMergeService
    {
        private readonly ILogger<MergeService> _logger;
        private readonly AppSettings _appSettings;
        private readonly IImageProcessor _imageProcessor;
        private readonly IAIService _aiService;
        private readonly IAIResultParser _aiResultParser;
        
        /// <summary>
        /// 收集器字典，键为图片路径+坐标的组合
        /// </summary>
        private readonly ConcurrentDictionary<string, TimeWindowCollector> _collectors = new();

        public MergeService(
            ILogger<MergeService> logger,
            ConfigurationService configurationService,
            IImageProcessor imageProcessor,
            IAIService aiService,
            IAIResultParser aiResultParser)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _appSettings = configurationService?.AppSettings ?? throw new ArgumentNullException(nameof(configurationService));
            _imageProcessor = imageProcessor ?? throw new ArgumentNullException(nameof(imageProcessor));
            _aiService = aiService ?? throw new ArgumentNullException(nameof(aiService));
            _aiResultParser = aiResultParser ?? throw new ArgumentNullException(nameof(aiResultParser));
        }

        /// <summary>
        /// 收集并等待合并处理结果
        /// </summary>
        public async Task<Dictionary<string, bool>> CollectAndWaitAsync(
            string imagePath, 
            Coordinates coordinates, 
            string prompt, 
            string requestId)
        {
            var collectorKey = GenerateCollectorKey(imagePath, coordinates);
            
            _logger.LogInformation("开始收集合并请求，RequestId: {RequestId}, Key: {Key}", requestId, collectorKey);

            TimeWindowCollector collector;
            bool shouldCreateNew = false;

            // 获取或创建收集器
            collector = _collectors.GetOrAdd(collectorKey, key => 
            {
                shouldCreateNew = true;
                return CreateNewCollector(imagePath, coordinates);
            });

            // 检查收集器状态并添加请求
            lock (collector.LockObject)
            {
                // 如果收集器正在处理中，创建新的收集器
                if (collector.IsProcessing)
                {
                    _logger.LogInformation("收集器正在处理中，创建新收集器，RequestId: {RequestId}", requestId);
                    collector = CreateNewCollector(imagePath, coordinates);
                    _collectors.TryAdd(collectorKey + "_" + DateTime.UtcNow.Ticks, collector);
                    shouldCreateNew = true;
                }

                // 添加当前请求到收集器
                collector.Prompts.Add(prompt);
                collector.RequestIds.Add(requestId);
                
                _logger.LogInformation("请求已添加到收集器，RequestId: {RequestId}, 当前收集数量: {Count}", 
                    requestId, collector.Prompts.Count);
            }

            // 如果是新创建的收集器，启动定时器
            if (shouldCreateNew)
            {
                StartExpirationTimer(collector, collectorKey);
            }

            try
            {
                // 等待合并结果
                var allResults = await collector.CompletionSource.Task;
                
                // 返回当前请求对应的结果
                if (allResults.TryGetValue(requestId, out var result))
                {
                    _logger.LogInformation("成功获取合并结果，RequestId: {RequestId}", requestId);
                    return result;
                }
                else
                {
                    _logger.LogWarning("未找到请求对应的结果，返回空结果，RequestId: {RequestId}", requestId);
                    return new Dictionary<string, bool>();
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "等待合并结果时发生错误，RequestId: {RequestId}", requestId);
                throw;
            }
        }

        /// <summary>
        /// 生成收集器键
        /// </summary>
        private string GenerateCollectorKey(string imagePath, Coordinates coordinates)
        {
            return $"{imagePath}|{coordinates.X1},{coordinates.Y1},{coordinates.X2},{coordinates.Y2}";
        }

        /// <summary>
        /// 创建新的收集器
        /// </summary>
        private TimeWindowCollector CreateNewCollector(string imagePath, Coordinates coordinates)
        {
            return new TimeWindowCollector
            {
                ImagePath = imagePath,
                Coordinates = coordinates
            };
        }

        /// <summary>
        /// 启动过期定时器
        /// </summary>
        private void StartExpirationTimer(TimeWindowCollector collector, string collectorKey)
        {
            var windowSeconds = _appSettings.Processing.MergeWindowSeconds;

            collector.ExpirationTimer = new Timer(async _ =>
            {
                try
                {
                    await ExecuteMergedRequest(collector, collectorKey);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "执行合并请求时发生错误，Key: {Key}", collectorKey);
                }
            }, null, TimeSpan.FromSeconds(windowSeconds), Timeout.InfiniteTimeSpan);

            _logger.LogInformation("已启动过期定时器，窗口时间: {WindowSeconds}秒, Key: {Key}",
                windowSeconds, collectorKey);
        }

        /// <summary>
        /// 执行合并请求
        /// </summary>
        private async Task ExecuteMergedRequest(TimeWindowCollector collector, string collectorKey)
        {
            List<string> prompts;
            List<string> requestIds;

            // 在锁内设置处理状态并复制数据
            lock (collector.LockObject)
            {
                if (collector.IsProcessing)
                {
                    _logger.LogWarning("收集器已在处理中，跳过执行，Key: {Key}", collectorKey);
                    return;
                }

                collector.IsProcessing = true;
                prompts = new List<string>(collector.Prompts);
                requestIds = new List<string>(collector.RequestIds);
            }

            _logger.LogInformation("开始执行合并请求，Key: {Key}, 请求数量: {Count}",
                collectorKey, requestIds.Count);

            try
            {
                // 合并提示词
                var mergedPrompt = string.Join("\n\n", prompts);
                _logger.LogInformation("提示词合并完成，合并后长度: {Length}", mergedPrompt.Length);

                // 加载和处理图片
                using var originalImage = await Image.LoadAsync<Rgba32>(collector.ImagePath);
                var processedImage = _imageProcessor.ProcessImage(originalImage, collector.Coordinates);

                // 调用AI服务
                var aiResponse = await _aiService.AnalyzeImageAsync(processedImage, mergedPrompt);
                _logger.LogInformation("AI分析完成，Key: {Key}", collectorKey);

                // 解析复合结果
                var allResults = ParseCompositeResults(aiResponse, requestIds, prompts);

                // 设置完成结果
                collector.CompletionSource.SetResult(allResults);
                _logger.LogInformation("合并请求执行成功，Key: {Key}", collectorKey);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "执行合并请求失败，Key: {Key}", collectorKey);

                // 设置异常，通知所有等待的任务
                collector.CompletionSource.SetException(ex);
            }
            finally
            {
                // 清理资源
                try
                {
                    collector.ExpirationTimer?.Dispose();
                    _collectors.TryRemove(collectorKey, out _);
                    _logger.LogInformation("收集器资源已清理，Key: {Key}", collectorKey);
                }
                catch (Exception cleanupEx)
                {
                    _logger.LogWarning(cleanupEx, "清理收集器资源时发生错误，Key: {Key}", collectorKey);
                }
            }
        }

        /// <summary>
        /// 解析复合结果并分发给各个请求
        /// </summary>
        private Dictionary<string, Dictionary<string, bool>> ParseCompositeResults(
            AIResponse aiResponse,
            List<string> requestIds,
            List<string> prompts)
        {
            var results = new Dictionary<string, Dictionary<string, bool>>();

            try
            {
                // 首先尝试解析AI返回的复合结果
                var compositeResult = ParseCompositeAIResponse(aiResponse);

                if (compositeResult != null && compositeResult.Count > 0)
                {
                    // 按顺序分发结果给每个请求
                    for (int i = 0; i < requestIds.Count; i++)
                    {
                        var requestId = requestIds[i];
                        results[requestId] = new Dictionary<string, bool>(compositeResult);
                        _logger.LogInformation("已分发结果给请求，RequestId: {RequestId}, 结果数量: {Count}",
                            requestId, compositeResult.Count);
                    }
                }
                else
                {
                    // 如果解析失败，使用默认解析器处理整个结果
                    _logger.LogWarning("复合结果解析失败，使用默认解析器");
                    var fallbackResult = _aiResultParser.Parse(aiResponse);

                    for (int i = 0; i < requestIds.Count; i++)
                    {
                        var requestId = requestIds[i];
                        results[requestId] = new Dictionary<string, bool>(fallbackResult);
                        _logger.LogInformation("已使用默认解析器分发结果，RequestId: {RequestId}", requestId);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "解析复合结果时发生错误，使用空结果");

                // 如果所有解析都失败，返回空结果
                for (int i = 0; i < requestIds.Count; i++)
                {
                    results[requestIds[i]] = new Dictionary<string, bool>();
                }
            }

            return results;
        }

        /// <summary>
        /// 解析AI返回的复合响应
        /// </summary>
        private Dictionary<string, bool>? ParseCompositeAIResponse(AIResponse aiResponse)
        {
            try
            {
                // 使用现有的AI结果解析器
                return _aiResultParser.Parse(aiResponse);
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "使用默认解析器解析复合响应失败");
                return null;
            }
        }
    }
}
