using System;
using System.Collections.Generic;
using System.Text;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using MQTTnet;
using MQTTnet.Protocol;
using SixLabors.ImageSharp;
using SixLabors.ImageSharp.PixelFormats;
using AIProcessor.Models;
using AIProcessor.Services;
using AIProcessor.Validation;
using AIProcessor.Processing;
using AIProcessor.Abstractions;

namespace AIProcessor.Services;

/// <summary>
/// MQTT后台服务，负责订阅MQTT主题、处理消息并发布结果
/// </summary>
public class MqttHostedService : IHostedService, IDisposable
{
    private readonly ILogger<MqttHostedService> _logger;
    private readonly AppSettings _appSettings;
    private readonly IControlMessageValidator _controlMessageValidator;
    private readonly IImageFileValidator _imageFileValidator;
    private readonly ICoordinateValidator _coordinateValidator;
    private readonly IImageProcessor _imageProcessor;
    private readonly IAIService _aiService;
    private readonly IAIResultParser _aiResultParser;
    private readonly IFileSystem _fileSystem;
    private readonly IDeduplicationService _deduplicationService;
    private readonly IMergeService _mergeService;
    private readonly SemaphoreSlim _semaphore;
    private readonly CancellationTokenSource _stoppingCts = new();
    private IMqttClient? _mqttClient;
    private bool _disposed;

    public MqttHostedService(
        ILogger<MqttHostedService> logger,
        ConfigurationService configurationService,
        IControlMessageValidator controlMessageValidator,
        IImageFileValidator imageFileValidator,
        ICoordinateValidator coordinateValidator,
        IImageProcessor imageProcessor,
        IAIService aiService,
        IAIResultParser aiResultParser,
        IFileSystem fileSystem,
        IDeduplicationService deduplicationService,
        IMergeService mergeService)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _appSettings = configurationService?.AppSettings ?? throw new ArgumentNullException(nameof(configurationService));
        _controlMessageValidator = controlMessageValidator ?? throw new ArgumentNullException(nameof(controlMessageValidator));
        _imageFileValidator = imageFileValidator ?? throw new ArgumentNullException(nameof(imageFileValidator));
        _coordinateValidator = coordinateValidator ?? throw new ArgumentNullException(nameof(coordinateValidator));
        _imageProcessor = imageProcessor ?? throw new ArgumentNullException(nameof(imageProcessor));
        _aiService = aiService ?? throw new ArgumentNullException(nameof(aiService));
        _aiResultParser = aiResultParser ?? throw new ArgumentNullException(nameof(aiResultParser));
        _fileSystem = fileSystem ?? throw new ArgumentNullException(nameof(fileSystem));
        _deduplicationService = deduplicationService ?? throw new ArgumentNullException(nameof(deduplicationService));
        _mergeService = mergeService ?? throw new ArgumentNullException(nameof(mergeService));
        _semaphore = new SemaphoreSlim(_appSettings.Processing.MaxConcurrentRequests, _appSettings.Processing.MaxConcurrentRequests);
    }

    /// <summary>
    /// 启动服务
    /// </summary>
    public async Task StartAsync(CancellationToken cancellationToken)
    {
        _logger.LogInformation("启动MQTT后台服务...");
        
        // 注册应用程序关闭时的处理
        AppDomain.CurrentDomain.ProcessExit += OnProcessExit;

        try
        {
            // 创建MQTT客户端
            var factory = new MqttClientFactory();
            _mqttClient = factory.CreateMqttClient();

            // 配置MQTT客户端选项
            // 给ClientId添加8位随机字符
            string clientId = _appSettings.Mqtt.ClientId + "_" + Guid.NewGuid().ToString("N").Substring(0, 8);
            var options = new MqttClientOptionsBuilder()
                .WithTcpServer(_appSettings.Mqtt.BrokerHost, _appSettings.Mqtt.BrokerPort)
                .WithClientId(clientId)
                .WithCredentials(_appSettings.Mqtt.Username, _appSettings.Mqtt.Password)
                .WithKeepAlivePeriod(TimeSpan.FromSeconds(_appSettings.Mqtt.KeepAliveInterval))
                .WithCleanSession()
                .Build();

            // 设置消息接收处理器
            _mqttClient.ApplicationMessageReceivedAsync += OnMessageReceivedAsync;

            // 连接到MQTT Broker
            await _mqttClient.ConnectAsync(options, cancellationToken);
            _logger.LogInformation("已连接到MQTT Broker: {Host}:{Port}", _appSettings.Mqtt.BrokerHost, _appSettings.Mqtt.BrokerPort);

            // 订阅主题
            var subscribeOptions = new MqttClientSubscribeOptionsBuilder()
                .WithTopicFilter(f => f.WithTopic("ai/+/control").WithQualityOfServiceLevel(MQTTnet.Protocol.MqttQualityOfServiceLevel.AtLeastOnce))
                .Build();

            await _mqttClient.SubscribeAsync(subscribeOptions, cancellationToken);
            _logger.LogInformation("已订阅MQTT主题: ai/+/control");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "启动MQTT服务失败");
            throw;
        }
    }

    /// <summary>
    /// 停止服务
    /// </summary>
    public async Task StopAsync(CancellationToken cancellationToken)
    {
        _logger.LogInformation("正在停止MQTT后台服务...");

        // 触发停止信号
        _stoppingCts.Cancel();

        try
        {
            if (_mqttClient?.IsConnected == true)
            {
                await _mqttClient.DisconnectAsync(cancellationToken: cancellationToken);
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "断开MQTT连接时发生错误");
        }
        finally
        {
            _logger.LogInformation("MQTT后台服务已停止");
        }
    }

    private void OnProcessExit(object? sender, EventArgs e)
    {
        if (_disposed) return;
        
        _logger.LogInformation("应用程序正在退出，正在清理资源...");
        
        try
        {
            // 停止服务
            StopAsync(CancellationToken.None).GetAwaiter().GetResult();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "清理资源时发生错误");
        }
    }

    /// <summary>
    /// 处理接收到的MQTT消息
    /// </summary>
    private async Task OnMessageReceivedAsync(MqttApplicationMessageReceivedEventArgs e)
    {
        // 检查是否正在停止
        if (_stoppingCts.IsCancellationRequested)
        {
            _logger.LogDebug("服务正在停止，忽略新消息");
            return;
        }

        await _semaphore.WaitAsync(_stoppingCts.Token);
        try
        {
            // 再次检查是否正在停止
            if (_stoppingCts.IsCancellationRequested)
            {
                _logger.LogDebug("服务正在停止，取消处理消息");
                return;
            }

            var topic = e.ApplicationMessage.Topic;
            string payload;
            
            try
            {
                payload = Encoding.UTF8.GetString(e.ApplicationMessage.Payload);
                _logger.LogInformation("接收到MQTT消息，主题: {Topic}", topic);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "解码MQTT消息时发生错误");
                return;
            }

            await ProcessMessageAsync(topic, payload);
        }
        catch (OperationCanceledException)
        {
            _logger.LogInformation("消息处理已被取消");
            throw;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "处理MQTT消息时发生错误");
        }
        finally
        {
            _semaphore.Release();
        }
    }

    /// <summary>
    /// 处理消息的完整流水线
    /// </summary>
    private async Task ProcessMessageAsync(string topic, string payload)
    {
        try
        {
            // 0. 检查消息是否为空或空白
            if (string.IsNullOrWhiteSpace(payload))
            {
                _logger.LogWarning("收到空消息，已忽略");
                return;
            }

            // 1. 解析JSON消息
            ControlMessage? controlMessage;
            try
            {
                controlMessage = JsonSerializer.Deserialize<ControlMessage>(payload);
                if (controlMessage == null)
                {
                    _logger.LogWarning("无法将消息反序列化为ControlMessage: {Payload}", payload);
                    await PublishFailureEventAsync(topic, "无法解析控制消息", "");
                    return;
                }
            }
            catch (JsonException jsonEx)
            {
                _logger.LogWarning(jsonEx, "JSON解析错误，收到无效消息: {Payload}", payload);
                await PublishFailureEventAsync(topic, "无效的JSON格式", "");
                return;
            }

            // 2. 检查消息去重
            if (_deduplicationService.IsDuplicate(controlMessage.RequestId!))
            {
                _logger.LogInformation("重复消息被忽略，RequestId: {RequestId}", controlMessage.RequestId);
                return;
            }

            // 3. 验证控制消息
            var validationResult = _controlMessageValidator.Validate(controlMessage);
            if (!validationResult.IsValid)
            {
                var errors = string.Join(", ", validationResult.Errors);
                await PublishFailureEventAsync(topic, $"控制消息验证失败: {errors}", controlMessage.RequestId ?? "");
                return;
            }

            // 4. 验证图片文件
            var imageValidationResult = _imageFileValidator.Validate(controlMessage.ImagePath!);
            if (!imageValidationResult.IsValid)
            {
                var errors = string.Join(", ", imageValidationResult.Errors);
                await PublishFailureEventAsync(topic, $"图片文件验证失败: {errors}", controlMessage.RequestId!);
                return;
            }

            // 5. 验证和解析坐标字符串
            var coordinateValidationResult = _coordinateValidator.Validate(controlMessage.ImageCropCoordinates!);
            if (!coordinateValidationResult.IsValid)
            {
                await PublishFailureEventAsync(topic, $"坐标验证失败: {string.Join(", ", coordinateValidationResult.Errors)}", controlMessage.RequestId!);
                return;
            }

            // 6. 调用合并服务收集并等待结果
            var mergeResult = await _mergeService.CollectAndWaitAsync(
                controlMessage.ImagePath!,
                coordinateValidationResult.Coordinates!,
                controlMessage.Prompt!,
                controlMessage.RequestId!);

            // 7. 发布成功事件
            await PublishSuccessEventAsync(topic, controlMessage, mergeResult);

            _logger.LogInformation("成功处理消息，RequestId: {RequestId}", controlMessage.RequestId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "处理消息流水线时发生错误");
            await PublishFailureEventAsync(topic, $"处理失败: {ex.Message}", "");
        }
    }

    /// <summary>
    /// 发布成功事件消息
    /// </summary>
    private async Task PublishSuccessEventAsync(string originalTopic, ControlMessage controlMessage, Dictionary<string, bool> analysisResults)
    {
        var eventMessage = new EventMessage
        {
            EventId = controlMessage.EventId!,
            RequestId = controlMessage.RequestId!,
            Timestamp = DateTime.UtcNow.ToString("yyyyMMddHHmmssfff"),
            Success = true,
            Result = analysisResults,
            ProcessingTime = 0.0 // MVP版本暂时不支持处理时间
        };

        var eventTopic = originalTopic.Replace("/control", "/event");
        var eventPayload = JsonSerializer.Serialize(eventMessage);

        var message = new MqttApplicationMessageBuilder()
            .WithTopic(eventTopic)
            .WithPayload(eventPayload)
            .WithQualityOfServiceLevel(MQTTnet.Protocol.MqttQualityOfServiceLevel.AtLeastOnce)
            .Build();

        if (_mqttClient?.IsConnected == true)
        {
            await _mqttClient.PublishAsync(message);
            _logger.LogInformation("已发布成功事件到主题: {Topic}", eventTopic);
        }
    }

    /// <summary>
    /// 发布失败事件消息
    /// </summary>
    private async Task PublishFailureEventAsync(string originalTopic, string errorMessage, string requestId)
    {
        var eventMessage = new EventMessage
        {
            EventId = "",
            RequestId = requestId,
            Timestamp = DateTime.UtcNow.ToString("yyyyMMddHHmmssfff"),
            Success = false,
            Result = new Dictionary<string, bool>(),
            ErrorMessage = errorMessage,
            ProcessingTime = 0.0
        };

        var eventTopic = originalTopic.Replace("/control", "/event");
        var eventPayload = JsonSerializer.Serialize(eventMessage);

        var message = new MqttApplicationMessageBuilder()
            .WithTopic(eventTopic)
            .WithPayload(eventPayload)
            .WithQualityOfServiceLevel(MQTTnet.Protocol.MqttQualityOfServiceLevel.AtLeastOnce)
            .Build();

        if (_mqttClient?.IsConnected == true)
        {
            await _mqttClient.PublishAsync(message);
            _logger.LogInformation("已发布失败事件到主题: {Topic}", eventTopic);
        }
    }

    /// <summary>
    /// 释放资源
    /// </summary>
    public void Dispose()
    {
        if (_disposed) return;
        
        try
        {
            _stoppingCts.Cancel();
            _mqttClient?.Dispose();
            _semaphore.Dispose();
            _stoppingCts.Dispose();
            AppDomain.CurrentDomain.ProcessExit -= OnProcessExit;
        }
        finally
        {
            _disposed = true;
        }
    }
}