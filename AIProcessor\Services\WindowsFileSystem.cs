using System;
using System.IO;
using AIProcessor.Abstractions;

namespace AIProcessor.Services
{
    /// <summary>
    /// Windows文件系统的具体实现
    /// </summary>
    public class WindowsFileSystem : IFileSystem
    {
        /// <summary>
        /// 检查指定路径的文件是否存在
        /// </summary>
        /// <param name="path">文件路径</param>
        /// <returns>如果文件存在返回true，否则返回false</returns>
        /// <exception cref="ArgumentNullException">当path为null时抛出</exception>
        public bool FileExists(string path)
        {
            if (path == null)
                throw new ArgumentNullException(nameof(path));

            try
            {
                return File.Exists(path);
            }
            catch (Exception)
            {
                // 如果发生任何异常（如路径格式错误、权限问题等），返回false
                return false;
            }
        }
    }
}