using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Serilog;
using System.IO;
using AIProcessor.Services;
using AIProcessor.Models;

namespace AIProcessor;

/// <summary>
/// 测试配置和日志功能的简单程序
/// </summary>
public class TestConfigAndLogging
{
    public static async Task<int> TestAsync()
    {
        try
        {
            Console.WriteLine("=== AIProcessor 配置和日志系统测试 ===");
            Console.WriteLine($"当前工作目录: {Directory.GetCurrentDirectory()}");

            var configPath1 = "appsettings.json";
            var configPath2 = Path.Combine("AIProcessor", "appsettings.json");
            Console.WriteLine($"配置文件是否存在 (当前目录): {File.Exists(configPath1)}");
            Console.WriteLine($"配置文件是否存在 (AIProcessor目录): {File.Exists(configPath2)}");
            
            // 创建主机构建器
            var hostBuilder = Host.CreateDefaultBuilder()
                .ConfigureAppConfiguration((context, config) =>
                {
                    // 设置基础路径为AIProcessor目录
                    var basePath = Path.Combine(Directory.GetCurrentDirectory(), "AIProcessor");
                    if (!Directory.Exists(basePath))
                    {
                        basePath = Directory.GetCurrentDirectory(); // 如果已经在AIProcessor目录中
                    }
                    config.SetBasePath(basePath);
                    config.AddJsonFile("appsettings.json", optional: false, reloadOnChange: true);
                })
                .ConfigureServices((context, services) =>
                {
                    // 注册配置服务
                    services.AddSingleton<ConfigurationService>();
                    
                    // 注册文件日志服务
                    services.AddSingleton<FileLoggingService>(serviceProvider =>
                    {
                        var configuration = serviceProvider.GetRequiredService<IConfiguration>();
                        var logger = serviceProvider.GetRequiredService<ILogger<FileLoggingService>>();
                        var logFilePath = configuration.GetSection("Logging:LogFilePath").Value;
                        return new FileLoggingService(logFilePath, logger);
                    });
                })
                .UseSerilog((context, services, loggerConfiguration) =>
                {
                    // 从配置中获取日志文件路径
                    var logFilePath = context.Configuration.GetSection("Logging:LogFilePath").Value;
                    
                    loggerConfiguration
                        .Enrich.FromLogContext()
                        .WriteTo.Console();
                    
                    if (!string.IsNullOrWhiteSpace(logFilePath))
                    {
                        // 确保日志目录存在
                        var directory = Path.GetDirectoryName(logFilePath);
                        if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
                        {
                            Directory.CreateDirectory(directory);
                        }
                        
                        // 添加文件日志
                        loggerConfiguration.WriteTo.File(logFilePath,
                            rollingInterval: RollingInterval.Day,
                            retainedFileCountLimit: 30,
                            shared: true,
                            flushToDiskInterval: TimeSpan.FromSeconds(1));
                    }
                });

            // 构建主机
            using var host = hostBuilder.Build();
            
            // 获取服务
            var configService = host.Services.GetRequiredService<ConfigurationService>();
            var fileLoggingService = host.Services.GetRequiredService<FileLoggingService>();
            var logger = host.Services.GetRequiredService<ILogger<TestConfigAndLogging>>();
            
            Console.WriteLine("1. 开始测试配置加载...");

            // 先检查原始配置
            var configuration = host.Services.GetRequiredService<IConfiguration>();
            var mqttSection = configuration.GetSection("Mqtt");
            Console.WriteLine($"   原始配置 - BrokerHost: {mqttSection["BrokerHost"]}");
            Console.WriteLine($"   原始配置 - BrokerPort: {mqttSection["BrokerPort"]}");
            Console.WriteLine($"   原始配置 - ClientId: {mqttSection["ClientId"]}");

            // 测试配置加载
            AppSettings appSettings;
            try
            {
                appSettings = configService.AppSettings;
                Console.WriteLine("✓ 配置加载成功");
                logger.LogInformation("配置加载测试成功");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"✗ 配置加载失败: {ex.Message}");
                logger.LogError(ex, "配置加载测试失败");
                return 1;
            }
            
            Console.WriteLine("2. 验证配置内容...");
            
            // 验证配置内容
            Console.WriteLine($"   MQTT Broker: {appSettings.Mqtt.BrokerHost}:{appSettings.Mqtt.BrokerPort}");
            Console.WriteLine($"   AI Model: {appSettings.AI.ModelName}");
            Console.WriteLine($"   Log File Path: {appSettings.Logging.LogFilePath ?? "未配置"}");
            
            logger.LogInformation("MQTT配置 - Host: {Host}, Port: {Port}", 
                appSettings.Mqtt.BrokerHost, appSettings.Mqtt.BrokerPort);
            logger.LogInformation("AI配置 - Model: {Model}", appSettings.AI.ModelName);
            
            Console.WriteLine("3. 测试日志文件功能...");
            
            // 测试日志文件功能
            if (!string.IsNullOrWhiteSpace(appSettings.Logging.LogFilePath))
            {
                // 验证日志文件路径
                if (fileLoggingService.ValidateLogFilePath())
                {
                    Console.WriteLine("✓ 日志文件路径验证通过");
                    logger.LogInformation("日志文件路径验证通过");
                }
                else
                {
                    Console.WriteLine("✗ 日志文件路径验证失败");
                    logger.LogWarning("日志文件路径验证失败");
                }
                
                // 确保日志目录存在
                if (fileLoggingService.EnsureLogDirectoryExists())
                {
                    Console.WriteLine("✓ 日志目录创建/验证成功");
                    logger.LogInformation("日志目录创建/验证成功");
                }
                else
                {
                    Console.WriteLine("✗ 日志目录创建失败");
                    logger.LogWarning("日志目录创建失败");
                }
                
                // 测试写入权限
                if (fileLoggingService.TestLogFileWritePermission())
                {
                    Console.WriteLine("✓ 日志文件写入权限测试通过");
                    logger.LogInformation("日志文件写入权限测试通过");
                }
                else
                {
                    Console.WriteLine("✗ 日志文件写入权限测试失败");
                    logger.LogWarning("日志文件写入权限测试失败");
                }
                
                Console.WriteLine($"   日志文件路径: {fileLoggingService.LogFilePath}");
            }
            else
            {
                Console.WriteLine("! 未配置日志文件路径，仅使用控制台日志");
                logger.LogWarning("未配置日志文件路径，仅使用控制台日志");
            }
            
            Console.WriteLine("4. 测试各种日志级别...");
            
            // 测试各种日志级别
            logger.LogTrace("这是一条Trace级别的日志");
            logger.LogDebug("这是一条Debug级别的日志");
            logger.LogInformation("这是一条Information级别的日志");
            logger.LogWarning("这是一条Warning级别的日志");
            logger.LogError("这是一条Error级别的日志");
            logger.LogCritical("这是一条Critical级别的日志");
            
            Console.WriteLine("✓ 各种日志级别测试完成");
            
            Console.WriteLine("5. 测试配置验证功能...");
            
            // 测试配置验证（通过访问配置属性触发验证）
            try
            {
                var mqttPort = appSettings.Mqtt.BrokerPort;
                var aiTimeout = appSettings.AI.Timeout;
                var maxConcurrent = appSettings.Processing.MaxConcurrentRequests;
                
                Console.WriteLine($"✓ 配置验证通过 - MQTT端口: {mqttPort}, AI超时: {aiTimeout}s, 最大并发: {maxConcurrent}");
                logger.LogInformation("配置验证通过 - MQTT端口: {MqttPort}, AI超时: {AiTimeout}s, 最大并发: {MaxConcurrent}",
                    mqttPort, aiTimeout, maxConcurrent);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"✗ 配置验证失败: {ex.Message}");
                logger.LogError(ex, "配置验证失败");
                return 1;
            }
            
            // 等待一下确保日志写入
            await Task.Delay(2000);
            
            Console.WriteLine("\n=== 测试完成 ===");
            Console.WriteLine("所有配置和日志功能测试通过！");
            logger.LogInformation("所有配置和日志功能测试通过！");
            
            return 0;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"测试过程中发生未预期的错误: {ex.Message}");
            Console.WriteLine($"详细信息: {ex}");
            return 1;
        }
    }
}
