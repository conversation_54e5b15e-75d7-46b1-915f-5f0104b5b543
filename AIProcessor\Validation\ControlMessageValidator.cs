using AIProcessor.Models;

namespace AIProcessor.Validation;

/// <summary>
/// 控制消息验证器实现
/// </summary>
public class ControlMessageValidator : IControlMessageValidator
{
    /// <summary>
    /// 验证控制消息的有效性
    /// </summary>
    /// <param name="message">要验证的控制消息</param>
    /// <returns>验证结果</returns>
    public ValidationResult Validate(ControlMessage message)
    {
        if (message == null)
        {
            return ValidationResult.Failure(new List<string> { "控制消息不能为空" });
        }

        var errors = new List<string>();

        // 验证所有必填字段
        if (string.IsNullOrWhiteSpace(message.EventId))
        {
            errors.Add("EventId不能为空");
        }

        if (string.IsNullOrWhiteSpace(message.ImagePath))
        {
            errors.Add("ImagePath不能为空");
        }

        if (string.IsNullOrWhiteSpace(message.ImageCropCoordinates))
        {
            errors.Add("ImageCropCoordinates不能为空");
        }

        if (string.IsNullOrWhiteSpace(message.Prompt))
        {
            errors.Add("Prompt不能为空");
        }

        if (string.IsNullOrWhiteSpace(message.Timestamp))
        {
            errors.Add("Timestamp不能为空");
        }

        if (string.IsNullOrWhiteSpace(message.RequestId))
        {
            errors.Add("RequestId不能为空");
        }

        return errors.Count > 0 
            ? ValidationResult.Failure(errors) 
            : ValidationResult.Success();
    }
}