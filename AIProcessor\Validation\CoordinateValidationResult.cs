using System;
using System.Collections.Generic;

namespace AIProcessor.Validation
{
    /// <summary>
    /// 坐标验证结果，扩展基础验证结果以包含解析后的坐标信息
    /// </summary>
    public class CoordinateValidationResult : ValidationResult
    {
        /// <summary>
        /// 解析后的坐标信息，验证失败时为null
        /// </summary>
        public Coordinates? Coordinates { get; set; }

        /// <summary>
        /// 初始化坐标验证结果
        /// </summary>
        /// <param name="isValid">是否验证成功</param>
        /// <param name="errors">错误信息列表</param>
        /// <param name="coordinates">解析后的坐标，可为null</param>
        public CoordinateValidationResult(bool isValid, List<string> errors, Coordinates? coordinates = null)
        {
            IsValid = isValid;
            Errors = errors;
            Coordinates = coordinates;
        }

        /// <summary>
        /// 创建成功的坐标验证结果
        /// </summary>
        /// <param name="coordinates">解析后的坐标</param>
        /// <returns>成功的验证结果</returns>
        public static CoordinateValidationResult Success(Coordinates coordinates)
        {
            return new CoordinateValidationResult(true, new List<string>(), coordinates);
        }

        /// <summary>
        /// 创建失败的坐标验证结果
        /// </summary>
        /// <param name="errors">错误信息列表</param>
        /// <returns>失败的验证结果</returns>
        public static new CoordinateValidationResult Failure(List<string> errors)
        {
            return new CoordinateValidationResult(false, errors, null);
        }

        /// <summary>
        /// 创建失败的坐标验证结果
        /// </summary>
        /// <param name="error">单个错误信息</param>
        /// <returns>失败的验证结果</returns>
        public static CoordinateValidationResult Failure(string error)
        {
            return new CoordinateValidationResult(false, new List<string> { error }, null);
        }
    }
}