using System;
using System.Collections.Generic;
using System.Linq;

namespace AIProcessor.Validation
{
    /// <summary>
    /// 坐标验证器实现类
    /// </summary>
    public class CoordinateValidator : ICoordinateValidator
    {
        /// <summary>
        /// 验证坐标字符串的格式和逻辑有效性
        /// </summary>
        /// <param name="coordinateString">坐标字符串，格式为"x1,y1,x2,y2"</param>
        /// <returns>坐标验证结果，包含是否有效、错误信息和解析后的坐标</returns>
        public CoordinateValidationResult Validate(string coordinateString)
        {
            var errors = new List<string>();

            // 检查空值或空白字符串
            if (string.IsNullOrWhiteSpace(coordinateString))
            {
                errors.Add("坐标字符串不能为空");
                return CoordinateValidationResult.Failure(errors);
            }

            // 检查是否包含逗号分隔符
            if (!coordinateString.Contains(','))
            {
                errors.Add("坐标必须由逗号分隔");
                return CoordinateValidationResult.Failure(errors);
            }

            // 分割字符串并去除空格
            var parts = coordinateString.Split(',').Select(p => p.Trim()).ToArray();

            // 检查是否有4个部分
            if (parts.Length != 4)
            {
                errors.Add("坐标必须包含四个部分");
                return CoordinateValidationResult.Failure(errors);
            }

            // 尝试解析每个部分为整数
            var coordinates = new int[4];
            for (int i = 0; i < 4; i++)
            {
                if (!int.TryParse(parts[i], out coordinates[i]))
                {
                    errors.Add($"坐标值'{parts[i]}'必须是有效的整数");
                    return CoordinateValidationResult.Failure(errors);
                }
            }

            // 检查是否有负数
            if (coordinates.Any(c => c < 0))
            {
                errors.Add("坐标值不能为负数");
                return CoordinateValidationResult.Failure(errors);
            }

            // 创建坐标对象
            var coord = new Coordinates(coordinates[0], coordinates[1], coordinates[2], coordinates[3]);

            // 检查逻辑有效性（特殊坐标(0,0,0,0)除外）
            if (!coord.IsSmartScaling)
            {
                if (coord.X2 <= coord.X1)
                {
                    errors.Add("x2必须大于x1");
                }
                if (coord.Y2 <= coord.Y1)
                {
                    errors.Add("y2必须大于y1");
                }
                
                if (errors.Count > 0)
                {
                    return CoordinateValidationResult.Failure(errors);
                }
            }

            return CoordinateValidationResult.Success(coord);
        }
    }
}