using System;

namespace AIProcessor.Validation
{
    /// <summary>
    /// 表示图片裁剪坐标的不可变记录
    /// </summary>
    /// <param name="X1">左上角X坐标</param>
    /// <param name="Y1">左上角Y坐标</param>
    /// <param name="X2">右下角X坐标</param>
    /// <param name="Y2">右下角Y坐标</param>
    public record Coordinates(int X1, int Y1, int X2, int Y2)
    {
        /// <summary>
        /// 检查是否为特殊的智能缩放坐标 (0,0,0,0)
        /// </summary>
        public bool IsSmartScaling => X1 == 0 && Y1 == 0 && X2 == 0 && Y2 == 0;

        /// <summary>
    /// 检查坐标是否逻辑有效（x2 > x1 且 y2 > y1，或者是智能缩放坐标）
    /// 注意：此属性仅用于快速检查，详细验证逻辑在CoordinateValidator中实现
    /// </summary>
    public bool IsLogicallyValid => IsSmartScaling || (X2 > X1 && Y2 > Y1);

        /// <summary>
        /// 检查所有坐标值是否为非负数
        /// </summary>
        public bool AreAllNonNegative => X1 >= 0 && Y1 >= 0 && X2 >= 0 && Y2 >= 0;
    }
}