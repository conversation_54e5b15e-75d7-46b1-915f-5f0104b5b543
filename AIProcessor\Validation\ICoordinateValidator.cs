using System;

namespace AIProcessor.Validation
{
    /// <summary>
    /// 坐标验证器接口
    /// </summary>
    public interface ICoordinateValidator
    {
        /// <summary>
        /// 验证坐标字符串的格式和逻辑有效性
        /// </summary>
        /// <param name="coordinateString">坐标字符串，格式为"x1,y1,x2,y2"</param>
        /// <returns>坐标验证结果，包含是否有效、错误信息和解析后的坐标</returns>
        CoordinateValidationResult Validate(string coordinateString);
    }
}