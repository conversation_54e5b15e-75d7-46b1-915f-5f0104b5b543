using System;
using System.Collections.Generic;
using AIProcessor.Abstractions;

namespace AIProcessor.Validation
{
    /// <summary>
    /// 图片文件验证器实现类
    /// </summary>
    public class ImageFileValidator : IImageFileValidator
    {
        private readonly IFileSystem _fileSystem;

        /// <summary>
        /// 初始化图片文件验证器
        /// </summary>
        /// <param name="fileSystem">文件系统抽象接口</param>
        /// <exception cref="ArgumentNullException">当fileSystem为null时抛出</exception>
        public ImageFileValidator(IFileSystem fileSystem)
        {
            _fileSystem = fileSystem ?? throw new ArgumentNullException(nameof(fileSystem));
        }

        /// <summary>
        /// 验证指定路径的图片文件是否存在且可访问
        /// </summary>
        /// <param name="filePath">图片文件路径</param>
        /// <returns>验证结果，包含是否有效和错误信息</returns>
        public ValidationResult Validate(string filePath)
        {
            var errors = new List<string>();

            // 检查路径是否为空或空白
            if (string.IsNullOrWhiteSpace(filePath))
            {
                errors.Add("图片路径不能为空");
                return ValidationResult.Failure(errors);
            }

            // 检查文件是否存在
            if (!_fileSystem.FileExists(filePath))
            {
                errors.Add($"图片文件不存在: {filePath}");
                return ValidationResult.Failure(errors);
            }

            return ValidationResult.Success();
        }
    }
}