namespace AIProcessor.Validation;

/// <summary>
/// 表示验证操作的结果
/// </summary>
public class ValidationResult
{
    /// <summary>
    /// 获取或设置验证是否成功
    /// </summary>
    public bool IsValid { get; set; }

    /// <summary>
    /// 获取或设置验证错误列表
    /// </summary>
    public List<string> Errors { get; set; } = new List<string>();

    /// <summary>
    /// 创建一个成功的验证结果
    /// </summary>
    /// <returns>成功的验证结果</returns>
    public static ValidationResult Success()
    {
        return new ValidationResult { IsValid = true };
    }

    /// <summary>
    /// 创建一个失败的验证结果
    /// </summary>
    /// <param name="errors">错误列表</param>
    /// <returns>失败的验证结果</returns>
    public static ValidationResult Failure(List<string> errors)
    {
        return new ValidationResult { IsValid = false, Errors = errors };
    }
}