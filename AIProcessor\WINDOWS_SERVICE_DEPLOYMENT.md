# AIProcessor Windows Service 部署指南

## 概述

AIProcessor现在支持作为Windows Service运行，提供稳定的后台服务能力。本指南详细说明了如何部署、安装和管理AIProcessor Windows Service。

## 前置要求

- Windows 10/11 或 Windows Server 2019/2022
- .NET 8.0 Runtime（如果使用自包含部署则不需要）
- 管理员权限（用于服务安装和管理）
- 已配置的appsettings.json文件

## 部署步骤

### 1. 发布应用程序

在项目根目录下运行以下命令，生成自包含的可执行文件：

```bash
dotnet publish AIProcessor -c Release -r win-x64 --self-contained true
```

这将在 `AIProcessor\bin\Release\net8.0\win-x64\publish\` 目录下生成所有必需的文件，包括：
- `AIProcessor.exe` - 主可执行文件
- 所有依赖的DLL文件
- 运行时文件

### 2. 配置文件准备

确保在发布目录中有正确配置的 `appsettings.json` 文件：

```json
{
  "Mqtt": {
    "BrokerHost": "localhost",
    "BrokerPort": 1883,
    "ClientId": "AIProcessor_Service",
    "Username": "your_username",
    "Password": "your_password"
  },
  "AI": {
    "ApiKey": "your_api_key",
    "ModelName": "qwen-vl-plus-latest",
    "ApiUrl": "https://dashscope.aliyuncs.com/compatible-mode/v1",
    "Timeout": 30
  },
  "Processing": {
    "MaxConcurrentRequests": 100
  },
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft": "Warning"
    }
  }
}
```

## 服务安装和管理

### 方法一：使用PowerShell脚本（推荐）

项目提供了自动化的PowerShell脚本 `install-service.ps1`，支持完整的服务生命周期管理。

#### 交互式使用：
```powershell
# 以管理员权限运行PowerShell
.\install-service.ps1
```

#### 命令行使用：
```powershell
# 安装服务
.\install-service.ps1 install

# 启动服务
.\install-service.ps1 start

# 查询状态
.\install-service.ps1 status

# 停止服务
.\install-service.ps1 stop

# 删除服务
.\install-service.ps1 remove

# 查看事件日志
.\install-service.ps1 logs
```

### 方法二：使用sc.exe命令

#### 创建服务
```cmd
sc.exe create AIProcessor binPath= "C:\path\to\your\publish\AIProcessor.exe" DisplayName= "AI Processor Service" start= auto
```

#### 配置服务描述
```cmd
sc.exe description AIProcessor "AI图片分析处理服务，用于接收MQTT消息并执行AI分析"
```

#### 配置失败自动重启
```cmd
sc.exe failure AIProcessor reset= 86400 actions= restart/5000/restart/10000/restart/30000
```

#### 启动服务
```cmd
sc.exe start AIProcessor
```

#### 查询服务状态
```cmd
sc.exe query AIProcessor
```

#### 停止服务
```cmd
sc.exe stop AIProcessor
```

#### 删除服务
```cmd
sc.exe delete AIProcessor
```

### 方法三：使用Windows服务管理器

1. 按 `Win + R`，输入 `services.msc`
2. 在服务列表中找到 "AI Processor Service"
3. 右键点击服务，选择相应操作（启动、停止、重启等）
4. 双击服务可以配置启动类型、恢复选项等

## 服务验证

### 1. 检查服务状态
```cmd
sc.exe query AIProcessor
```

预期输出应显示服务状态为 `RUNNING`。

### 2. 检查事件日志

在Windows事件查看器中：
1. 打开 "Windows日志" > "应用程序"
2. 查找来源为 "AIProcessor" 的事件
3. 确认服务启动和MQTT连接成功的日志

### 3. 功能测试

通过MQTT客户端发送测试消息到 `ai/{COMM_ID}/{POSITION_ID}/control` 主题，验证服务是否正常处理并返回结果。

## 故障排除

### 服务无法启动

1. **检查可执行文件路径**：确保服务配置中的路径正确且文件存在
2. **检查权限**：确保服务账户有权限访问可执行文件和配置文件
3. **检查配置文件**：验证 `appsettings.json` 格式正确且包含所有必需配置
4. **查看事件日志**：检查Windows事件查看器中的错误信息

### 服务运行但无法连接MQTT

1. **网络连接**：确保服务器可以访问MQTT Broker
2. **防火墙设置**：检查防火墙是否阻止了MQTT端口（通常是1883或8883）
3. **认证信息**：验证MQTT用户名和密码是否正确
4. **Broker状态**：确认MQTT Broker正在运行且可访问

### 服务频繁重启

1. **内存不足**：检查系统内存使用情况
2. **配置错误**：验证AI API配置是否正确
3. **依赖服务**：确保所有依赖的服务（如MQTT Broker）正常运行
4. **日志分析**：查看详细的错误日志确定根本原因

## 服务配置选项

### 启动类型
- **自动**：系统启动时自动启动服务（推荐）
- **手动**：需要手动启动服务
- **禁用**：禁用服务

### 恢复选项
服务已配置为在失败时自动重启：
- 第一次失败：5秒后重启
- 第二次失败：10秒后重启
- 后续失败：30秒后重启
- 重置失败计数：24小时

### 服务账户
默认使用 "本地系统" 账户运行。如需更高安全性，可以：
1. 创建专用的服务账户
2. 授予必要的权限（登录为服务、访问文件等）
3. 在服务属性中配置使用该账户

## 性能监控

### 关键指标
- **CPU使用率**：正常负载下应低于50%
- **内存使用**：应低于1GB
- **网络连接**：MQTT连接状态
- **处理延迟**：AI分析请求的响应时间

### 监控工具
- Windows性能监视器（perfmon）
- 任务管理器
- Windows事件查看器
- 自定义日志文件

## 维护建议

1. **定期检查日志**：监控错误和警告信息
2. **更新配置**：根据需要调整并发数、超时等参数
3. **备份配置**：定期备份 `appsettings.json` 文件
4. **版本更新**：按需更新AIProcessor版本
5. **性能调优**：根据实际负载调整配置参数

## 安全考虑

1. **最小权限原则**：服务账户只授予必要的权限
2. **网络安全**：使用TLS加密MQTT连接（如果支持）
3. **API密钥保护**：确保AI API密钥安全存储
4. **访问控制**：限制对服务文件和配置的访问
5. **日志安全**：避免在日志中记录敏感信息

---

## 快速参考

### 常用命令
```cmd
# 查看服务状态
sc.exe query AIProcessor

# 启动服务
sc.exe start AIProcessor

# 停止服务
sc.exe stop AIProcessor

# 重启服务
sc.exe stop AIProcessor && sc.exe start AIProcessor
```

### 重要文件路径
- 可执行文件：`AIProcessor\bin\Release\net8.0\win-x64\publish\AIProcessor.exe`
- 配置文件：`AIProcessor\bin\Release\net8.0\win-x64\publish\appsettings.json`
- 管理脚本：`install-service.ps1`
- 命令参考：`service-commands.bat`