# AIProcessor Windows Service Installation and Management Script
# Note: This script requires Administrator privileges

# Set variables
$ServiceName = "AIProcessor"
$ExecutablePath = "$PSScriptRoot\AIProcessor.exe"
$ServiceDisplayName = "AI Processor Service"
$ServiceDescription = "AI Image Analysis Processing Service for MQTT message handling"

# Check if running as Administrator
if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-Host "Error: This script requires Administrator privileges" -ForegroundColor Red
    Write-Host "Please right-click PowerShell and select 'Run as Administrator'" -ForegroundColor Yellow
    exit 1
}

# Check if executable exists
if (-not (Test-Path $ExecutablePath)) {
    Write-Host "Error: Executable not found at: $ExecutablePath" -ForegroundColor Red
    Write-Host "" 
    Write-Host "Please ensure you have published the application first:" -ForegroundColor Yellow
    Write-Host "  1. Open PowerShell in the AIProcessor directory" -ForegroundColor Cyan
    Write-Host "  2. Run: dotnet publish -c Release -r win-x64 --self-contained true" -ForegroundColor Cyan
    Write-Host "  3. Verify the executable exists at the expected path" -ForegroundColor Cyan
    Write-Host "" 
    Write-Host "Current script location: $PSScriptRoot" -ForegroundColor Gray
    Write-Host "Expected executable path: $ExecutablePath" -ForegroundColor Gray
    exit 1
}

function Install-AIProcessorService {
    Write-Host "Installing $ServiceDisplayName..." -ForegroundColor Green
    
    # Check if service already exists
    $existingService = Get-Service -Name $ServiceName -ErrorAction SilentlyContinue
    if ($existingService) {
        Write-Host "Service already exists, removing old service..." -ForegroundColor Yellow
        Stop-Service -Name $ServiceName -Force -ErrorAction SilentlyContinue
        sc.exe delete $ServiceName
        Start-Sleep -Seconds 2
    }
    
    # Create new service
    Write-Host "Creating Windows Service with executable: $ExecutablePath" -ForegroundColor Cyan
    $result = sc.exe create $ServiceName binPath= "\"$ExecutablePath\"" DisplayName= "$ServiceDisplayName" start= auto
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✓ Service created successfully" -ForegroundColor Green
        
        # Set service description
        Write-Host "Setting service description..." -ForegroundColor Cyan
        sc.exe description $ServiceName "$ServiceDescription"
        
        # Configure service failure recovery
        Write-Host "Configuring automatic restart on failure..." -ForegroundColor Cyan
        sc.exe failure $ServiceName reset= 86400 actions= restart/5000/restart/10000/restart/30000
        
        Write-Host "✓ Service installation completed successfully!" -ForegroundColor Green
        Write-Host "" 
        Write-Host "Next steps:" -ForegroundColor Yellow
        Write-Host "  - Use '.\install-service.ps1 start' to start the service" -ForegroundColor Cyan
        Write-Host "  - Use '.\install-service.ps1 status' to check service status" -ForegroundColor Cyan
        Write-Host "  - Check Windows Event Viewer for service logs" -ForegroundColor Cyan
    } else {
        Write-Host "✗ Service creation failed!" -ForegroundColor Red
        Write-Host "" 
        Write-Host "Common causes:" -ForegroundColor Yellow
        Write-Host "  - Insufficient administrator privileges" -ForegroundColor Cyan
        Write-Host "  - Service name already exists" -ForegroundColor Cyan
        Write-Host "  - Invalid executable path" -ForegroundColor Cyan
        Write-Host "  - Executable file is locked or in use" -ForegroundColor Cyan
        Write-Host "" 
        Write-Host "Try running 'sc.exe query $ServiceName' to check if service exists" -ForegroundColor Gray
    }
}

function Start-AIProcessorService {
    Write-Host "Starting $ServiceDisplayName..." -ForegroundColor Green
    $result = sc.exe start $ServiceName
    if ($LASTEXITCODE -eq 0) {
        Write-Host "Service started successfully" -ForegroundColor Green
    } else {
        Write-Host "Service start failed" -ForegroundColor Red
    }
}

function Stop-AIProcessorService {
    Write-Host "Stopping $ServiceDisplayName..." -ForegroundColor Yellow
    $result = sc.exe stop $ServiceName
    if ($LASTEXITCODE -eq 0) {
        Write-Host "Service stopped successfully" -ForegroundColor Green
    } else {
        Write-Host "Service stop failed" -ForegroundColor Red
    }
}

function Get-AIProcessorServiceStatus {
    Write-Host "Querying $ServiceDisplayName status..." -ForegroundColor Cyan
    sc.exe query $ServiceName
}

function Remove-AIProcessorService {
    Write-Host "Removing $ServiceDisplayName..." -ForegroundColor Red
    Stop-AIProcessorService
    Start-Sleep -Seconds 2
    $result = sc.exe delete $ServiceName
    if ($LASTEXITCODE -eq 0) {
        Write-Host "Service removed successfully" -ForegroundColor Green
    } else {
        Write-Host "Service removal failed" -ForegroundColor Red
    }
}

# Main menu
function Show-Menu {
    Clear-Host
    Write-Host "=== AIProcessor Windows Service Management Tool ===" -ForegroundColor Cyan
    Write-Host "1. Install Service"
    Write-Host "2. Start Service"
    Write-Host "3. Stop Service"
    Write-Host "4. Query Service Status"
    Write-Host "5. Remove Service"
    Write-Host "6. View Event Logs"
    Write-Host "0. Exit"
    Write-Host "================================================" -ForegroundColor Cyan
}

function Show-EventLogs {
    Write-Host "Viewing recent application event logs..." -ForegroundColor Cyan
    Get-EventLog -LogName Application -Source "AIProcessor" -Newest 10 -ErrorAction SilentlyContinue | Format-Table TimeGenerated, EntryType, Message -Wrap
}

# If arguments provided, execute corresponding operation directly
if ($args.Count -gt 0) {
    switch ($args[0].ToLower()) {
        "install" { Install-AIProcessorService }
        "start" { Start-AIProcessorService }
        "stop" { Stop-AIProcessorService }
        "status" { Get-AIProcessorServiceStatus }
        "remove" { Remove-AIProcessorService }
        "logs" { Show-EventLogs }
        default {
            Write-Host "Usage: .\install-service.ps1 [install|start|stop|status|remove|logs]" -ForegroundColor Yellow
        }
    }
    exit
}

# Interactive menu
$choice = ""
while ($choice -ne "0") {
    Show-Menu
    $choice = Read-Host "Please select an operation (0-6)"
    
    switch ($choice) {
        "1" { Install-AIProcessorService; Read-Host "Press any key to continue" }
        "2" { Start-AIProcessorService; Read-Host "Press any key to continue" }
        "3" { Stop-AIProcessorService; Read-Host "Press any key to continue" }
        "4" { Get-AIProcessorServiceStatus; Read-Host "Press any key to continue" }
        "5" { Remove-AIProcessorService; Read-Host "Press any key to continue" }
        "6" { Show-EventLogs; Read-Host "Press any key to continue" }
        "0" { Write-Host "Exiting..." -ForegroundColor Green }
        default { Write-Host "Invalid selection, please try again" -ForegroundColor Red; Start-Sleep -Seconds 1 }
    }
}