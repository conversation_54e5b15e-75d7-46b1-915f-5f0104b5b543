# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## 项目概述

这是一个基于AI的智能事件处理系统（Event Processor EP_V3），专为物联网环境设计。系统通过MQTT消息监听、AI图像分析和智能告警机制，为智慧社区、工业监控等场景提供实时的异常检测和告警服务。

### EP_V3 统一事件处理架构特性

**三种事件模式**：
- 🤖 **纯AI模式**: 有AI提示词 + 简化排除匹配（如EV001008, EV001009）
- 📊 **纯业务逻辑模式**: 无AI提示词 + 简化排除匹配 + 业务逻辑判断（如EV001003, EV001005）
- ⚙️ **混合模式**: 有AI提示词 + 业务逻辑判断 + 排除匹配（预留扩展）

### V3配置示例
```ini
# V3简化配置示例
EVENT_ID_EV001008_EP_START_DEVICE_EVENT_TOPIC=["device/BDN8888/event"]
EVENT_ID_EV001008_EP_MQTT_EXCLUDE_INFO_SOURCE=ajb/101013/log
EVENT_ID_EV001008_EP_MQTT_EXCLUDE_INFO_FIELD=log_car_no
EVENT_ID_EV001008_EP_MQTT_EXCLUDE_LOGIC=exists
EVENT_ID_EV001008_EP_PV_ALARM_TOPIC=hq/101013/P001LfyBmIn/event

# V3业务逻辑事件示例  
EVENT_ID_EV001003_EP_BusinessLogic_JUDGE_SOURCE=ajb/101013/log
EVENT_ID_EV001003_EP_BusinessLogic_JUDGE_FIELD=CardType
EVENT_ID_EV001003_EP_BusinessLogic_JUDGE_LOGIC=contains
EVENT_ID_EV001003_EP_BusinessLogic_JUDGE_VALUE=月租卡|万全卡|贵宾卡
```

## 英语提示词教学
如果我输入的提示词内容有英语, 请帮我用更正确的英语教我提高英语能力.

## 常用开发命令

### 启动和运行（V3统一配置架构）
```bash
# 标准启动
python3 main.py --env .env

# 调试模式启动
python3 main.py --env .env --debug

# 配置测试模式（仅验证配置，不启动服务）
python3 main.py --env .env -t

# 配置测试模式（带调试信息）
python3 main.py --env .env -t --debug

# 使用V3统一配置示例
python3 main.py --env .env.unified.example -t

# 指定日志文件名
python3 main.py --env .env -l log_101013_P001LfyBmIn_EV001001.log

# 综合使用（指定日志文件 + 调试模式）
python3 main.py --env .env --debug -l log_101013_P001LfyBmIn_EV001001.log

```
### 配置验证和测试

#### 配置测试模式（-t参数）
使用 `-t` 参数进行全面的配置验证，不启动实际服务：

```bash
# V3统一配置测试
python3 main.py --env .env -t
```

**V3配置测试功能：**
- 📁 **文件检查**: 验证.env文件存在性和可读性，检查V3统一配置格式
- ⚙️  **配置解析**: 测试配置管理器初始化和配置加载
- 🌍 **全局配置**: 验证COMM_ID、POSITION_ID、EVENT_IDS、MQTT等全局配置
- 🎯 **事件配置**: 逐个检查每个事件的必需和可选配置项
- 🛡️  **排除匹配**: 验证排除匹配配置的完整性和格式正确性
- 🔍 **事件ID一致性**: 检查EVENT_IDS与实际EVENT_ID_EVxxxxxx_EP_*配置的一致性
- 🤖 **AI提示词**: 检查.env文件中每个事件的AI提示词配置（EVENT_ID_*_EP_AI_PROMPT）

**输出示例：**
```
================================================================================
Event Processor 配置测试报告
================================================================================
环境变量文件: .env
事件配置文件: events.ini
调试模式: 禁用
================================================================================

📁 阶段1: 配置文件基本检查
----------------------------------------
✅ 配置文件存在性检查通过

⚙️  阶段2: 配置管理器初始化
----------------------------------------
✅ 配置管理器初始化成功

📋 阶段3: 配置加载和解析
----------------------------------------
✅ 配置加载成功，发现 1 个事件配置

🌍 阶段4: 全局配置验证
----------------------------------------
✅ 小区编号: 101013
✅ 点位编号: P001LfyBmIn
✅ 事件ID列表: ['EV001008']
✅ MQTT服务器配置: 主机=mq.bangdouni.com, 端口=1883

🎯 阶段5: 事件配置详细验证 (1个事件)
----------------------------------------

🔍 检查事件: EV001008
   ✅ EP_START_DEVICE_EVENT_TOPIC: ['device/BDN8888/event']
   ✅ EP_START_FIELD_FROM_DEVICE_EVENT: I1
   ✅ EP_POSITION_START_VALUE_FROM_DEVICE_EVENT: {'true': '0', 'false': '1'}
   ✅ EP_PV_HOLDING_TIMEOUT: 15秒
   ✅ EP_EV_PRIORITY: P3
   ✅ EP_PV_IMAGE_CROP_COORDINATES: (751, 652, 2533, 1343)
   ✅ EP_PV_ALARM_TOPIC: hq/101013/in/P001LfyBmIn/event
   ✅ EP_PV_DETAIL_INFO_TOPIC: ajb/101013/in/P001LfyBmIn/time_log
   ✅ EP_PV_DETAIL_INFO_FIELDS: 3个字段配置
   🛡️  排除匹配配置:
      ✅ EP_PV_EXCLUDE_INFO_SOURCE_TYPE: MQTT
      ✅ EP_PV_EXCLUDE_INFO_SOURCE: ajb/101013/in/P001LfyBmIn/time_log
      ✅ EP_PV_EXCLUDE_INFO_FIELDS: 1个排除字段
         - 字段1: 车牌 (exists)
      ✅ EP_PV_EXCLUDE_LOGIC: ANY
   ✅ 事件 EV001008 配置完整且正确

🔍 阶段6: 事件ID一致性检查
----------------------------------------
📋 EVENT_IDS中声明的事件: ['EV001008']
🔍 环境变量中检测到的事件: ['EV001008']
✅ 配置一致的事件 (1个): ['EV001008']
✅ 所有事件ID配置一致性检查通过

🤖 阶段7: AI提示词配置验证
----------------------------------------
✅ 事件 EV001008 AI提示词: 已配置 (156字符)

================================================================================
📊 配置测试总结报告
================================================================================
🎉 恭喜！配置完全正确，所有检查均通过
✅ 发现 1 个事件，所有配置项验证成功
✅ 系统可以正常启动运行
================================================================================
```

🔍 阶段6: 事件ID一致性检查
----------------------------------------
📋 EVENT_IDS中声明的事件: ['EV001008']
🔍 环境变量中检测到的事件: ['EV001008', 'EV001009']
⚠️  发现孤立的事件配置 (1个):
   • EV001009: 已配置环境变量但未添加到EVENT_IDS中
     配置项: START_DEVICE_EVENT_TOPIC, START_FIELD_FROM_DEVICE_EVENT, POSITION_START_VALUE_FROM_DEVICE_EVENT...
✅ 配置一致的事件 (1个): ['EV001008']

📊 配置测试总结报告
================================================================================
⚠️  发现 1 个配置问题需要修复

🔍 事件ID一致性问题 (1个):
   • 事件EV001009未添加到EVENT_IDS

💡 修复建议:
   1. 检查 .env 文件中的环境变量配置
   2. 将遗漏的事件ID添加到EVENT_IDS列表中: EVENT_IDS=["EV001008", "EV001009"]
```

### V3测试命令 ✅ **测试套件完成**
```bash
# V3架构测试套件
python3 tests/test_v3_config_manager.py      # V3配置管理器测试
python3 tests/test_v3_multi_event_processor.py  # V3多事件处理器测试
python3 tests/test_v3_integration.py        # V3集成测试

# 设备控制器测试
python3 tests/test_device_controller.py     # 设备控制器单元测试

# 运行所有V3测试
python3 -m pytest tests/test_v3_*.py -v

# 传统测试（如果仍然存在）
python3 tests/test_integration.py           # 如果存在
python3 tests/test_business_scenarios.py    # 如果存在
python3 tests/test_real_functionality.py    # 如果存在
```

### 依赖管理
```bash
# 安装项目依赖
pip install -r requirements.txt

# 检查Python代码格式（可选）
# pylint src/
# black src/
```

## 核心架构

### V3简化分层设计
- **core/**: 核心业务层，包含主要处理逻辑（V3简化架构）
- **utils/**: 工具层，提供图片处理、时间格式化等通用功能
- **tests/**: V3测试套件，包含完整的V3架构测试覆盖

### V3关键组件理解

#### MultiEventProcessor (`src/core/multi_event_processor.py`) ✅ **V3迁移完成**
- 系统的核心调度器，管理所有事件的生命周期
- **V3简化状态机**：IDLE → COLLECTING → WAITING_AI_RESULT/ALARM_READY → EXCLUDED
- 集成了V2所有组件功能：排除匹配、业务逻辑处理、MQTT AI控制
- 支持MQTT外部AI服务集成

#### ConfigManager (`src/core/config_manager.py`) ✅ **V3迁移完成**
- V3配置管理器，支持简化参数格式
- 业务逻辑参数格式：`EP_BusinessLogic_*`
- 简化排除匹配：`EP_MQTT_EXCLUDE_*`
- 统一图片配置：移除per-event重复配置

#### ~~V2组件已移除~~ ❌ **清理完成**
- **~~ProgressiveEventManager~~**: 功能已集成到MultiEventProcessor
- **~~EventAggregationCoordinator~~**: 功能已集成到MultiEventProcessor
- **~~ExclusionMatcher~~**: 功能已集成到MultiEventProcessor，支持简化配置
- 排除匹配仍支持exists、equals、contains、regex四种条件
- 排除匹配仍支持多关键字匹配：field_keyword和exclude_value支持用`|`分隔的多值匹配

### V3统一配置系统 ✅ **已完成**
- **统一配置文件**: `.env` 文件管理全局、事件特定配置和AI提示词
- **简化架构**: 移除`events.ini`文件依赖，实现单文件配置管理
- **AI提示词集成**: 使用`EVENT_ID_{event_id}_EP_AI_PROMPT`参数格式

### V3简化状态管理 ✅ **迁移完成**
使用EventState枚举管理事件生命周期（V3简化版本）：
- `IDLE`: 空闲等待触发
- `COLLECTING`: 信息收集期（替代V2的多阶段收集）
- `WAITING_AI_RESULT`: 等待AI分析结果（MQTT外部AI服务）
- `ALARM_READY`: 告警就绪（AI分析完成或业务逻辑匹配）
- `EXCLUDED`: 已被排除（排除匹配生效）

**V2复杂状态已移除**: ~~STAGE1_COLLECTING~~, ~~STAGE2_COLLECTING~~, ~~AGGREGATION_WAITING~~, ~~AI_ANALYZING~~

## 开发约定

### 代码风格
- 小步快跑、快速迭代
- 在需求范围内开发为主, 现阶段不考虑复杂架构（如多层级抽象、冗余扩展接口、未来可能用不上的功能），也不用纠结 “完美代码”—— 能跑通核心流程即可，后续迭代再优化。
- Follow SOLID, DRY & SRP. Always reply in Chinese.
- 使用中文注释和文档字符串
- 遵循PEP 8代码规范
- 类名使用PascalCase，函数名使用snake_case
- 常量使用UPPER_CASE

### 配置参数命名
- 全局参数：`EP_` 前缀
- 事件特定参数：`EVENT_ID_{event_id}_EP_` 前缀

### V3简化排除匹配配置规范 ✅ **V3格式**

#### V3简化格式
排除匹配已简化为单字段格式，支持多值匹配：

```ini
# V3简化配置（推荐）
EVENT_ID_{event_id}_EP_MQTT_EXCLUDE_INFO_SOURCE=topic/path
EVENT_ID_{event_id}_EP_MQTT_EXCLUDE_INFO_FIELD=field_name
EVENT_ID_{event_id}_EP_MQTT_EXCLUDE_LOGIC=exists|equals|contains|regex

# V2复杂格式（仍支持向后兼容）
EVENT_ID_{event_id}_EP_PV_EXCLUDE_INFO_SOURCE_TYPE=MQTT
EVENT_ID_{event_id}_EP_PV_EXCLUDE_INFO_SOURCE=topic/path
EVENT_ID_{event_id}_EP_PV_EXCLUDE_INFO_FIELDS=[字段配置JSON]
EVENT_ID_{event_id}_EP_PV_EXCLUDE_LOGIC=ANY
```

#### 多关键字配置方式

**方式1: 多字段名匹配**
```json
[{
  "field_name": "多卡类型检查",
  "field_keyword": "CardType|VipCard|MonthlyCard",
  "exclude_condition": "exists"
}]
```
- 检查消息中是否存在 `CardType` 或 `VipCard` 或 `MonthlyCard` 字段
- 任何一个字段存在即触发排除

**方式2: 多值内容匹配（推荐）**
```json
[{
  "field_name": "卡类型",
  "field_keyword": "CardType",
  "exclude_condition": "contains",
  "exclude_value": "月租卡|万全卡|贵宾卡|储值卡"
}]
```
- 检查 `CardType` 字段值是否包含任一指定卡类型
- 如果包含任一卡类型，则触发排除

**方式3: 多值精确匹配**
```json
[{
  "field_name": "车辆类型",
  "field_keyword": "VehicleType",
  "exclude_condition": "equals",
  "exclude_value": "消防车|救护车|警车|工程车"
}]
```
- 检查 `VehicleType` 字段值是否等于任一指定车辆类型
- 精确匹配任一类型即触发排除

**方式4: 多正则表达式匹配**
```json
[{
  "field_name": "车牌格式",
  "field_keyword": "PlateNumber",
  "exclude_condition": "regex",
  "exclude_value": "^消[A-Z]|^急[A-Z]|^警[A-Z]"
}]
```
- 使用多个正则表达式检查车牌格式
- 匹配任一正则表达式即触发排除

#### 配置示例场景

**场景1: 停车场卡类型排除**
```ini
# 排除有效卡用户的违规停车事件
EVENT_ID_EV001009_EP_PV_EXCLUDE_INFO_SOURCE_TYPE=MQTT
EVENT_ID_EV001009_EP_PV_EXCLUDE_INFO_SOURCE=parking/card/recognition
EVENT_ID_EV001009_EP_PV_EXCLUDE_INFO_FIELDS=[{"field_name": "卡类型", "field_keyword": "CardType", "exclude_condition": "contains", "exclude_value": "月租卡|万全卡|贵宾卡|储值卡"}]
EVENT_ID_EV001009_EP_PV_EXCLUDE_LOGIC=ANY
```

**测试消息示例：**
```json
{"CardType": "月租卡", "UserID": "12345"}  // ✅ 匹配，事件被排除
{"CardType": "万全卡", "UserID": "67890"}  // ✅ 匹配，事件被排除  
{"CardType": "临时卡", "UserID": "11111"}  // ❌ 不匹配，事件不被排除
```

**场景2: 特种车辆类型排除**
```ini
# 排除特种车辆的违规行为检测
EVENT_ID_EV001010_EP_PV_EXCLUDE_INFO_SOURCE_TYPE=MQTT
EVENT_ID_EV001010_EP_PV_EXCLUDE_INFO_SOURCE=vehicle/type/detection
EVENT_ID_EV001010_EP_PV_EXCLUDE_INFO_FIELDS=[{"field_name": "车辆类型", "field_keyword": "VehicleType", "exclude_condition": "equals", "exclude_value": "消防车|救护车|警车|市政车"}]
EVENT_ID_EV001010_EP_PV_EXCLUDE_LOGIC=ANY
```

**测试消息示例：**
```json
{"VehicleType": "消防车", "PlateNumber": "消A12345"}  // ✅ 匹配，事件被排除
{"VehicleType": "救护车", "PlateNumber": "急B67890"}  // ✅ 匹配，事件被排除
{"VehicleType": "私家车", "PlateNumber": "粤A11111"}  // ❌ 不匹配，事件不被排除
```

#### 配置验证工具

使用配置测试模式验证多关键字配置：
```bash
# 测试排除配置
python3 main.py --env .env --events-ini events.ini -t
```

系统会检查并报告：
- ✅ 多关键字配置格式是否正确
- ✅ JSON解析是否成功
- ✅ 字段配置是否完整
- ⚠️ 发现的配置问题和修复建议

#### 禁用排除匹配配置

如果某个事件不需要排除匹配功能，可以使用以下方式禁用：

**方式1: 省略排除配置（推荐）**
```ini
# EV001002 不需要排除匹配，完全省略排除配置参数
# EVENT_ID_EV001002_EP_PV_EXCLUDE_INFO_SOURCE_TYPE=
# EVENT_ID_EV001002_EP_PV_EXCLUDE_INFO_SOURCE=
# EVENT_ID_EV001002_EP_PV_EXCLUDE_INFO_FIELDS=
# EVENT_ID_EV001002_EP_PV_EXCLUDE_LOGIC=
```

**方式2: 明确设置为None值**
```ini
# EV001002 排除匹配配置明确禁用
EVENT_ID_EV001002_EP_PV_EXCLUDE_INFO_SOURCE_TYPE=None
EVENT_ID_EV001002_EP_PV_EXCLUDE_INFO_SOURCE=None
EVENT_ID_EV001002_EP_PV_EXCLUDE_INFO_FIELDS=None
EVENT_ID_EV001002_EP_PV_EXCLUDE_LOGIC=None
```

**验证输出示例：**
```
[EXCLUDE]  排除匹配配置: 已禁用 (所有配置设置为None)
```

**工作原理：**
- 省略配置：系统自动跳过排除匹配检查
- None值配置：系统识别None值并禁用排除匹配
- 两种方式效果相同：事件直接进入AI分析或业务逻辑处理，不进行排除匹配

### 日志记录
- 使用Python标准logging模块
- 日志文件：`event_processor_v3.log`
- 重要状态变更必须记录日志
- 错误和异常信息详细记录

### V3功能开发原则 ✅ **架构迁移完成**
- **简化集成设计**：V3移除复杂组件分层，功能直接集成到core层
- **统一事件处理**：AI事件和业务逻辑事件使用相同的处理流程
- **MQTT外部AI**：AI分析通过MQTT外部服务实现，降低系统复杂度
- **向后兼容**：支持V2配置格式的向后兼容，平滑迁移

## MQTT业务绑定设备控制系统

### 系统概述
MQTT业务绑定设备控制系统是一个独立的模块，用于监听MQTT业务消息，根据配置的条件自动触发设备控制操作。系统支持多种配置方式和灵活的触发条件设置。

### 核心功能
- **业务消息监听**: 订阅指定的MQTT主题，监听业务相关消息
- **条件匹配**: 检查消息中的特定字段是否满足预设条件
- **设备控制**: 条件满足时自动向设备控制主题发送控制指令
- **多主题支持**: 支持监听多个业务主题，控制多个设备主题
- **实时日志**: 提供详细的操作日志，便于调试和监控

### 配置参数说明

#### 配置文件格式
系统使用INI格式的配置文件，包含两个主要段落：

```ini
[mqtt]
# MQTT服务器连接配置
host = mq.example.com
port = 1883
username = your_username
password = your_password
comm_id = DeviceController
position_id = DC001

[device_control]
# 业务监听主题配置
business_topics = ["business/events", "system/alerts"]
business_keyword = event_type
trigger_value = EV001008
control_topics = ["device/BDN8888/control", "device/secondary/control"]
control_payload = {"simple_command": "485station01O7"}
```

#### 配置参数详解

**[mqtt] 段配置项**
- `host`: MQTT服务器地址
- `port`: MQTT服务器端口 (默认1883)
- `username`: MQTT用户名 (可选)
- `password`: MQTT密码 (可选)
- `comm_id`: 小区ID标识
- `position_id`: 位置ID标识

**[device_control] 段配置项**
- `business_topics`: 业务监听主题，支持JSON数组或逗号分隔格式
- `business_keyword`: 业务消息中的关键字段名称
- `trigger_value`: 触发设备控制的关键字值
- `control_topics`: 设备控制主题，支持JSON数组或逗号分隔格式
- `control_payload`: 设备控制消息内容，JSON格式

**主题配置格式支持**
- 单个主题：`business/events`
- JSON数组：`["topic1", "topic2", "topic3"]`
- 逗号分隔：`topic1, topic2, topic3`

### 使用示例

#### 场景1: 智能门禁控制
当接收到特定事件类型时，自动开启门禁：

**配置文件 (smart_door_control.ini):**
```ini
[mqtt]
host = localhost
port = 1883
username = door_controller
password = secure_password
comm_id = SmartDoor
position_id = Entrance01

[device_control]
business_topics = ["access/events", "security/authorization"]
business_keyword = event_type
trigger_value = AUTHORIZED_ENTRY
control_topics = ["door/main/control"]
control_payload = {"action": "unlock", "duration": 5, "door_id": "main_entrance"}
```

**启动命令:**
```bash
python3 device_control_main.py --config smart_door_control.ini
```

#### 场景2: 停车场道闸控制
根据车牌识别结果控制道闸：

**配置文件 (parking_barrier_control.ini):**
```ini
[mqtt]
host = mq.parking.com
port = 1883
username = barrier_controller
password = barrier_pass_2024
comm_id = ParkingSystem
position_id = Entrance_A01

[device_control]
business_topics = ["parking/plate/recognition", "vehicle/detection"]
business_keyword = recognition_result
trigger_value = VALID_PLATE
control_topics = ["barrier/entrance/control"]
control_payload = {"command": "open_barrier", "lane": "A01", "duration": 10}
```

**启动命令:**
```bash
python3 device_control_main.py --config parking_barrier_control.ini
```

#### 场景3: 多设备联动控制
一个触发条件控制多个设备：

**配置文件 (emergency_multi_device.ini):**
```ini
[mqtt]
host = emergency.control.local
port = 1883
username = emergency_controller
password = emergency_2024
comm_id = EmergencySystem
position_id = Building_Main

[device_control]
business_topics = ["emergency/alerts", "fire/detection", "security/panic"]
business_keyword = alert_level
trigger_value = CRITICAL
control_topics = [
    "alarm/audio/control",
    "lights/emergency/control", 
    "notification/broadcast/control",
    "doors/emergency/control",
    "elevator/emergency/control"
]
control_payload = {
    "emergency_mode": "activate",
    "priority": "critical",
    "timestamp": "auto",
    "building_zone": "all"
}
```

**启动命令:**
```bash
python3 device_control_main.py --config emergency_multi_device.ini
```

### 工作流程
1. **初始化**: 解析配置参数，验证必需字段
2. **MQTT连接**: 建立到MQTT服务器的连接
3. **主题订阅**: 订阅所有配置的业务主题
4. **消息监听**: 持续监听接收到的MQTT消息
5. **条件检查**: 检查消息中的关键字段值
6. **设备控制**: 条件满足时发送控制指令到所有目标主题
7. **日志记录**: 记录完整的操作过程和结果

### 状态监控
系统提供详细的状态信息：
- **连接状态**: MQTT连接是否正常
- **运行状态**: 系统是否在正常运行
- **配置信息**: 当前生效的配置参数
- **消息统计**: 接收和发送的消息计数

### 测试和调试

#### 配置测试
```bash
# 验证配置正确性
python3 device_control_main.py --config device_control.ini -t

# 测试示例配置
python3 device_control_main.py --config examples/smart_door_control.ini -t
python3 device_control_main.py --config examples/parking_barrier_control.ini -t
python3 device_control_main.py --config examples/emergency_multi_device.ini -t
```

#### 调试模式
```bash
# 启用详细日志输出
python3 device_control_main.py --config device_control.ini --debug

# 调试示例配置
python3 device_control_main.py --config examples/smart_door_control.ini --debug
```

#### 单元测试
```bash
# 运行设备控制器测试
python3 -m pytest tests/test_device_controller.py -v
```

### 错误处理和重连机制
- **MQTT重连**: 自动检测连接断开并重新连接
- **消息重发**: QoS=1确保控制消息送达
- **异常捕获**: 全面的异常处理和错误日志
- **优雅关闭**: 支持信号量控制的优雅关闭

### 安全考虑
- **认证支持**: 支持MQTT用户名/密码认证
- **客户端ID**: 自动生成唯一的客户端ID避免冲突
- **配置验证**: 严格的配置参数验证
- **权限控制**: 建议配置适当的MQTT主题权限

## 技术栈详情

### 核心依赖
- `paho-mqtt>=1.6.0`: MQTT客户端小区
- `requests>=2.25.0`: HTTP请求处理
- `python-dotenv>=0.19.0`: 环境变量管理
- `Pillow>=8.0.0`: 图片处理
- `configparser>=5.0.0`: 配置文件解析

### AI集成
- 结果格式验证

### 外部服务依赖
- MQTT Broker: 设备消息小区
- FTP服务器: 图片存储和CDN分发
- AI API服务: 图像分析

## V3常见开发任务 ✅ **适用于V3架构**

### 添加新事件类型（V3统一配置）
1. 在`.env`文件中添加事件配置参数（支持V3简化格式）
2. 在`.env`文件中添加AI提示词：`EVENT_ID_{event_id}_EP_AI_PROMPT=提示词内容`（AI事件）或`=None`（业务逻辑事件）
3. V3架构中，所有逻辑已集成到`MultiEventProcessor`，无需扩展额外组件
4. 添加对应的V3测试用例到`tests/test_v3_*.py`

### 扩展排除条件
1. 在`MultiEventProcessor._check_event_exclusion`中添加新的条件类型
2. 更新`ConfigManager`中的配置验证逻辑
3. 添加相应的V3测试用例

### V3业务逻辑事件配置
1. 设置`EP_BusinessLogic_JUDGE_*`参数
2. 在`.env`文件中设置`EVENT_ID_{event_id}_EP_AI_PROMPT=None`
3. 在`MultiEventProcessor._check_business_logic_match`中扩展匹配逻辑

### 优化AI分析
1. 优化提示词模板
2. 改进结果解析和验证

### 添加新的告警渠道
1. 扩展`AlarmNotifier`添加新通知方式
2. 更新配置系统支持新渠道参数
3. 实现相应的消息格式转换

## 注意事项
- 所有异步操作需要适当的错误处理
- MQTT连接需要重连机制
- MQTT Client ID必须带随机字符, 相同的id连接同一个服务器会引起错误.
- AI调用需要成本优化考虑(已有的优化: EP_PV_AI_DETECT_TIME_BEFORE_ALARM过滤短时间触发即解除的事件, 如正常通行)

## 调试约束
- **进程管理约束**: 不能用 `taskkill /F /IM python.exe` 结束当前进程，因为系统中有别的python进程。需要结束进程时，在代码中增加pid的日志输出再精准的结束单一进程。
- **推荐工具**: 使用 `utils/process_manager.py` 进行精准的进程管理：
  ```bash
  # 列出所有Event Processor进程
  python utils/process_manager.py --list
  
  # 停止Event Processor进程
  python utils/process_manager.py --stop
  
  # 强制终止指定PID的进程
  python utils/process_manager.py --kill <PID> --force
  ```

## EP_V3 约定缩略语
- ep=event processor
- ajb=parking system (car plate recognition)
- car=vehicle
- carno=car number or car plate number
- bl=business logic (业务逻辑)
- ai=artificial intelligence (AI分析)
- v3=version 3 (统一架构版本)