<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net8.0-windows</TargetFramework>
    <UseWindowsForms>true</UseWindowsForms>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <LangVersion>latest</LangVersion>
    <GenerateDocumentationFile>true</GenerateDocumentationFile>
    <TreatWarningsAsErrors>false</TreatWarningsAsErrors>
    <WarningsNotAsErrors>CS1591</WarningsNotAsErrors>
  </PropertyGroup>

  <PropertyGroup>
    <AssemblyTitle>EPConfigTool V2 - YAML Configuration Editor</AssemblyTitle>
    <AssemblyDescription>基于WinForms的轻量级YAML配置编辑器，专门用于编辑EventProcessor V4.1的统一配置文件</AssemblyDescription>
    <AssemblyCompany>BDN</AssemblyCompany>
    <AssemblyProduct>EPConfigTool V2</AssemblyProduct>
    <AssemblyVersion>*******</AssemblyVersion>
    <FileVersion>*******</FileVersion>
    <InformationalVersion>2.0.0</InformationalVersion>

    <Authors>Joe</Authors>
    <Company>BDN</Company>
    <Copyright>Copyright © 2025 BDN All rights reserved.</Copyright>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="YamlDotNet" Version="15.1.2" />
    <PackageReference Include="System.ComponentModel.Annotations" Version="5.0.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Logging" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Options" Version="8.0.2" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\ep_v4.1_augment\src\EventProcessor.Core\EventProcessor.Core.csproj" />
  </ItemGroup>

</Project>