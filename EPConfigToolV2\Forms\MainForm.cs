using System.ComponentModel.DataAnnotations;
using EPConfigToolV2.Models;
using EPConfigToolV2.Services;
using EventProcessor.Core.Models;

namespace EPConfigToolV2.Forms;

/// <summary>
/// 主窗口，提供YAML配置编辑功能
/// </summary>
public partial class MainForm : Form
{
    private readonly ConfigurationService _configurationService;
    private bool _isLoading;

    // UI控件
    private MenuStrip _menuStrip;
    private ToolStripMenuItem _fileMenuItem;
    private ToolStripMenuItem _newMenuItem;
    private ToolStripMenuItem _openMenuItem;
    private ToolStripMenuItem _saveMenuItem;
    private ToolStripMenuItem _saveAsMenuItem;
    private ToolStripMenuItem _exitMenuItem;
    
    private ToolStripMenuItem _editMenuItem;
    private ToolStripMenuItem _validateMenuItem;
    
    private ToolStripMenuItem _helpMenuItem;
    private ToolStripMenuItem _aboutMenuItem;
    
    private StatusStrip _statusStrip;
    private ToolStripStatusLabel _statusLabel;
    private ToolStripStatusLabel _filePathLabel;
    private ToolStripStatusLabel _modifiedLabel;
    
    private SplitContainer _mainSplitContainer;
    private TabControl _configTabControl;
    private RichTextBox _yamlTextBox;
    private ListBox _validationListBox;
    
    // 告警配置Tab页控件
    private DataGridView _fieldMappingGrid;
    private TextBox _customAlarmTopicTextBox;
    private TextBox _customCancellationTopicTextBox;
    private TextBox _customTemplateTextBox;
    private Button _addFieldMappingButton;
    private Button _removeFieldMappingButton;
    
    // MQTT配置Tab页控件
    private TextBox _brokerHostTextBox;
    private NumericUpDown _brokerPortNumeric;
    private TextBox _clientIdTextBox;
    private TextBox _usernameTextBox;
    private TextBox _passwordTextBox;
    
    // 日志配置Tab页控件
    private ComboBox _logLevelComboBox;
    private CheckBox _enableFileLoggingCheckBox;
    private CheckBox _enableConsoleLoggingCheckBox;
    
    public MainForm()
    {
        _configurationService = new ConfigurationService();
        InitializeComponent();
        InitializeEventHandlers();
        
        // 创建新配置
        _configurationService.CreateNewConfiguration();
        UpdateUI();
    }

    private void InitializeComponent()
    {
        SuspendLayout();
        
        // 窗口基本设置
        Text = "EPConfigTool V2 - YAML配置编辑器";
        Size = new Size(1200, 800);
        StartPosition = FormStartPosition.CenterScreen;
        MinimumSize = new Size(800, 600);
        Icon = SystemIcons.Application;
        
        // 创建菜单栏
        CreateMenuStrip();
        
        // 创建状态栏
        CreateStatusStrip();
        
        // 创建主界面
        CreateMainInterface();
        
        ResumeLayout(false);
        PerformLayout();
    }
    
    private void CreateMenuStrip()
    {
        _menuStrip = new MenuStrip();
        
        // 文件菜单
        _fileMenuItem = new ToolStripMenuItem("文件(&F)");
        _newMenuItem = new ToolStripMenuItem("新建(&N)", null, OnNewFile) { ShortcutKeys = Keys.Control | Keys.N };
        _openMenuItem = new ToolStripMenuItem("打开(&O)", null, OnOpenFile) { ShortcutKeys = Keys.Control | Keys.O };
        _saveMenuItem = new ToolStripMenuItem("保存(&S)", null, OnSaveFile) { ShortcutKeys = Keys.Control | Keys.S };
        _saveAsMenuItem = new ToolStripMenuItem("另存为(&A)", null, OnSaveAsFile) { ShortcutKeys = Keys.Control | Keys.Shift | Keys.S };
        _exitMenuItem = new ToolStripMenuItem("退出(&X)", null, OnExit) { ShortcutKeys = Keys.Alt | Keys.F4 };
        
        _fileMenuItem.DropDownItems.AddRange(new ToolStripItem[]
        {
            _newMenuItem,
            _openMenuItem,
            new ToolStripSeparator(),
            _saveMenuItem,
            _saveAsMenuItem,
            new ToolStripSeparator(),
            _exitMenuItem
        });
        
        // 编辑菜单
        _editMenuItem = new ToolStripMenuItem("编辑(&E)");
        _validateMenuItem = new ToolStripMenuItem("验证配置(&V)", null, OnValidateConfiguration) { ShortcutKeys = Keys.F5 };
        
        _editMenuItem.DropDownItems.AddRange(new ToolStripItem[]
        {
            _validateMenuItem
        });
        
        // 帮助菜单
        _helpMenuItem = new ToolStripMenuItem("帮助(&H)");
        _aboutMenuItem = new ToolStripMenuItem("关于(&A)", null, OnAbout);
        
        _helpMenuItem.DropDownItems.AddRange(new ToolStripItem[]
        {
            _aboutMenuItem
        });
        
        _menuStrip.Items.AddRange(new ToolStripItem[]
        {
            _fileMenuItem,
            _editMenuItem,
            _helpMenuItem
        });
        
        MainMenuStrip = _menuStrip;
        Controls.Add(_menuStrip);
    }
    
    private void CreateStatusStrip()
    {
        _statusStrip = new StatusStrip();
        
        _statusLabel = new ToolStripStatusLabel("就绪") { Spring = true, TextAlign = ContentAlignment.MiddleLeft };
        _filePathLabel = new ToolStripStatusLabel("无文件") { AutoSize = false, Width = 300, TextAlign = ContentAlignment.MiddleLeft };
        _modifiedLabel = new ToolStripStatusLabel("") { AutoSize = false, Width = 50, TextAlign = ContentAlignment.MiddleCenter };
        
        _statusStrip.Items.AddRange(new ToolStripItem[]
        {
            _statusLabel,
            new ToolStripStatusLabel("|"),
            _filePathLabel,
            _modifiedLabel
        });
        
        Controls.Add(_statusStrip);
    }
    
    private void CreateMainInterface()
    {
        _mainSplitContainer = new SplitContainer()
        {
            Dock = DockStyle.Fill,
            Orientation = Orientation.Horizontal,
            SplitterDistance = 600,
            FixedPanel = FixedPanel.Panel2,
            Panel2MinSize = 100
        };
        
        // 上半部分：配置编辑区域
        CreateConfigurationPanel();
        
        // 下半部分：验证结果区域
        CreateValidationPanel();
        
        Controls.Add(_mainSplitContainer);
    }
    
    private void CreateConfigurationPanel()
    {
        _configTabControl = new TabControl()
        {
            Dock = DockStyle.Fill
        };
        
        // YAML源码标签页
        var yamlTabPage = new TabPage("YAML源码")
        {
            UseVisualStyleBackColor = true
        };
        
        _yamlTextBox = new RichTextBox()
        {
            Dock = DockStyle.Fill,
            Font = new Font("Consolas", 10F, FontStyle.Regular),
            WordWrap = false,
            AcceptsTab = true,
            DetectUrls = false,
            HideSelection = false,
            EnableAutoDragDrop = false
        };
        
        yamlTabPage.Controls.Add(_yamlTextBox);
        _configTabControl.TabPages.Add(yamlTabPage);
        
        // 告警配置标签页
        CreateAlarmConfigTab();
        
        // MQTT配置标签页
        CreateMqttConfigTab();
        
        // 日志配置标签页
        CreateLoggingConfigTab();
        
        _mainSplitContainer.Panel1.Controls.Add(_configTabControl);
    }
    
    private void CreateAlarmConfigTab()
    {
        var alarmTabPage = new TabPage("告警配置")
        {
            UseVisualStyleBackColor = true
        };
        
        var mainPanel = new TableLayoutPanel()
        {
            Dock = DockStyle.Fill,
            ColumnCount = 2,
            RowCount = 4,
            Padding = new Padding(10)
        };
        
        mainPanel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 30F));
        mainPanel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 70F));
        mainPanel.RowStyles.Add(new RowStyle(SizeType.AutoSize));
        mainPanel.RowStyles.Add(new RowStyle(SizeType.AutoSize));
        mainPanel.RowStyles.Add(new RowStyle(SizeType.AutoSize));
        mainPanel.RowStyles.Add(new RowStyle(SizeType.Percent, 100F));
        
        // 自定义告警主题
        var alarmTopicLabel = new Label()
        {
            Text = "自定义告警主题:",
            Anchor = AnchorStyles.Left,
            AutoSize = true
        };
        _customAlarmTopicTextBox = new TextBox()
        {
            Dock = DockStyle.Fill,
            PlaceholderText = "例如: alarm/{deviceId}/{eventId}"
        };
        
        // 自定义撤销主题
        var cancellationTopicLabel = new Label()
        {
            Text = "自定义撤销主题:",
            Anchor = AnchorStyles.Left,
            AutoSize = true
        };
        _customCancellationTopicTextBox = new TextBox()
        {
            Dock = DockStyle.Fill,
            PlaceholderText = "例如: alarm/cancel/{deviceId}/{eventId}"
        };
        
        // 自定义模板
        var templateLabel = new Label()
        {
            Text = "自定义模板:",
            Anchor = AnchorStyles.Left,
            AutoSize = true
        };
        _customTemplateTextBox = new TextBox()
        {
            Dock = DockStyle.Fill,
            PlaceholderText = "例如: 设备 {deviceId} 触发事件: {message}"
        };
        
        // 字段映射区域
        var fieldMappingLabel = new Label()
        {
            Text = "字段映射:",
            Anchor = AnchorStyles.Left | AnchorStyles.Top,
            AutoSize = true
        };
        
        var fieldMappingPanel = new Panel()
        {
            Dock = DockStyle.Fill
        };
        
        _fieldMappingGrid = new DataGridView()
        {
            Dock = DockStyle.Fill,
            AllowUserToAddRows = false,
            AllowUserToDeleteRows = false,
            SelectionMode = DataGridViewSelectionMode.FullRowSelect,
            MultiSelect = false,
            AutoGenerateColumns = false
        };
        
        // 添加列
        _fieldMappingGrid.Columns.Add(new DataGridViewTextBoxColumn
        {
            Name = "AlarmFieldName",
            HeaderText = "告警字段名称",
            DataPropertyName = "AlarmFieldName",
            Width = 150
        });
        
        _fieldMappingGrid.Columns.Add(new DataGridViewComboBoxColumn
        {
            Name = "SourceRuleType",
            HeaderText = "源规则类型",
            DataPropertyName = "SourceRuleType",
            Width = 120,
            Items = { "BusinessRule", "ExclusionRule", "DeviceSignal" }
        });
        
        _fieldMappingGrid.Columns.Add(new DataGridViewTextBoxColumn
        {
            Name = "SourceFieldName",
            HeaderText = "源字段名称",
            DataPropertyName = "SourceFieldName",
            Width = 150
        });
        
        var buttonPanel = new FlowLayoutPanel()
        {
            Dock = DockStyle.Bottom,
            Height = 35,
            FlowDirection = FlowDirection.LeftToRight,
            Padding = new Padding(0, 5, 0, 0)
        };
        
        _addFieldMappingButton = new Button()
        {
            Text = "添加映射",
            Size = new Size(80, 25)
        };
        
        _removeFieldMappingButton = new Button()
        {
            Text = "删除映射",
            Size = new Size(80, 25)
        };
        
        buttonPanel.Controls.Add(_addFieldMappingButton);
        buttonPanel.Controls.Add(_removeFieldMappingButton);
        
        fieldMappingPanel.Controls.Add(_fieldMappingGrid);
        fieldMappingPanel.Controls.Add(buttonPanel);
        
        // 添加控件到主面板
        mainPanel.Controls.Add(alarmTopicLabel, 0, 0);
        mainPanel.Controls.Add(_customAlarmTopicTextBox, 1, 0);
        mainPanel.Controls.Add(cancellationTopicLabel, 0, 1);
        mainPanel.Controls.Add(_customCancellationTopicTextBox, 1, 1);
        mainPanel.Controls.Add(templateLabel, 0, 2);
        mainPanel.Controls.Add(_customTemplateTextBox, 1, 2);
        mainPanel.Controls.Add(fieldMappingLabel, 0, 3);
        mainPanel.Controls.Add(fieldMappingPanel, 1, 3);
        
        alarmTabPage.Controls.Add(mainPanel);
        _configTabControl.TabPages.Add(alarmTabPage);
        
        // 绑定事件
        _addFieldMappingButton.Click += OnAddFieldMapping;
        _removeFieldMappingButton.Click += OnRemoveFieldMapping;
        _customAlarmTopicTextBox.TextChanged += OnAlarmConfigChanged;
        _customCancellationTopicTextBox.TextChanged += OnAlarmConfigChanged;
        _customTemplateTextBox.TextChanged += OnAlarmConfigChanged;
        _fieldMappingGrid.CellValueChanged += OnFieldMappingChanged;
    }
    
    private void CreateMqttConfigTab()
    {
        var mqttTabPage = new TabPage("MQTT配置")
        {
            UseVisualStyleBackColor = true
        };
        
        var mainPanel = new TableLayoutPanel()
        {
            Dock = DockStyle.Fill,
            ColumnCount = 2,
            RowCount = 5,
            Padding = new Padding(10)
        };
        
        mainPanel.ColumnStyles.Add(new ColumnStyle(SizeType.AutoSize));
        mainPanel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100F));
        
        // 代理主机
        var brokerHostLabel = new Label() { Text = "代理主机:", Anchor = AnchorStyles.Left, AutoSize = true };
        _brokerHostTextBox = new TextBox() { Dock = DockStyle.Fill };
        
        // 代理端口
        var brokerPortLabel = new Label() { Text = "代理端口:", Anchor = AnchorStyles.Left, AutoSize = true };
        _brokerPortNumeric = new NumericUpDown() { Minimum = 1, Maximum = 65535, Value = 1883, Dock = DockStyle.Fill };
        
        // 客户端ID
        var clientIdLabel = new Label() { Text = "客户端ID:", Anchor = AnchorStyles.Left, AutoSize = true };
        _clientIdTextBox = new TextBox() { Dock = DockStyle.Fill };
        
        // 用户名
        var usernameLabel = new Label() { Text = "用户名:", Anchor = AnchorStyles.Left, AutoSize = true };
        _usernameTextBox = new TextBox() { Dock = DockStyle.Fill };
        
        // 密码
        var passwordLabel = new Label() { Text = "密码:", Anchor = AnchorStyles.Left, AutoSize = true };
        _passwordTextBox = new TextBox() { Dock = DockStyle.Fill, UseSystemPasswordChar = true };
        
        // 添加控件
        mainPanel.Controls.Add(brokerHostLabel, 0, 0);
        mainPanel.Controls.Add(_brokerHostTextBox, 1, 0);
        mainPanel.Controls.Add(brokerPortLabel, 0, 1);
        mainPanel.Controls.Add(_brokerPortNumeric, 1, 1);
        mainPanel.Controls.Add(clientIdLabel, 0, 2);
        mainPanel.Controls.Add(_clientIdTextBox, 1, 2);
        mainPanel.Controls.Add(usernameLabel, 0, 3);
        mainPanel.Controls.Add(_usernameTextBox, 1, 3);
        mainPanel.Controls.Add(passwordLabel, 0, 4);
        mainPanel.Controls.Add(_passwordTextBox, 1, 4);
        
        mqttTabPage.Controls.Add(mainPanel);
        _configTabControl.TabPages.Add(mqttTabPage);
        
        // 绑定事件
        _brokerHostTextBox.TextChanged += OnMqttConfigChanged;
        _brokerPortNumeric.ValueChanged += OnMqttConfigChanged;
        _clientIdTextBox.TextChanged += OnMqttConfigChanged;
        _usernameTextBox.TextChanged += OnMqttConfigChanged;
        _passwordTextBox.TextChanged += OnMqttConfigChanged;
    }
    
    private void CreateLoggingConfigTab()
    {
        var loggingTabPage = new TabPage("日志配置")
        {
            UseVisualStyleBackColor = true
        };
        
        var mainPanel = new TableLayoutPanel()
        {
            Dock = DockStyle.Fill,
            ColumnCount = 2,
            RowCount = 3,
            Padding = new Padding(10)
        };
        
        mainPanel.ColumnStyles.Add(new ColumnStyle(SizeType.AutoSize));
        mainPanel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100F));
        
        // 日志级别
        var logLevelLabel = new Label() { Text = "日志级别:", Anchor = AnchorStyles.Left, AutoSize = true };
        _logLevelComboBox = new ComboBox()
        {
            Dock = DockStyle.Fill,
            DropDownStyle = ComboBoxStyle.DropDownList
        };
        _logLevelComboBox.Items.AddRange(new[] { "Verbose", "Debug", "Information", "Warning", "Error", "Fatal" });
        
        // 启用文件日志
        _enableFileLoggingCheckBox = new CheckBox()
        {
            Text = "启用文件日志",
            Dock = DockStyle.Fill,
            AutoSize = true
        };
        
        // 启用控制台日志
        _enableConsoleLoggingCheckBox = new CheckBox()
        {
            Text = "启用控制台日志",
            Dock = DockStyle.Fill,
            AutoSize = true
        };
        
        // 添加控件
        mainPanel.Controls.Add(logLevelLabel, 0, 0);
        mainPanel.Controls.Add(_logLevelComboBox, 1, 0);
        mainPanel.Controls.Add(_enableFileLoggingCheckBox, 0, 1);
        mainPanel.SetColumnSpan(_enableFileLoggingCheckBox, 2);
        mainPanel.Controls.Add(_enableConsoleLoggingCheckBox, 0, 2);
        mainPanel.SetColumnSpan(_enableConsoleLoggingCheckBox, 2);
        
        loggingTabPage.Controls.Add(mainPanel);
        _configTabControl.TabPages.Add(loggingTabPage);
        
        // 绑定事件
        _logLevelComboBox.SelectedIndexChanged += OnLoggingConfigChanged;
        _enableFileLoggingCheckBox.CheckedChanged += OnLoggingConfigChanged;
        _enableConsoleLoggingCheckBox.CheckedChanged += OnLoggingConfigChanged;
    }
    
    private void CreateValidationPanel()
    {
        var validationPanel = new Panel()
        {
            Dock = DockStyle.Fill
        };
        
        var validationLabel = new Label()
        {
            Text = "验证结果:",
            Dock = DockStyle.Top,
            Height = 25,
            TextAlign = ContentAlignment.MiddleLeft,
            Padding = new Padding(5, 5, 0, 0)
        };
        
        _validationListBox = new ListBox()
        {
            Dock = DockStyle.Fill,
            Font = new Font("Microsoft YaHei UI", 9F),
            IntegralHeight = false
        };
        
        validationPanel.Controls.Add(_validationListBox);
        validationPanel.Controls.Add(validationLabel);
        
        _mainSplitContainer.Panel2.Controls.Add(validationPanel);
    }
    
    private void InitializeEventHandlers()
    {
        // 配置服务事件
        _configurationService.ConfigurationChanged += OnConfigurationChanged;
        _configurationService.ValidationError += OnValidationError;
        _configurationService.FieldMappingsChanged += OnFieldMappingsChanged;
        
        // UI事件
        _yamlTextBox.TextChanged += OnYamlTextChanged;
        FormClosing += OnFormClosing;
        
        // 键盘快捷键
        KeyPreview = true;
        KeyDown += OnKeyDown;
    }
    
    #region 事件处理
    
    private async void OnNewFile(object? sender, EventArgs e)
    {
        if (!await CheckSaveChanges()) return;
        
        _configurationService.CreateNewConfiguration();
        UpdateYamlDisplay();
        UpdateUI();
        
        _statusLabel.Text = "已创建新配置";
    }
    
    private async void OnOpenFile(object? sender, EventArgs e)
    {
        if (!await CheckSaveChanges()) return;
        
        using var openFileDialog = new OpenFileDialog()
        {
            Filter = "YAML文件 (*.yaml;*.yml)|*.yaml;*.yml|所有文件 (*.*)|*.*",
            FilterIndex = 1,
            RestoreDirectory = true,
            CheckFileExists = true,
            CheckPathExists = true
        };
        
        if (openFileDialog.ShowDialog() == DialogResult.OK)
        {
            await LoadConfigurationFile(openFileDialog.FileName);
        }
    }
    
    private async void OnSaveFile(object? sender, EventArgs e)
    {
        if (string.IsNullOrEmpty(_configurationService.CurrentFilePath))
        {
            OnSaveAsFile(sender, e);
            return;
        }
        
        await SaveCurrentConfiguration();
    }
    
    private async void OnSaveAsFile(object? sender, EventArgs e)
    {
        using var saveFileDialog = new SaveFileDialog()
        {
            Filter = "YAML文件 (*.yaml)|*.yaml|YML文件 (*.yml)|*.yml|所有文件 (*.*)|*.*",
            FilterIndex = 1,
            RestoreDirectory = true,
            AddExtension = true,
            DefaultExt = "yaml"
        };
        
        if (saveFileDialog.ShowDialog() == DialogResult.OK)
        {
            await SaveConfigurationToFile(saveFileDialog.FileName);
        }
    }
    
    private async void OnExit(object? sender, EventArgs e)
    {
        if (await CheckSaveChanges())
        {
            Application.Exit();
        }
    }
    
    private void OnValidateConfiguration(object? sender, EventArgs e)
    {
        ValidateCurrentConfiguration();
    }
    
    private void OnAbout(object? sender, EventArgs e)
    {
        MessageBox.Show(
            "EPConfigTool V2 - YAML配置编辑器\n\n" +
            "版本: 2.0.0\n" +
            "基于 .NET 8.0 和 WinForms 构建\n" +
            "专门用于编辑 EventProcessor V4.1 的统一配置文件\n\n" +
            "Copyright © 2025 BDN All rights reserved.",
            "关于 EPConfigTool V2",
            MessageBoxButtons.OK,
            MessageBoxIcon.Information);
    }
    
    private void OnConfigurationChanged(object? sender, ConfigurationChangedEventArgs e)
    {
        if (!_isLoading)
        {
            UpdateYamlDisplay();
            RefreshTabControls();
        }
        UpdateUI();
    }
    
    private void OnValidationError(object? sender, ValidationErrorEventArgs e)
    {
        DisplayValidationResults(e.ValidationResults);
    }
    
    private void OnYamlTextChanged(object? sender, EventArgs e)
    {
        if (_isLoading) return;
        
        // 标记为已修改
        if (_configurationService.CurrentConfiguration != null)
        {
            UpdateUI();
        }
    }
    
    private async void OnFormClosing(object? sender, FormClosingEventArgs e)
    {
        if (!await CheckSaveChanges())
        {
            e.Cancel = true;
        }
    }
    
    private void OnKeyDown(object? sender, KeyEventArgs e)
    {
        // 处理快捷键
        if (e.Control && e.KeyCode == Keys.S)
        {
            OnSaveFile(sender, e);
            e.Handled = true;
        }
        else if (e.KeyCode == Keys.F5)
        {
            OnValidateConfiguration(sender, e);
            e.Handled = true;
        }
    }
    
    #endregion
    
    #region 辅助方法
    
    private async Task LoadConfigurationFile(string filePath)
    {
        try
        {
            _isLoading = true;
            _statusLabel.Text = "正在加载配置文件...";
            
            await _configurationService.LoadConfigurationAsync(filePath);
            UpdateYamlDisplay();
            UpdateUI();
            
            _statusLabel.Text = $"已加载配置文件: {Path.GetFileName(filePath)}";
        }
        catch (Exception ex)
        {
            MessageBox.Show(
                $"加载配置文件失败:\n{ex.Message}",
                "错误",
                MessageBoxButtons.OK,
                MessageBoxIcon.Error);
            
            _statusLabel.Text = "加载配置文件失败";
        }
        finally
        {
            _isLoading = false;
        }
    }
    
    private async Task SaveCurrentConfiguration()
    {
        await SaveConfigurationToFile(_configurationService.CurrentFilePath!);
    }
    
    private async Task SaveConfigurationToFile(string filePath)
    {
        try
        {
            _statusLabel.Text = "正在保存配置文件...";
            
            // 从YAML文本更新配置
            UpdateConfigurationFromYaml();
            
            await _configurationService.SaveConfigurationAsync(filePath);
            UpdateUI();
            
            _statusLabel.Text = $"已保存配置文件: {Path.GetFileName(filePath)}";
        }
        catch (Exception ex)
        {
            MessageBox.Show(
                $"保存配置文件失败:\n{ex.Message}",
                "错误",
                MessageBoxButtons.OK,
                MessageBoxIcon.Error);
            
            _statusLabel.Text = "保存配置文件失败";
        }
    }
    
    private void UpdateConfigurationFromYaml()
    {
        try
        {
            var yamlContent = _yamlTextBox.Text;
            if (!string.IsNullOrWhiteSpace(yamlContent))
            {
                var configuration = _configurationService.DeserializeFromYaml(yamlContent);
                _configurationService.UpdateConfiguration(configuration);
            }
        }
        catch (Exception ex)
        {
            throw new InvalidOperationException($"YAML格式错误: {ex.Message}", ex);
        }
    }
    
    private void UpdateYamlDisplay()
    {
        if (_configurationService.CurrentConfiguration != null)
        {
            _isLoading = true;
            try
            {
                var yamlContent = _configurationService.SerializeToYaml(_configurationService.CurrentConfiguration);
                _yamlTextBox.Text = yamlContent;
            }
            finally
            {
                _isLoading = false;
            }
        }
    }
    
    private void OnFieldMappingsChanged(object? sender, List<FieldMapping> fieldMappings)
    {
        if (!_isLoading)
        {
            RefreshFieldMappingGrid();
        }
    }
    
    private void ValidateCurrentConfiguration()
    {
        if (_configurationService.CurrentConfiguration == null) return;
        
        try
        {
            // 先从YAML文本更新配置
            UpdateConfigurationFromYaml();
            
            var validationResults = _configurationService.ValidateConfiguration(_configurationService.CurrentConfiguration);
            DisplayValidationResults(validationResults);
            
            if (validationResults.Count == 0)
            {
                _statusLabel.Text = "配置验证通过";
            }
            else
            {
                _statusLabel.Text = $"发现 {validationResults.Count} 个验证错误";
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show(
                $"验证配置时发生错误:\n{ex.Message}",
                "验证错误",
                MessageBoxButtons.OK,
                MessageBoxIcon.Warning);
        }
    }
    
    private void DisplayValidationResults(List<Models.ValidationResult> validationResults)
    {
        _validationListBox.Items.Clear();
        
        if (validationResults.Count == 0)
        {
            _validationListBox.Items.Add("✓ 配置验证通过，没有发现错误");
        }
        else
        {
            foreach (var result in validationResults)
            {
                foreach (var error in result.Errors)
                {
                    var memberPath = !string.IsNullOrEmpty(error.PropertyName) ? error.PropertyName : "根对象";
                    _validationListBox.Items.Add($"✗ {memberPath}: {error.ErrorMessage}");
                }
            }
        }
    }
    
    private void UpdateUI()
    {
        var hasConfiguration = _configurationService.CurrentConfiguration != null;
        var hasFilePath = !string.IsNullOrEmpty(_configurationService.CurrentFilePath);
        var isModified = _configurationService.IsModified;
        
        // 更新菜单状态
        _saveMenuItem.Enabled = hasConfiguration;
        _saveAsMenuItem.Enabled = hasConfiguration;
        _validateMenuItem.Enabled = hasConfiguration;
        
        // 更新标题
        var title = "EPConfigTool V2 - YAML配置编辑器";
        if (hasFilePath)
        {
            title += $" - {Path.GetFileName(_configurationService.CurrentFilePath)}";
        }
        else if (hasConfiguration)
        {
            title += " - 新配置";
        }
        if (isModified)
        {
            title += " *";
        }
        Text = title;
        
        // 更新状态栏
        _filePathLabel.Text = hasFilePath ? _configurationService.CurrentFilePath : "无文件";
        _modifiedLabel.Text = isModified ? "*" : "";
    }
    
    private async Task<bool> CheckSaveChanges()
    {
        if (!_configurationService.IsModified) return true;
        
        var result = MessageBox.Show(
            "当前配置已修改，是否保存更改？",
            "保存更改",
            MessageBoxButtons.YesNoCancel,
            MessageBoxIcon.Question);
        
        switch (result)
        {
            case DialogResult.Yes:
                if (string.IsNullOrEmpty(_configurationService.CurrentFilePath))
                {
                    OnSaveAsFile(null, EventArgs.Empty);
                    return !_configurationService.IsModified; // 如果用户取消保存，则返回false
                }
                else
                {
                    await SaveCurrentConfiguration();
                    return true;
                }
            case DialogResult.No:
                return true;
            case DialogResult.Cancel:
            default:
                return false;
        }
    }
    
    #endregion
    
    #region Tab页事件处理
    
    private void OnAddFieldMapping(object? sender, EventArgs e)
    {
        var newMapping = new FieldMapping
        {
            AlarmFieldName = "新字段",
            SourceRuleType = "BusinessRule",
            SourceFieldName = "源字段"
        };
        
        _configurationService.AddFieldMapping(newMapping);
        RefreshFieldMappingGrid();
    }
    
    private void OnRemoveFieldMapping(object? sender, EventArgs e)
    {
        if (_fieldMappingGrid.SelectedRows.Count > 0)
        {
            var selectedIndex = _fieldMappingGrid.SelectedRows[0].Index;
            _configurationService.RemoveFieldMapping(selectedIndex);
            RefreshFieldMappingGrid();
        }
    }
    
    private void OnFieldMappingChanged(object? sender, DataGridViewCellEventArgs e)
    {
        if (e.RowIndex >= 0 && !_isLoading)
        {
            var fieldMappings = new List<FieldMapping>();
            
            foreach (DataGridViewRow row in _fieldMappingGrid.Rows)
            {
                if (row.Cells["AlarmFieldName"].Value != null)
                {
                    fieldMappings.Add(new FieldMapping
                    {
                        AlarmFieldName = row.Cells["AlarmFieldName"].Value?.ToString() ?? "",
                        SourceRuleType = row.Cells["SourceRuleType"].Value?.ToString() ?? "BusinessRule",
                        SourceFieldName = row.Cells["SourceFieldName"].Value?.ToString() ?? ""
                    });
                }
            }
            
            _configurationService.UpdateFieldMappings(fieldMappings);
        }
    }
    
    private void OnAlarmConfigChanged(object? sender, EventArgs e)
    {
        if (_isLoading || _configurationService.CurrentConfiguration?.EventProcessor?.AlarmConfiguration == null)
            return;
            
        var currentConfig = _configurationService.CurrentConfiguration;
        var alarmConfig = currentConfig.EventProcessor.AlarmConfiguration;
        
        // 创建新的告警配置对象
        var newAlarmConfig = new AlarmConfiguration
        {
            CustomAlarmTopic = sender == _customAlarmTopicTextBox ? _customAlarmTopicTextBox.Text : alarmConfig.CustomAlarmTopic,
            CustomAlarmCancellationTopic = sender == _customCancellationTopicTextBox ? _customCancellationTopicTextBox.Text : alarmConfig.CustomAlarmCancellationTopic,
            CustomTemplate = sender == _customTemplateTextBox ? _customTemplateTextBox.Text : alarmConfig.CustomTemplate,
            Fields = alarmConfig.Fields
        };
        
        // 创建新的规则配置对象
        var newRuleConfig = new RuleConfiguration
        {
            AlarmConfig = newAlarmConfig
        };
        
        // 创建新的事件配置对象
        var newEventConfig = new EventConfiguration
        {
            EventId = currentConfig.EventProcessor.EventId,
            EventName = currentConfig.EventProcessor.EventName,
            EvaluationStrategy = currentConfig.EventProcessor.EvaluationStrategy,
            Priority = currentConfig.EventProcessor.Priority,
            CommId = currentConfig.EventProcessor.CommId,
            PositionId = currentConfig.EventProcessor.PositionId,
            CompanyName = currentConfig.EventProcessor.CompanyName,
            AIPrompt = currentConfig.EventProcessor.AIPrompt,
            AIAnalysisDelaySec = currentConfig.EventProcessor.AIAnalysisDelaySec,
            ImageCropCoordinates = currentConfig.EventProcessor.ImageCropCoordinates,
            DeviceSignal = currentConfig.EventProcessor.DeviceSignal,
            RuleConfiguration = newRuleConfig,
            AlarmGracePeriodSeconds = currentConfig.EventProcessor.AlarmGracePeriodSeconds,
            EnableAlarmCancellation = currentConfig.EventProcessor.EnableAlarmCancellation,
            CustomAlarmTopic = currentConfig.EventProcessor.CustomAlarmTopic,
            CustomAlarmCancellationTopic = currentConfig.EventProcessor.CustomAlarmCancellationTopic,
            CorrelationTimeWindow = currentConfig.EventProcessor.CorrelationTimeWindow,
            CustomTimeWindowMinutes = currentConfig.EventProcessor.CustomTimeWindowMinutes,
            AlarmConfiguration = newAlarmConfig
        };
        
        // 创建新的统一配置对象
        var newConfig = new UnifiedConfigurationModel
        {
            EventProcessor = newEventConfig,
            Mqtt = currentConfig.Mqtt,
            Logging = currentConfig.Logging,
            Serilog = currentConfig.Serilog
        };
        
        _configurationService.UpdateConfiguration(newConfig);
    }
    
    private void OnMqttConfigChanged(object? sender, EventArgs e)
    {
        if (_isLoading || _configurationService.CurrentConfiguration?.Mqtt == null)
            return;
            
        var currentConfig = _configurationService.CurrentConfiguration;
        var mqttConfig = currentConfig.Mqtt;
        
        // 创建新的MQTT配置对象
        var newMqttConfig = new MqttConfiguration
        {
            BrokerHost = sender == _brokerHostTextBox ? _brokerHostTextBox.Text : mqttConfig.BrokerHost,
            BrokerPort = sender == _brokerPortNumeric ? (int)_brokerPortNumeric.Value : mqttConfig.BrokerPort,
            ClientId = sender == _clientIdTextBox ? _clientIdTextBox.Text : mqttConfig.ClientId,
            Username = sender == _usernameTextBox ? (string.IsNullOrWhiteSpace(_usernameTextBox.Text) ? null : _usernameTextBox.Text) : mqttConfig.Username,
            Password = sender == _passwordTextBox ? (string.IsNullOrWhiteSpace(_passwordTextBox.Text) ? null : _passwordTextBox.Text) : mqttConfig.Password
        };
        
        // 创建新的统一配置对象
        var newConfig = new UnifiedConfigurationModel
        {
            EventProcessor = currentConfig.EventProcessor,
            Mqtt = newMqttConfig,
            Logging = currentConfig.Logging,
            Serilog = currentConfig.Serilog
        };
        
        _configurationService.UpdateConfiguration(newConfig);
    }
    
    private void OnLoggingConfigChanged(object? sender, EventArgs e)
    {
        if (_isLoading || _configurationService.CurrentConfiguration?.Logging == null)
            return;
            
        var currentConfig = _configurationService.CurrentConfiguration;
        var loggingConfig = currentConfig.Logging;
        
        // 创建新的日志配置对象
        LoggingConfiguration newLoggingConfig;
        
        if (sender == _logLevelComboBox)
        {
            var newMinimumLevel = new SerilogMinimumLevel
            {
                Default = _logLevelComboBox.SelectedItem?.ToString() ?? "Information"
            };
            
            newLoggingConfig = new LoggingConfiguration
            {
                MinimumLevel = newMinimumLevel,
                WriteTo = loggingConfig.WriteTo
            };
        }
        else if (sender == _enableFileLoggingCheckBox || sender == _enableConsoleLoggingCheckBox)
        {
            var newWriteTo = new List<SerilogWriteTo>();
            
            if (_enableConsoleLoggingCheckBox.Checked)
            {
                newWriteTo.Add(new SerilogWriteTo { Name = "Console" });
            }
            
            if (_enableFileLoggingCheckBox.Checked)
            {
                newWriteTo.Add(new SerilogWriteTo 
                { 
                    Name = "File",
                    Args = new Dictionary<string, object> 
                    {
                        { "path", "logs/log-.txt" },
                        { "rollingInterval", "Day" }
                    }
                });
            }
            
            newLoggingConfig = new LoggingConfiguration
            {
                MinimumLevel = loggingConfig.MinimumLevel,
                WriteTo = newWriteTo
            };
        }
        else
        {
            return;
        }
        
        // 创建新的统一配置对象
        var newConfig = new UnifiedConfigurationModel
        {
            EventProcessor = currentConfig.EventProcessor,
            Mqtt = currentConfig.Mqtt,
            Logging = newLoggingConfig,
            Serilog = currentConfig.Serilog
        };
        
        _configurationService.UpdateConfiguration(newConfig);
    }
    
    private void RefreshFieldMappingGrid()
    {
        _isLoading = true;
        try
        {
            var fieldMappings = _configurationService.GetFieldMappings();
            _fieldMappingGrid.DataSource = null;
            _fieldMappingGrid.DataSource = fieldMappings;
        }
        finally
        {
            _isLoading = false;
        }
    }
    
    private void RefreshTabControls()
    {
        if (_configurationService.CurrentConfiguration == null) return;
        
        _isLoading = true;
        try
        {
            var config = _configurationService.CurrentConfiguration;
            
            // 更新告警配置Tab页
            if (config.EventProcessor?.AlarmConfiguration != null)
            {
                var alarmConfig = config.EventProcessor.AlarmConfiguration;
                _customAlarmTopicTextBox.Text = alarmConfig.CustomAlarmTopic ?? "";
                _customCancellationTopicTextBox.Text = alarmConfig.CustomAlarmCancellationTopic ?? "";
                _customTemplateTextBox.Text = alarmConfig.CustomTemplate ?? "";
                RefreshFieldMappingGrid();
            }
            
            // 更新MQTT配置Tab页
            if (config.Mqtt != null)
            {
                var mqttConfig = config.Mqtt;
                _brokerHostTextBox.Text = mqttConfig.BrokerHost ?? "";
                _brokerPortNumeric.Value = mqttConfig.BrokerPort;
                _clientIdTextBox.Text = mqttConfig.ClientId ?? "";
                _usernameTextBox.Text = mqttConfig.Username ?? "";
                _passwordTextBox.Text = mqttConfig.Password ?? "";
            }
            
            // 更新日志配置Tab页
            if (config.Logging != null)
            {
                var loggingConfig = config.Logging;
                if (loggingConfig.MinimumLevel != null)
                {
                    _logLevelComboBox.SelectedItem = loggingConfig.MinimumLevel.Default;
                }
                
                _enableConsoleLoggingCheckBox.Checked = loggingConfig.WriteTo.Any(w => w.Name == "Console");
                _enableFileLoggingCheckBox.Checked = loggingConfig.WriteTo.Any(w => w.Name == "File");
            }
        }
        finally
        {
            _isLoading = false;
        }
    }
    
    #endregion
}