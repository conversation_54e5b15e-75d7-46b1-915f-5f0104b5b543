using System.ComponentModel.DataAnnotations;
using System.Text.RegularExpressions;
using EPConfigToolV2.Models;
using EventProcessor.Core.Models;

namespace EPConfigToolV2.Helpers;

/// <summary>
/// 验证辅助类，提供配置验证功能
/// </summary>
public static class ValidationHelper
{
    /// <summary>
    /// 验证完整配置模型
    /// </summary>
    /// <param name="configuration">配置模型</param>
    /// <returns>验证结果列表</returns>
    public static List<Models.ValidationResult> ValidateConfiguration(UnifiedConfigurationModel configuration)
    {
        var results = new List<Models.ValidationResult>();
        
        if (configuration == null)
        {
            results.Add(new Models.ValidationResult
        {
            IsValid = false,
            Errors = { new ValidationError { ErrorMessage = "配置不能为空" } }
        });
            return results;
        }
        
        // 验证基本属性
        var context = new ValidationContext(configuration);
        var validationResults = new List<System.ComponentModel.DataAnnotations.ValidationResult>();
        if (!Validator.TryValidateObject(configuration, context, validationResults, true))
        {
            results.Add(new Models.ValidationResult
            {
                IsValid = false,
                Errors = validationResults.Select(r => new ValidationError
                {
                    PropertyName = string.Join(", ", r.MemberNames),
                    ErrorMessage = r.ErrorMessage ?? "未知验证错误"
                }).ToList()
            });
        }
        
        // 验证嵌套对象
        ValidateEventProcessorConfiguration(configuration.EventProcessor, results);
        ValidateMqttConfiguration(configuration.Mqtt, results);
        ValidateLoggingConfiguration(configuration.Logging, results);
        
        return results;
    }
    
    /// <summary>
    /// 验证事件处理器配置
    /// </summary>
    /// <param name="eventConfig">事件处理器配置</param>
    /// <param name="results">验证结果列表</param>
    private static void ValidateEventProcessorConfiguration(EventConfiguration? eventConfig, List<Models.ValidationResult> results)
    {
        if (eventConfig == null)
        {
            results.Add(new Models.ValidationResult
        {
            IsValid = false,
            Errors = { new ValidationError
            {
                ErrorMessage = "事件处理器配置不能为空",
                PropertyName = "EventProcessor"
            }}
        });
            return;
        }
        
        var context = new ValidationContext(eventConfig);
        var validationResults = new List<System.ComponentModel.DataAnnotations.ValidationResult>();
        if (!Validator.TryValidateObject(eventConfig, context, validationResults, true))
        {
            results.Add(new Models.ValidationResult
            {
                IsValid = false,
                Errors = validationResults.Select(r => new ValidationError
                {
                    PropertyName = string.Join(", ", r.MemberNames),
                    ErrorMessage = r.ErrorMessage ?? "未知验证错误"
                }).ToList()
            });
        }
        
        // 验证告警配置
        ValidateAlarmConfiguration(eventConfig.AlarmConfiguration, results);
    }
    
    /// <summary>
    /// 验证告警配置
    /// </summary>
    /// <param name="alarmConfig">告警配置</param>
    /// <param name="results">验证结果列表</param>
    private static void ValidateAlarmConfiguration(AlarmConfiguration? alarmConfig, List<Models.ValidationResult> results)
    {
        if (alarmConfig == null)
        {
            results.Add(new Models.ValidationResult
        {
            IsValid = false,
            Errors = { new ValidationError
            {
                ErrorMessage = "告警配置不能为空",
                PropertyName = "EventProcessor.AlarmConfiguration"
            }}
        });
            return;
        }
        
        var context = new ValidationContext(alarmConfig);
        var validationResults = new List<System.ComponentModel.DataAnnotations.ValidationResult>();
        if (!Validator.TryValidateObject(alarmConfig, context, validationResults, true))
        {
            results.Add(new Models.ValidationResult
            {
                IsValid = false,
                Errors = validationResults.Select(r => new ValidationError
                {
                    PropertyName = string.Join(", ", r.MemberNames),
                    ErrorMessage = r.ErrorMessage ?? "未知验证错误"
                }).ToList()
            });
        }
        
        // 验证自定义MQTT主题
        ValidateCustomMqttTopic(alarmConfig.CustomAlarmTopic, "CustomAlarmTopic", results);
        ValidateCustomMqttTopic(alarmConfig.CustomAlarmCancellationTopic, "CustomAlarmCancellationTopic", results);
        
        // 验证字段映射
        ValidateFieldMappings(alarmConfig.Fields, results);
        
        // 验证自定义模板
        ValidateCustomTemplate(alarmConfig.CustomTemplate, alarmConfig.Fields, results);
    }
    
    /// <summary>
    /// 验证MQTT配置
    /// </summary>
    /// <param name="mqttConfig">MQTT配置</param>
    /// <param name="results">验证结果列表</param>
    private static void ValidateMqttConfiguration(MqttConfiguration? mqttConfig, List<Models.ValidationResult> results)
    {
        if (mqttConfig == null)
        {
            results.Add(new Models.ValidationResult
        {
            IsValid = false,
            Errors = { new ValidationError
            {
                ErrorMessage = "MQTT配置不能为空",
                PropertyName = "Mqtt"
            }}
        });
            return;
        }
        
        var context = new ValidationContext(mqttConfig);
        var validationResults = new List<System.ComponentModel.DataAnnotations.ValidationResult>();
        if (!Validator.TryValidateObject(mqttConfig, context, validationResults, true))
        {
            results.Add(new Models.ValidationResult
            {
                IsValid = false,
                Errors = validationResults.Select(r => new ValidationError
                {
                    PropertyName = string.Join(", ", r.MemberNames),
                    ErrorMessage = r.ErrorMessage ?? "未知验证错误"
                }).ToList()
            });
        }
        
        // 验证主机名格式
        if (!string.IsNullOrWhiteSpace(mqttConfig.BrokerHost))
        {
            if (!IsValidHostname(mqttConfig.BrokerHost))
            {
                results.Add(new Models.ValidationResult
            {
                IsValid = false,
                Errors = { new ValidationError
                {
                    ErrorMessage = "MQTT代理主机格式无效",
                    PropertyName = "Mqtt.BrokerHost"
                }}
            });
            }
        }
    }
    
    /// <summary>
    /// 验证日志配置
    /// </summary>
    /// <param name="loggingConfig">日志配置</param>
    /// <param name="results">验证结果列表</param>
    private static void ValidateLoggingConfiguration(LoggingConfiguration? loggingConfig, List<Models.ValidationResult> results)
    {
        if (loggingConfig == null)
        {
            results.Add(new Models.ValidationResult
        {
            IsValid = false,
            Errors = { new ValidationError
            {
                ErrorMessage = "日志配置不能为空",
                PropertyName = "Logging"
            }}
        });
            return;
        }
        
        var context = new ValidationContext(loggingConfig);
        var validationResults = new List<System.ComponentModel.DataAnnotations.ValidationResult>();
        if (!Validator.TryValidateObject(loggingConfig, context, validationResults, true))
        {
            results.Add(new Models.ValidationResult
            {
                IsValid = false,
                Errors = validationResults.Select(r => new ValidationError
                {
                    PropertyName = string.Join(", ", r.MemberNames),
                    ErrorMessage = r.ErrorMessage ?? "未知验证错误"
                }).ToList()
            });
        }
        
        // 验证日志级别
        if (loggingConfig.MinimumLevel != null)
        {
            ValidateLogLevel(loggingConfig.MinimumLevel.Default, "Logging.MinimumLevel.Default", results);
            
            foreach (var kvp in loggingConfig.MinimumLevel.Override)
            {
                ValidateLogLevel(kvp.Value, $"Logging.MinimumLevel.Override[{kvp.Key}]", results);
            }
        }
        
        // 验证写入器配置
        ValidateWriteToConfiguration(loggingConfig.WriteTo, results);
    }
    
    /// <summary>
    /// 验证自定义MQTT主题
    /// </summary>
    /// <param name="topic">主题字符串</param>
    /// <param name="propertyName">属性名称</param>
    /// <param name="results">验证结果列表</param>
    private static void ValidateCustomMqttTopic(string? topic, string propertyName, List<Models.ValidationResult> results)
    {
        if (string.IsNullOrWhiteSpace(topic))
        {
            return; // 允许为空
        }
        
        // 检查主题格式：允许字母、数字、斜杠、连字符、下划线和花括号
        var topicPattern = @"^[a-zA-Z0-9/_\-{}]+$";
        if (!Regex.IsMatch(topic, topicPattern))
        {
            results.Add(new Models.ValidationResult
            {
                IsValid = false,
                Errors = { new ValidationError
                {
                    ErrorMessage = "自定义MQTT主题格式无效，只允许字母、数字、斜杠、连字符、下划线和花括号",
                    PropertyName = $"EventProcessor.AlarmConfiguration.{propertyName}"
                }}
            });
        }
        
        // 检查主题层级（不超过10级）
        var levels = topic.Split('/');
        if (levels.Length > 10)
        {
            results.Add(new Models.ValidationResult
            {
                IsValid = false,
                Errors = { new ValidationError
                {
                    ErrorMessage = "MQTT主题层级过深，最多支持10级",
                    PropertyName = $"EventProcessor.AlarmConfiguration.{propertyName}"
                }}
            });
        }
        
        // 检查每个层级长度（不超过65535字符）
        foreach (var level in levels)
        {
            if (level.Length > 65535)
            {
                results.Add(new Models.ValidationResult
                {
                    IsValid = false,
                    Errors = { new ValidationError
                    {
                        ErrorMessage = "MQTT主题层级长度过长，每级最多65535字符",
                        PropertyName = $"EventProcessor.AlarmConfiguration.{propertyName}"
                    }}
                });
                break;
            }
        }
    }
    
    /// <summary>
    /// 验证字段映射列表
    /// </summary>
    /// <param name="fieldMappings">字段映射列表</param>
    /// <param name="results">验证结果列表</param>
    private static void ValidateFieldMappings(List<FieldMapping>? fieldMappings, List<Models.ValidationResult> results)
    {
        if (fieldMappings == null || fieldMappings.Count == 0)
        {
            results.Add(new Models.ValidationResult
            {
                IsValid = false,
                Errors = { new ValidationError
                {
                    ErrorMessage = "字段映射列表不能为空，至少需要一个字段映射",
                    PropertyName = "EventProcessor.AlarmConfiguration.Fields"
                }}
            });
            return;
        }
        
        var alarmFieldNames = new HashSet<string>();
        
        for (int i = 0; i < fieldMappings.Count; i++)
        {
            var mapping = fieldMappings[i];
            var context = new ValidationContext(mapping);
            var mappingResults = new List<System.ComponentModel.DataAnnotations.ValidationResult>();
            
            if (!Validator.TryValidateObject(mapping, context, mappingResults, true))
            {
                // 添加索引信息到验证结果
                results.Add(new Models.ValidationResult
                {
                    IsValid = false,
                    Errors = mappingResults.Select(result => new ValidationError
                    {
                        ErrorMessage = result.ErrorMessage ?? "验证失败",
                        PropertyName = string.Join(", ", result.MemberNames.Select(name => $"EventProcessor.AlarmConfiguration.Fields[{i}].{name}"))
                    }).ToList()
                });
            }
            
            // 检查告警字段名称重复
            if (!string.IsNullOrWhiteSpace(mapping.AlarmFieldName))
            {
                if (alarmFieldNames.Contains(mapping.AlarmFieldName))
                {
                    results.Add(new Models.ValidationResult
                    {
                        IsValid = false,
                        Errors = { new ValidationError
                        {
                            ErrorMessage = $"告警字段名称 '{mapping.AlarmFieldName}' 重复",
                            PropertyName = $"EventProcessor.AlarmConfiguration.Fields[{i}].AlarmFieldName"
                        }}
                    });
                }
                else
                {
                    alarmFieldNames.Add(mapping.AlarmFieldName);
                }
            }
            
            // 验证源规则类型
            ValidateSourceRuleType(mapping.SourceRuleType, i, results);
        }
    }
    
    /// <summary>
    /// 验证自定义模板
    /// </summary>
    /// <param name="template">模板字符串</param>
    /// <param name="fieldMappings">字段映射列表</param>
    /// <param name="results">验证结果列表</param>
    private static void ValidateCustomTemplate(string? template, List<FieldMapping>? fieldMappings, List<Models.ValidationResult> results)
    {
        if (string.IsNullOrWhiteSpace(template))
        {
            return; // 允许为空
        }
        
        // 提取模板中的字段引用
        var fieldReferences = ExtractFieldReferences(template);
        
        if (fieldMappings != null)
        {
            var availableFields = fieldMappings.Select(m => m.AlarmFieldName).Where(name => !string.IsNullOrWhiteSpace(name)).ToHashSet();
            
            // 检查模板中引用的字段是否都在字段映射中定义
            foreach (var fieldRef in fieldReferences)
            {
                if (!availableFields.Contains(fieldRef))
                {
                    results.Add(new Models.ValidationResult
                    {
                        IsValid = false,
                        Errors = { new ValidationError
                        {
                            ErrorMessage = $"自定义模板中引用的字段 '{fieldRef}' 未在字段映射中定义",
                            PropertyName = "EventProcessor.AlarmConfiguration.CustomTemplate"
                        }}
                    });
                }
            }
        }
    }
    
    /// <summary>
    /// 验证源规则类型
    /// </summary>
    /// <param name="sourceRuleType">源规则类型</param>
    /// <param name="index">索引</param>
    /// <param name="results">验证结果列表</param>
    private static void ValidateSourceRuleType(string? sourceRuleType, int index, List<Models.ValidationResult> results)
    {
        if (string.IsNullOrWhiteSpace(sourceRuleType))
        {
            return;
        }
        
        var validTypes = new[] { "BusinessRule", "ExclusionRule", "DeviceSignal" };
        if (!validTypes.Contains(sourceRuleType))
        {
            results.Add(new Models.ValidationResult
            {
                IsValid = false,
                Errors = { new ValidationError
                {
                    ErrorMessage = $"源规则类型 '{sourceRuleType}' 无效，有效值为: {string.Join(", ", validTypes)}",
                    PropertyName = $"EventProcessor.AlarmConfiguration.Fields[{index}].SourceRuleType"
                }}
            });
        }
    }
    
    /// <summary>
    /// 验证日志级别
    /// </summary>
    /// <param name="logLevel">日志级别</param>
    /// <param name="propertyName">属性名称</param>
    /// <param name="results">验证结果列表</param>
    private static void ValidateLogLevel(string? logLevel, string propertyName, List<Models.ValidationResult> results)
    {
        if (string.IsNullOrWhiteSpace(logLevel))
        {
            return;
        }
        
        var validLevels = new[] { "Verbose", "Debug", "Information", "Warning", "Error", "Fatal" };
        if (!validLevels.Contains(logLevel))
        {
            results.Add(new Models.ValidationResult
            {
                IsValid = false,
                Errors = { new ValidationError
                {
                    ErrorMessage = $"日志级别 '{logLevel}' 无效，有效值为: {string.Join(", ", validLevels)}",
                    PropertyName = propertyName
                }}
            });
        }
    }
    
    /// <summary>
    /// 验证写入器配置
    /// </summary>
    /// <param name="writeTo">写入器配置列表</param>
    /// <param name="results">验证结果列表</param>
    private static void ValidateWriteToConfiguration(List<SerilogWriteTo>? writeTo, List<Models.ValidationResult> results)
    {
        if (writeTo == null || writeTo.Count == 0)
        {
            results.Add(new Models.ValidationResult
            {
                IsValid = false,
                Errors = { new ValidationError
                {
                    ErrorMessage = "日志写入器配置不能为空，至少需要一个写入器",
                    PropertyName = "Logging.WriteTo"
                }}
            });
            return;
        }
        
        for (int i = 0; i < writeTo.Count; i++)
        {
            var writer = writeTo[i];
            
            if (string.IsNullOrWhiteSpace(writer.Name))
            {
                results.Add(new Models.ValidationResult
                {
                    IsValid = false,
                    Errors = { new ValidationError
                    {
                        ErrorMessage = "写入器名称不能为空",
                        PropertyName = $"Logging.WriteTo[{i}].Name"
                    }}
                });
            }
            
            // 验证常见写入器类型
            ValidateWriterType(writer.Name, writer.Args, i, results);
        }
    }
    
    /// <summary>
    /// 验证写入器类型
    /// </summary>
    /// <param name="writerName">写入器名称</param>
    /// <param name="args">参数</param>
    /// <param name="index">索引</param>
    /// <param name="results">验证结果列表</param>
    private static void ValidateWriterType(string? writerName, Dictionary<string, object>? args, int index, List<Models.ValidationResult> results)
    {
        if (string.IsNullOrWhiteSpace(writerName))
        {
            return;
        }
        
        switch (writerName.ToLower())
        {
            case "file":
                if (args == null || !args.ContainsKey("path"))
                {
                    results.Add(new Models.ValidationResult
                    {
                        IsValid = false,
                        Errors = { new ValidationError
                        {
                            ErrorMessage = "File写入器必须指定path参数",
                            PropertyName = $"Logging.WriteTo[{index}].Args"
                        }}
                    });
                }
                break;
                
            case "console":
                // Console写入器通常不需要特殊参数
                break;
                
            case "debug":
                // Debug写入器通常不需要特殊参数
                break;
                
            default:
                // 其他写入器类型，暂不验证
                break;
        }
    }
    
    /// <summary>
    /// 提取模板中的字段引用
    /// </summary>
    /// <param name="template">模板字符串</param>
    /// <returns>字段引用列表</returns>
    private static List<string> ExtractFieldReferences(string template)
    {
        var fieldReferences = new List<string>();
        var pattern = @"\{([^}]+)\}";
        var matches = Regex.Matches(template, pattern);
        
        foreach (Match match in matches)
        {
            if (match.Groups.Count > 1)
            {
                fieldReferences.Add(match.Groups[1].Value);
            }
        }
        
        return fieldReferences.Distinct().ToList();
    }
    
    /// <summary>
    /// 验证主机名格式
    /// </summary>
    /// <param name="hostname">主机名</param>
    /// <returns>是否有效</returns>
    private static bool IsValidHostname(string hostname)
    {
        if (string.IsNullOrWhiteSpace(hostname))
        {
            return false;
        }
        
        // 检查是否为IP地址
        if (System.Net.IPAddress.TryParse(hostname, out _))
        {
            return true;
        }
        
        // 检查主机名格式
        var hostnamePattern = @"^[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?)*$";
        return Regex.IsMatch(hostname, hostnamePattern) && hostname.Length <= 253;
    }
}