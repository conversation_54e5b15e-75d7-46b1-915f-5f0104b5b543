using System.ComponentModel.DataAnnotations;
using YamlDotNet.Serialization;
using EventProcessor.Core.Models;

namespace EPConfigToolV2.Models;

/// <summary>
/// 统一配置模型，对应appsettings.yaml的完整结构
/// </summary>
public class UnifiedConfigurationModel
{
    /// <summary>
    /// 事件处理器配置
    /// </summary>
    [Required(ErrorMessage = "EventProcessor配置不能为空")]
    [YamlMember(Alias = "EventProcessor")]
    public required EventConfiguration EventProcessor { get; set; }

    /// <summary>
    /// MQTT配置
    /// </summary>
    [Required(ErrorMessage = "MQTT配置不能为空")]
    [YamlMember(Alias = "Mqtt")]
    public required MqttConfiguration Mqtt { get; set; }

    /// <summary>
    /// 错误处理配置
    /// </summary>
    [YamlMember(Alias = "ErrorHandling")]
    public ErrorHandlingConfiguration ErrorHandling { get; set; } = new();

    /// <summary>
    /// 日志配置
    /// </summary>
    [YamlMember(Alias = "Logging")]
    public required LoggingConfiguration Logging { get; set; }

    /// <summary>
    /// Serilog配置
    /// </summary>
    [YamlMember(Alias = "Serilog")]
    public required LoggingConfiguration Serilog { get; set; }
}

/// <summary>
/// 验证结果模型
/// </summary>
public class ValidationResult
{
    public bool IsValid { get; set; }
    public List<ValidationError> Errors { get; set; } = new();
    public List<ValidationWarning> Warnings { get; set; } = new();
}

/// <summary>
/// 验证错误模型
/// </summary>
public class ValidationError
{
    public string PropertyName { get; set; } = string.Empty;
    public string ErrorMessage { get; set; } = string.Empty;
    public string TabName { get; set; } = string.Empty;
    public object? AttemptedValue { get; set; }
}

/// <summary>
/// 验证警告模型
/// </summary>
public class ValidationWarning
{
    public string PropertyName { get; set; } = string.Empty;
    public string WarningMessage { get; set; } = string.Empty;
    public string TabName { get; set; } = string.Empty;
}

/// <summary>
/// 表单控件映射
/// </summary>
public class FormControlMapping
{
    public string PropertyName { get; set; } = string.Empty;
    public Control? FormControl { get; set; }
    public string TabPageName { get; set; } = string.Empty;
    public bool IsRequired { get; set; }
    public string ValidationGroup { get; set; } = string.Empty;
}

/// <summary>
/// Tab页配置
/// </summary>
public class TabPageConfiguration
{
    public string TabName { get; set; } = string.Empty;
    public string DisplayName { get; set; } = string.Empty;
    public List<FormControlMapping> Controls { get; set; } = new();
    public int DisplayOrder { get; set; }
}

/// <summary>
/// AlarmConfig Tab页配置
/// </summary>
public class AlarmConfigTabConfiguration : TabPageConfiguration
{
    public DataGridView? FieldsDataGridView { get; set; }
    public Button? AddFieldButton { get; set; }
    public Button? RemoveFieldButton { get; set; }
    public TextBox? CustomAlarmTopicTextBox { get; set; }
    public TextBox? CustomAlarmCancellationTopicTextBox { get; set; }
    public TextBox? CustomTemplateTextBox { get; set; }
}

/// <summary>
/// 获取默认配置模板
/// </summary>
public static class ConfigurationDefaults
{
    public static UnifiedConfigurationModel GetDefaultConfiguration()
    {
        return new UnifiedConfigurationModel
        {
            EventProcessor = new EventConfiguration
            {
                EventId = "EV000000",
                EventName = "新建事件",
                EvaluationStrategy = "BusinessOnly",
                Priority = "P3",
                CommId = "COMM001",
                PositionId = "POS001",
                CompanyName = "hq",
                AlarmGracePeriodSeconds = 3,
                EnableAlarmCancellation = true,
                CustomTimeWindowMinutes = 5,
                RuleConfiguration = new RuleConfiguration
                {
                    AlarmConfig = new AlarmConfiguration
                    {
                        CustomAlarmTopic = "",
                        CustomAlarmCancellationTopic = "",
                        CustomTemplate = "",
                        Fields = new List<FieldMapping>
                        {
                            new FieldMapping
                            {
                                AlarmFieldName = "详情",
                                SourceRuleType = "BusinessRules",
                                SourceFieldName = "CardType,log_car_no,log_user_name,log_end_time",
                                FormatTemplate = "[{CardType}][{log_car_no}][{log_user_name}][{log_end_time}]"
                            },
                            new FieldMapping
                            {
                                AlarmFieldName = "事件",
                                SourceRuleType = "DeviceSignal",
                                SourceFieldName = "duration",
                                FormatTemplate = "停留时间{duration}秒"
                            },
                            new FieldMapping
                            {
                                AlarmFieldName = "设备",
                                SourceRuleType = "DeviceSignal",
                                SourceFieldName = "device_name",
                                DefaultValue = "帮豆你门岗智能监测"
                            },
                            new FieldMapping
                            {
                                AlarmFieldName = "名称",
                                SourceRuleType = "DeviceSignal",
                                SourceFieldName = "event_name",
                                DefaultValue = "月租车超时滞留出口（卡未过期）"
                            },
                            new FieldMapping
                            {
                                AlarmFieldName = "等级",
                                SourceRuleType = "DeviceSignal",
                                SourceFieldName = "level",
                                DefaultValue = "通知"
                            }
                        }
                    }
                },
                AlarmConfiguration = new AlarmConfiguration
                {
                    CustomAlarmTopic = "",
                    CustomAlarmCancellationTopic = "",
                    CustomTemplate = "",
                    Fields = new List<FieldMapping>
                    {
                        new FieldMapping
                        {
                            AlarmFieldName = "详情",
                            SourceRuleType = "BusinessRules",
                            SourceFieldName = "CardType,log_car_no,log_user_name,log_end_time",
                            FormatTemplate = "[{CardType}][{log_car_no}][{log_user_name}][{log_end_time}]"
                        },
                        new FieldMapping
                        {
                            AlarmFieldName = "事件",
                            SourceRuleType = "DeviceSignal",
                            SourceFieldName = "duration",
                            FormatTemplate = "停留时间{duration}秒"
                        },
                        new FieldMapping
                        {
                            AlarmFieldName = "设备",
                            SourceRuleType = "DeviceSignal",
                            SourceFieldName = "device_name",
                            DefaultValue = "帮豆你门岗智能监测"
                        },
                        new FieldMapping
                        {
                            AlarmFieldName = "名称",
                            SourceRuleType = "DeviceSignal",
                            SourceFieldName = "event_name",
                            DefaultValue = "月租车超时滞留出口（卡未过期）"
                        },
                        new FieldMapping
                        {
                            AlarmFieldName = "等级",
                            SourceRuleType = "DeviceSignal",
                            SourceFieldName = "level",
                            DefaultValue = "通知"
                        }
                    }
                }
            },
            Mqtt = new MqttConfiguration
            {
                BrokerHost = "localhost",
                BrokerPort = 1883,
                ClientId = "EPConfigTool",
                KeepAliveInterval = 60,
                ReconnectInterval = 5,
                QualityOfServiceLevel = 1
            },
            Logging = new LoggingConfiguration
            {
                WriteTo = new List<SerilogWriteTo>
                {
                    new SerilogWriteTo
                    {
                        Name = "Console",
                        RestrictedToMinimumLevel = "Information"
                    },
                    new SerilogWriteTo
                    {
                        Name = "File",
                        Args = new Dictionary<string, object>
                        {
                            ["path"] = "logs/app-.log",
                            ["rollingInterval"] = "Day",
                            ["retainedFileCountLimit"] = 30
                        },
                        RestrictedToMinimumLevel = "Information"
                    }
                }
            },
            Serilog = new LoggingConfiguration
            {
                WriteTo = new List<SerilogWriteTo>
                {
                    new SerilogWriteTo
                    {
                        Name = "Console",
                        RestrictedToMinimumLevel = "Information"
                    },
                    new SerilogWriteTo
                    {
                        Name = "File",
                        Args = new Dictionary<string, object>
                        {
                            ["path"] = "logs/serilog-.log",
                            ["rollingInterval"] = "Day",
                            ["retainedFileCountLimit"] = 30
                        },
                        RestrictedToMinimumLevel = "Information"
                    }
                }
            }
        };
    }
}