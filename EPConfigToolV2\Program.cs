using EPConfigToolV2.Forms;

namespace EPConfigToolV2;

/// <summary>
/// 应用程序入口点
/// </summary>
internal static class Program
{
    /// <summary>
    /// 应用程序的主入口点
    /// </summary>
    [STAThread]
    static void Main()
    {
        // 启用应用程序视觉样式
        Application.EnableVisualStyles();
        Application.SetCompatibleTextRenderingDefault(false);
        
        // 设置高DPI支持
        Application.SetHighDpiMode(HighDpiMode.SystemAware);
        
        // 设置未处理异常处理器
        Application.SetUnhandledExceptionMode(UnhandledExceptionMode.CatchException);
        Application.ThreadException += OnThreadException;
        AppDomain.CurrentDomain.UnhandledException += OnUnhandledException;
        
        try
        {
            // 启动主窗口
            using var mainForm = new MainForm();
            Application.Run(mainForm);
        }
        catch (Exception ex)
        {
            ShowErrorMessage("应用程序启动失败", ex);
        }
    }
    
    /// <summary>
    /// 处理UI线程异常
    /// </summary>
    private static void OnThreadException(object sender, ThreadExceptionEventArgs e)
    {
        ShowErrorMessage("应用程序发生未处理的异常", e.Exception);
    }
    
    /// <summary>
    /// 处理非UI线程异常
    /// </summary>
    private static void OnUnhandledException(object sender, UnhandledExceptionEventArgs e)
    {
        if (e.ExceptionObject is Exception ex)
        {
            ShowErrorMessage("应用程序发生严重错误", ex);
        }
    }
    
    /// <summary>
    /// 显示错误消息
    /// </summary>
    private static void ShowErrorMessage(string title, Exception exception)
    {
        var message = $"错误信息: {exception.Message}\n\n" +
                     $"异常类型: {exception.GetType().Name}\n\n" +
                     $"堆栈跟踪:\n{exception.StackTrace}";
        
        MessageBox.Show(
            message,
            title,
            MessageBoxButtons.OK,
            MessageBoxIcon.Error);
    }
}