using System.ComponentModel.DataAnnotations;
using System.Text;
using EPConfigToolV2.Models;
using EventProcessor.Core.Models;
using YamlDotNet.Serialization;
using YamlDotNet.Serialization.NamingConventions;

namespace EPConfigToolV2.Services;

/// <summary>
/// 配置服务，负责YAML配置文件的加载、保存、验证等操作
/// </summary>
public class ConfigurationService
{
    private readonly ISerializer _yamlSerializer;
    private readonly IDeserializer _yamlDeserializer;
    private UnifiedConfigurationModel? _currentConfiguration;
    private string? _currentFilePath;

    /// <summary>
    /// 配置变更事件
    /// </summary>
    public event EventHandler<ConfigurationChangedEventArgs>? ConfigurationChanged;

    /// <summary>
    /// 验证错误事件
    /// </summary>
    public event EventHandler<ValidationErrorEventArgs>? ValidationError;

    /// <summary>
    /// 字段映射变更事件
    /// </summary>
    public event EventHandler<List<FieldMapping>>? FieldMappingsChanged;

    public ConfigurationService()
    {
        // 配置YAML序列化器
        _yamlSerializer = new SerializerBuilder()
            .WithNamingConvention(CamelCaseNamingConvention.Instance)
            .WithIndentedSequences()
            .Build();

        // 配置YAML反序列化器
        _yamlDeserializer = new DeserializerBuilder()
            .WithNamingConvention(CamelCaseNamingConvention.Instance)
            .IgnoreUnmatchedProperties()
            .Build();
    }

    /// <summary>
    /// 当前配置
    /// </summary>
    public UnifiedConfigurationModel? CurrentConfiguration => _currentConfiguration;

    /// <summary>
    /// 当前文件路径
    /// </summary>
    public string? CurrentFilePath => _currentFilePath;

    /// <summary>
    /// 配置是否已修改
    /// </summary>
    public bool IsModified { get; private set; }

    /// <summary>
    /// 创建新的配置
    /// </summary>
    /// <returns>新的配置实例</returns>
    public UnifiedConfigurationModel CreateNewConfiguration()
    {
        _currentConfiguration = ConfigurationDefaults.GetDefaultConfiguration();
        _currentFilePath = null;
        IsModified = false;
        
        OnConfigurationChanged(new ConfigurationChangedEventArgs(_currentConfiguration, ConfigurationChangeType.Created));
        return _currentConfiguration;
    }

    /// <summary>
    /// 从文件加载配置
    /// </summary>
    /// <param name="filePath">文件路径</param>
    /// <returns>加载的配置实例</returns>
    /// <exception cref="FileNotFoundException">文件不存在</exception>
    /// <exception cref="InvalidOperationException">文件格式错误</exception>
    public async Task<UnifiedConfigurationModel> LoadConfigurationAsync(string filePath)
    {
        if (!File.Exists(filePath))
        {
            throw new FileNotFoundException($"配置文件不存在: {filePath}");
        }

        try
        {
            var yamlContent = await File.ReadAllTextAsync(filePath, Encoding.UTF8);
            
            if (string.IsNullOrWhiteSpace(yamlContent))
            {
                throw new InvalidOperationException("配置文件内容为空");
            }

            _currentConfiguration = _yamlDeserializer.Deserialize<UnifiedConfigurationModel>(yamlContent);
            _currentFilePath = filePath;
            IsModified = false;

            // 验证配置
            var validationResults = ValidateConfiguration(_currentConfiguration);
            if (validationResults.Any())
            {
                OnValidationError(new ValidationErrorEventArgs(validationResults));
            }

            OnConfigurationChanged(new ConfigurationChangedEventArgs(_currentConfiguration, ConfigurationChangeType.Loaded));
            return _currentConfiguration;
        }
        catch (Exception ex) when (!(ex is FileNotFoundException))
        {
            throw new InvalidOperationException($"加载配置文件失败: {ex.Message}", ex);
        }
    }

    /// <summary>
    /// 保存配置到文件
    /// </summary>
    /// <param name="filePath">文件路径，如果为null则使用当前文件路径</param>
    /// <returns>保存操作的任务</returns>
    /// <exception cref="InvalidOperationException">没有配置可保存或文件路径为空</exception>
    public async Task SaveConfigurationAsync(string? filePath = null)
    {
        if (_currentConfiguration == null)
        {
            throw new InvalidOperationException("没有配置可保存");
        }

        var targetPath = filePath ?? _currentFilePath;
        if (string.IsNullOrEmpty(targetPath))
        {
            throw new InvalidOperationException("文件路径不能为空");
        }

        try
        {
            // 验证配置
            var validationResults = ValidateConfiguration(_currentConfiguration);
            if (validationResults.Any(r => !r.IsValid))
            {
                var errorMessages = validationResults
                    .Where(r => !r.IsValid)
                    .SelectMany(r => r.Errors.Select(e => e.ErrorMessage));
                var errorMessage = string.Join("\n", errorMessages);
                throw new InvalidOperationException($"配置验证失败:\n{errorMessage}");
            }

            // 确保目录存在
            var directory = Path.GetDirectoryName(targetPath);
            if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
            {
                Directory.CreateDirectory(directory);
            }

            // 序列化并保存
            var yamlContent = _yamlSerializer.Serialize(_currentConfiguration);
            await File.WriteAllTextAsync(targetPath, yamlContent, Encoding.UTF8);

            _currentFilePath = targetPath;
            IsModified = false;

            OnConfigurationChanged(new ConfigurationChangedEventArgs(_currentConfiguration, ConfigurationChangeType.Saved));
        }
        catch (Exception ex)
        {
            throw new InvalidOperationException($"保存配置文件失败: {ex.Message}", ex);
        }
    }

    /// <summary>
    /// 更新配置
    /// </summary>
    /// <param name="configuration">新的配置</param>
    public void UpdateConfiguration(UnifiedConfigurationModel configuration)
    {
        _currentConfiguration = configuration ?? throw new ArgumentNullException(nameof(configuration));
        IsModified = true;

        OnConfigurationChanged(new ConfigurationChangedEventArgs(_currentConfiguration, ConfigurationChangeType.Modified));
    }

    /// <summary>
    /// 验证配置
    /// </summary>
    /// <param name="configuration">要验证的配置</param>
    /// <returns>验证结果列表</returns>
    public List<Models.ValidationResult> ValidateConfiguration(UnifiedConfigurationModel configuration)
    {
        var dataAnnotationResults = new List<System.ComponentModel.DataAnnotations.ValidationResult>();
        var context = new ValidationContext(configuration);

        // 验证根对象
        Validator.TryValidateObject(configuration, context, dataAnnotationResults, true);

        // 验证嵌套对象
        ValidateNestedObject(configuration.EventProcessor, "EventProcessor", dataAnnotationResults);
        ValidateNestedObject(configuration.Mqtt, "Mqtt", dataAnnotationResults);
        ValidateNestedObject(configuration.ErrorHandling, "ErrorHandling", dataAnnotationResults);
        ValidateNestedObject(configuration.Serilog, "Serilog", dataAnnotationResults);

        // 验证业务规则条件组
        if (configuration.EventProcessor.RuleConfiguration?.BusinessRules != null)
        {
            for (int i = 0; i < configuration.EventProcessor.RuleConfiguration.BusinessRules.Length; i++)
            {
                ValidateNestedObject(configuration.EventProcessor.RuleConfiguration.BusinessRules[i], $"RuleConfiguration.BusinessRules[{i}]", dataAnnotationResults);
                if (configuration.EventProcessor.RuleConfiguration.BusinessRules[i].ConditionGroups != null)
                {
                    for (int j = 0; j < configuration.EventProcessor.RuleConfiguration.BusinessRules[i].ConditionGroups.Length; j++)
                    {
                        ValidateNestedObject(configuration.EventProcessor.RuleConfiguration.BusinessRules[i].ConditionGroups[j], $"RuleConfiguration.BusinessRules[{i}].ConditionGroups[{j}]", dataAnnotationResults);
                    }
                }
            }
        }
        if (configuration.EventProcessor.RuleConfiguration?.ExclusionRules != null)
        {
            for (int i = 0; i < configuration.EventProcessor.RuleConfiguration.ExclusionRules.Length; i++)
            {
                ValidateNestedObject(configuration.EventProcessor.RuleConfiguration.ExclusionRules[i], $"RuleConfiguration.ExclusionRules[{i}]", dataAnnotationResults);
                if (configuration.EventProcessor.RuleConfiguration.ExclusionRules[i].ConditionGroups != null)
                {
                    for (int j = 0; j < configuration.EventProcessor.RuleConfiguration.ExclusionRules[i].ConditionGroups.Length; j++)
                    {
                        ValidateNestedObject(configuration.EventProcessor.RuleConfiguration.ExclusionRules[i].ConditionGroups[j], $"RuleConfiguration.ExclusionRules[{i}].ConditionGroups[{j}]", dataAnnotationResults);
                    }
                }
            }
        }

        // 转换为自定义ValidationResult格式
        var results = new List<Models.ValidationResult>();
        if (dataAnnotationResults.Count > 0)
        {
            var validationResult = new Models.ValidationResult
            {
                IsValid = false,
                Errors = dataAnnotationResults.Select(r => new ValidationError
                {
                    PropertyName = string.Join(", ", r.MemberNames),
                    ErrorMessage = r.ErrorMessage ?? "未知验证错误"
                }).ToList()
            };
            results.Add(validationResult);
        }
        else
        {
            results.Add(new Models.ValidationResult { IsValid = true });
        }

        return results;
    }

    /// <summary>
    /// 验证嵌套对象
    /// </summary>
    private void ValidateNestedObject(object obj, string propertyPath, List<System.ComponentModel.DataAnnotations.ValidationResult> results)
    {
        if (obj == null) return;

        var context = new ValidationContext(obj);
        var nestedResults = new List<System.ComponentModel.DataAnnotations.ValidationResult>();
        
        Validator.TryValidateObject(obj, context, nestedResults, true);
        
        // 添加属性路径前缀
        foreach (var result in nestedResults)
        {
            var memberNames = result.MemberNames.Select(name => $"{propertyPath}.{name}").ToArray();
            results.Add(new System.ComponentModel.DataAnnotations.ValidationResult(result.ErrorMessage, memberNames));
        }
    }



    /// <summary>
    /// 获取配置的YAML字符串表示
    /// </summary>
    /// <param name="configuration">配置对象</param>
    /// <returns>YAML字符串</returns>
    public string SerializeToYaml(UnifiedConfigurationModel configuration)
    {
        return _yamlSerializer.Serialize(configuration);
    }

    /// <summary>
    /// 从YAML字符串反序列化配置
    /// </summary>
    /// <param name="yamlContent">YAML内容</param>
    /// <returns>配置对象</returns>
    public UnifiedConfigurationModel DeserializeFromYaml(string yamlContent)
    {
        return _yamlDeserializer.Deserialize<UnifiedConfigurationModel>(yamlContent);
    }

    /// <summary>
    /// 获取字段映射列表
    /// </summary>
    /// <returns>字段映射列表</returns>
    public List<FieldMapping> GetFieldMappings()
    {
        return CurrentConfiguration?.EventProcessor?.RuleConfiguration?.AlarmConfig?.Fields ?? new List<FieldMapping>();
    }

    /// <summary>
    /// 更新字段映射
    /// </summary>
    /// <param name="fieldMappings">新的字段映射列表</param>
    public void UpdateFieldMappings(List<FieldMapping> fieldMappings)
    {
        if (CurrentConfiguration?.EventProcessor?.RuleConfiguration?.AlarmConfig != null)
        {
            // 由于Fields是init-only属性，需要创建新的AlarmConfiguration对象
            var currentAlarmConfig = CurrentConfiguration.EventProcessor.RuleConfiguration.AlarmConfig;
            var newAlarmConfig = new AlarmConfiguration
            {
                Fields = fieldMappings
            };
            
            // 创建新的RuleConfiguration
            var newRuleConfig = new RuleConfiguration
            {
                AlarmConfig = newAlarmConfig,
                BusinessRules = CurrentConfiguration.EventProcessor.RuleConfiguration.BusinessRules,
                ExclusionRules = CurrentConfiguration.EventProcessor.RuleConfiguration.ExclusionRules
            };
            
            // 创建新的EventConfiguration
            var newEventConfig = new EventConfiguration
            {
                EventId = CurrentConfiguration.EventProcessor.EventId,
                EventName = CurrentConfiguration.EventProcessor.EventName,
                EvaluationStrategy = CurrentConfiguration.EventProcessor.EvaluationStrategy,
                Priority = CurrentConfiguration.EventProcessor.Priority,
                CommId = CurrentConfiguration.EventProcessor.CommId,
                PositionId = CurrentConfiguration.EventProcessor.PositionId,
                CompanyName = CurrentConfiguration.EventProcessor.CompanyName,
                AIPrompt = CurrentConfiguration.EventProcessor.AIPrompt,
                AIAnalysisDelaySec = CurrentConfiguration.EventProcessor.AIAnalysisDelaySec,
                ImageCropCoordinates = CurrentConfiguration.EventProcessor.ImageCropCoordinates,
                DeviceSignal = CurrentConfiguration.EventProcessor.DeviceSignal,
                RuleConfiguration = newRuleConfig,
                AlarmGracePeriodSeconds = CurrentConfiguration.EventProcessor.AlarmGracePeriodSeconds,
                EnableAlarmCancellation = CurrentConfiguration.EventProcessor.EnableAlarmCancellation,
                CustomAlarmTopic = CurrentConfiguration.EventProcessor.CustomAlarmTopic,
                CustomAlarmCancellationTopic = CurrentConfiguration.EventProcessor.CustomAlarmCancellationTopic,
                CorrelationTimeWindow = CurrentConfiguration.EventProcessor.CorrelationTimeWindow,
                CustomTimeWindowMinutes = CurrentConfiguration.EventProcessor.CustomTimeWindowMinutes,
                AlarmConfiguration = CurrentConfiguration.EventProcessor.AlarmConfiguration
            };
            
            // 创建新的UnifiedConfigurationModel
            var newConfig = new UnifiedConfigurationModel
            {
                EventProcessor = newEventConfig,
                Mqtt = CurrentConfiguration.Mqtt,
                Logging = CurrentConfiguration.Logging,
                Serilog = CurrentConfiguration.Serilog
            };
            
            UpdateConfiguration(newConfig);
            FieldMappingsChanged?.Invoke(this, fieldMappings);
        }
    }

    /// <summary>
    /// 添加字段映射
    /// </summary>
    /// <param name="fieldMapping">要添加的字段映射</param>
    public void AddFieldMapping(FieldMapping fieldMapping)
    {
        if (CurrentConfiguration?.EventProcessor?.RuleConfiguration?.AlarmConfig != null)
        {
            CurrentConfiguration.EventProcessor.RuleConfiguration.AlarmConfig.Fields.Add(fieldMapping);
            IsModified = true;
            FieldMappingsChanged?.Invoke(this, GetFieldMappings());
        }
    }

    /// <summary>
    /// 删除字段映射
    /// </summary>
    /// <param name="index">要删除的索引</param>
    public void RemoveFieldMapping(int index)
    {
        var fieldMappings = GetFieldMappings();
        if (index >= 0 && index < fieldMappings.Count)
        {
            fieldMappings.RemoveAt(index);
            IsModified = true;
            FieldMappingsChanged?.Invoke(this, fieldMappings);
        }
    }

    /// <summary>
    /// 验证自定义MQTT主题格式
    /// </summary>
    /// <param name="topic">主题字符串</param>
    /// <returns>验证结果</returns>
    public Models.ValidationResult ValidateCustomMqttTopic(string topic)
    {
        if (string.IsNullOrWhiteSpace(topic))
        {
            return new Models.ValidationResult { IsValid = false, Errors = { new ValidationError { ErrorMessage = "自定义MQTT主题不能为空" } } };
        }

        // 检查主题格式：允许字母、数字、斜杠、连字符、下划线和花括号
        var topicPattern = @"^[a-zA-Z0-9/_\-{}]+$";
        if (!System.Text.RegularExpressions.Regex.IsMatch(topic, topicPattern))
        {
            return new Models.ValidationResult { IsValid = false, Errors = { new ValidationError { ErrorMessage = "自定义MQTT主题格式无效，只允许字母、数字、斜杠、连字符、下划线和花括号" } } };
        }

        return new Models.ValidationResult { IsValid = true };
    }

    /// <summary>
    /// 验证字段映射
    /// </summary>
    /// <param name="fieldMapping">字段映射</param>
    /// <returns>验证结果列表</returns>
    public List<System.ComponentModel.DataAnnotations.ValidationResult> ValidateFieldMapping(FieldMapping fieldMapping)
    {
        var results = new List<System.ComponentModel.DataAnnotations.ValidationResult>();
        var context = new ValidationContext(fieldMapping);
        
        Validator.TryValidateObject(fieldMapping, context, results, true);
        
        return results;
    }

    /// <summary>
    /// 重置配置服务状态
    /// </summary>
    public void Reset()
    {
        _currentConfiguration = null;
        _currentFilePath = null;
        IsModified = false;
    }

    /// <summary>
    /// 触发配置变更事件
    /// </summary>
    protected virtual void OnConfigurationChanged(ConfigurationChangedEventArgs e)
    {
        ConfigurationChanged?.Invoke(this, e);
    }

    /// <summary>
    /// 触发验证错误事件
    /// </summary>
    protected virtual void OnValidationError(ValidationErrorEventArgs e)
    {
        ValidationError?.Invoke(this, e);
    }
}

/// <summary>
/// 配置变更事件参数
/// </summary>
public class ConfigurationChangedEventArgs : EventArgs
{
    public UnifiedConfigurationModel Configuration { get; }
    public ConfigurationChangeType ChangeType { get; }
    public DateTime Timestamp { get; }

    public ConfigurationChangedEventArgs(UnifiedConfigurationModel configuration, ConfigurationChangeType changeType)
    {
        Configuration = configuration;
        ChangeType = changeType;
        Timestamp = DateTime.Now;
    }
}

/// <summary>
/// 字段映射变更事件参数
/// </summary>
public class FieldMappingChangedEventArgs : EventArgs
{
    public List<FieldMapping> FieldMappings { get; }
    public DateTime Timestamp { get; }

    public FieldMappingChangedEventArgs(List<FieldMapping> fieldMappings)
    {
        FieldMappings = fieldMappings;
        Timestamp = DateTime.Now;
    }
}

/// <summary>
/// 验证错误事件参数
/// </summary>
public class ValidationErrorEventArgs : EventArgs
{
    public List<Models.ValidationResult> ValidationResults { get; }
    public DateTime Timestamp { get; }

    public ValidationErrorEventArgs(List<Models.ValidationResult> validationResults)
    {
        ValidationResults = validationResults;
        Timestamp = DateTime.Now;
    }
}

/// <summary>
/// 配置变更类型
/// </summary>
public enum ConfigurationChangeType
{
    /// <summary>
    /// 创建新配置
    /// </summary>
    Created,
    
    /// <summary>
    /// 加载配置
    /// </summary>
    Loaded,
    
    /// <summary>
    /// 修改配置
    /// </summary>
    Modified,
    
    /// <summary>
    /// 保存配置
    /// </summary>
    Saved
}