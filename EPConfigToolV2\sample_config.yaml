EventProcessor:
  EventId: "EV000001"
  EventName: "设备状态异常事件"
  EvaluationStrategy: "AIAndBusiness"
  Priority: 5
  DeviceSignal:
    TriggerField: "status"
    TriggerValues:
      - "alarm"
      - "error"
    TimeoutSeconds: 60
    DeviceIdField: "deviceId"
  BusinessRules:
    LogicOperator: "AND"
    ConditionGroups:
      - Field: "temperature"
        Operator: "greaterThan"
        Value: "80"
      - Field: "pressure"
        Operator: "lessThan"
        Value: "10"
  ExclusionRules:
    LogicOperator: "OR"
    ConditionGroups:
      - Field: "maintenance_mode"
        Operator: "equals"
        Value: "true"
      - Field: "device_status"
        Operator: "equals"
        Value: "offline"
  AlarmConfig:
    CustomAlarmTopic: "alarm/{deviceId}/{eventId}"
    FormatTemplate: "设备 {deviceId} 触发事件 {eventId}: {message}"
    FieldMappings:
      deviceId: "device_id"
      eventId: "event_id"
      timestamp: "ts"
      message: "msg"
    EnableCustomTopic: true
  AlarmSilencePeriodMinutes: 30
  AIAnalysisDelaySeconds: 5
  IsEnabled: true

MQTT:
  BrokerHost: "localhost"
  BrokerPort: 1883
  ClientId: "EPConfigTool"
  Username: null
  Password: null
  QoSLevel: 1
  KeepAliveSeconds: 60

ErrorHandling:
  MaxRetryAttempts: 3
  RetryDelayMs: 1000
  FallbackStrategy: "LogAndContinue"
  EnableVerboseLogging: true

Serilog:
  MinimumLevel:
    Default: "Information"
    Override:
      Microsoft: "Warning"
      System: "Warning"
  WriteTo:
    - Name: "Console"
      Args:
        outputTemplate: "[{Timestamp:HH:mm:ss} {Level:u3}] {Message:lj} {Properties:j}{NewLine}{Exception}"
    - Name: "File"
      Args:
        path: "logs/epconfig-.log"
        rollingInterval: "Day"
        retainedFileCountLimit: 7
        outputTemplate: "[{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} {Level:u3}] [{MachineName}] [{ThreadId}] {Message:lj} {Properties:j}{NewLine}{Exception}"
  Enrich:
    - "FromLogContext"
    - "WithMachineName"
    - "WithThreadId"