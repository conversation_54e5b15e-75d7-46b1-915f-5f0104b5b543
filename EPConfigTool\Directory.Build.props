<Project>

  <PropertyGroup>
    <Product>EPConfigTool V4.1</Product>
    <Company>Event Processor Team</Company>
    <Copyright>Copyright © 2025 Event Processor Team. All rights reserved.</Copyright>
    <Authors>Event Processor Team</Authors>
    <Description>事件处理器配置工具 V4.1 - 基于EP_V4.1的图形化配置工具</Description>
    <PackageProjectUrl>https://github.com/event-processor/epconfigtool-v4.1</PackageProjectUrl>
    <RepositoryUrl>https://github.com/event-processor/epconfigtool-v4.1</RepositoryUrl>
    <RepositoryType>git</RepositoryType>
    <PackageLicenseExpression>MIT</PackageLicenseExpression>
    <PackageRequireLicenseAcceptance>false</PackageRequireLicenseAcceptance>
    <PackageReleaseNotes>EPConfigTool V4.1 - 基于EP_V4.1的WPF图形化配置工具</PackageReleaseNotes>
    <PackageTags>event-processor;configuration-tool;wpf;yaml;gui</PackageTags>
  </PropertyGroup>

  <PropertyGroup>
    <TargetFramework>net8.0-windows</TargetFramework>
    <LangVersion>latest</LangVersion>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <UseWPF>true</UseWPF>
    <TreatWarningsAsErrors>false</TreatWarningsAsErrors>
    <WarningsAsErrors />
    <WarningsNotAsErrors>CS1591</WarningsNotAsErrors>
  </PropertyGroup>

  <PropertyGroup>
    <AssemblyTitle>事件处理器配置工具 V4.1</AssemblyTitle>
    <AssemblyDescription>基于EP_V4.1的图形化配置工具</AssemblyDescription>
    <AssemblyCompany>Event Processor Team</AssemblyCompany>
    <AssemblyProduct>EPConfigTool</AssemblyProduct>
    <AssemblyVersion>*******</AssemblyVersion>
    <FileVersion>*******</FileVersion>
    <InformationalVersion>4.1.0</InformationalVersion>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)' == 'Debug'">
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <DebugType>full</DebugType>
    <DebugSymbols>true</DebugSymbols>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)' == 'Release'">
    <DefineConstants>TRACE</DefineConstants>
    <DebugType>pdbonly</DebugType>
    <DebugSymbols>true</DebugSymbols>
    <Optimize>true</Optimize>
  </PropertyGroup>

  <!-- 发布配置 -->
  <PropertyGroup Condition="'$(Configuration)' == 'Release'">
    <PublishSingleFile>true</PublishSingleFile>
    <SelfContained>false</SelfContained>
    <RuntimeIdentifier>win-x64</RuntimeIdentifier>
    <UseAppHost>true</UseAppHost>
  </PropertyGroup>

</Project>