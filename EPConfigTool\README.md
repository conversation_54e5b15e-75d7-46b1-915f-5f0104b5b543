# EPConfigTool - 事件处理器配置工具 v4.1

EPConfigTool 是专门为 EP_V4.1 版本设计的图形化配置工具，支持 YAML 格式的事件配置文件的创建、编辑和管理。

## 功能特性

### 🎯 专门针对 YAML 格式优化
- **仅支持 YAML 格式**：专门处理 .yaml 和 .yml 文件
- **YamlDotNet 集成**：使用业界标准的 YAML 处理库
- **格式验证**：实时验证 YAML 格式和数据结构
- **错误提示**：详细的错误信息和修复建议

### 📋 完整的配置管理
- **事件基本信息**：事件ID、名称、评估策略、优先级等
- **设备信号配置**：MQTT主题、触发字段、值映射等
- **规则系统**：排除规则、业务规则、AI规则的完整支持
- **告警配置**：字段映射、格式化模板、默认值设置

### 🎨 直观的用户界面
- **MVVM 架构**：清晰的数据绑定和命令模式
- **标签页设计**：分类组织不同配置区域
- **实时验证**：输入时即时验证和错误提示
- **嵌套逻辑**：支持复杂的条件组合和嵌套

## 系统要求

- .NET 8.0 或更高版本
- Windows 操作系统
- 至少 4GB 内存
- 1024x768 或更高分辨率显示器

## 安装和运行

### 从源码构建

```bash
# 克隆项目
git clone <repository-url>
cd EPConfigTool

# 还原依赖
dotnet restore

# 构建项目
dotnet build

# 运行应用程序
dotnet run --project EPConfigTool
```

### 发布独立应用

```bash
# 发布为独立可执行文件
dotnet publish EPConfigTool -c Release -r win-x64 --self-contained true

# 运行发布的应用程序
./EPConfigTool/bin/Release/net8.0-windows/win-x64/publish/EPConfigTool.exe
```

## 使用指南

### 1. 创建新配置

1. 启动 EPConfigTool
2. 点击工具栏的"新建"按钮
3. 在"基本信息"标签页中填写事件基本信息
4. 根据需要配置其他标签页的内容
5. 点击"保存"或"另存为"保存配置

### 2. 打开现有配置

1. 点击工具栏的"打开"按钮
2. 选择要编辑的 YAML 配置文件
3. 工具会自动加载并显示配置内容
4. 编辑完成后保存更改

### 3. 配置验证

- 工具会实时验证输入的数据
- 点击"验证"按钮进行完整的配置验证
- 错误和警告会在状态栏和对话框中显示

## 配置文件结构

EPConfigTool 支持的 YAML 配置文件结构：

```yaml
eventId: EV001001
eventName: 示例事件
evaluationStrategy: BusinessOnly
priority: P3
commId: test-comm
positionId: test-position

deviceSignal:
  topics:
    - device/sensor/data
  triggerField: status
  triggerValues:
    "true": "1"
    "false": "0"
  holdingTimeoutSec: 30

ruleConfiguration:
  exclusionRules:
    - sourceType: MQTT
      sourceTopic: device/status
      logicOperator: AND
      conditions:
        - fieldName: enabled
          dataType: string
          operator: Equals
          value: "false"

  businessRules:
    - sourceTopic: business/data
      logicOperator: AND
      conditions:
        - fieldName: type
          dataType: string
          operator: Equals
          value: "alert"

  aiResultRules:
    - logicOperator: AND
      conditions:
        - fieldName: confidence
          dataType: number
          operator: GreaterThan
          value: "0.8"

  alarmConfig:
    fields:
      - alarmFieldName: 详情
        sourceRuleType: BusinessRules
        sourceFieldName: description
        defaultValue: "未知事件"
        formatTemplate: "[{type}] {description}"
```

## 架构设计

### MVVM 模式
- **Model**：EventProcessor.Core.Models 中的数据模型
- **ViewModel**：EPConfigTool.ViewModels 中的视图模型
- **View**：EPConfigTool.Views 中的 XAML 视图

### 服务层
- **IYamlConfigurationService**：YAML 配置文件处理
- **IFileDialogService**：文件对话框操作
- **依赖注入**：使用 Microsoft.Extensions.DependencyInjection

### 数据验证
- **DataAnnotations**：属性级别的验证
- **实时验证**：输入时即时反馈
- **批量验证**：保存前的完整性检查

## 开发和测试

### 运行单元测试

```bash
dotnet test
```

### 代码覆盖率

```bash
dotnet test --collect:"XPlat Code Coverage"
```

### 调试模式运行

```bash
dotnet run --project EPConfigTool --configuration Debug
```

## 故障排除

### 常见问题

1. **应用程序无法启动**
   - 检查 .NET 8.0 运行时是否已安装
   - 确认所有依赖项已正确还原

2. **YAML 文件加载失败**
   - 检查文件格式是否正确
   - 确认文件编码为 UTF-8
   - 查看错误消息中的具体问题

3. **配置验证失败**
   - 检查必填字段是否已填写
   - 确认数据格式符合要求
   - 查看验证错误的详细信息

### 日志记录

应用程序使用 Microsoft.Extensions.Logging 进行日志记录，日志会输出到控制台。

## 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 许可证

本项目采用 MIT 许可证。详见 LICENSE 文件。

## 版本历史

### v4.1.0
- 初始版本
- 支持完整的 EP_V4.1 配置格式
- YAML 专用处理
- 图形化用户界面
- 实时验证和错误提示

## 联系方式

如有问题或建议，请通过以下方式联系：

- 项目 Issues：<repository-issues-url>
- 邮箱：<contact-email>

---

**注意**：本工具专门为 EP_V4.1 版本设计，仅支持 YAML 格式的配置文件。
