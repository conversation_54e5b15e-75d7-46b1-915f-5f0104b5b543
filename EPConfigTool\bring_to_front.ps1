# 将 EPConfigTool 窗口带到前台的脚本

Add-Type @"
    using System;
    using System.Runtime.InteropServices;
    public class Win32 {
        [DllImport("user32.dll")]
        public static extern bool SetForegroundWindow(IntPtr hWnd);
        
        [DllImport("user32.dll")]
        public static extern bool ShowWindow(IntPtr hWnd, int nCmdShow);
        
        [DllImport("user32.dll")]
        public static extern bool IsIconic(IntPtr hWnd);
        
        public const int SW_RESTORE = 9;
        public const int SW_SHOW = 5;
    }
"@

$process = Get-Process -Name "EPConfigTool" -ErrorAction SilentlyContinue
if ($process) {
    $hwnd = $process.MainWindowHandle
    if ($hwnd -ne 0) {
        Write-Host "找到 EPConfigTool 窗口，句柄: $hwnd"
        
        # 如果窗口最小化，先恢复
        if ([Win32]::IsIconic($hwnd)) {
            Write-Host "窗口已最小化，正在恢复..."
            [Win32]::ShowWindow($hwnd, [Win32]::SW_RESTORE)
        }
        
        # 显示窗口
        [Win32]::ShowWindow($hwnd, [Win32]::SW_SHOW)
        
        # 将窗口带到前台
        $result = [Win32]::SetForegroundWindow($hwnd)
        if ($result) {
            Write-Host "✅ 窗口已带到前台"
        } else {
            Write-Host "⚠️  无法将窗口带到前台，请手动点击任务栏图标"
        }
    } else {
        Write-Host "❌ 进程存在但没有主窗口"
    }
} else {
    Write-Host "❌ 没有找到 EPConfigTool 进程"
}
