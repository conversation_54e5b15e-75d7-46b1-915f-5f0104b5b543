# ============================================================================ 
# EPConfigTool Deploy Script
# Target Server: \\bdnserver\BDN\EP_V4.1\EPConfigTool
# Description: Automatically build and deploy EPConfigTool to a remote share.
# Version: 1.0
# ============================================================================ 

[CmdletBinding()]
param(
    [Parameter(HelpMessage = "Target deployment path")]
    [ValidateNotNullOrEmpty()]
    [string]$TargetPath = "\\bdnserver\BDN\EP_V4.1\EPConfigTool",
    
    [Parameter(HelpMessage = "Build configuration")]
    [ValidateSet("Debug", "Release")]
    [string]$Configuration = "Release",
    
    [Parameter(HelpMessage = "Skip build step")]
    [switch]$SkipBuild = $false,

    [Parameter(HelpMessage = "Show verbose output")]
    [switch]$VerboseOutput = $false
)

# Set error handling and preferences
$ErrorActionPreference = "Stop"
$ProgressPreference = "Continue"
$VerbosePreference = if ($VerboseOutput) { "Continue" } else { "SilentlyContinue" }

# Script variables
$ScriptRoot = Split-Path -Parent $MyInvocation.MyCommand.Path
$ProjectPath = Join-Path $ScriptRoot "src\EPConfigTool"
$ProjectFile = Join-Path $ProjectPath "EPConfigTool.csproj"
$PublishDir = Join-Path $ProjectPath "bin\$Configuration\net8.0-windows" # Typical path for WPF apps
$LogFile = Join-Path $ScriptRoot "logs\deploy_epconfigtool_$(Get-Date -Format 'yyyyMMdd_HHmmss').log"

# Logging function
function Write-Log {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $true, Position = 0)]
        [string]$Message,
        
        [Parameter(Position = 1)]
        [ValidateSet("INFO", "WARN", "ERROR", "SUCCESS")]
        [string]$Level = "INFO"
    )
    
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logMessage = "[$timestamp] [$Level] $Message"
    
    $color = switch ($Level) {
        "ERROR"   { "Red" }
        "WARN"    { "Yellow" }
        "SUCCESS" { "Green" }
        default   { "White" }
    }
    Write-Host $logMessage -ForegroundColor $color
    Add-Content -Path $LogFile -Value $logMessage -Encoding UTF8
}

# Main execution block
try {
    Write-Log "=========================================="
    Write-Log "EPConfigTool Deployment Script Started"
    Write-Log "Target: $TargetPath"
    Write-Log "Config: $Configuration"
    Write-Log "=========================================="

    # 1. Prerequisites Check
    Write-Log "Step 1/5: Checking prerequisites..."
    if (-not (Test-Path $ProjectFile)) {
        throw "Project file not found: $ProjectFile"
    }
    if (-not (Get-Command dotnet -ErrorAction SilentlyContinue)) {
        throw ".NET SDK is not installed or not in PATH."
    }
    Write-Log "Prerequisites check passed." -Level SUCCESS

    # 2. Build Project
    if ($SkipBuild) {
        Write-Log "Step 2/5: Skipping build step as requested."
    } else {
        Write-Log "Step 2/5: Building project..."
        $buildArgs = @(
            "build",
            "`"$ProjectFile`"",
            "--configuration", $Configuration,
            "--verbosity", "minimal"
        )
        & dotnet $buildArgs
        if ($LASTEXITCODE -ne 0) {
            throw "Project build failed."
        }
        Write-Log "Project build successful." -Level SUCCESS
    }

    # 3. Backup existing deployment
    Write-Log "Step 3/5: Backing up existing deployment..."
    if (Test-Path $TargetPath) {
        $backupName = "BAK_$(Get-Date -Format 'yyyyMMddHHmmss')"
        $backupPath = Join-Path (Split-Path $TargetPath -Parent) "EPConfigTool_$backupName"
        Write-Log "Found existing deployment, creating backup at $backupPath"
        Copy-Item -Path $TargetPath -Destination $backupPath -Recurse -Force
        Write-Log "Backup created successfully." -Level SUCCESS
    } else {
        Write-Log "Target path does not exist, no backup needed."
    }

    # 4. Deploy Files
    Write-Log "Step 4/5: Deploying files..."
    $sourcePath = Join-Path $ProjectPath "publish"
    
    # Use dotnet publish for a clean output
    Write-Log "Publishing project to temporary directory..."
    $publishArgs = @(
        "publish",
        "`"$ProjectFile`"",
        "--configuration", $Configuration,
        "--output", "`"$sourcePath`"",
        "--runtime", "win-x64",
        "--self-contained", "false", # Depends on .NET Desktop Runtime
        "--verbosity", "minimal",
        "--no-build"
    )
    & dotnet $publishArgs
    if ($LASTEXITCODE -ne 0) {
        throw "dotnet publish failed."
    }

    Write-Log "Copying files from '$sourcePath' to '$TargetPath'..."
    # Ensure target directory exists
    if (-not (Test-Path $TargetPath)) {
        New-Item -Path $TargetPath -ItemType Directory -Force | Out-Null
    }
    Copy-Item -Path "$sourcePath\*" -Destination $TargetPath -Recurse -Force
    Write-Log "File deployment successful." -Level SUCCESS

    
    
    

    Write-Log "==========================================" -Level SUCCESS
    Write-Log "Deployment completed successfully!" -Level SUCCESS
    Write-Log "Log file: $LogFile"
    Write-Log "==========================================" -Level SUCCESS

} catch {
    Write-Log "Deployment failed: $($_.Exception.Message)" -Level ERROR
    exit 1
}
