# EPConfigTool UI 问题诊断脚本
# 用于诊断 EPConfigTool.exe 启动后没有显示图形界面的问题

param(
    [string]$EPConfigToolPath = ".\src\EPConfigTool\bin\Release\net8.0-windows\EPConfigTool.exe",
    [switch]$Verbose = $false
)

Write-Host "=== EPConfigTool UI 问题诊断脚本 ===" -ForegroundColor Green
Write-Host "诊断时间: $(Get-Date)" -ForegroundColor Gray
Write-Host ""

# 1. 检查文件是否存在
Write-Host "1. 检查可执行文件..." -ForegroundColor Cyan
if (Test-Path $EPConfigToolPath) {
    Write-Host "✅ 找到可执行文件: $EPConfigToolPath" -ForegroundColor Green
    $fileInfo = Get-Item $EPConfigToolPath
    Write-Host "   文件大小: $($fileInfo.Length) 字节" -ForegroundColor Gray
    Write-Host "   修改时间: $($fileInfo.LastWriteTime)" -ForegroundColor Gray
} else {
    Write-Host "❌ 可执行文件不存在: $EPConfigToolPath" -ForegroundColor Red
    Write-Host "请先编译项目或检查路径是否正确" -ForegroundColor Yellow
    exit 1
}

# 2. 检查依赖文件
Write-Host ""
Write-Host "2. 检查依赖文件..." -ForegroundColor Cyan
$basePath = Split-Path $EPConfigToolPath -Parent
$requiredFiles = @(
    "EPConfigTool.dll",
    "EPConfigTool.deps.json",
    "EPConfigTool.runtimeconfig.json"
)

foreach ($file in $requiredFiles) {
    $filePath = Join-Path $basePath $file
    if (Test-Path $filePath) {
        Write-Host "✅ $file" -ForegroundColor Green
    } else {
        Write-Host "❌ $file (缺失)" -ForegroundColor Red
    }
}

# 3. 检查 .NET 运行时
Write-Host ""
Write-Host "3. 检查 .NET 运行时..." -ForegroundColor Cyan
try {
    $dotnetInfo = dotnet --info 2>&1
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ .NET 运行时已安装" -ForegroundColor Green
        $runtimeVersion = ($dotnetInfo | Select-String "Microsoft.WindowsDesktop.App" | Select-Object -First 1).ToString()
        if ($runtimeVersion) {
            Write-Host "   Windows Desktop Runtime: $($runtimeVersion.Trim())" -ForegroundColor Gray
        }
    } else {
        Write-Host "❌ .NET 运行时未安装或配置错误" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ 无法检查 .NET 运行时: $($_.Exception.Message)" -ForegroundColor Red
}

# 4. 检查进程是否启动
Write-Host ""
Write-Host "4. 检查进程状态..." -ForegroundColor Cyan
$processes = Get-Process -Name "EPConfigTool" -ErrorAction SilentlyContinue
if ($processes) {
    Write-Host "⚠️  发现正在运行的 EPConfigTool 进程:" -ForegroundColor Yellow
    foreach ($proc in $processes) {
        Write-Host "   PID: $($proc.Id), 启动时间: $($proc.StartTime)" -ForegroundColor Gray
        Write-Host "   窗口标题: '$($proc.MainWindowTitle)'" -ForegroundColor Gray
        Write-Host "   主窗口句柄: $($proc.MainWindowHandle)" -ForegroundColor Gray
        
        if ($proc.MainWindowHandle -eq 0) {
            Write-Host "   ❌ 进程没有主窗口 (可能是后台运行或崩溃)" -ForegroundColor Red
        } else {
            Write-Host "   ✅ 进程有主窗口" -ForegroundColor Green
        }
    }
} else {
    Write-Host "✅ 没有发现正在运行的 EPConfigTool 进程" -ForegroundColor Green
}

# 5. 尝试启动并监控
Write-Host ""
Write-Host "5. 尝试启动应用程序..." -ForegroundColor Cyan
Write-Host "启动命令: $EPConfigToolPath" -ForegroundColor Gray

try {
    # 启动进程并等待一段时间
    $startInfo = New-Object System.Diagnostics.ProcessStartInfo
    $startInfo.FileName = $EPConfigToolPath
    $startInfo.UseShellExecute = $false
    $startInfo.RedirectStandardOutput = $true
    $startInfo.RedirectStandardError = $true
    $startInfo.CreateNoWindow = $true
    
    $process = New-Object System.Diagnostics.Process
    $process.StartInfo = $startInfo
    
    # 事件处理器
    $outputData = @()
    $errorData = @()
    
    $process.add_OutputDataReceived({
        param($sender, $e)
        if ($e.Data) {
            $script:outputData += $e.Data
            if ($Verbose) {
                Write-Host "STDOUT: $($e.Data)" -ForegroundColor Blue
            }
        }
    })
    
    $process.add_ErrorDataReceived({
        param($sender, $e)
        if ($e.Data) {
            $script:errorData += $e.Data
            Write-Host "STDERR: $($e.Data)" -ForegroundColor Red
        }
    })
    
    Write-Host "正在启动..." -ForegroundColor Yellow
    $process.Start()
    $process.BeginOutputReadLine()
    $process.BeginErrorReadLine()
    
    # 等待 5 秒
    $waited = $process.WaitForExit(5000)
    
    if ($waited) {
        Write-Host "❌ 进程在 5 秒内退出，退出代码: $($process.ExitCode)" -ForegroundColor Red
        
        if ($errorData.Count -gt 0) {
            Write-Host ""
            Write-Host "错误输出:" -ForegroundColor Red
            foreach ($line in $errorData) {
                Write-Host "  $line" -ForegroundColor Red
            }
        }
        
        if ($outputData.Count -gt 0 -and $Verbose) {
            Write-Host ""
            Write-Host "标准输出:" -ForegroundColor Blue
            foreach ($line in $outputData) {
                Write-Host "  $line" -ForegroundColor Blue
            }
        }
    } else {
        Write-Host "✅ 进程已启动并在运行中" -ForegroundColor Green
        
        # 检查窗口
        Start-Sleep -Seconds 2
        $runningProcess = Get-Process -Id $process.Id -ErrorAction SilentlyContinue
        if ($runningProcess) {
            if ($runningProcess.MainWindowHandle -ne 0) {
                Write-Host "✅ 检测到主窗口句柄: $($runningProcess.MainWindowHandle)" -ForegroundColor Green
                Write-Host "✅ 窗口标题: '$($runningProcess.MainWindowTitle)'" -ForegroundColor Green
            } else {
                Write-Host "❌ 进程运行中但没有主窗口" -ForegroundColor Red
                Write-Host "   这可能表示 WPF 应用程序启动失败或窗口创建失败" -ForegroundColor Yellow
            }
        }
        
        # 不要杀死进程，让用户自己处理
        Write-Host ""
        Write-Host "进程仍在运行中 (PID: $($process.Id))" -ForegroundColor Yellow
        Write-Host "请检查是否有窗口显示，或手动终止进程" -ForegroundColor Yellow
    }
    
} catch {
    Write-Host "❌ 启动失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 6. 检查事件日志
Write-Host ""
Write-Host "6. 检查 Windows 事件日志..." -ForegroundColor Cyan
try {
    $recentErrors = Get-WinEvent -FilterHashtable @{LogName='Application'; Level=2; StartTime=(Get-Date).AddMinutes(-10)} -MaxEvents 5 -ErrorAction SilentlyContinue | 
                   Where-Object { $_.ProcessId -eq $process.Id -or $_.Message -like "*EPConfigTool*" }
    
    if ($recentErrors) {
        Write-Host "⚠️  发现相关错误事件:" -ForegroundColor Yellow
        foreach ($event in $recentErrors) {
            Write-Host "   时间: $($event.TimeCreated)" -ForegroundColor Gray
            Write-Host "   消息: $($event.Message)" -ForegroundColor Red
        }
    } else {
        Write-Host "✅ 没有发现相关错误事件" -ForegroundColor Green
    }
} catch {
    Write-Host "⚠️  无法检查事件日志: $($_.Exception.Message)" -ForegroundColor Yellow
}

# 7. 提供修复建议
Write-Host ""
Write-Host "=== 修复建议 ===" -ForegroundColor Green

if ($errorData -and $errorData -like "*System.IO.FileNotFoundException*") {
    Write-Host "🔧 检测到文件缺失错误，建议:" -ForegroundColor Yellow
    Write-Host "   1. 重新编译项目: dotnet build --configuration Release" -ForegroundColor White
    Write-Host "   2. 检查所有依赖文件是否完整" -ForegroundColor White
}

if ($errorData -and $errorData -like "*System.Windows*") {
    Write-Host "🔧 检测到 WPF 相关错误，建议:" -ForegroundColor Yellow
    Write-Host "   1. 确保安装了 .NET Desktop Runtime" -ForegroundColor White
    Write-Host "   2. 检查 XAML 文件是否有语法错误" -ForegroundColor White
    Write-Host "   3. 尝试在命令行中运行以查看详细错误" -ForegroundColor White
}

if ($processes -and $processes[0].MainWindowHandle -eq 0) {
    Write-Host "🔧 进程运行但无窗口，建议:" -ForegroundColor Yellow
    Write-Host "   1. 检查是否有未处理的异常阻止窗口显示" -ForegroundColor White
    Write-Host "   2. 检查 App.xaml.cs 中的 OnStartup 方法" -ForegroundColor White
    Write-Host "   3. 添加异常处理和日志记录" -ForegroundColor White
}

Write-Host ""
Write-Host "=== 诊断完成 ===" -ForegroundColor Green
Write-Host "如需更详细的输出，请使用 -Verbose 参数" -ForegroundColor Gray
