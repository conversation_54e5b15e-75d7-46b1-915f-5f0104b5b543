#!/usr/bin/env pwsh
# EPConfigTool 测试执行和代码覆盖率生成脚本
# 使用方法: .\scripts\run-tests-with-coverage.ps1 [-Configuration Debug|Release] [-GenerateReport]

param(
    [Parameter(Mandatory=$false)]
    [ValidateSet("Debug", "Release")]
    [string]$Configuration = "Debug",
    
    [Parameter(Mandatory=$false)]
    [switch]$GenerateReport = $false,
    
    [Parameter(Mandatory=$false)]
    [switch]$OpenReport = $false,
    
    [Parameter(Mandatory=$false)]
    [string]$Filter = "",
    
    [Parameter(Mandatory=$false)]
    [switch]$Verbose = $false
)

# 脚本配置
$ErrorActionPreference = "Stop"
$ProgressPreference = "SilentlyContinue"

# 路径配置
$RootPath = Split-Path -Parent $PSScriptRoot
$TestResultsPath = Join-Path $RootPath "TestResults"
$CoverageReportPath = Join-Path $TestResultsPath "CoverageReport"
$RunSettingsFile = Join-Path $RootPath "coverlet.runsettings"

# 颜色输出函数
function Write-ColorOutput {
    param(
        [string]$Message,
        [string]$Color = "White"
    )
    Write-Host $Message -ForegroundColor $Color
}

function Write-Success {
    param([string]$Message)
    Write-ColorOutput "✅ $Message" "Green"
}

function Write-Info {
    param([string]$Message)
    Write-ColorOutput "ℹ️  $Message" "Cyan"
}

function Write-Warning {
    param([string]$Message)
    Write-ColorOutput "⚠️  $Message" "Yellow"
}

function Write-Error {
    param([string]$Message)
    Write-ColorOutput "❌ $Message" "Red"
}

# 检查必要工具
function Test-Prerequisites {
    Write-Info "检查必要工具..."
    
    # 检查 .NET SDK
    try {
        $dotnetVersion = dotnet --version
        Write-Success "找到 .NET SDK 版本: $dotnetVersion"
    }
    catch {
        Write-Error "未找到 .NET SDK，请先安装 .NET 8.0 SDK"
        exit 1
    }
    
    # 检查 reportgenerator 工具（如果需要生成报告）
    if ($GenerateReport) {
        try {
            $reportGenVersion = dotnet tool list -g | Select-String "dotnet-reportgenerator-globaltool"
            if ($reportGenVersion) {
                Write-Success "找到 ReportGenerator 工具"
            } else {
                Write-Info "安装 ReportGenerator 工具..."
                dotnet tool install -g dotnet-reportgenerator-globaltool
                Write-Success "ReportGenerator 工具安装完成"
            }
        }
        catch {
            Write-Warning "无法检查或安装 ReportGenerator 工具，将跳过 HTML 报告生成"
            $script:GenerateReport = $false
        }
    }
}

# 清理旧的测试结果
function Clear-TestResults {
    Write-Info "清理旧的测试结果..."
    
    if (Test-Path $TestResultsPath) {
        Remove-Item $TestResultsPath -Recurse -Force
        Write-Success "已清理旧的测试结果目录"
    }
    
    New-Item -ItemType Directory -Path $TestResultsPath -Force | Out-Null
    Write-Success "创建测试结果目录: $TestResultsPath"
}

# 构建解决方案
function Build-Solution {
    Write-Info "构建解决方案 ($Configuration 配置)..."
    
    Push-Location $RootPath
    try {
        $buildArgs = @(
            "build"
            "--configuration", $Configuration
            "--no-restore"
            "--verbosity", $(if ($Verbose) { "normal" } else { "minimal" })
        )
        
        dotnet @buildArgs
        if ($LASTEXITCODE -ne 0) {
            throw "构建失败"
        }
        Write-Success "解决方案构建成功"
    }
    finally {
        Pop-Location
    }
}

# 运行测试
function Run-Tests {
    Write-Info "运行测试并收集代码覆盖率..."
    
    Push-Location $RootPath
    try {
        $testArgs = @(
            "test"
            "--configuration", $Configuration
            "--no-build"
            "--settings", $RunSettingsFile
            "--collect:XPlat Code Coverage"
            "--results-directory", $TestResultsPath
            "--logger", "trx;LogFileName=EPConfigTool.TestResults.trx"
            "--logger", "console;verbosity=normal"
        )
        
        if ($Filter) {
            $testArgs += "--filter", $Filter
        }
        
        if ($Verbose) {
            $testArgs += "--verbosity", "detailed"
        }
        
        Write-Info "执行命令: dotnet $($testArgs -join ' ')"
        dotnet @testArgs
        
        if ($LASTEXITCODE -ne 0) {
            throw "测试执行失败"
        }
        
        Write-Success "测试执行完成"
    }
    finally {
        Pop-Location
    }
}

# 生成覆盖率报告
function Generate-CoverageReport {
    if (-not $GenerateReport) {
        return
    }
    
    Write-Info "生成代码覆盖率报告..."
    
    # 查找覆盖率文件
    $coverageFiles = Get-ChildItem -Path $TestResultsPath -Filter "coverage.cobertura.xml" -Recurse
    
    if ($coverageFiles.Count -eq 0) {
        Write-Warning "未找到代码覆盖率文件，跳过报告生成"
        return
    }
    
    $coverageFilePaths = $coverageFiles | ForEach-Object { $_.FullName }
    Write-Info "找到 $($coverageFiles.Count) 个覆盖率文件"
    
    try {
        $reportArgs = @(
            "-reports:$($coverageFilePaths -join ';')"
            "-targetdir:$CoverageReportPath"
            "-reporttypes:Html;HtmlSummary;Badges;TextSummary"
            "-sourcedirs:$RootPath\src"
            "-title:EPConfigTool 代码覆盖率报告"
            "-tag:$Configuration"
        )
        
        reportgenerator @reportArgs
        
        if ($LASTEXITCODE -ne 0) {
            throw "报告生成失败"
        }
        
        Write-Success "代码覆盖率报告生成完成: $CoverageReportPath"
        
        # 显示覆盖率摘要
        $summaryFile = Join-Path $CoverageReportPath "Summary.txt"
        if (Test-Path $summaryFile) {
            Write-Info "代码覆盖率摘要:"
            Get-Content $summaryFile | Write-Host
        }
        
        # 打开报告
        if ($OpenReport) {
            $indexFile = Join-Path $CoverageReportPath "index.html"
            if (Test-Path $indexFile) {
                Write-Info "打开覆盖率报告..."
                Start-Process $indexFile
            }
        }
    }
    catch {
        Write-Error "生成覆盖率报告时出错: $($_.Exception.Message)"
    }
}

# 显示测试结果摘要
function Show-TestSummary {
    Write-Info "测试结果摘要:"
    
    # 查找 TRX 文件
    $trxFiles = Get-ChildItem -Path $TestResultsPath -Filter "*.trx" -Recurse
    
    if ($trxFiles.Count -gt 0) {
        Write-Success "测试结果文件: $($trxFiles[0].FullName)"
    }
    
    # 查找覆盖率文件
    $coverageFiles = Get-ChildItem -Path $TestResultsPath -Filter "coverage.cobertura.xml" -Recurse
    
    if ($coverageFiles.Count -gt 0) {
        Write-Success "覆盖率文件: $($coverageFiles.Count) 个"
        $coverageFiles | ForEach-Object {
            Write-Info "  - $($_.FullName)"
        }
    }
    
    if ($GenerateReport -and (Test-Path $CoverageReportPath)) {
        $indexFile = Join-Path $CoverageReportPath "index.html"
        if (Test-Path $indexFile) {
            Write-Success "HTML 覆盖率报告: $indexFile"
        }
    }
}

# 主执行流程
function Main {
    Write-Info "开始执行 EPConfigTool 测试和代码覆盖率分析"
    Write-Info "配置: $Configuration"
    Write-Info "生成报告: $GenerateReport"
    Write-Info "打开报告: $OpenReport"
    if ($Filter) {
        Write-Info "测试过滤器: $Filter"
    }
    
    try {
        Test-Prerequisites
        Clear-TestResults
        Build-Solution
        Run-Tests
        Generate-CoverageReport
        Show-TestSummary
        
        Write-Success "所有任务完成！"
    }
    catch {
        Write-Error "执行过程中出现错误: $($_.Exception.Message)"
        exit 1
    }
}

# 执行主函数
Main