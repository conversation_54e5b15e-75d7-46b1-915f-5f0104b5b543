#!/usr/bin/env pwsh
# EPConfigTool 简化测试执行脚本
# 使用方法: .\scripts\run-tests.ps1 [-Quick] [-UnitOnly] [-IntegrationOnly]

param(
    [Parameter(Mandatory=$false)]
    [switch]$Quick = $false,
    
    [Parameter(Mandatory=$false)]
    [switch]$UnitOnly = $false,
    
    [Parameter(Mandatory=$false)]
    [switch]$IntegrationOnly = $false,
    
    [Parameter(Mandatory=$false)]
    [string]$Filter = "",
    
    [Parameter(Mandatory=$false)]
    [switch]$Verbose = $false
)

# 脚本配置
$ErrorActionPreference = "Stop"
$RootPath = Split-Path -Parent $PSScriptRoot

# 颜色输出函数
function Write-ColorOutput {
    param(
        [string]$Message,
        [string]$Color = "White"
    )
    Write-Host $Message -ForegroundColor $Color
}

function Write-Success {
    param([string]$Message)
    Write-ColorOutput "✅ $Message" "Green"
}

function Write-Info {
    param([string]$Message)
    Write-ColorOutput "ℹ️  $Message" "Cyan"
}

function Write-Error {
    param([string]$Message)
    Write-ColorOutput "❌ $Message" "Red"
}

# 主执行函数
function Main {
    Write-Info "开始执行 EPConfigTool 测试"
    
    Push-Location $RootPath
    try {
        # 构建项目（除非是快速模式）
        if (-not $Quick) {
            Write-Info "构建项目..."
            dotnet build --configuration Debug
            if ($LASTEXITCODE -ne 0) {
                throw "构建失败"
            }
            Write-Success "项目构建成功"
        }
        
        # 准备测试参数
        $testArgs = @(
            "test"
            "--configuration", "Debug"
        )
        
        if ($Quick) {
            $testArgs += "--no-build"
        }
        
        # 添加项目过滤器
        if ($UnitOnly) {
            $testArgs += "tests\EPConfigTool.Tests\EPConfigTool.Tests.csproj"
            Write-Info "仅运行单元测试"
        }
        elseif ($IntegrationOnly) {
            $testArgs += "tests\EPConfigTool.IntegrationTests\EPConfigTool.IntegrationTests.csproj"
            Write-Info "仅运行集成测试"
        }
        else {
            Write-Info "运行所有测试"
        }
        
        # 添加测试过滤器
        if ($Filter) {
            $testArgs += "--filter", $Filter
            Write-Info "使用过滤器: $Filter"
        }
        
        # 添加详细输出
        if ($Verbose) {
            $testArgs += "--verbosity", "detailed"
        } else {
            $testArgs += "--verbosity", "normal"
        }
        
        # 执行测试
        Write-Info "执行命令: dotnet $($testArgs -join ' ')"
        dotnet @testArgs
        
        if ($LASTEXITCODE -ne 0) {
            throw "测试执行失败"
        }
        
        Write-Success "所有测试执行完成！"
    }
    catch {
        Write-Error "执行过程中出现错误: $($_.Exception.Message)"
        exit 1
    }
    finally {
        Pop-Location
    }
}

# 执行主函数
Main