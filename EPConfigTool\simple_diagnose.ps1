# EPConfigTool UI 问题简化诊断脚本

param(
    [string]$EPConfigToolPath = ".\src\EPConfigTool\bin\Release\net8.0-windows\EPConfigTool.exe"
)

Write-Host "=== EPConfigTool UI 问题诊断 ===" -ForegroundColor Green
Write-Host ""

# 1. 检查文件是否存在
Write-Host "1. 检查可执行文件..." -ForegroundColor Cyan
if (Test-Path $EPConfigToolPath) {
    Write-Host "✅ 找到可执行文件: $EPConfigToolPath" -ForegroundColor Green
    $fileInfo = Get-Item $EPConfigToolPath
    Write-Host "   文件大小: $($fileInfo.Length) 字节" -ForegroundColor Gray
    Write-Host "   修改时间: $($fileInfo.LastWriteTime)" -ForegroundColor Gray
} else {
    Write-Host "❌ 可执行文件不存在: $EPConfigToolPath" -ForegroundColor Red
    
    # 尝试查找其他可能的位置
    $possiblePaths = @(
        ".\src\EPConfigTool\bin\Debug\net8.0-windows\EPConfigTool.exe",
        ".\bin\Release\net8.0-windows\EPConfigTool.exe",
        ".\bin\Debug\net8.0-windows\EPConfigTool.exe"
    )
    
    Write-Host "尝试查找其他位置..." -ForegroundColor Yellow
    foreach ($path in $possiblePaths) {
        if (Test-Path $path) {
            Write-Host "✅ 找到: $path" -ForegroundColor Green
            $EPConfigToolPath = $path
            break
        }
    }
    
    if (-not (Test-Path $EPConfigToolPath)) {
        Write-Host "❌ 未找到可执行文件，请先编译项目" -ForegroundColor Red
        Write-Host "编译命令: dotnet build src\EPConfigTool --configuration Release" -ForegroundColor Yellow
        exit 1
    }
}

# 2. 检查依赖文件
Write-Host ""
Write-Host "2. 检查依赖文件..." -ForegroundColor Cyan
$basePath = Split-Path $EPConfigToolPath -Parent
$requiredFiles = @(
    "EPConfigTool.dll",
    "EPConfigTool.deps.json",
    "EPConfigTool.runtimeconfig.json"
)

$missingFiles = @()
foreach ($file in $requiredFiles) {
    $filePath = Join-Path $basePath $file
    if (Test-Path $filePath) {
        Write-Host "✅ $file" -ForegroundColor Green
    } else {
        Write-Host "❌ $file (缺失)" -ForegroundColor Red
        $missingFiles += $file
    }
}

if ($missingFiles.Count -gt 0) {
    Write-Host "⚠️  缺失关键文件，请重新编译项目" -ForegroundColor Yellow
}

# 3. 检查 .NET 运行时
Write-Host ""
Write-Host "3. 检查 .NET 运行时..." -ForegroundColor Cyan
try {
    $dotnetVersion = dotnet --version 2>$null
    if ($dotnetVersion) {
        Write-Host "✅ .NET SDK 版本: $dotnetVersion" -ForegroundColor Green
    } else {
        Write-Host "❌ .NET SDK 未安装" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ 无法检查 .NET 运行时" -ForegroundColor Red
}

# 4. 检查现有进程
Write-Host ""
Write-Host "4. 检查现有进程..." -ForegroundColor Cyan
$processes = Get-Process -Name "EPConfigTool" -ErrorAction SilentlyContinue
if ($processes) {
    Write-Host "⚠️  发现正在运行的进程:" -ForegroundColor Yellow
    foreach ($proc in $processes) {
        Write-Host "   PID: $($proc.Id)" -ForegroundColor Gray
        if ($proc.MainWindowHandle -eq 0) {
            Write-Host "   ❌ 无主窗口 (可能崩溃或后台运行)" -ForegroundColor Red
        } else {
            Write-Host "   ✅ 有主窗口" -ForegroundColor Green
        }
    }
    
    Write-Host ""
    $killChoice = Read-Host "是否要终止现有进程? (y/N)"
    if ($killChoice -eq 'y' -or $killChoice -eq 'Y') {
        $processes | Stop-Process -Force
        Write-Host "已终止现有进程" -ForegroundColor Yellow
        Start-Sleep -Seconds 2
    }
} else {
    Write-Host "✅ 没有发现正在运行的进程" -ForegroundColor Green
}

# 5. 尝试启动
Write-Host ""
Write-Host "5. 尝试启动应用程序..." -ForegroundColor Cyan
Write-Host "启动路径: $EPConfigToolPath" -ForegroundColor Gray

try {
    Write-Host "正在启动..." -ForegroundColor Yellow
    $process = Start-Process -FilePath $EPConfigToolPath -PassThru -WindowStyle Normal
    
    if ($process) {
        Write-Host "✅ 进程已启动 (PID: $($process.Id))" -ForegroundColor Green
        
        # 等待几秒钟让窗口初始化
        Write-Host "等待窗口初始化..." -ForegroundColor Yellow
        Start-Sleep -Seconds 3
        
        # 检查进程状态
        $runningProcess = Get-Process -Id $process.Id -ErrorAction SilentlyContinue
        if ($runningProcess) {
            if ($runningProcess.MainWindowHandle -ne 0) {
                Write-Host "✅ 窗口已显示!" -ForegroundColor Green
                Write-Host "   窗口句柄: $($runningProcess.MainWindowHandle)" -ForegroundColor Gray
                Write-Host "   窗口标题: '$($runningProcess.MainWindowTitle)'" -ForegroundColor Gray
            } else {
                Write-Host "❌ 进程运行但窗口未显示" -ForegroundColor Red
                Write-Host "   这可能是 WPF 初始化问题" -ForegroundColor Yellow
            }
        } else {
            Write-Host "❌ 进程已退出" -ForegroundColor Red
        }
    } else {
        Write-Host "❌ 无法启动进程" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ 启动失败: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host "=== 诊断完成 ===" -ForegroundColor Green
