using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using System;
using System.IO;
using System.Windows;
using System.Windows.Controls;
using EPConfigTool.Services;
using EPConfigTool.ViewModels;

namespace EPConfigTool;

/// <summary>
/// EPConfigTool 应用程序主类
/// 专门为 EP_V4.1 YAML 配置文件设计的图形配置工具
/// </summary>
public partial class App : Application
{
    private ServiceProvider? _serviceProvider;

    protected override void OnStartup(StartupEventArgs e)
    {
        base.OnStartup(e);

        try
        {
            // 注册全局异常处理器
            DispatcherUnhandledException += App_DispatcherUnhandledException;
            AppDomain.CurrentDomain.UnhandledException += App_UnhandledException;

            // 配置依赖注入容器
            var services = new ServiceCollection();
            ConfigureServices(services);
            _serviceProvider = services.BuildServiceProvider();

            // 获取日志记录器
            var logger = _serviceProvider.GetRequiredService<ILogger<App>>();
            logger.LogInformation("EPConfigTool 正在启动...");

            // 创建并显示主窗口
            logger.LogInformation("正在创建主窗口...");
            var mainWindow = _serviceProvider.GetRequiredService<MainWindow>();

            logger.LogInformation("正在显示主窗口...");
            mainWindow.Show();

            logger.LogInformation("EPConfigTool 启动完成");
        }
        catch (Exception ex)
        {
            MessageBox.Show($"应用程序启动失败:\n{ex.Message}\n\n详细信息:\n{ex}",
                "启动错误", MessageBoxButton.OK, MessageBoxImage.Error);
            Shutdown(1);
        }
    }

    [System.Runtime.InteropServices.DllImport("kernel32.dll")]
    private static extern bool AllocConsole();

    private void App_DispatcherUnhandledException(object sender, System.Windows.Threading.DispatcherUnhandledExceptionEventArgs e)
    {
        MessageBox.Show($"未处理的UI异常:\n{e.Exception.Message}\n\n详细信息:\n{e.Exception}",
            "运行时错误", MessageBoxButton.OK, MessageBoxImage.Error);
        e.Handled = true;
    }

    private void App_UnhandledException(object sender, UnhandledExceptionEventArgs e)
    {
        var exception = e.ExceptionObject as Exception;
        MessageBox.Show($"未处理的应用程序异常:\n{exception?.Message}\n\n详细信息:\n{exception}",
            "严重错误", MessageBoxButton.OK, MessageBoxImage.Error);
    }

    private void ConfigureServices(IServiceCollection services)
    {
        // 配置日志服务
        services.AddLogging(builder =>
        {
            builder.AddConsole();
            builder.AddDebug();
            builder.SetMinimumLevel(LogLevel.Debug);
        });

        // 注册服务
        services.AddSingleton<IYamlConfigurationService, YamlConfigurationService>();
        services.AddSingleton<IUnifiedConfigurationService, UnifiedConfigurationService>();
        services.AddSingleton<IFileDialogService, FileDialogService>();
        services.AddSingleton<IHelpInfoService, HelpInfoService>();
        services.AddSingleton<IAlarmPreviewService, AlarmPreviewService>();

        // 注册 ViewModels
        services.AddTransient<MainViewModel>();

        // 注册主窗口
        services.AddTransient<MainWindow>();
    }

    protected override void OnExit(ExitEventArgs e)
    {
        _serviceProvider?.Dispose();
        base.OnExit(e);
    }
}
