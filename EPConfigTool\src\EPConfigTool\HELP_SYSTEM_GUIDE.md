# EPConfigTool 帮助信息系统使用指南

## 🎯 **概述**

EPConfigTool 现在配备了全面的帮助信息系统，为每个配置项提供详细的说明和指导，特别针对新用户和初学者设计。

## ✨ **帮助信息功能特性**

### **1. 鼠标悬停提示 (ToolTip)**
- **位置**: 所有配置输入控件
- **内容**: 
  - 配置项的中文和英文名称
  - 简短功能描述
  - 格式要求和有效值范围
  - 具体示例值
- **触发**: 鼠标悬停在配置控件上

### **2. 状态栏信息显示**
- **位置**: 主窗口底部状态栏（双行显示）
- **第一行**: 当前操作状态和进度
- **第二行**: 当前选中配置项的详细帮助信息
- **触发**: 点击或选中任何配置项

### **3. 详细帮助面板**
- **位置**: 可通过右侧面板或弹出窗口显示
- **内容**: 完整的配置项帮助信息
- **包含**: 功能描述、使用场景、注意事项、最佳实践等

## 📋 **支持的配置项类别**

### **基本事件配置**
| 配置项 | 中文名称 | 帮助重点 |
|--------|----------|----------|
| `EventId` | 事件ID | 格式规范、唯一性要求 |
| `EventName` | 事件名称 | 命名规范、显示用途 |
| `EvaluationStrategy` | 评估策略 | 三种策略的区别和适用场景 |
| `Priority` | 优先级 | P1-P5的含义和影响 |
| `CommId` | 小区ID | 编码规范、权限关联 |
| `PositionId` | 位置ID | 位置标识、设备关联 |

### **设备信号配置**
| 配置项 | 中文名称 | 帮助重点 |
|--------|----------|----------|
| `DeviceSignal.Topics` | 设备信号主题 | MQTT主题格式、通配符使用 |
| `DeviceSignal.TriggerField` | 触发字段 | 字段名匹配、消息格式 |
| `DeviceSignal.TriggerValues` | 触发值映射 | 状态映射、业务含义 |
| `DeviceSignal.HoldingTimeoutSec` | 保持超时时间 | 防抖机制、时间设置 |

### **规则配置**
| 配置项 | 中文名称 | 帮助重点 |
|--------|----------|----------|
| `ExclusionRules` | 排除规则 | 过滤逻辑、执行优先级 |
| `BusinessRules` | 业务规则 | 核心逻辑、条件组合 |
| `Conditions.FieldName` | 条件字段名 | 字段路径、数据源匹配 |
| `Conditions.DataType` | 数据类型 | 类型选择、操作符兼容性 |
| `Conditions.Operator` | 比较操作符 | 操作符含义、适用场景 |
| `Conditions.Value` | 比较值 | 值格式、特殊值支持 |

### **告警配置**
| 配置项 | 中文名称 | 帮助重点 |
|--------|----------|----------|
| `AlarmConfig.Fields` | 告警字段配置 | 字段结构、信息完整性 |
| `AlarmFieldName` | 告警字段名 | 命名规范、显示效果 |
| `SourceRuleType` | 数据源类型 | 数据来源、可用性 |

## 🚀 **使用方法**

### **方法 1: 鼠标悬停查看快速帮助**
1. 将鼠标悬停在任何配置输入框或下拉框上
2. 等待1-2秒，会显示详细的ToolTip
3. ToolTip包含：
   - 配置项名称（中英文）
   - 功能描述
   - 格式要求
   - 示例值

### **方法 2: 点击查看状态栏详细说明**
1. 点击或选中任何配置项
2. 查看主窗口底部状态栏的第二行
3. 状态栏显示：
   - 配置项的详细描述
   - 使用场景说明
   - 重要注意事项

### **方法 3: 查看完整帮助信息**
1. 右键点击配置项（如果实现了上下文菜单）
2. 或通过帮助面板查看完整信息
3. 包含：
   - 功能描述和工作原理
   - 有效值范围和格式要求
   - 实际使用示例
   - 与其他配置项的关联关系
   - 常见错误和解决方案
   - 最佳实践建议

## 💡 **帮助信息内容结构**

每个配置项的帮助信息包含以下部分：

### **基本信息**
- **中文名称**: 用户友好的中文描述
- **英文名称**: 对应的英文术语
- **必需标识**: 是否为必填项
- **配置分类**: 所属的配置类别

### **功能说明**
- **功能描述**: 配置项的作用和意义
- **工作原理**: 在系统中如何发挥作用
- **影响范围**: 对其他功能的影响

### **使用指导**
- **有效值范围**: 可接受的值和格式
- **示例值**: 具体的使用示例
- **使用场景**: 适用的业务场景

### **注意事项**
- **重要提醒**: 配置时需要注意的要点
- **常见错误**: 经常出现的配置错误
- **最佳实践**: 推荐的配置方法

### **关联信息**
- **相关配置项**: 有关联的其他配置
- **依赖关系**: 配置的前置条件
- **影响分析**: 修改后的影响范围

## 🎨 **界面设计特点**

### **视觉层次**
- **标题**: 使用较大字体和粗体
- **分类**: 使用颜色标签区分
- **内容**: 使用适中字体和行距
- **示例**: 使用等宽字体和背景色

### **颜色编码**
- **必需项**: 红色标签标识
- **注意事项**: 黄色背景提醒
- **错误信息**: 红色背景警告
- **最佳实践**: 蓝色背景建议

### **交互反馈**
- **即时响应**: 鼠标悬停立即显示
- **状态更新**: 选中项目实时更新状态栏
- **滚动支持**: 长内容支持滚动查看

## 🔧 **技术实现**

### **服务架构**
- `IHelpInfoService`: 帮助信息服务接口
- `HelpInfoService`: 帮助信息服务实现
- `ConfigHelpInfo`: 帮助信息数据模型

### **ViewModel 集成**
- `IHelpAware`: 支持帮助信息的接口
- `HelpAwareViewModelBase`: 帮助信息基类
- 事件驱动的帮助信息更新机制

### **UI 组件**
- `HelpInfoPanel`: 帮助信息显示面板
- `StringToVisibilityConverter`: 字符串可见性转换器
- 增强的状态栏布局

## 📚 **最佳实践建议**

### **对于新用户**
1. **从基本配置开始**: 先配置EventId、EventName等基本信息
2. **逐步学习**: 每次专注于一个配置类别
3. **多看帮助**: 充分利用ToolTip和状态栏信息
4. **参考示例**: 仔细阅读示例值和使用场景

### **对于高级用户**
1. **快速查看**: 使用ToolTip快速确认配置要求
2. **关注关联**: 注意配置项之间的依赖关系
3. **遵循最佳实践**: 参考推荐的配置方法
4. **避免常见错误**: 查看常见错误说明

### **对于管理员**
1. **建立规范**: 基于帮助信息建立配置规范
2. **培训用户**: 指导用户使用帮助系统
3. **收集反馈**: 根据使用情况改进帮助内容
4. **维护更新**: 定期更新帮助信息内容

## 🎉 **总结**

EPConfigTool 的帮助信息系统提供了：

- **✅ 全面覆盖**: 所有配置项都有详细说明
- **✅ 多层次展示**: ToolTip、状态栏、详细面板
- **✅ 用户友好**: 中文界面，清晰的视觉设计
- **✅ 实用指导**: 包含示例、注意事项、最佳实践
- **✅ 即时帮助**: 无需查阅外部文档

通过这个帮助系统，初学者可以独立完成配置，高级用户可以提高配置效率，管理员可以确保配置的规范性和正确性。
