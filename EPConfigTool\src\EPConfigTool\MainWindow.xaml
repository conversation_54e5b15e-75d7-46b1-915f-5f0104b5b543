<Window x:Class="EPConfigTool.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:EPConfigTool"
        xmlns:vm="clr-namespace:EPConfigTool.ViewModels"
        xmlns:views="clr-namespace:EPConfigTool.Views"
        mc:Ignorable="d"
        Title="{Binding WindowTitle}"
        Height="600" Width="800"
        MinHeight="400" MinWidth="600"
        WindowStartupLocation="CenterScreen"
        WindowState="Normal"
        Topmost="False">

    <Window.Resources>
        <!-- 数据模板定义 -->
        <DataTemplate DataType="{x:Type vm:EventViewModel}">
            <views:EventConfigurationView/>
        </DataTemplate>
        <DataTemplate DataType="{x:Type vm:UnifiedConfigurationViewModel}">
            <views:UnifiedConfigurationView/>
        </DataTemplate>
    </Window.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 工具栏 -->
        <ToolBar Grid.Row="0" Style="{StaticResource {x:Type ToolBar}}">
            <Button Content="新建"
                    Command="{Binding NewCommand}"
                    Style="{StaticResource ToolBarButtonStyle}"
                    ToolTip="创建新的事件配置"/>
            <Separator/>
            <Button Content="打开"
                    Command="{Binding LoadCommand}"
                    Style="{StaticResource ToolBarButtonStyle}"
                    ToolTip="打开 YAML 配置文件"/>
            <Button Content="保存"
                    Command="{Binding SaveCommand}"
                    Style="{StaticResource ToolBarButtonStyle}"
                    ToolTip="保存当前配置"/>
            <Button Content="另存为"
                    Command="{Binding SaveAsCommand}"
                    Style="{StaticResource ToolBarButtonStyle}"
                    ToolTip="另存为新的 YAML 文件"/>
            <Separator/>
            <Button Content="验证"
                    Command="{Binding ValidateCommand}"
                    Style="{StaticResource ToolBarButtonStyle}"
                    ToolTip="验证当前配置"/>
            <Separator/>
            <TextBlock Text="仅支持 YAML 格式配置文件"
                       VerticalAlignment="Center"
                       Foreground="#666666"
                       FontStyle="Italic"
                       Margin="10,0"/>
        </ToolBar>

        <!-- 主内容区域 -->
        <Grid Grid.Row="1">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="300" MinWidth="250"/>
                <ColumnDefinition Width="5"/>
                <ColumnDefinition Width="*" MinWidth="400"/>
            </Grid.ColumnDefinitions>

            <!-- 左侧面板 - 配置信息 -->
            <Border Grid.Column="0"
                    Background="White"
                    BorderBrush="#CCCCCC"
                    BorderThickness="0,0,1,0">
                <DockPanel Margin="10">
                    <TextBlock Text="配置文件信息"
                               DockPanel.Dock="Top"
                               Style="{StaticResource HeaderTextBlockStyle}"/>

                    <StackPanel DockPanel.Dock="Top" Margin="0,10,0,0">
                        <!-- 文件路径信息 -->
                        <TextBlock Text="文件路径:" Style="{StaticResource LabelTextBlockStyle}"/>
                        <TextBlock Text="{Binding CurrentFilePath, TargetNullValue='未选择文件'}"
                                   TextWrapping="Wrap"
                                   Foreground="#666666"
                                   Margin="0,0,0,10"/>

                        <!-- 事件基本信息 -->
                        <Border BorderBrush="#EEEEEE" BorderThickness="1" Padding="10" Margin="0,0,0,10"
                                Visibility="{Binding HasConfiguration, Converter={StaticResource BooleanToVisibilityConverter}}">
                            <StackPanel>
                                <TextBlock Text="事件信息" Style="{StaticResource LabelTextBlockStyle}"/>

                                <!-- 统一配置模式 -->
                                <StackPanel Visibility="{Binding IsUnifiedMode, Converter={StaticResource BooleanToVisibilityConverter}}">
                                    <TextBlock Text="{Binding CurrentUnifiedConfig.EventConfiguration.EventId, StringFormat='ID: {0}'}"
                                               Style="{StaticResource BaseTextBlockStyle}"/>
                                    <TextBlock Text="{Binding CurrentUnifiedConfig.EventConfiguration.EventName, StringFormat='名称: {0}'}"
                                               Style="{StaticResource BaseTextBlockStyle}"/>
                                    <TextBlock Text="{Binding CurrentUnifiedConfig.EventConfiguration.EvaluationStrategy, StringFormat='策略: {0}'}"
                                               Style="{StaticResource BaseTextBlockStyle}"/>
                                    <TextBlock Text="{Binding CurrentUnifiedConfig.EventConfiguration.Priority, StringFormat='优先级: {0}'}"
                                               Style="{StaticResource BaseTextBlockStyle}"/>
                                </StackPanel>

                                <!-- 传统配置模式 -->
                                <StackPanel Visibility="{Binding IsUnifiedMode, Converter={StaticResource InverseBooleanToVisibilityConverter}}">
                                    <TextBlock Text="{Binding CurrentEvent.EventId, StringFormat='ID: {0}'}"
                                               Style="{StaticResource BaseTextBlockStyle}"/>
                                    <TextBlock Text="{Binding CurrentEvent.EventName, StringFormat='名称: {0}'}"
                                               Style="{StaticResource BaseTextBlockStyle}"/>
                                    <TextBlock Text="{Binding CurrentEvent.EvaluationStrategy, StringFormat='策略: {0}'}"
                                               Style="{StaticResource BaseTextBlockStyle}"/>
                                    <TextBlock Text="{Binding CurrentEvent.Priority, StringFormat='优先级: {0}'}"
                                               Style="{StaticResource BaseTextBlockStyle}"/>
                                </StackPanel>
                            </StackPanel>
                        </Border>

                        <!-- 规则统计信息 -->
                        <Border BorderBrush="#EEEEEE" BorderThickness="1" Padding="10"
                                Visibility="{Binding HasConfiguration, Converter={StaticResource BooleanToVisibilityConverter}}">
                            <StackPanel>
                                <TextBlock Text="规则统计" Style="{StaticResource LabelTextBlockStyle}"/>

                                <!-- 统一配置模式 -->
                                <StackPanel Visibility="{Binding IsUnifiedMode, Converter={StaticResource BooleanToVisibilityConverter}}">
                                    <TextBlock Text="{Binding CurrentUnifiedConfig.EventConfiguration.RuleConfiguration.ExclusionRules.Count, StringFormat='排除规则: {0} 条'}"
                                               Style="{StaticResource BaseTextBlockStyle}"/>
                                    <TextBlock Text="{Binding CurrentUnifiedConfig.EventConfiguration.RuleConfiguration.BusinessRules.Count, StringFormat='业务规则: {0} 条'}"
                                               Style="{StaticResource BaseTextBlockStyle}"/>
                                    <TextBlock Text="{Binding CurrentUnifiedConfig.EventConfiguration.RuleConfiguration.AIResultRules.Count, StringFormat='AI规则: {0} 条'}"
                                               Style="{StaticResource BaseTextBlockStyle}"/>
                                    <TextBlock Text="{Binding CurrentUnifiedConfig.EventConfiguration.RuleConfiguration.AlarmConfig.Fields.Count, StringFormat='告警字段: {0} 个'}"
                                               Style="{StaticResource BaseTextBlockStyle}"/>
                                </StackPanel>

                                <!-- 传统配置模式 -->
                                <StackPanel Visibility="{Binding IsUnifiedMode, Converter={StaticResource InverseBooleanToVisibilityConverter}}">
                                    <TextBlock Text="{Binding CurrentEvent.RuleConfiguration.ExclusionRules.Count, StringFormat='排除规则: {0} 条'}"
                                               Style="{StaticResource BaseTextBlockStyle}"/>
                                    <TextBlock Text="{Binding CurrentEvent.RuleConfiguration.BusinessRules.Count, StringFormat='业务规则: {0} 条'}"
                                               Style="{StaticResource BaseTextBlockStyle}"/>
                                    <TextBlock Text="{Binding CurrentEvent.RuleConfiguration.AIResultRules.Count, StringFormat='AI规则: {0} 条'}"
                                               Style="{StaticResource BaseTextBlockStyle}"/>
                                    <TextBlock Text="{Binding CurrentEvent.RuleConfiguration.AlarmConfig.Fields.Count, StringFormat='告警字段: {0} 个'}"
                                               Style="{StaticResource BaseTextBlockStyle}"/>
                                </StackPanel>
                            </StackPanel>
                        </Border>
                    </StackPanel>
                </DockPanel>
            </Border>

            <!-- 分隔符 -->
            <GridSplitter Grid.Column="1"
                          HorizontalAlignment="Stretch"
                          Background="#CCCCCC"/>

            <!-- 右侧面板 - 配置编辑 -->
            <Grid Grid.Column="2">
                <!-- 无配置时的提示 -->
                <TextBlock Text="请创建新配置或打开现有的 YAML 配置文件开始编辑。"
                           VerticalAlignment="Center"
                           HorizontalAlignment="Center"
                           Foreground="#999999"
                           FontSize="14"
                           TextAlignment="Center"
                           Visibility="{Binding HasNoConfiguration, Converter={StaticResource BooleanToVisibilityConverter}}"/>

                <!-- 统一配置编辑界面 -->
                <ContentControl Content="{Binding CurrentUnifiedConfig}"
                                Visibility="{Binding IsUnifiedMode, Converter={StaticResource BooleanToVisibilityConverter}}"
                                Margin="10"/>

                <!-- 传统配置编辑界面 -->
                <ContentControl Content="{Binding CurrentEvent}"
                                Visibility="{Binding IsLegacyMode, Converter={StaticResource BooleanToVisibilityConverter}}"
                                Margin="10"/>
            </Grid>
        </Grid>

        <!-- 状态栏 -->
        <StatusBar Grid.Row="2" Style="{StaticResource BaseStatusBarStyle}">
            <StatusBarItem HorizontalContentAlignment="Stretch">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <!-- 第一行：状态消息 -->
                    <StackPanel Grid.Row="0" Orientation="Horizontal">
                        <!-- 忙碌指示器 -->
                        <ProgressBar Width="16" Height="16"
                                     IsIndeterminate="True"
                                     Visibility="{Binding IsBusy, Converter={StaticResource BooleanToVisibilityConverter}}"
                                     Margin="0,0,5,0"/>

                        <!-- 状态消息 -->
                        <TextBlock Text="{Binding StatusMessage}"
                                   VerticalAlignment="Center"
                                   FontWeight="SemiBold"/>
                    </StackPanel>

                    <!-- 第二行：帮助信息 -->
                    <TextBlock Grid.Row="1"
                               Text="{Binding CurrentHelpInfo}"
                               VerticalAlignment="Center"
                               Foreground="#666666"
                               FontSize="11"
                               TextWrapping="Wrap"
                               Margin="0,2,0,0"/>
                </Grid>
            </StatusBarItem>

            <StatusBarItem HorizontalAlignment="Right">
                <StackPanel Orientation="Horizontal">
                    <!-- 未保存更改指示器 -->
                    <TextBlock Text="●"
                               Foreground="{StaticResource ErrorBrush}"
                               FontWeight="Bold"
                               ToolTip="有未保存的更改"
                               Visibility="{Binding HasUnsavedChanges, Converter={StaticResource BooleanToVisibilityConverter}}"
                               Margin="0,0,5,0"/>

                    <!-- 版本信息 -->
                    <TextBlock Text="EPConfigTool v4.1"
                               Foreground="#666666"/>
                </StackPanel>
            </StatusBarItem>
        </StatusBar>
    </Grid>
</Window>
