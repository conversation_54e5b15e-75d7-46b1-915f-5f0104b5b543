using EPConfigTool.ViewModels;
using System.ComponentModel;
using System.Windows;

namespace EPConfigTool;

/// <summary>
/// MainWindow.xaml 的交互逻辑
/// </summary>
public partial class MainWindow : Window
{
    private readonly MainViewModel _viewModel;

    public MainWindow(MainViewModel viewModel)
    {
        InitializeComponent();

        _viewModel = viewModel;
        DataContext = _viewModel;

        // 订阅窗口关闭事件以检查未保存的更改
        Closing += OnWindowClosing;

        // 确保窗口正确显示
        Loaded += OnWindowLoaded;
    }

    private void OnWindowLoaded(object sender, RoutedEventArgs e)
    {
        // 确保窗口在屏幕内
        EnsureWindowOnScreen();

        // 激活窗口
        Activate();
        Focus();
    }

    private void EnsureWindowOnScreen()
    {
        // 获取工作区域
        var workingArea = SystemParameters.WorkArea;

        // 确保窗口不超出屏幕边界
        if (Left < 0) Left = 0;
        if (Top < 0) Top = 0;
        if (Left + Width > workingArea.Width) Left = workingArea.Width - Width;
        if (Top + Height > workingArea.Height) Top = workingArea.Height - Height;

        // 如果窗口太大，调整大小
        if (Width > workingArea.Width) Width = workingArea.Width * 0.9;
        if (Height > workingArea.Height) Height = workingArea.Height * 0.9;
    }

    private void OnWindowClosing(object? sender, CancelEventArgs e)
    {
        // 检查是否有未保存的更改
        if (_viewModel.HasUnsavedChanges)
        {
            var result = MessageBox.Show(
                "当前配置有未保存的更改，是否要保存？\n\n点击\"是\"保存并退出\n点击\"否\"不保存直接退出\n点击\"取消\"继续编辑",
                "未保存的更改",
                MessageBoxButton.YesNoCancel,
                MessageBoxImage.Question);

            switch (result)
            {
                case MessageBoxResult.Yes:
                    // 尝试保存
                    if (_viewModel.SaveCommand.CanExecute(null))
                    {
                        try
                        {
                            _viewModel.SaveCommand.Execute(null);
                            // 如果保存成功，允许关闭
                            if (_viewModel.HasUnsavedChanges)
                            {
                                // 保存失败，取消关闭
                                e.Cancel = true;
                            }
                        }
                        catch
                        {
                            // 保存出错，取消关闭
                            e.Cancel = true;
                        }
                    }
                    else
                    {
                        // 无法保存（可能没有文件路径），显示另存为对话框
                        if (_viewModel.SaveAsCommand.CanExecute(null))
                        {
                            try
                            {
                                _viewModel.SaveAsCommand.Execute(null);
                                // 如果保存成功，允许关闭
                                if (_viewModel.HasUnsavedChanges)
                                {
                                    // 保存失败或用户取消，取消关闭
                                    e.Cancel = true;
                                }
                            }
                            catch
                            {
                                // 保存出错，取消关闭
                                e.Cancel = true;
                            }
                        }
                        else
                        {
                            // 无法保存，取消关闭
                            e.Cancel = true;
                        }
                    }
                    break;

                case MessageBoxResult.No:
                    // 不保存，直接退出
                    break;

                case MessageBoxResult.Cancel:
                    // 取消关闭
                    e.Cancel = true;
                    break;
            }
        }
    }
}
