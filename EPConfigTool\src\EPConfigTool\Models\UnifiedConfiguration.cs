using EventProcessor.Core.Models;
using System.ComponentModel.DataAnnotations;

namespace EPConfigTool.Models;

/// <summary>
/// 统一配置模型 - 支持新的 YAML 配置文件格式
/// 包含所有配置节：Logging、Serilog、EventProcessor、Mqtt、ErrorHandling
/// </summary>
public record UnifiedConfiguration
{
    /// <summary>
    /// 日志配置
    /// </summary>
    public LoggingConfiguration? Logging { get; init; }

    /// <summary>
    /// Serilog 配置
    /// </summary>
    public SerilogConfiguration? Serilog { get; init; }

    /// <summary>
    /// 事件处理配置
    /// </summary>
    [Required(ErrorMessage = "事件处理配置不能为空")]
    public required EventConfiguration EventProcessor { get; init; }

    /// <summary>
    /// MQTT 配置
    /// </summary>
    [Required(ErrorMessage = "MQTT配置不能为空")]
    public required MqttConfiguration Mqtt { get; init; }

    /// <summary>
    /// 错误处理配置
    /// </summary>
    [Required(ErrorMessage = "错误处理配置不能为空")]
    public required ErrorHandlingConfiguration ErrorHandling { get; init; }
}

/// <summary>
/// 日志配置
/// </summary>
public record LoggingConfiguration
{
    /// <summary>
    /// 日志级别配置
    /// </summary>
    public LogLevelConfiguration? LogLevel { get; init; }
}

/// <summary>
/// 日志级别配置
/// </summary>
public record LogLevelConfiguration
{
    /// <summary>
    /// 默认日志级别
    /// </summary>
    [RegularExpression("^(Trace|Debug|Information|Warning|Error|Critical)$", 
        ErrorMessage = "日志级别必须是Trace、Debug、Information、Warning、Error或Critical")]
    public string Default { get; init; } = "Information";

    /// <summary>
    /// Microsoft 组件日志级别
    /// </summary>
    [RegularExpression("^(Trace|Debug|Information|Warning|Error|Critical)$", 
        ErrorMessage = "日志级别必须是Trace、Debug、Information、Warning、Error或Critical")]
    public string Microsoft { get; init; } = "Warning";

    /// <summary>
    /// Microsoft.Hosting.Lifetime 日志级别
    /// </summary>
    [RegularExpression("^(Trace|Debug|Information|Warning|Error|Critical)$", 
        ErrorMessage = "日志级别必须是Trace、Debug、Information、Warning、Error或Critical")]
    public string? MicrosoftHostingLifetime { get; init; } = "Information";

    /// <summary>
    /// EventProcessor 日志级别
    /// </summary>
    [RegularExpression("^(Trace|Debug|Information|Warning|Error|Critical)$", 
        ErrorMessage = "日志级别必须是Trace、Debug、Information、Warning、Error或Critical")]
    public string EventProcessor { get; init; } = "Debug";
}

/// <summary>
/// Serilog 配置
/// </summary>
public record SerilogConfiguration
{
    /// <summary>
    /// 使用的 Serilog 包
    /// </summary>
    public string[]? Using { get; init; }

    /// <summary>
    /// 最小日志级别配置
    /// </summary>
    public SerilogMinimumLevelConfiguration? MinimumLevel { get; init; }

    /// <summary>
    /// 写入目标配置
    /// </summary>
    public SerilogWriteToConfiguration[]? WriteTo { get; init; }

    /// <summary>
    /// 日志增强配置
    /// </summary>
    public string[]? Enrich { get; init; }
}

/// <summary>
/// Serilog 最小日志级别配置
/// </summary>
public record SerilogMinimumLevelConfiguration
{
    /// <summary>
    /// 默认最小级别
    /// </summary>
    [RegularExpression("^(Verbose|Debug|Information|Warning|Error|Fatal)$", 
        ErrorMessage = "Serilog日志级别必须是Verbose、Debug、Information、Warning、Error或Fatal")]
    public string Default { get; init; } = "Information";

    /// <summary>
    /// 覆盖配置
    /// </summary>
    public Dictionary<string, string>? Override { get; init; }
}

/// <summary>
/// Serilog 写入目标配置
/// </summary>
public record SerilogWriteToConfiguration
{
    /// <summary>
    /// 写入目标名称
    /// </summary>
    [Required(ErrorMessage = "写入目标名称不能为空")]
    public required string Name { get; init; }

    /// <summary>
    /// 写入目标参数
    /// </summary>
    public Dictionary<string, object>? Args { get; init; }
}

/// <summary>
/// 统一配置验证结果
/// </summary>
public record UnifiedConfigurationValidationResult
{
    /// <summary>
    /// 验证是否通过
    /// </summary>
    public bool IsValid { get; init; }

    /// <summary>
    /// 错误信息列表
    /// </summary>
    public List<string> Errors { get; init; } = new();

    /// <summary>
    /// 警告信息列表
    /// </summary>
    public List<string> Warnings { get; init; } = new();

    /// <summary>
    /// 各配置节的验证结果
    /// </summary>
    public Dictionary<string, List<string>> SectionErrors { get; init; } = new();
}
