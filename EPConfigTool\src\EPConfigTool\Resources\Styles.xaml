<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <!-- 字体定义 -->
    <FontFamily x:Key="DefaultFont">Microsoft YaHei UI</FontFamily>
    <FontFamily x:Key="MonospaceFont">Consolas</FontFamily>

    <!-- 颜色定义 -->
    <SolidColorBrush x:Key="PrimaryBrush" Color="#2196F3"/>
    <SolidColorBrush x:Key="SecondaryBrush" Color="#FFC107"/>
    <SolidColorBrush x:Key="ErrorBrush" Color="#F44336"/>
    <SolidColorBrush x:Key="SuccessBrush" Color="#4CAF50"/>
    <SolidColorBrush x:Key="WarningBrush" Color="#FF9800"/>

    <!-- 基础样式 -->
    <Style x:Key="BaseTextBlockStyle" TargetType="TextBlock">
        <Setter Property="FontFamily" Value="{StaticResource DefaultFont}"/>
        <Setter Property="FontSize" Value="12"/>
        <Setter Property="Foreground" Value="#333333"/>
    </Style>

    <Style x:Key="HeaderTextBlockStyle" TargetType="TextBlock" BasedOn="{StaticResource BaseTextBlockStyle}">
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="FontWeight" Value="SemiBold"/>
        <Setter Property="Foreground" Value="{StaticResource PrimaryBrush}"/>
        <Setter Property="Margin" Value="0,0,0,5"/>
    </Style>

    <Style x:Key="LabelTextBlockStyle" TargetType="TextBlock" BasedOn="{StaticResource BaseTextBlockStyle}">
        <Setter Property="FontWeight" Value="SemiBold"/>
        <Setter Property="Margin" Value="0,5,0,2"/>
    </Style>

    <!-- 输入控件样式 -->
    <Style x:Key="BaseTextBoxStyle" TargetType="TextBox">
        <Setter Property="FontFamily" Value="{StaticResource DefaultFont}"/>
        <Setter Property="FontSize" Value="12"/>
        <Setter Property="Padding" Value="5"/>
        <Setter Property="Margin" Value="0,0,0,10"/>
        <Setter Property="BorderBrush" Value="#CCCCCC"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="Background" Value="White"/>
        <Style.Triggers>
            <Trigger Property="IsReadOnly" Value="True">
                <Setter Property="Background" Value="#F5F5F5"/>
                <Setter Property="Foreground" Value="#666666"/>
            </Trigger>
            <Trigger Property="IsFocused" Value="True">
                <Setter Property="BorderBrush" Value="{StaticResource PrimaryBrush}"/>
            </Trigger>
        </Style.Triggers>
    </Style>

    <Style x:Key="BaseComboBoxStyle" TargetType="ComboBox">
        <Setter Property="FontFamily" Value="{StaticResource DefaultFont}"/>
        <Setter Property="FontSize" Value="12"/>
        <Setter Property="Padding" Value="5"/>
        <Setter Property="Margin" Value="0,0,0,10"/>
        <Setter Property="BorderBrush" Value="#CCCCCC"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="Background" Value="White"/>
        <Style.Triggers>
            <Trigger Property="IsFocused" Value="True">
                <Setter Property="BorderBrush" Value="{StaticResource PrimaryBrush}"/>
            </Trigger>
        </Style.Triggers>
    </Style>

    <!-- 按钮样式 -->
    <Style x:Key="PrimaryButtonStyle" TargetType="Button">
        <Setter Property="FontFamily" Value="{StaticResource DefaultFont}"/>
        <Setter Property="FontSize" Value="12"/>
        <Setter Property="FontWeight" Value="SemiBold"/>
        <Setter Property="Padding" Value="15,8"/>
        <Setter Property="Margin" Value="5"/>
        <Setter Property="Background" Value="{StaticResource PrimaryBrush}"/>
        <Setter Property="Foreground" Value="White"/>
        <Setter Property="BorderThickness" Value="0"/>
        <Setter Property="Cursor" Value="Hand"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Button">
                    <Border Background="{TemplateBinding Background}" 
                            CornerRadius="3"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}">
                        <ContentPresenter HorizontalAlignment="Center" 
                                        VerticalAlignment="Center"
                                        Margin="{TemplateBinding Padding}"/>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter Property="Background" Value="#1976D2"/>
                        </Trigger>
                        <Trigger Property="IsPressed" Value="True">
                            <Setter Property="Background" Value="#1565C0"/>
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter Property="Background" Value="#CCCCCC"/>
                            <Setter Property="Foreground" Value="#666666"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style x:Key="SecondaryButtonStyle" TargetType="Button" BasedOn="{StaticResource PrimaryButtonStyle}">
        <Setter Property="Background" Value="White"/>
        <Setter Property="Foreground" Value="{StaticResource PrimaryBrush}"/>
        <Setter Property="BorderBrush" Value="{StaticResource PrimaryBrush}"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Style.Triggers>
            <Trigger Property="IsMouseOver" Value="True">
                <Setter Property="Background" Value="#F5F5F5"/>
            </Trigger>
            <Trigger Property="IsPressed" Value="True">
                <Setter Property="Background" Value="#EEEEEE"/>
            </Trigger>
        </Style.Triggers>
    </Style>

    <Style x:Key="ToolBarButtonStyle" TargetType="Button">
        <Setter Property="FontFamily" Value="{StaticResource DefaultFont}"/>
        <Setter Property="FontSize" Value="12"/>
        <Setter Property="Padding" Value="10,5"/>
        <Setter Property="Margin" Value="2"/>
        <Setter Property="Background" Value="Transparent"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="BorderBrush" Value="Transparent"/>
        <Setter Property="Cursor" Value="Hand"/>
        <Style.Triggers>
            <Trigger Property="IsMouseOver" Value="True">
                <Setter Property="Background" Value="#E3F2FD"/>
                <Setter Property="BorderBrush" Value="{StaticResource PrimaryBrush}"/>
            </Trigger>
            <Trigger Property="IsPressed" Value="True">
                <Setter Property="Background" Value="#BBDEFB"/>
            </Trigger>
            <Trigger Property="IsEnabled" Value="False">
                <Setter Property="Foreground" Value="#CCCCCC"/>
            </Trigger>
        </Style.Triggers>
    </Style>

    <!-- 数据网格样式 -->
    <Style x:Key="BaseDataGridStyle" TargetType="DataGrid">
        <Setter Property="FontFamily" Value="{StaticResource DefaultFont}"/>
        <Setter Property="FontSize" Value="12"/>
        <Setter Property="Background" Value="White"/>
        <Setter Property="BorderBrush" Value="#CCCCCC"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="GridLinesVisibility" Value="Horizontal"/>
        <Setter Property="HorizontalGridLinesBrush" Value="#EEEEEE"/>
        <Setter Property="AlternatingRowBackground" Value="#FAFAFA"/>
        <Setter Property="RowBackground" Value="White"/>
        <Setter Property="HeadersVisibility" Value="Column"/>
        <Setter Property="AutoGenerateColumns" Value="False"/>
        <Setter Property="CanUserAddRows" Value="False"/>
        <Setter Property="CanUserDeleteRows" Value="False"/>
        <Setter Property="SelectionMode" Value="Single"/>
        <Setter Property="SelectionUnit" Value="FullRow"/>
    </Style>

    <!-- TabControl 样式 -->
    <Style x:Key="BaseTabControlStyle" TargetType="TabControl">
        <Setter Property="FontFamily" Value="{StaticResource DefaultFont}"/>
        <Setter Property="FontSize" Value="12"/>
        <Setter Property="Background" Value="White"/>
        <Setter Property="BorderBrush" Value="#CCCCCC"/>
        <Setter Property="BorderThickness" Value="1"/>
    </Style>

    <!-- GroupBox 样式 -->
    <Style x:Key="BaseGroupBoxStyle" TargetType="GroupBox">
        <Setter Property="FontFamily" Value="{StaticResource DefaultFont}"/>
        <Setter Property="FontSize" Value="12"/>
        <Setter Property="FontWeight" Value="SemiBold"/>
        <Setter Property="Foreground" Value="{StaticResource PrimaryBrush}"/>
        <Setter Property="BorderBrush" Value="#CCCCCC"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="Margin" Value="0,5,0,10"/>
        <Setter Property="Padding" Value="10"/>
    </Style>

    <!-- 状态栏样式 -->
    <Style x:Key="BaseStatusBarStyle" TargetType="StatusBar">
        <Setter Property="FontFamily" Value="{StaticResource DefaultFont}"/>
        <Setter Property="FontSize" Value="11"/>
        <Setter Property="Background" Value="#F5F5F5"/>
        <Setter Property="BorderBrush" Value="#CCCCCC"/>
        <Setter Property="BorderThickness" Value="0,1,0,0"/>
    </Style>

</ResourceDictionary>
