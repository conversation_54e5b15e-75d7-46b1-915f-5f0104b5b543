using EventProcessor.Core.Models;
using Microsoft.Extensions.Logging;
using System.Text.Json;
using System.Text.RegularExpressions;

namespace EPConfigTool.Services;

/// <summary>
/// 告警消息预览服务实现
/// 基于EP的AlarmGenerator逻辑，提供告警消息预览功能
/// </summary>
public class AlarmPreviewService : IAlarmPreviewService
{
    private readonly ILogger<AlarmPreviewService> _logger;

    // 各种数据源的示例数据模板
    private static readonly Dictionary<string, Dictionary<string, object>> SampleDataTemplates = new()
    {
        ["ExclusionRules"] = new()
        {
            ["log_remain_days"] = 15,
            ["CardType"] = "月租卡",
            ["log_car_no"] = "粤A12345",
            ["user_name"] = "张三",
            ["card_status"] = "valid"
        },
        ["BusinessRules"] = new()
        {
            ["log_remain_days"] = 0,
            ["CardType"] = "过期卡",
            ["log_car_no"] = "粤B67890",
            ["user_name"] = "李四",
            ["parking_duration"] = 120,
            ["violation_type"] = "超时停车"
        },
        ["AIResultRules"] = new()
        {
            ["ai_result"] = "检测到违规停车",
            ["confidence"] = 0.95,
            ["detected_objects"] = "车辆,人员",
            ["analysis_time"] = "2024-01-15 14:30:25"
        },
        ["DeviceSignal"] = new()
        {
            ["device_id"] = "BDN8888",
            ["signal_type"] = "motion_detected",
            ["timestamp"] = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
            ["location"] = "出入口1"
        }
    };

    public AlarmPreviewService(ILogger<AlarmPreviewService> logger)
    {
        _logger = logger;
    }

    public AlarmPreviewResult GeneratePreview(AlarmConfiguration alarmConfig, Dictionary<string, object>? sampleData = null)
    {
        try
        {
            var errors = new List<string>();
            var warnings = new List<string>();
            var previewFields = new Dictionary<string, string>();

            // 检查配置有效性
            if (alarmConfig.Fields == null || alarmConfig.Fields.Length == 0)
            {
                return new AlarmPreviewResult
                {
                    IsSuccess = false,
                    Fields = new Dictionary<string, string>(),
                    JsonPreview = "{}",
                    Errors = new[] { "告警配置为空，无法生成预览" }
                };
            }

            // 处理每个字段映射
            foreach (var fieldMapping in alarmConfig.Fields)
            {
                try
                {
                    var fieldValue = GenerateFieldPreview(fieldMapping, sampleData);
                    previewFields[fieldMapping.AlarmFieldName] = fieldValue;
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "生成字段预览失败: {FieldName}", fieldMapping.AlarmFieldName);
                    previewFields[fieldMapping.AlarmFieldName] = $"[错误: {ex.Message}]";
                    errors.Add($"字段 '{fieldMapping.AlarmFieldName}' 预览失败: {ex.Message}");
                }
            }

            // 生成JSON预览
            var jsonPreview = GenerateJsonPreview(previewFields, alarmConfig);

            return new AlarmPreviewResult
            {
                IsSuccess = errors.Count == 0,
                Fields = previewFields,
                JsonPreview = jsonPreview,
                Errors = errors.ToArray(),
                Warnings = warnings.ToArray()
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "生成告警预览失败");
            return new AlarmPreviewResult
            {
                IsSuccess = false,
                Fields = new Dictionary<string, string>(),
                JsonPreview = "{}",
                Errors = new[] { $"预览生成失败: {ex.Message}" }
            };
        }
    }

    public Dictionary<string, object> GetSampleData(string sourceRuleType)
    {
        if (SampleDataTemplates.TryGetValue(sourceRuleType, out var sampleData))
        {
            return new Dictionary<string, object>(sampleData);
        }

        // 返回通用示例数据
        return new Dictionary<string, object>
        {
            ["field1"] = "示例值1",
            ["field2"] = "示例值2",
            ["timestamp"] = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")
        };
    }

    public TemplateValidationResult ValidateFormatTemplate(string formatTemplate, string[] sourceFieldNames)
    {
        var errors = new List<string>();
        var suggestions = new List<string>();

        try
        {
            // 提取占位符
            var placeholders = ExtractPlaceholders(formatTemplate);
            
            // 检查未匹配的占位符
            var unmatchedPlaceholders = placeholders
                .Where(p => !sourceFieldNames.Contains(p))
                .ToArray();

            // 检查未使用的字段
            var unusedFields = sourceFieldNames
                .Where(f => !placeholders.Contains(f))
                .ToArray();

            // 验证占位符格式
            foreach (var placeholder in placeholders)
            {
                if (!IsValidPlaceholder(placeholder))
                {
                    errors.Add($"占位符格式无效: {placeholder}");
                }
            }

            // 生成建议
            if (unmatchedPlaceholders.Length > 0)
            {
                suggestions.Add($"以下占位符在源字段中不存在: {string.Join(", ", unmatchedPlaceholders)}");
            }

            if (unusedFields.Length > 0)
            {
                suggestions.Add($"以下源字段未在模板中使用: {string.Join(", ", unusedFields)}");
            }

            if (placeholders.Length == 0 && !string.IsNullOrEmpty(formatTemplate))
            {
                suggestions.Add("模板中未找到占位符，考虑使用 {字段名} 格式引用字段值");
            }

            return new TemplateValidationResult
            {
                IsValid = errors.Count == 0,
                Placeholders = placeholders,
                UnmatchedPlaceholders = unmatchedPlaceholders,
                UnusedFields = unusedFields,
                Errors = errors.ToArray(),
                Suggestions = suggestions.ToArray()
            };
        }
        catch (Exception ex)
        {
            return new TemplateValidationResult
            {
                IsValid = false,
                Placeholders = Array.Empty<string>(),
                UnmatchedPlaceholders = Array.Empty<string>(),
                UnusedFields = Array.Empty<string>(),
                Errors = new[] { $"模板验证失败: {ex.Message}" }
            };
        }
    }

    /// <summary>
    /// 生成单个字段的预览值
    /// </summary>
    private string GenerateFieldPreview(FieldMapping fieldMapping, Dictionary<string, object>? customSampleData)
    {
        // 获取数据源
        var sourceData = customSampleData ?? GetSampleData(fieldMapping.SourceRuleType);

        // 处理空字段名的情况
        if (string.IsNullOrWhiteSpace(fieldMapping.SourceFieldName))
        {
            return fieldMapping.DefaultValue ?? $"{{{fieldMapping.SourceRuleType}}}";
        }

        // 处理多字段情况
        if (fieldMapping.SourceFieldName.Contains(','))
        {
            return ProcessMultipleFields(sourceData, fieldMapping);
        }

        // 处理单字段情况
        var fieldName = fieldMapping.SourceFieldName.Trim();
        if (sourceData.TryGetValue(fieldName, out var value))
        {
            return ApplyFormatTemplate(value, fieldMapping.FormatTemplate, fieldName);
        }

        // 字段不存在时返回默认值或占位符
        return fieldMapping.DefaultValue ?? $"{{{fieldName}}}";
    }

    /// <summary>
    /// 处理多字段映射
    /// </summary>
    private string ProcessMultipleFields(Dictionary<string, object> sourceData, FieldMapping fieldMapping)
    {
        var fieldNames = fieldMapping.SourceFieldName.Split(',', StringSplitOptions.RemoveEmptyEntries);
        var fieldValues = new Dictionary<string, object>();

        foreach (var fieldName in fieldNames)
        {
            var trimmedFieldName = fieldName.Trim();
            if (sourceData.TryGetValue(trimmedFieldName, out var value))
            {
                fieldValues[trimmedFieldName] = value;
            }
            else
            {
                fieldValues[trimmedFieldName] = $"{{{trimmedFieldName}}}";
            }
        }

        return ApplyMultiFieldFormatTemplate(fieldValues, fieldMapping.FormatTemplate);
    }

    /// <summary>
    /// 应用格式化模板（单字段）
    /// </summary>
    private string ApplyFormatTemplate(object value, string? formatTemplate, string fieldName)
    {
        if (string.IsNullOrEmpty(formatTemplate))
        {
            return value?.ToString() ?? "";
        }

        try
        {
            // 检查是否包含占位符
            if (formatTemplate.Contains("{") && formatTemplate.Contains("}"))
            {
                // 检查格式化字符串（如 {value:P2}）
                var formatMatch = Regex.Match(formatTemplate, @"\{[^:}]+:([^}]+)\}");
                if (formatMatch.Success)
                {
                    var format = formatMatch.Groups[1].Value;
                    if (double.TryParse(value?.ToString(), out var numValue))
                    {
                        return numValue.ToString(format);
                    }
                }
                
                // 普通占位符替换
                var result = formatTemplate;
                result = result.Replace("{value}", value?.ToString() ?? "");
                result = result.Replace($"{{{fieldName}}}", value?.ToString() ?? "");
                return result;
            }
            else
            {
                // 直接格式化
                return string.Format(formatTemplate, value);
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "应用格式化模板失败: {Template}, 字段: {Field}", formatTemplate, fieldName);
            return value?.ToString() ?? "";
        }
    }

    /// <summary>
    /// 应用格式化模板（多字段）
    /// </summary>
    private string ApplyMultiFieldFormatTemplate(Dictionary<string, object> fieldValues, string? formatTemplate)
    {
        if (string.IsNullOrEmpty(formatTemplate))
        {
            // 没有模板时，使用默认格式
            return string.Join(",", fieldValues.Select(kvp => $"{kvp.Key}:{kvp.Value}"));
        }

        try
        {
            var result = formatTemplate;
            foreach (var kvp in fieldValues)
            {
                var placeholder = $"{{{kvp.Key}}}";
                result = result.Replace(placeholder, kvp.Value?.ToString() ?? "");
            }
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "应用多字段格式化模板失败: {Template}", formatTemplate);
            return string.Join(",", fieldValues.Select(kvp => $"{kvp.Key}:{kvp.Value}"));
        }
    }

    /// <summary>
    /// 生成JSON预览
    /// </summary>
    private string GenerateJsonPreview(Dictionary<string, string> fields, AlarmConfiguration alarmConfig)
    {
        try
        {
            var previewObject = new Dictionary<string, object>
            {
                ["event_id"] = "EV001001",
                ["instance_id"] = Guid.NewGuid().ToString("N")[..8],
                ["timestamp"] = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
                ["alarm_type"] = "Alarm"
            };

            // 添加字段映射的结果
            foreach (var kvp in fields)
            {
                previewObject[kvp.Key] = kvp.Value;
            }

            // 添加自定义模板（如果有）
            if (!string.IsNullOrEmpty(alarmConfig.CustomTemplate))
            {
                previewObject["custom_template"] = alarmConfig.CustomTemplate;
            }

            var options = new JsonSerializerOptions
            {
                WriteIndented = true,
                Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping
            };

            return JsonSerializer.Serialize(previewObject, options);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "生成JSON预览失败");
            return "{ \"error\": \"JSON预览生成失败\" }";
        }
    }

    /// <summary>
    /// 提取模板中的占位符
    /// </summary>
    private string[] ExtractPlaceholders(string formatTemplate)
    {
        if (string.IsNullOrEmpty(formatTemplate))
        {
            return Array.Empty<string>();
        }

        var placeholderPattern = @"\{([^}:]+)(?::[^}]*)?\}";
        var matches = Regex.Matches(formatTemplate, placeholderPattern);
        
        return matches
            .Cast<Match>()
            .Select(m => m.Groups[1].Value.Trim())
            .Where(p => p != "value") // 排除通用的 {value} 占位符
            .Distinct()
            .ToArray();
    }

    /// <summary>
    /// 验证占位符格式
    /// </summary>
    private bool IsValidPlaceholder(string placeholder)
    {
        // 检查占位符是否为有效的字段名格式
        return Regex.IsMatch(placeholder, @"^[a-zA-Z_][a-zA-Z0-9_]*$");
    }
}