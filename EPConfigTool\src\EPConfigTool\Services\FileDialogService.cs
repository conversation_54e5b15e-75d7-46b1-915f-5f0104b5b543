using Microsoft.Win32;
using System.IO;

namespace EPConfigTool.Services;

/// <summary>
/// 文件对话框服务实现
/// 专门处理 YAML 配置文件的打开和保存对话框
/// </summary>
public class FileDialogService : IFileDialogService
{
    private const string YamlFileFilter = "YAML 配置文件 (*.yaml;*.yml)|*.yaml;*.yml|YAML 文件 (*.yaml)|*.yaml|YML 文件 (*.yml)|*.yml|所有文件 (*.*)|*.*";
    private const string DefaultExtension = ".yaml";

    public string? ShowOpenYamlFileDialog(string title = "打开 YAML 配置文件", string? initialDirectory = null)
    {
        var dialog = new OpenFileDialog
        {
            Title = title,
            Filter = YamlFileFilter,
            FilterIndex = 1, // 默认选择 YAML 配置文件过滤器
            DefaultExt = DefaultExtension,
            CheckFileExists = true,
            CheckPathExists = true,
            Multiselect = false
        };

        // 设置初始目录
        if (!string.IsNullOrEmpty(initialDirectory) && Directory.Exists(initialDirectory))
        {
            dialog.InitialDirectory = initialDirectory;
        }
        else
        {
            // 默认使用 ep_v4.1_augment/config 目录
            var defaultConfigPath = Path.Combine(
                Environment.CurrentDirectory, 
                "..", "..", "ep_v4.1_augment", "config");
            
            if (Directory.Exists(defaultConfigPath))
            {
                dialog.InitialDirectory = Path.GetFullPath(defaultConfigPath);
            }
        }

        return dialog.ShowDialog() == true ? dialog.FileName : null;
    }

    public string? ShowSaveYamlFileDialog(string title = "保存 YAML 配置文件", string? defaultFileName = null, string? initialDirectory = null)
    {
        var dialog = new SaveFileDialog
        {
            Title = title,
            Filter = YamlFileFilter,
            FilterIndex = 1, // 默认选择 YAML 配置文件过滤器
            DefaultExt = DefaultExtension,
            CheckPathExists = true,
            OverwritePrompt = true,
            AddExtension = true
        };

        // 设置默认文件名
        if (!string.IsNullOrEmpty(defaultFileName))
        {
            dialog.FileName = defaultFileName;
        }

        // 设置初始目录
        if (!string.IsNullOrEmpty(initialDirectory) && Directory.Exists(initialDirectory))
        {
            dialog.InitialDirectory = initialDirectory;
        }
        else
        {
            // 默认使用 ep_v4.1_augment/config 目录
            var defaultConfigPath = Path.Combine(
                Environment.CurrentDirectory, 
                "..", "..", "ep_v4.1_augment", "config");
            
            if (Directory.Exists(defaultConfigPath))
            {
                dialog.InitialDirectory = Path.GetFullPath(defaultConfigPath);
            }
        }

        return dialog.ShowDialog() == true ? dialog.FileName : null;
    }

    public string[] ShowOpenMultipleYamlFilesDialog(string title = "选择 YAML 配置文件", string? initialDirectory = null)
    {
        var dialog = new OpenFileDialog
        {
            Title = title,
            Filter = YamlFileFilter,
            FilterIndex = 1, // 默认选择 YAML 配置文件过滤器
            DefaultExt = DefaultExtension,
            CheckFileExists = true,
            CheckPathExists = true,
            Multiselect = true
        };

        // 设置初始目录
        if (!string.IsNullOrEmpty(initialDirectory) && Directory.Exists(initialDirectory))
        {
            dialog.InitialDirectory = initialDirectory;
        }
        else
        {
            // 默认使用 ep_v4.1_augment/config 目录
            var defaultConfigPath = Path.Combine(
                Environment.CurrentDirectory, 
                "..", "..", "ep_v4.1_augment", "config");
            
            if (Directory.Exists(defaultConfigPath))
            {
                dialog.InitialDirectory = Path.GetFullPath(defaultConfigPath);
            }
        }

        return dialog.ShowDialog() == true ? dialog.FileNames : Array.Empty<string>();
    }
}
