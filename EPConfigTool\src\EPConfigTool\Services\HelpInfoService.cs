using System.Collections.Generic;
using System.Linq;

namespace EPConfigTool.Services;

/// <summary>
/// 帮助信息服务实现
/// 提供所有配置项的详细帮助信息
/// </summary>
public class HelpInfoService : IHelpInfoService
{
    private readonly Dictionary<string, ConfigHelpInfo> _helpInfos;

    public HelpInfoService()
    {
        _helpInfos = InitializeHelpInfos();
    }

    public string GetToolTip(string configKey)
    {
        if (_helpInfos.TryGetValue(configKey, out var helpInfo))
        {
            return $"{helpInfo.DisplayName}\n\n{helpInfo.ShortDescription}\n\n" +
                   $"格式要求: {helpInfo.ValidValues}\n" +
                   $"示例: {helpInfo.ExampleValue}";
        }
        return "暂无帮助信息";
    }

    public string GetStatusBarInfo(string configKey)
    {
        if (_helpInfos.TryGetValue(configKey, out var helpInfo))
        {
            return $"【{helpInfo.DisplayName}】{helpInfo.DetailedDescription} | " +
                   $"使用场景: {helpInfo.UsageScenario} | " +
                   $"注意事项: {helpInfo.Notes}";
        }
        return "请选择配置项查看详细说明";
    }

    public ConfigHelpInfo GetHelpInfo(string configKey)
    {
        return _helpInfos.TryGetValue(configKey, out var helpInfo) 
            ? helpInfo 
            : new ConfigHelpInfo { Key = configKey, DisplayName = "未知配置项" };
    }

    public IEnumerable<string> GetAllConfigKeys()
    {
        return _helpInfos.Keys;
    }

    private Dictionary<string, ConfigHelpInfo> InitializeHelpInfos()
    {
        return new Dictionary<string, ConfigHelpInfo>
        {
            // 基本事件配置
            ["EventId"] = new ConfigHelpInfo
            {
                Key = "EventId",
                DisplayName = "事件ID",
                EnglishName = "Event ID",
                ShortDescription = "事件的唯一标识符，用于区分不同的事件类型",
                DetailedDescription = "事件ID是整个事件处理系统中最重要的标识符，必须全局唯一。系统通过事件ID来识别、路由和处理不同类型的事件。",
                FunctionDescription = "作为事件的主键，用于数据库存储、日志记录、告警生成等所有相关操作",
                ValidValues = "必须以'EV'开头，后跟6位数字，如 EV001001",
                ExampleValue = "EV001001, EV002003, EV999999",
                UsageScenario = "每个具体的业务场景都需要一个独特的事件ID，如停车场滞留检测、违规停车检测等",
                Notes = "一旦配置并投入使用，不建议修改事件ID，因为会影响历史数据的关联性",
                RelatedConfigs = "EventName, Priority",
                CommonErrors = "格式错误（不是EV开头或数字位数不对）、重复使用已存在的ID",
                BestPractices = "建议按业务模块分配ID段，如EV001xxx为停车场相关，EV002xxx为交通相关",
                IsRequired = true,
                Category = "基本配置"
            },

            ["EventName"] = new ConfigHelpInfo
            {
                Key = "EventName",
                DisplayName = "事件名称",
                EnglishName = "Event Name",
                ShortDescription = "事件的中文描述名称，用于界面显示和日志记录",
                DetailedDescription = "事件名称是对事件业务含义的直观描述，会显示在告警信息、日志记录和管理界面中，帮助用户快速理解事件的业务含义。",
                FunctionDescription = "用于告警标题、日志记录、统计报表等场景的显示文本",
                ValidValues = "1-100个字符的中文或英文描述",
                ExampleValue = "月租车未过期超时滞留出口, 访客车辆违规停放, 消防通道占用检测",
                UsageScenario = "所有需要向用户展示事件信息的场景，如告警通知、管理后台、移动端应用",
                Notes = "建议使用简洁明了的中文描述，避免使用技术术语",
                RelatedConfigs = "EventId, AlarmConfig.Fields",
                CommonErrors = "名称过长、使用特殊字符、描述不清晰",
                BestPractices = "使用业务人员容易理解的术语，保持命名的一致性",
                IsRequired = true,
                Category = "基本配置"
            },

            ["EvaluationStrategy"] = new ConfigHelpInfo
            {
                Key = "EvaluationStrategy",
                DisplayName = "评估策略",
                EnglishName = "Evaluation Strategy",
                ShortDescription = "决定事件处理使用的评估方式：纯业务规则、AI分析或两者结合",
                DetailedDescription = "评估策略决定了系统如何判断事件是否应该触发告警。不同策略适用于不同的业务场景和准确性要求。",
                FunctionDescription = "控制事件处理引擎的工作模式，影响规则执行顺序和结果合并方式",
                ValidValues = "BusinessOnly（仅业务规则）, AI（仅AI分析）, AIAndBusiness（AI和业务规则结合）",
                ExampleValue = "BusinessOnly - 适用于规则明确的场景\nAI - 适用于需要图像识别的场景\nAIAndBusiness - 适用于复杂判断场景",
                UsageScenario = "根据业务复杂度和准确性要求选择：简单规则用BusinessOnly，复杂场景用AIAndBusiness",
                Notes = "AI策略需要配置AIPrompt和相关AI服务，会增加处理时间和成本",
                RelatedConfigs = "AIPrompt, AIAnalysisDelaySec, BusinessRules, AIResultRules",
                CommonErrors = "选择AI策略但未配置AI相关参数",
                BestPractices = "优先使用BusinessOnly，确实需要时再考虑AI策略",
                IsRequired = true,
                Category = "基本配置"
            },

            ["Priority"] = new ConfigHelpInfo
            {
                Key = "Priority",
                DisplayName = "优先级",
                EnglishName = "Priority",
                ShortDescription = "事件的重要程度，影响告警处理的优先顺序",
                DetailedDescription = "优先级决定了事件在系统中的处理顺序和告警的紧急程度。高优先级事件会优先处理，并可能触发更紧急的通知方式。",
                FunctionDescription = "用于告警排序、资源分配、通知策略选择等",
                ValidValues = "P1（最高）, P2（高）, P3（中等）, P4（低）, P5（最低）",
                ExampleValue = "P1 - 消防安全事件\nP2 - 安全违规事件\nP3 - 一般违规事件\nP4 - 提醒类事件\nP5 - 统计类事件",
                UsageScenario = "根据事件对业务的影响程度设置，影响告警通知方式和处理时效",
                Notes = "P1和P2级别的事件可能会触发短信或电话通知",
                RelatedConfigs = "AlarmGracePeriodSeconds, EnableAlarmCancellation",
                CommonErrors = "所有事件都设置为高优先级，导致优先级失去意义",
                BestPractices = "合理分配优先级，确保真正重要的事件能够得到及时处理",
                IsRequired = true,
                Category = "基本配置"
            },

            ["CommId"] = new ConfigHelpInfo
            {
                Key = "CommId",
                DisplayName = "小区ID",
                EnglishName = "Community ID",
                ShortDescription = "标识事件所属的小区或区域",
                DetailedDescription = "小区ID用于标识事件发生的具体区域或小区，便于按区域进行事件管理、统计分析和权限控制。",
                FunctionDescription = "用于事件分组、权限控制、统计报表的区域维度",
                ValidValues = "数字字符串，通常为6位数字",
                ExampleValue = "101013, 102001, 999888",
                UsageScenario = "多小区管理场景，需要按小区分别管理和统计事件",
                Notes = "必须与实际的小区编码体系保持一致",
                RelatedConfigs = "PositionId, DeviceSignal.Topics",
                CommonErrors = "使用了不存在的小区ID，导致权限或统计问题",
                BestPractices = "建立统一的小区编码规范，确保编码的唯一性和可维护性",
                IsRequired = true,
                Category = "基本配置"
            },

            ["PositionId"] = new ConfigHelpInfo
            {
                Key = "PositionId",
                DisplayName = "位置ID",
                EnglishName = "Position ID",
                ShortDescription = "标识事件发生的具体位置或设备点位",
                DetailedDescription = "位置ID精确标识事件发生的具体位置，如某个出入口、停车位、摄像头点位等，用于精确定位和设备关联。",
                FunctionDescription = "用于设备关联、位置定位、维护管理等",
                ValidValues = "字符串，通常包含位置编码信息",
                ExampleValue = "P002LfyBmOut（2号出口）, CAM001（1号摄像头）, PARK_A01（A区1号车位）",
                UsageScenario = "需要精确定位事件发生位置的场景，如设备维护、现场处理等",
                Notes = "应与实际的设备部署和位置编码保持一致",
                RelatedConfigs = "CommId, DeviceSignal.Topics",
                CommonErrors = "位置ID与实际设备不匹配，导致定位错误",
                BestPractices = "建立清晰的位置编码规范，包含区域、类型、序号等信息",
                IsRequired = true,
                Category = "基本配置"
            },

            // 设备信号配置
            ["DeviceSignal.Topics"] = new ConfigHelpInfo
            {
                Key = "DeviceSignal.Topics",
                DisplayName = "设备信号主题",
                EnglishName = "Device Signal Topics",
                ShortDescription = "MQTT主题列表，用于接收设备信号数据",
                DetailedDescription = "设备信号主题定义了系统监听哪些MQTT主题来接收设备数据。每个主题对应一个或多个设备的数据流。",
                FunctionDescription = "指定系统订阅的MQTT主题，用于接收设备状态变化信号",
                ValidValues = "MQTT主题字符串数组，支持通配符",
                ExampleValue = "device/BDN861290073715232/event, sensor/+/status, camera/*/motion",
                UsageScenario = "需要监控设备状态变化的场景，如传感器触发、摄像头检测等",
                Notes = "主题名称必须与实际设备发布的主题完全匹配",
                RelatedConfigs = "TriggerField, TriggerValues, HoldingTimeoutSec",
                CommonErrors = "主题名称拼写错误、权限不足无法订阅",
                BestPractices = "使用有意义的主题命名规范，避免使用过于宽泛的通配符",
                IsRequired = true,
                Category = "设备信号配置"
            },

            ["DeviceSignal.TriggerField"] = new ConfigHelpInfo
            {
                Key = "DeviceSignal.TriggerField",
                DisplayName = "触发字段",
                EnglishName = "Trigger Field",
                ShortDescription = "设备消息中用于判断触发条件的字段名",
                DetailedDescription = "触发字段指定了在接收到的设备消息中，哪个字段的值用于判断是否应该触发事件处理流程。",
                FunctionDescription = "从设备消息JSON中提取指定字段的值，与触发值进行比较",
                ValidValues = "设备消息JSON中存在的字段名",
                ExampleValue = "I2（输入端口2）, motion_detected, door_status, temperature",
                UsageScenario = "需要根据设备特定状态触发事件的场景",
                Notes = "字段名必须与设备实际发送的消息格式匹配",
                RelatedConfigs = "Topics, TriggerValues",
                CommonErrors = "字段名不存在、大小写不匹配",
                BestPractices = "与设备厂商确认消息格式，使用稳定的字段名",
                IsRequired = true,
                Category = "设备信号配置"
            },

            ["DeviceSignal.TriggerValues"] = new ConfigHelpInfo
            {
                Key = "DeviceSignal.TriggerValues",
                DisplayName = "触发值映射",
                EnglishName = "Trigger Values",
                ShortDescription = "定义触发字段的值与事件状态的映射关系",
                DetailedDescription = "触发值映射定义了当触发字段为特定值时，应该如何解释这个状态。通常用于将设备的原始值转换为业务含义。",
                FunctionDescription = "将设备原始状态值映射为标准化的事件状态",
                ValidValues = "键值对映射，键为设备值，值为标准状态",
                ExampleValue = "\"true\": \"0\" (触发时为0), \"false\": \"1\" (未触发时为1)",
                UsageScenario = "设备状态值与业务逻辑不直接对应的场景",
                Notes = "映射关系必须覆盖所有可能的设备状态值",
                RelatedConfigs = "TriggerField, HoldingTimeoutSec",
                CommonErrors = "遗漏某些状态值的映射、映射关系错误",
                BestPractices = "详细了解设备的所有可能状态，建立完整的映射关系",
                IsRequired = true,
                Category = "设备信号配置"
            },

            ["DeviceSignal.HoldingTimeoutSec"] = new ConfigHelpInfo
            {
                Key = "DeviceSignal.HoldingTimeoutSec",
                DisplayName = "保持超时时间",
                EnglishName = "Holding Timeout Seconds",
                ShortDescription = "设备信号保持状态的超时时间（秒）",
                DetailedDescription = "保持超时时间定义了设备信号需要持续多长时间才认为是有效触发。这可以避免因为设备抖动或瞬时干扰导致的误触发。",
                FunctionDescription = "防抖动机制，只有信号持续超过指定时间才触发事件处理",
                ValidValues = "正整数，单位为秒，建议范围1-3600",
                ExampleValue = "20（20秒）, 60（1分钟）, 300（5分钟）",
                UsageScenario = "需要避免误触发的场景，如车辆检测、人员滞留检测",
                Notes = "时间过短可能导致误触发，过长可能延迟有效事件的处理",
                RelatedConfigs = "TriggerField, TriggerValues",
                CommonErrors = "设置过短导致频繁误报、设置过长导致响应延迟",
                BestPractices = "根据实际业务场景和设备特性调整，通常设置为10-60秒",
                IsRequired = true,
                Category = "设备信号配置"
            },

            // 规则配置
            ["ExclusionRules"] = new ConfigHelpInfo
            {
                Key = "ExclusionRules",
                DisplayName = "排除规则",
                EnglishName = "Exclusion Rules",
                ShortDescription = "定义哪些情况下不应该触发事件告警",
                DetailedDescription = "排除规则用于过滤掉不需要告警的情况，如正常的业务操作、维护时段、特殊车辆等。排除规则优先于业务规则执行。",
                FunctionDescription = "在业务规则执行前先执行排除规则，如果满足排除条件则不触发告警",
                ValidValues = "规则组数组，每组包含数据源、逻辑操作符和条件列表",
                ExampleValue = "排除已过期的卡、排除维护时段、排除特殊车辆类型",
                UsageScenario = "需要过滤正常业务操作或特殊情况的场景",
                Notes = "排除规则会影响所有后续的业务规则判断",
                RelatedConfigs = "BusinessRules, Conditions",
                CommonErrors = "排除条件过于宽泛导致漏报、条件设置错误",
                BestPractices = "谨慎设置排除规则，定期检查排除效果",
                IsRequired = false,
                Category = "规则配置"
            },

            ["BusinessRules"] = new ConfigHelpInfo
            {
                Key = "BusinessRules",
                DisplayName = "业务规则",
                EnglishName = "Business Rules",
                ShortDescription = "定义触发事件告警的业务逻辑条件",
                DetailedDescription = "业务规则是事件处理的核心逻辑，定义了在什么条件下应该触发告警。支持复杂的条件组合和嵌套逻辑。",
                FunctionDescription = "根据业务数据判断是否应该生成告警，支持AND/OR逻辑组合",
                ValidValues = "规则组数组，支持条件组嵌套和复杂逻辑",
                ExampleValue = "车辆滞留超时、违规停放、未授权访问等业务场景",
                UsageScenario = "所有需要基于业务数据进行判断的告警场景",
                Notes = "业务规则的执行顺序和逻辑关系会影响最终的告警结果",
                RelatedConfigs = "ExclusionRules, AIResultRules, Conditions",
                CommonErrors = "逻辑关系设置错误、条件冲突、性能问题",
                BestPractices = "保持规则简洁明了，避免过于复杂的嵌套",
                IsRequired = true,
                Category = "规则配置"
            },

            ["Conditions.FieldName"] = new ConfigHelpInfo
            {
                Key = "Conditions.FieldName",
                DisplayName = "条件字段名",
                EnglishName = "Condition Field Name",
                ShortDescription = "用于条件判断的数据字段名称",
                DetailedDescription = "条件字段名指定了从数据源中提取哪个字段的值进行条件判断。字段名必须与数据源的实际字段匹配。",
                FunctionDescription = "从MQTT消息或其他数据源中提取指定字段的值用于条件比较",
                ValidValues = "数据源中存在的字段名，支持嵌套字段（用.分隔）",
                ExampleValue = "log_remain_days, log_car_no, user.name, device.status",
                UsageScenario = "所有需要基于具体数据字段进行判断的条件",
                Notes = "字段名大小写敏感，必须与数据源完全匹配",
                RelatedConfigs = "DataType, Operator, Value",
                CommonErrors = "字段名拼写错误、字段不存在、路径错误",
                BestPractices = "与数据提供方确认字段名称和结构",
                IsRequired = true,
                Category = "条件配置"
            },

            ["Conditions.DataType"] = new ConfigHelpInfo
            {
                Key = "Conditions.DataType",
                DisplayName = "数据类型",
                EnglishName = "Data Type",
                ShortDescription = "指定条件字段的数据类型",
                DetailedDescription = "数据类型决定了如何解析和比较字段值。不同的数据类型支持不同的比较操作符和比较逻辑。",
                FunctionDescription = "控制字段值的解析方式和可用的比较操作",
                ValidValues = "string（字符串）, number（数字）, datetime（日期时间）, boolean（布尔值）",
                ExampleValue = "string - 车牌号比较\nnumber - 数值大小比较\ndatetime - 时间范围比较",
                UsageScenario = "根据实际字段的数据类型选择相应的类型",
                Notes = "数据类型必须与实际字段值的类型匹配",
                RelatedConfigs = "FieldName, Operator, Value",
                CommonErrors = "类型不匹配导致比较失败、转换错误",
                BestPractices = "仔细确认字段的实际数据类型",
                IsRequired = true,
                Category = "条件配置"
            },

            ["Conditions.Operator"] = new ConfigHelpInfo
            {
                Key = "Conditions.Operator",
                DisplayName = "比较操作符",
                EnglishName = "Comparison Operator",
                ShortDescription = "定义字段值与目标值的比较方式",
                DetailedDescription = "比较操作符决定了如何比较字段值与设定的目标值。不同的数据类型支持不同的操作符。",
                FunctionDescription = "执行字段值与目标值的比较操作，返回布尔结果",
                ValidValues = "Equals, NotEquals, GreaterThan, LessThan, Contains, StartsWith, IsNull, IsNotNull等",
                ExampleValue = "GreaterThan - 数值大于比较\nContains - 字符串包含检查\nIsNull - 空值检查",
                UsageScenario = "根据业务逻辑需要选择合适的比较方式",
                Notes = "操作符必须与数据类型兼容",
                RelatedConfigs = "FieldName, DataType, Value",
                CommonErrors = "操作符与数据类型不匹配、逻辑错误",
                BestPractices = "选择最直观和高效的操作符",
                IsRequired = true,
                Category = "条件配置"
            },

            ["Conditions.Value"] = new ConfigHelpInfo
            {
                Key = "Conditions.Value",
                DisplayName = "比较值",
                EnglishName = "Comparison Value",
                ShortDescription = "用于与字段值进行比较的目标值",
                DetailedDescription = "比较值是条件判断的基准值，字段值将与此值进行比较。支持静态值、动态值和特殊值。",
                FunctionDescription = "作为比较操作的右操作数，与字段值进行比较",
                ValidValues = "根据数据类型而定，支持特殊值如now、today等",
                ExampleValue = "0（数字）, \"已过期\"（字符串）, \"now-1hour\"（相对时间）",
                UsageScenario = "所有需要设定比较基准的条件判断",
                Notes = "值的格式必须与数据类型匹配",
                RelatedConfigs = "FieldName, DataType, Operator",
                CommonErrors = "值格式错误、类型不匹配",
                BestPractices = "使用清晰明确的值，避免歧义",
                IsRequired = true,
                Category = "条件配置"
            },

            // 告警配置
            ["AlarmConfig.Fields"] = new ConfigHelpInfo
            {
                Key = "AlarmConfig.Fields",
                DisplayName = "告警字段配置",
                EnglishName = "Alarm Field Configuration",
                ShortDescription = "定义告警消息中包含的字段和格式",
                DetailedDescription = "告警字段配置决定了生成的告警消息包含哪些信息以及如何格式化这些信息。每个字段可以从不同的数据源获取值。",
                FunctionDescription = "从各种数据源提取信息，格式化后生成结构化的告警消息",
                ValidValues = "字段映射数组，每个映射包含告警字段名、数据源类型、源字段名等",
                ExampleValue = "详情字段、位置字段、时间字段、优先级字段等",
                UsageScenario = "所有需要生成告警消息的场景",
                Notes = "字段配置直接影响告警消息的可读性和有用性",
                RelatedConfigs = "SourceRuleType, FormatTemplate",
                CommonErrors = "字段映射错误、格式模板语法错误",
                BestPractices = "包含足够的上下文信息，使告警消息易于理解和处理",
                IsRequired = true,
                Category = "告警配置"
            },

            ["AlarmFieldName"] = new ConfigHelpInfo
            {
                Key = "AlarmFieldName",
                DisplayName = "告警字段名",
                EnglishName = "Alarm Field Name",
                ShortDescription = "告警消息中的字段名称",
                DetailedDescription = "告警字段名定义了在最终生成的告警消息中，这个字段应该叫什么名字。这个名字会显示在告警界面和通知消息中。",
                FunctionDescription = "作为告警消息JSON中的键名",
                ValidValues = "有意义的中文或英文字段名",
                ExampleValue = "详情, 位置, 事件类型, 发生时间, 优先级",
                UsageScenario = "定义告警消息的结构和字段名称",
                Notes = "字段名应该简洁明了，便于理解",
                RelatedConfigs = "SourceRuleType, SourceFieldName, FormatTemplate",
                CommonErrors = "字段名重复、名称不清晰",
                BestPractices = "使用业务人员熟悉的术语",
                IsRequired = true,
                Category = "告警配置"
            },

            ["SourceRuleType"] = new ConfigHelpInfo
            {
                Key = "SourceRuleType",
                DisplayName = "数据源类型",
                EnglishName = "Source Rule Type",
                ShortDescription = "指定告警字段值的数据来源类型",
                DetailedDescription = "数据源类型决定了告警字段的值从哪里获取：业务规则的执行结果、设备信号数据、静态配置值等。",
                FunctionDescription = "指导系统从正确的数据源提取字段值",
                ValidValues = "BusinessRules, DeviceSignal, Static, AIResult",
                ExampleValue = "BusinessRules - 从业务规则结果获取\nStatic - 使用静态配置值\nDeviceSignal - 从设备数据获取",
                UsageScenario = "根据字段值的实际来源选择相应的类型",
                Notes = "数据源类型必须与实际的数据可用性匹配",
                RelatedConfigs = "AlarmFieldName, SourceFieldName",
                CommonErrors = "数据源类型与实际数据不匹配",
                BestPractices = "优先使用可靠和稳定的数据源",
                IsRequired = true,
                Category = "告警配置"
            },

            // 特殊字段说明
            ["duration"] = new ConfigHelpInfo
            {
                Key = "duration",
                DisplayName = "滞留时长字段",
                EnglishName = "Duration Field",
                ShortDescription = "系统内部计算的实时滞留时长（单位：秒）",
                DetailedDescription = "duration 是一个由 EventProcessor 内部计算产生的特殊字段，表示设备信号的持续时长。" +
                                    "它的值由 DeviceSignal.HoldingTimeoutSec 配置决定，当收到触发信号后开始计时，" +
                                    "如果在指定时间内未收到结束信号，则触发超时事件。",
                FunctionDescription = "提供设备检测到的实际滞留时间，用于告警消息显示和业务判断",
                ValidValues = "正整数，单位为秒",
                ExampleValue = "20, 30, 45（表示滞留了20秒、30秒、45秒）",
                UsageScenario = "仅用于告警配置中显示滞留时间，不应在业务规则条件中使用",
                Notes = "⚠️ 重要：此字段不能在 BusinessRules 中作为条件使用！只能在 AlarmConfig 中使用，且 SourceRuleType 必须设置为 'DeviceSignal'",
                RelatedConfigs = "DeviceSignal.HoldingTimeoutSec, AlarmConfig.Fields",
                CommonErrors = "在 BusinessRules 中使用 duration 字段、SourceRuleType 设置错误",
                BestPractices = "仅在告警配置中使用此字段显示实际滞留时间，滞留检测逻辑由 DeviceSignal.HoldingTimeoutSec 控制",
                IsRequired = false,
                Category = "特殊字段"
            }
        };
    }
}
