using EventProcessor.Core.Models;

namespace EPConfigTool.Services;

/// <summary>
/// 告警消息预览服务接口
/// 提供告警消息预览功能，模拟EP的消息拼接逻辑
/// </summary>
public interface IAlarmPreviewService
{
    /// <summary>
    /// 生成告警消息预览
    /// </summary>
    /// <param name="alarmConfig">告警配置</param>
    /// <param name="sampleData">示例数据（可选）</param>
    /// <returns>告警消息预览结果</returns>
    AlarmPreviewResult GeneratePreview(AlarmConfiguration alarmConfig, Dictionary<string, object>? sampleData = null);

    /// <summary>
    /// 获取示例数据模板
    /// </summary>
    /// <param name="sourceRuleType">数据源类型</param>
    /// <returns>示例数据字典</returns>
    Dictionary<string, object> GetSampleData(string sourceRuleType);

    /// <summary>
    /// 验证格式化模板
    /// </summary>
    /// <param name="formatTemplate">格式化模板</param>
    /// <param name="sourceFieldNames">源字段名列表</param>
    /// <returns>验证结果</returns>
    TemplateValidationResult ValidateFormatTemplate(string formatTemplate, string[] sourceFieldNames);
}

/// <summary>
/// 告警消息预览结果
/// </summary>
public record AlarmPreviewResult
{
    /// <summary>
    /// 是否预览成功
    /// </summary>
    public required bool IsSuccess { get; init; }

    /// <summary>
    /// 预览消息字段
    /// </summary>
    public required Dictionary<string, string> Fields { get; init; }

    /// <summary>
    /// 完整的JSON预览
    /// </summary>
    public required string JsonPreview { get; init; }

    /// <summary>
    /// 错误信息
    /// </summary>
    public string[]? Errors { get; init; }

    /// <summary>
    /// 警告信息
    /// </summary>
    public string[]? Warnings { get; init; }
}

/// <summary>
/// 模板验证结果
/// </summary>
public record TemplateValidationResult
{
    /// <summary>
    /// 是否有效
    /// </summary>
    public required bool IsValid { get; init; }

    /// <summary>
    /// 找到的占位符列表
    /// </summary>
    public required string[] Placeholders { get; init; }

    /// <summary>
    /// 未匹配的占位符（源字段中不存在的）
    /// </summary>
    public required string[] UnmatchedPlaceholders { get; init; }

    /// <summary>
    /// 未使用的源字段（模板中未引用的）
    /// </summary>
    public required string[] UnusedFields { get; init; }

    /// <summary>
    /// 错误信息
    /// </summary>
    public string[]? Errors { get; init; }

    /// <summary>
    /// 建议信息
    /// </summary>
    public string[]? Suggestions { get; init; }
}