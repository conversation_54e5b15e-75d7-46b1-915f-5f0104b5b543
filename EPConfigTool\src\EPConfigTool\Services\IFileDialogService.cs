namespace EPConfigTool.Services;

/// <summary>
/// 文件对话框服务接口
/// 专门处理 YAML 配置文件的打开和保存对话框
/// </summary>
public interface IFileDialogService
{
    /// <summary>
    /// 显示打开 YAML 文件对话框
    /// </summary>
    /// <param name="title">对话框标题</param>
    /// <param name="initialDirectory">初始目录</param>
    /// <returns>选择的文件路径，如果取消则返回 null</returns>
    string? ShowOpenYamlFileDialog(string title = "打开 YAML 配置文件", string? initialDirectory = null);

    /// <summary>
    /// 显示保存 YAML 文件对话框
    /// </summary>
    /// <param name="title">对话框标题</param>
    /// <param name="defaultFileName">默认文件名</param>
    /// <param name="initialDirectory">初始目录</param>
    /// <returns>选择的文件路径，如果取消则返回 null</returns>
    string? ShowSaveYamlFileDialog(string title = "保存 YAML 配置文件", string? defaultFileName = null, string? initialDirectory = null);

    /// <summary>
    /// 显示选择多个 YAML 文件对话框
    /// </summary>
    /// <param name="title">对话框标题</param>
    /// <param name="initialDirectory">初始目录</param>
    /// <returns>选择的文件路径数组，如果取消则返回空数组</returns>
    string[] ShowOpenMultipleYamlFilesDialog(string title = "选择 YAML 配置文件", string? initialDirectory = null);
}
