using System.Collections.Generic;

namespace EPConfigTool.Services;

/// <summary>
/// 帮助信息服务接口
/// 提供配置项的详细帮助信息，包括 ToolTip 和状态栏说明
/// </summary>
public interface IHelpInfoService
{
    /// <summary>
    /// 获取配置项的 ToolTip 文本
    /// </summary>
    /// <param name="configKey">配置项键名</param>
    /// <returns>ToolTip 文本</returns>
    string GetToolTip(string configKey);

    /// <summary>
    /// 获取配置项的状态栏详细说明
    /// </summary>
    /// <param name="configKey">配置项键名</param>
    /// <returns>状态栏说明文本</returns>
    string GetStatusBarInfo(string configKey);

    /// <summary>
    /// 获取配置项的完整帮助信息
    /// </summary>
    /// <param name="configKey">配置项键名</param>
    /// <returns>完整帮助信息</returns>
    ConfigHelpInfo GetHelpInfo(string configKey);

    /// <summary>
    /// 获取所有可用的配置项键名
    /// </summary>
    /// <returns>配置项键名列表</returns>
    IEnumerable<string> GetAllConfigKeys();
}

/// <summary>
/// 配置项帮助信息
/// </summary>
public record ConfigHelpInfo
{
    /// <summary>
    /// 配置项键名
    /// </summary>
    public string Key { get; init; } = string.Empty;

    /// <summary>
    /// 中文显示名称
    /// </summary>
    public string DisplayName { get; init; } = string.Empty;

    /// <summary>
    /// 英文名称
    /// </summary>
    public string EnglishName { get; init; } = string.Empty;

    /// <summary>
    /// 简短描述（用于 ToolTip）
    /// </summary>
    public string ShortDescription { get; init; } = string.Empty;

    /// <summary>
    /// 详细描述（用于状态栏）
    /// </summary>
    public string DetailedDescription { get; init; } = string.Empty;

    /// <summary>
    /// 功能说明
    /// </summary>
    public string FunctionDescription { get; init; } = string.Empty;

    /// <summary>
    /// 有效值范围
    /// </summary>
    public string ValidValues { get; init; } = string.Empty;

    /// <summary>
    /// 示例值
    /// </summary>
    public string ExampleValue { get; init; } = string.Empty;

    /// <summary>
    /// 使用场景
    /// </summary>
    public string UsageScenario { get; init; } = string.Empty;

    /// <summary>
    /// 注意事项
    /// </summary>
    public string Notes { get; init; } = string.Empty;

    /// <summary>
    /// 相关配置项
    /// </summary>
    public string RelatedConfigs { get; init; } = string.Empty;

    /// <summary>
    /// 常见错误
    /// </summary>
    public string CommonErrors { get; init; } = string.Empty;

    /// <summary>
    /// 最佳实践
    /// </summary>
    public string BestPractices { get; init; } = string.Empty;

    /// <summary>
    /// 是否为必需项
    /// </summary>
    public bool IsRequired { get; init; }

    /// <summary>
    /// 配置分类
    /// </summary>
    public string Category { get; init; } = string.Empty;
}
