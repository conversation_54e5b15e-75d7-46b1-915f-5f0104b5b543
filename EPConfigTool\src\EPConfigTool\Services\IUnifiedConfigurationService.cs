using EPConfigTool.Models;
using EventProcessor.Core.Models;

namespace EPConfigTool.Services;

/// <summary>
/// 统一配置文件服务接口
/// 专门处理新的统一 YAML 配置文件格式，包含所有配置节
/// </summary>
public interface IUnifiedConfigurationService
{
    /// <summary>
    /// 从统一 YAML 文件加载配置
    /// </summary>
    /// <param name="filePath">YAML 文件路径（.yaml 或 .yml）</param>
    /// <returns>统一配置对象</returns>
    /// <exception cref="FileNotFoundException">文件不存在</exception>
    /// <exception cref="InvalidDataException">YAML 格式无效或数据结构不匹配</exception>
    Task<UnifiedConfiguration> LoadFromYamlFileAsync(string filePath);

    /// <summary>
    /// 将统一配置保存到 YAML 文件
    /// </summary>
    /// <param name="filePath">YAML 文件路径（.yaml 或 .yml）</param>
    /// <param name="configuration">统一配置对象</param>
    /// <exception cref="UnauthorizedAccessException">文件访问权限不足</exception>
    /// <exception cref="DirectoryNotFoundException">目录不存在</exception>
    Task SaveToYamlFileAsync(string filePath, UnifiedConfiguration configuration);

    /// <summary>
    /// 验证统一 YAML 文件格式和内容
    /// </summary>
    /// <param name="filePath">YAML 文件路径</param>
    /// <returns>验证结果，包含错误信息</returns>
    Task<UnifiedConfigurationValidationResult> ValidateYamlFileAsync(string filePath);

    /// <summary>
    /// 从 YAML 字符串解析统一配置
    /// </summary>
    /// <param name="yamlContent">YAML 内容字符串</param>
    /// <returns>统一配置对象</returns>
    UnifiedConfiguration ParseFromYamlString(string yamlContent);

    /// <summary>
    /// 将统一配置序列化为 YAML 字符串
    /// </summary>
    /// <param name="configuration">统一配置对象</param>
    /// <returns>格式化的 YAML 字符串</returns>
    string SerializeToYamlString(UnifiedConfiguration configuration);

    /// <summary>
    /// 从旧的事件配置创建统一配置
    /// </summary>
    /// <param name="eventConfiguration">事件配置</param>
    /// <param name="mqttConfiguration">MQTT配置（可选）</param>
    /// <param name="errorHandlingConfiguration">错误处理配置（可选）</param>
    /// <returns>统一配置对象</returns>
    UnifiedConfiguration CreateUnifiedConfiguration(
        EventConfiguration eventConfiguration,
        MqttConfiguration? mqttConfiguration = null,
        ErrorHandlingConfiguration? errorHandlingConfiguration = null);

    /// <summary>
    /// 从统一配置中提取事件配置
    /// </summary>
    /// <param name="unifiedConfiguration">统一配置</param>
    /// <returns>事件配置对象</returns>
    EventConfiguration ExtractEventConfiguration(UnifiedConfiguration unifiedConfiguration);

    /// <summary>
    /// 创建默认的统一配置
    /// </summary>
    /// <param name="eventId">事件ID</param>
    /// <param name="eventName">事件名称</param>
    /// <returns>默认统一配置对象</returns>
    UnifiedConfiguration CreateDefaultUnifiedConfiguration(string eventId, string eventName);

    /// <summary>
    /// 验证配置文件是否为统一格式
    /// </summary>
    /// <param name="filePath">文件路径</param>
    /// <returns>是否为统一格式</returns>
    Task<bool> IsUnifiedConfigurationFileAsync(string filePath);

    /// <summary>
    /// 从旧格式配置文件迁移到统一格式
    /// </summary>
    /// <param name="oldConfigFilePath">旧配置文件路径</param>
    /// <param name="newConfigFilePath">新配置文件路径</param>
    /// <param name="mqttConfiguration">MQTT配置（可选）</param>
    /// <param name="errorHandlingConfiguration">错误处理配置（可选）</param>
    /// <returns>迁移是否成功</returns>
    Task<bool> MigrateFromOldFormatAsync(
        string oldConfigFilePath,
        string newConfigFilePath,
        MqttConfiguration? mqttConfiguration = null,
        ErrorHandlingConfiguration? errorHandlingConfiguration = null);
}
