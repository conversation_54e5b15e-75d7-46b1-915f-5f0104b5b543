using EventProcessor.Core.Models;

namespace EPConfigTool.Services;

/// <summary>
/// YAML 配置文件服务接口
/// 专门处理 EP_V4.1 的 YAML 格式配置文件
/// </summary>
public interface IYamlConfigurationService
{
    /// <summary>
    /// 从 YAML 文件加载事件配置
    /// </summary>
    /// <param name="filePath">YAML 文件路径（.yaml 或 .yml）</param>
    /// <returns>事件配置对象</returns>
    /// <exception cref="FileNotFoundException">文件不存在</exception>
    /// <exception cref="InvalidDataException">YAML 格式无效或数据结构不匹配</exception>
    Task<EventConfiguration> LoadFromYamlFileAsync(string filePath);

    /// <summary>
    /// 将事件配置保存到 YAML 文件
    /// </summary>
    /// <param name="filePath">YAML 文件路径（.yaml 或 .yml）</param>
    /// <param name="configuration">事件配置对象</param>
    /// <exception cref="UnauthorizedAccessException">文件访问权限不足</exception>
    /// <exception cref="DirectoryNotFoundException">目录不存在</exception>
    Task SaveToYamlFileAsync(string filePath, EventConfiguration configuration);

    /// <summary>
    /// 验证 YAML 文件格式和内容
    /// </summary>
    /// <param name="filePath">YAML 文件路径</param>
    /// <returns>验证结果，包含错误信息</returns>
    Task<YamlValidationResult> ValidateYamlFileAsync(string filePath);

    /// <summary>
    /// 从 YAML 字符串解析事件配置
    /// </summary>
    /// <param name="yamlContent">YAML 内容字符串</param>
    /// <returns>事件配置对象</returns>
    EventConfiguration ParseFromYamlString(string yamlContent);

    /// <summary>
    /// 将事件配置序列化为 YAML 字符串
    /// </summary>
    /// <param name="configuration">事件配置对象</param>
    /// <returns>格式化的 YAML 字符串</returns>
    string SerializeToYamlString(EventConfiguration configuration);
}

/// <summary>
/// YAML 验证结果
/// </summary>
public record YamlValidationResult
{
    /// <summary>
    /// 验证是否通过
    /// </summary>
    public bool IsValid { get; init; }

    /// <summary>
    /// 错误信息列表
    /// </summary>
    public List<string> Errors { get; init; } = new();

    /// <summary>
    /// 警告信息列表
    /// </summary>
    public List<string> Warnings { get; init; } = new();
}
