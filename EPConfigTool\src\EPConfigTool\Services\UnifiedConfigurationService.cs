using EPConfigTool.Models;
using EventProcessor.Core.Models;
using Microsoft.Extensions.Logging;
using System.ComponentModel.DataAnnotations;
using System.IO;
using YamlDotNet.Core;
using YamlDotNet.Serialization;
using YamlDotNet.Serialization.NamingConventions;

namespace EPConfigTool.Services;

/// <summary>
/// 统一配置文件服务实现
/// 专门处理新的统一 YAML 配置文件格式
/// </summary>
public class UnifiedConfigurationService : IUnifiedConfigurationService
{
    private readonly ILogger<UnifiedConfigurationService> _logger;
    private readonly ISerializer _yamlSerializer;
    private readonly IDeserializer _yamlDeserializer;
    private readonly IYamlConfigurationService _legacyConfigService;

    public UnifiedConfigurationService(
        ILogger<UnifiedConfigurationService> logger,
        IYamlConfigurationService legacyConfigService)
    {
        _logger = logger;
        _legacyConfigService = legacyConfigService;

        // 配置 YAML 序列化器 - 使用 PascalCase 命名约定以匹配 C# 属性名
        _yamlSerializer = new SerializerBuilder()
            .WithNamingConvention(PascalCaseNamingConvention.Instance)
            .WithIndentedSequences()
            .ConfigureDefaultValuesHandling(DefaultValuesHandling.OmitNull)
            .Build();

        // 配置 YAML 反序列化器 - 宽松模式，忽略未匹配的属性
        _yamlDeserializer = new DeserializerBuilder()
            .WithNamingConvention(PascalCaseNamingConvention.Instance)
            .IgnoreUnmatchedProperties()
            .Build();
    }

    public async Task<UnifiedConfiguration> LoadFromYamlFileAsync(string filePath)
    {
        _logger.LogInformation("开始从统一 YAML 文件加载配置: {FilePath}", filePath);

        try
        {
            // 验证文件扩展名
            UnifiedConfigurationServiceHelpers.ValidateYamlFileExtension(filePath);

            // 检查文件是否存在
            if (!File.Exists(filePath))
            {
                throw new FileNotFoundException($"统一配置文件不存在: {filePath}");
            }

            // 读取文件内容
            var yamlContent = await File.ReadAllTextAsync(filePath);
            
            if (string.IsNullOrWhiteSpace(yamlContent))
            {
                throw new InvalidDataException("YAML 文件内容为空");
            }

            // 解析 YAML 内容
            var configuration = ParseFromYamlString(yamlContent);
            
            _logger.LogInformation("成功加载统一配置: 事件ID: {EventId}, MQTT客户端: {ClientId}", 
                configuration.EventProcessor.EventId, configuration.Mqtt.ClientId);

            return configuration;
        }
        catch (YamlException ex)
        {
            _logger.LogError(ex, "YAML 格式解析错误: {FilePath}", filePath);
            throw new InvalidDataException($"YAML 格式错误: {ex.Message}", ex);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "加载统一配置文件失败: {FilePath}", filePath);
            throw;
        }
    }

    public async Task SaveToYamlFileAsync(string filePath, UnifiedConfiguration configuration)
    {
        _logger.LogInformation("开始保存统一配置到 YAML 文件: {FilePath}", filePath);

        try
        {
            // 验证文件扩展名
            UnifiedConfigurationServiceHelpers.ValidateYamlFileExtension(filePath);

            // 验证配置对象
            UnifiedConfigurationServiceHelpers.ValidateUnifiedConfiguration(configuration);

            // 确保目录存在
            var directory = Path.GetDirectoryName(filePath);
            if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
            {
                Directory.CreateDirectory(directory);
            }

            // 序列化为 YAML 字符串
            var yamlContent = SerializeToYamlString(configuration);

            // 添加文件头注释
            var fileHeader = UnifiedConfigurationServiceHelpers.GenerateFileHeader(configuration);
            var finalContent = fileHeader + Environment.NewLine + yamlContent;

            // 写入文件
            await File.WriteAllTextAsync(filePath, finalContent);

            _logger.LogInformation("成功保存统一配置: 事件ID: {EventId} 到 {FilePath}", 
                configuration.EventProcessor.EventId, filePath);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "保存统一配置文件失败: {FilePath}", filePath);
            throw;
        }
    }

    public async Task<UnifiedConfigurationValidationResult> ValidateYamlFileAsync(string filePath)
    {
        var errors = new List<string>();
        var warnings = new List<string>();
        var sectionErrors = new Dictionary<string, List<string>>();

        try
        {
            // 验证文件扩展名
            if (!UnifiedConfigurationServiceHelpers.IsValidYamlExtension(filePath))
            {
                errors.Add($"文件扩展名无效，仅支持 .yaml 和 .yml 格式: {Path.GetExtension(filePath)}");
            }

            // 检查文件是否存在
            if (!File.Exists(filePath))
            {
                errors.Add($"文件不存在: {filePath}");
                return new UnifiedConfigurationValidationResult 
                { 
                    IsValid = false, 
                    Errors = errors,
                    SectionErrors = sectionErrors
                };
            }

            // 读取并验证 YAML 内容
            var yamlContent = await File.ReadAllTextAsync(filePath);
            
            if (string.IsNullOrWhiteSpace(yamlContent))
            {
                errors.Add("YAML 文件内容为空");
                return new UnifiedConfigurationValidationResult 
                { 
                    IsValid = false, 
                    Errors = errors,
                    SectionErrors = sectionErrors
                };
            }

            // 尝试解析 YAML
            var configuration = ParseFromYamlString(yamlContent);
            
            // 验证配置对象
            var validationResults = UnifiedConfigurationServiceHelpers.ValidateUnifiedConfiguration(configuration);
            errors.AddRange(validationResults);

            // 分节验证
            UnifiedConfigurationServiceHelpers.ValidateConfigurationSections(configuration, sectionErrors, warnings);

            _logger.LogInformation("统一配置文件验证完成: {FilePath}, 错误数: {ErrorCount}, 警告数: {WarningCount}", 
                filePath, errors.Count, warnings.Count);

        }
        catch (YamlException ex)
        {
            errors.Add($"YAML 格式错误: {ex.Message}");
        }
        catch (Exception ex)
        {
            errors.Add($"验证过程中发生错误: {ex.Message}");
        }

        return new UnifiedConfigurationValidationResult
        { 
            IsValid = errors.Count == 0, 
            Errors = errors, 
            Warnings = warnings,
            SectionErrors = sectionErrors
        };
    }

    public UnifiedConfiguration ParseFromYamlString(string yamlContent)
    {
        try
        {
            var configuration = _yamlDeserializer.Deserialize<UnifiedConfiguration>(yamlContent);
            
            if (configuration == null)
            {
                throw new InvalidDataException("YAML 反序列化结果为空");
            }

            return configuration;
        }
        catch (YamlException ex)
        {
            throw new InvalidDataException($"YAML 格式错误: {ex.Message}", ex);
        }
    }

    public string SerializeToYamlString(UnifiedConfiguration configuration)
    {
        try
        {
            return _yamlSerializer.Serialize(configuration);
        }
        catch (Exception ex)
        {
            throw new InvalidOperationException($"序列化统一配置对象到 YAML 失败: {ex.Message}", ex);
        }
    }

    public UnifiedConfiguration CreateUnifiedConfiguration(
        EventConfiguration eventConfiguration,
        MqttConfiguration? mqttConfiguration = null,
        ErrorHandlingConfiguration? errorHandlingConfiguration = null)
    {
        return new UnifiedConfiguration
        {
            Logging = UnifiedConfigurationServiceHelpers.CreateDefaultLoggingConfiguration(),
            Serilog = UnifiedConfigurationServiceHelpers.CreateDefaultSerilogConfiguration(eventConfiguration.EventId),
            EventProcessor = eventConfiguration,
            Mqtt = mqttConfiguration ?? UnifiedConfigurationServiceHelpers.CreateDefaultMqttConfiguration(eventConfiguration.EventId),
            ErrorHandling = errorHandlingConfiguration ?? UnifiedConfigurationServiceHelpers.CreateDefaultErrorHandlingConfiguration()
        };
    }

    public EventConfiguration ExtractEventConfiguration(UnifiedConfiguration unifiedConfiguration)
    {
        return unifiedConfiguration.EventProcessor;
    }

    public UnifiedConfiguration CreateDefaultUnifiedConfiguration(string eventId, string eventName)
    {
        var eventConfig = UnifiedConfigurationServiceHelpers.CreateDefaultEventConfiguration(eventId, eventName);
        return CreateUnifiedConfiguration(eventConfig);
    }

    public async Task<bool> IsUnifiedConfigurationFileAsync(string filePath)
    {
        try
        {
            if (!File.Exists(filePath))
                return false;

            var yamlContent = await File.ReadAllTextAsync(filePath);
            
            // 简单检查是否包含统一配置的关键节
            return yamlContent.Contains("EventProcessor:") && 
                   yamlContent.Contains("Mqtt:") && 
                   yamlContent.Contains("ErrorHandling:");
        }
        catch
        {
            return false;
        }
    }

    public async Task<bool> MigrateFromOldFormatAsync(
        string oldConfigFilePath,
        string newConfigFilePath,
        MqttConfiguration? mqttConfiguration = null,
        ErrorHandlingConfiguration? errorHandlingConfiguration = null)
    {
        try
        {
            _logger.LogInformation("开始迁移配置文件: {OldPath} -> {NewPath}", oldConfigFilePath, newConfigFilePath);

            // 加载旧格式配置
            var oldEventConfig = await _legacyConfigService.LoadFromYamlFileAsync(oldConfigFilePath);

            // 创建统一配置
            var unifiedConfig = CreateUnifiedConfiguration(oldEventConfig, mqttConfiguration, errorHandlingConfiguration);

            // 保存新格式配置
            await SaveToYamlFileAsync(newConfigFilePath, unifiedConfig);

            _logger.LogInformation("配置文件迁移成功: {EventId}", oldEventConfig.EventId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "配置文件迁移失败: {OldPath} -> {NewPath}", oldConfigFilePath, newConfigFilePath);
            return false;
        }
    }
}