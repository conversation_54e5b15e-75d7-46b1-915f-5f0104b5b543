using EPConfigTool.Models;
using EventProcessor.Core.Models;
using System.ComponentModel.DataAnnotations;
using System.IO;

namespace EPConfigTool.Services;

/// <summary>
/// 统一配置服务的辅助方法
/// </summary>
public static class UnifiedConfigurationServiceHelpers
{
    public static void ValidateYamlFileExtension(string filePath)
    {
        if (!IsValidYamlExtension(filePath))
        {
            throw new ArgumentException($"不支持的文件格式，仅支持 .yaml 和 .yml 文件: {Path.GetExtension(filePath)}");
        }
    }

    public static bool IsValidYamlExtension(string filePath)
    {
        var extension = Path.GetExtension(filePath).ToLowerInvariant();
        return extension == ".yaml" || extension == ".yml";
    }

    public static List<string> ValidateUnifiedConfiguration(UnifiedConfiguration configuration)
    {
        var errors = new List<string>();
        var validationContext = new ValidationContext(configuration);
        var validationResults = new List<ValidationResult>();

        if (!Validator.TryValidateObject(configuration, validationContext, validationResults, true))
        {
            errors.AddRange(validationResults.Select(vr => vr.ErrorMessage ?? "未知验证错误"));
        }

        return errors;
    }

    public static void ValidateConfigurationSections(
        UnifiedConfiguration configuration, 
        Dictionary<string, List<string>> sectionErrors,
        List<string> warnings)
    {
        // 验证 EventProcessor 配置
        var eventErrors = ValidateEventConfiguration(configuration.EventProcessor);
        if (eventErrors.Any())
            sectionErrors["EventProcessor"] = eventErrors;

        // 验证 MQTT 配置
        var mqttErrors = ValidateMqttConfiguration(configuration.Mqtt);
        if (mqttErrors.Any())
            sectionErrors["Mqtt"] = mqttErrors;

        // 验证 ErrorHandling 配置
        var errorHandlingErrors = ValidateErrorHandlingConfiguration(configuration.ErrorHandling);
        if (errorHandlingErrors.Any())
            sectionErrors["ErrorHandling"] = errorHandlingErrors;

        // 检查配置一致性
        CheckConfigurationConsistency(configuration, warnings);
    }

    public static List<string> ValidateEventConfiguration(EventConfiguration eventConfig)
    {
        var errors = new List<string>();
        var validationContext = new ValidationContext(eventConfig);
        var validationResults = new List<ValidationResult>();

        if (!Validator.TryValidateObject(eventConfig, validationContext, validationResults, true))
        {
            errors.AddRange(validationResults.Select(vr => vr.ErrorMessage ?? "未知验证错误"));
        }

        return errors;
    }

    public static List<string> ValidateMqttConfiguration(MqttConfiguration mqttConfig)
    {
        var errors = new List<string>();
        var validationContext = new ValidationContext(mqttConfig);
        var validationResults = new List<ValidationResult>();

        if (!Validator.TryValidateObject(mqttConfig, validationContext, validationResults, true))
        {
            errors.AddRange(validationResults.Select(vr => vr.ErrorMessage ?? "未知验证错误"));
        }

        return errors;
    }

    public static List<string> ValidateErrorHandlingConfiguration(ErrorHandlingConfiguration errorConfig)
    {
        var errors = new List<string>();
        var validationContext = new ValidationContext(errorConfig);
        var validationResults = new List<ValidationResult>();

        if (!Validator.TryValidateObject(errorConfig, validationContext, validationResults, true))
        {
            errors.AddRange(validationResults.Select(vr => vr.ErrorMessage ?? "未知验证错误"));
        }

        return errors;
    }

    public static void CheckConfigurationConsistency(UnifiedConfiguration configuration, List<string> warnings)
    {
        // 检查 MQTT ClientId 是否包含事件ID
        if (!configuration.Mqtt.ClientId.Contains(configuration.EventProcessor.EventId))
        {
            warnings.Add($"建议 MQTT ClientId 包含事件ID以避免冲突: {configuration.EventProcessor.EventId}");
        }

        // 检查评估策略与配置的一致性
        if (configuration.EventProcessor.EvaluationStrategy == "AI" && 
            string.IsNullOrEmpty(configuration.EventProcessor.AIPrompt))
        {
            warnings.Add("评估策略为 AI 但未配置 AI 提示词");
        }
    }

    public static string GenerateFileHeader(UnifiedConfiguration configuration)
    {
        return $"""
            # EventProcessor V4.1 统一配置文件
            # 事件ID: {configuration.EventProcessor.EventId}
            # 事件名称: {configuration.EventProcessor.EventName}
            # 生成时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss}
            # 
            # 此文件包含所有必要的配置：
            # - Logging: 日志配置
            # - Serilog: Serilog 日志框架配置
            # - EventProcessor: 事件处理配置
            # - Mqtt: MQTT 连接配置
            # - ErrorHandling: 错误处理配置
            """;
    }

    public static LoggingConfiguration CreateDefaultLoggingConfiguration()
    {
        return new LoggingConfiguration
        {
            LogLevel = new LogLevelConfiguration
            {
                Default = "Information",
                Microsoft = "Warning",
                MicrosoftHostingLifetime = "Information",
                EventProcessor = "Debug"
            }
        };
    }

    public static SerilogConfiguration CreateDefaultSerilogConfiguration(string eventId)
    {
        return new SerilogConfiguration
        {
            Using = new[] { "Serilog.Sinks.Console", "Serilog.Sinks.File" },
            MinimumLevel = new SerilogMinimumLevelConfiguration
            {
                Default = "Information",
                Override = new Dictionary<string, string>
                {
                    ["Microsoft"] = "Warning",
                    ["System"] = "Warning",
                    ["EventProcessor"] = "Debug"
                }
            },
            WriteTo = new[]
            {
                new SerilogWriteToConfiguration
                {
                    Name = "Console",
                    Args = new Dictionary<string, object>
                    {
                        ["outputTemplate"] = "[{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} {Level:u3}] {SourceContext}: {Message:lj}{NewLine}{Exception}"
                    }
                },
                new SerilogWriteToConfiguration
                {
                    Name = "File",
                    Args = new Dictionary<string, object>
                    {
                        ["path"] = $"logs/eventprocessor-{eventId}-.log",
                        ["rollingInterval"] = "Day",
                        ["retainedFileCountLimit"] = 30,
                        ["outputTemplate"] = "[{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} {Level:u3}] {SourceContext}: {Message:lj}{NewLine}{Exception}"
                    }
                }
            },
            Enrich = new[] { "FromLogContext", "WithMachineName", "WithThreadId" }
        };
    }

    public static MqttConfiguration CreateDefaultMqttConfiguration(string eventId)
    {
        return new MqttConfiguration
        {
            BrokerHost = "mq.bangdouni.com",
            BrokerPort = 1883,
            ClientId = $"EP_V4.1_{eventId}",
            Username = "bdn_event_processor",
            Password = "Bdn@2024",
            KeepAliveInterval = 60,
            ReconnectDelay = 5,
            QualityOfServiceLevel = 1
        };
    }

    public static ErrorHandlingConfiguration CreateDefaultErrorHandlingConfiguration()
    {
        return new ErrorHandlingConfiguration
        {
            ToleranceLevel = "Normal",
            RetryPolicy = new RetryPolicyConfiguration
            {
                MaxRetries = 3,
                RetryDelay = 1000,
                UseExponentialBackoff = true,
                MaxBackoffDelay = 30000
            },
            FallbackStrategy = new FallbackStrategyConfiguration
            {
                OnRuleFailure = "ContinueProcessing",
                OnAIFailure = "UseBusinessOnly",
                OnTimerFailure = "ImmediateAlarm",
                OnMqttFailure = "Retry"
            },
            Logging = new ErrorLoggingConfiguration
            {
                ErrorLogLevel = "Error",
                DetailedStackTrace = true,
                IncludeMessagePayload = false,
                EnablePerformanceMonitoring = true,
                ErrorRateThreshold = 0.1
            }
        };
    }

    public static EventConfiguration CreateDefaultEventConfiguration(string eventId, string eventName)
    {
        return new EventConfiguration
        {
            EventId = eventId,
            EventName = eventName,
            EvaluationStrategy = "BusinessOnly",
            Priority = "P3",
            CommId = "101013",
            PositionId = "P001",
            AlarmGracePeriodSeconds = 3,
            EnableAlarmCancellation = true,
            CorrelationTimeWindow = "minute",
            DeviceSignal = new DeviceSignalConfiguration
            {
                Topics = new[] { "device/default/event" },
                TriggerField = "I2",
                TriggerValues = new Dictionary<string, string>
                {
                    ["true"] = "0",
                    ["false"] = "1"
                },
                HoldingTimeoutSec = 20
            },
            RuleConfiguration = new RuleConfiguration
            {
                BusinessRules = null,
                ExclusionRules = null,
                AIResultRules = null,
                AlarmConfig = new AlarmConfiguration()
            }
        };
    }

    public static UnifiedConfiguration CreateDefaultUnifiedConfiguration(string eventId, string eventName)
    {
        var eventConfig = CreateDefaultEventConfiguration(eventId, eventName);
        return new UnifiedConfiguration
        {
            Logging = CreateDefaultLoggingConfiguration(),
            Serilog = CreateDefaultSerilogConfiguration(eventId),
            EventProcessor = eventConfig,
            Mqtt = CreateDefaultMqttConfiguration(eventId),
            ErrorHandling = CreateDefaultErrorHandlingConfiguration()
        };
    }
}
