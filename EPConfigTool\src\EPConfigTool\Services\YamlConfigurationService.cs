using EventProcessor.Core.Models;
using Microsoft.Extensions.Logging;
using System.ComponentModel.DataAnnotations;
using System.IO;
using YamlDotNet.Core;
using YamlDotNet.Serialization;
using YamlDotNet.Serialization.NamingConventions;

namespace EPConfigTool.Services;

/// <summary>
/// YAML 配置文件服务实现
/// 专门处理 EP_V4.1 的 YAML 格式配置文件
/// </summary>
public class YamlConfigurationService : IYamlConfigurationService
{
    private readonly ILogger<YamlConfigurationService> _logger;
    private readonly ISerializer _yamlSerializer;
    private readonly IDeserializer _yamlDeserializer;

    public YamlConfigurationService(ILogger<YamlConfigurationService> logger)
    {
        _logger = logger;

        // 配置 YAML 序列化器 - 使用 camelCase 命名约定
        _yamlSerializer = new SerializerBuilder()
            .WithNamingConvention(CamelCaseNamingConvention.Instance)
            .WithIndentedSequences()
            .ConfigureDefaultValuesHandling(DefaultValuesHandling.OmitNull)
            .Build();

        // 配置 YAML 反序列化器 - 宽松模式，忽略未匹配的属性
        _yamlDeserializer = new DeserializerBuilder()
            .WithNamingConvention(CamelCaseNamingConvention.Instance)
            .IgnoreUnmatchedProperties()
            .Build();
    }

    public async Task<EventConfiguration> LoadFromYamlFileAsync(string filePath)
    {
        _logger.LogInformation("开始从 YAML 文件加载配置: {FilePath}", filePath);

        try
        {
            // 验证文件扩展名
            ValidateYamlFileExtension(filePath);

            // 检查文件是否存在
            if (!File.Exists(filePath))
            {
                throw new FileNotFoundException($"YAML 配置文件不存在: {filePath}");
            }

            // 读取文件内容
            var yamlContent = await File.ReadAllTextAsync(filePath);
            
            if (string.IsNullOrWhiteSpace(yamlContent))
            {
                throw new InvalidDataException("YAML 文件内容为空");
            }

            // 解析 YAML 内容
            var configuration = ParseFromYamlString(yamlContent);
            
            _logger.LogInformation("成功加载事件配置: {EventId} - {EventName}", 
                configuration.EventId, configuration.EventName);

            return configuration;
        }
        catch (YamlException ex)
        {
            _logger.LogError(ex, "YAML 格式解析错误: {FilePath}", filePath);
            throw new InvalidDataException($"YAML 格式错误: {ex.Message}", ex);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "加载 YAML 配置文件失败: {FilePath}", filePath);
            throw;
        }
    }

    public async Task SaveToYamlFileAsync(string filePath, EventConfiguration configuration)
    {
        _logger.LogInformation("开始保存配置到 YAML 文件: {FilePath}", filePath);

        try
        {
            // 验证文件扩展名
            ValidateYamlFileExtension(filePath);

            // 验证配置对象
            ValidateConfiguration(configuration);

            // 确保目录存在
            var directory = Path.GetDirectoryName(filePath);
            if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
            {
                Directory.CreateDirectory(directory);
            }

            // 序列化为 YAML 字符串
            var yamlContent = SerializeToYamlString(configuration);

            // 写入文件
            await File.WriteAllTextAsync(filePath, yamlContent);

            _logger.LogInformation("成功保存事件配置: {EventId} - {EventName} 到 {FilePath}", 
                configuration.EventId, configuration.EventName, filePath);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "保存 YAML 配置文件失败: {FilePath}", filePath);
            throw;
        }
    }

    public async Task<YamlValidationResult> ValidateYamlFileAsync(string filePath)
    {
        var result = new YamlValidationResult();
        var errors = new List<string>();
        var warnings = new List<string>();

        try
        {
            // 验证文件扩展名
            if (!IsValidYamlExtension(filePath))
            {
                errors.Add($"文件扩展名无效，仅支持 .yaml 和 .yml 格式: {Path.GetExtension(filePath)}");
            }

            // 检查文件是否存在
            if (!File.Exists(filePath))
            {
                errors.Add($"文件不存在: {filePath}");
                return result with { IsValid = false, Errors = errors };
            }

            // 读取并验证 YAML 内容
            var yamlContent = await File.ReadAllTextAsync(filePath);
            
            if (string.IsNullOrWhiteSpace(yamlContent))
            {
                errors.Add("YAML 文件内容为空");
                return result with { IsValid = false, Errors = errors };
            }

            // 尝试解析 YAML
            var configuration = ParseFromYamlString(yamlContent);
            
            // 验证配置对象
            var validationResults = ValidateConfiguration(configuration);
            errors.AddRange(validationResults);

            _logger.LogInformation("YAML 文件验证完成: {FilePath}, 错误数: {ErrorCount}, 警告数: {WarningCount}", 
                filePath, errors.Count, warnings.Count);

        }
        catch (YamlException ex)
        {
            errors.Add($"YAML 格式错误: {ex.Message}");
        }
        catch (Exception ex)
        {
            errors.Add($"验证过程中发生错误: {ex.Message}");
        }

        return result with 
        { 
            IsValid = errors.Count == 0, 
            Errors = errors, 
            Warnings = warnings 
        };
    }

    public EventConfiguration ParseFromYamlString(string yamlContent)
    {
        try
        {
            var configuration = _yamlDeserializer.Deserialize<EventConfiguration>(yamlContent);
            
            if (configuration == null)
            {
                throw new InvalidDataException("YAML 反序列化结果为空");
            }

            return configuration;
        }
        catch (YamlException ex)
        {
            throw new InvalidDataException($"YAML 格式错误: {ex.Message}", ex);
        }
    }

    public string SerializeToYamlString(EventConfiguration configuration)
    {
        try
        {
            return _yamlSerializer.Serialize(configuration);
        }
        catch (Exception ex)
        {
            throw new InvalidOperationException($"序列化配置对象到 YAML 失败: {ex.Message}", ex);
        }
    }

    private static void ValidateYamlFileExtension(string filePath)
    {
        if (!IsValidYamlExtension(filePath))
        {
            throw new ArgumentException($"不支持的文件格式，仅支持 .yaml 和 .yml 文件: {Path.GetExtension(filePath)}");
        }
    }

    private static bool IsValidYamlExtension(string filePath)
    {
        var extension = Path.GetExtension(filePath).ToLowerInvariant();
        return extension == ".yaml" || extension == ".yml";
    }

    private static List<string> ValidateConfiguration(EventConfiguration configuration)
    {
        var errors = new List<string>();
        var validationContext = new ValidationContext(configuration);
        var validationResults = new List<ValidationResult>();

        if (!Validator.TryValidateObject(configuration, validationContext, validationResults, true))
        {
            errors.AddRange(validationResults.Select(vr => vr.ErrorMessage ?? "未知验证错误"));
        }

        return errors;
    }
}
