using CommunityToolkit.Mvvm.Input;
using EventProcessor.Core.Models;
using System.Collections.ObjectModel;
using System.ComponentModel.DataAnnotations;

namespace EPConfigTool.ViewModels;

/// <summary>
/// AI结果规则组 ViewModel
/// </summary>
public partial class AIResultRuleGroupViewModel : ViewModelBase
{
    private string _logicOperator = "AND";

    public AIResultRuleGroupViewModel(AIResultRuleGroup? model = null)
    {
        // 初始化集合
        ConditionGroups = new ObservableCollection<ConditionGroupViewModel>();
        Conditions = new ObservableCollection<ConditionViewModel>();

        // 初始化命令
        AddConditionGroupCommand = new RelayCommand(AddConditionGroup);
        RemoveConditionGroupCommand = new RelayCommand<ConditionGroupViewModel>(RemoveConditionGroup);
        AddConditionCommand = new RelayCommand(AddCondition);
        RemoveConditionCommand = new RelayCommand<ConditionViewModel>(RemoveCondition);

        // 从模型加载数据
        if (model != null)
        {
            LoadFromModel(model);
        }

        // 订阅集合变更事件
        ConditionGroups.CollectionChanged += (s, e) => OnPropertyChanged(nameof(ConditionGroups));
        Conditions.CollectionChanged += (s, e) => OnPropertyChanged(nameof(Conditions));
    }

    #region Properties

    /// <summary>
    /// 逻辑操作符
    /// </summary>
    [Required(ErrorMessage = "逻辑操作符不能为空")]
    public string LogicOperator
    {
        get => _logicOperator;
        set => SetProperty(ref _logicOperator, value);
    }

    /// <summary>
    /// 嵌套条件组
    /// </summary>
    public ObservableCollection<ConditionGroupViewModel> ConditionGroups { get; }

    /// <summary>
    /// 直接条件
    /// </summary>
    public ObservableCollection<ConditionViewModel> Conditions { get; }

    /// <summary>
    /// 可用的逻辑操作符
    /// </summary>
    public static readonly string[] LogicOperators = { "AND", "OR" };

    /// <summary>
    /// 是否有条件组
    /// </summary>
    public bool HasConditionGroups => ConditionGroups.Count > 0;

    /// <summary>
    /// 是否有直接条件
    /// </summary>
    public bool HasConditions => Conditions.Count > 0;

    #endregion

    #region Commands

    public IRelayCommand AddConditionGroupCommand { get; }
    public IRelayCommand<ConditionGroupViewModel> RemoveConditionGroupCommand { get; }
    public IRelayCommand AddConditionCommand { get; }
    public IRelayCommand<ConditionViewModel> RemoveConditionCommand { get; }

    #endregion

    #region Command Implementations

    private void AddConditionGroup()
    {
        var newGroup = new ConditionGroupViewModel(new ConditionGroup
        {
            LogicOperator = "AND"
        });

        ConditionGroups.Add(newGroup);
        OnPropertyChanged(nameof(HasConditionGroups));
    }

    private void RemoveConditionGroup(ConditionGroupViewModel? group)
    {
        if (group != null && ConditionGroups.Contains(group))
        {
            ConditionGroups.Remove(group);
            OnPropertyChanged(nameof(HasConditionGroups));
        }
    }

    private void AddCondition()
    {
        var newCondition = new ConditionViewModel(new Condition
        {
            FieldName = "",
            DataType = "string",
            Operator = "Equals",
            Value = ""
        });

        Conditions.Add(newCondition);
        OnPropertyChanged(nameof(HasConditions));
    }

    private void RemoveCondition(ConditionViewModel? condition)
    {
        if (condition != null && Conditions.Contains(condition))
        {
            Conditions.Remove(condition);
            OnPropertyChanged(nameof(HasConditions));
        }
    }

    #endregion

    #region Methods

    /// <summary>
    /// 从模型加载数据
    /// </summary>
    /// <param name="model">AI结果规则组模型</param>
    public void LoadFromModel(AIResultRuleGroup model)
    {
        LogicOperator = model.LogicOperator;

        // 清空现有数据
        ConditionGroups.Clear();
        Conditions.Clear();

        // 加载条件组
        if (model.ConditionGroups != null)
        {
            foreach (var group in model.ConditionGroups)
            {
                ConditionGroups.Add(new ConditionGroupViewModel(group));
            }
        }

        // 加载直接条件
        if (model.Conditions != null)
        {
            foreach (var condition in model.Conditions)
            {
                Conditions.Add(new ConditionViewModel(condition));
            }
        }

        // 更新属性通知
        OnPropertyChanged(nameof(HasConditionGroups));
        OnPropertyChanged(nameof(HasConditions));
    }

    /// <summary>
    /// 转换为模型对象
    /// </summary>
    /// <returns>AI结果规则组模型</returns>
    public AIResultRuleGroup ToModel()
    {
        return new AIResultRuleGroup
        {
            LogicOperator = LogicOperator,
            ConditionGroups = ConditionGroups.Count > 0 
                ? ConditionGroups.Select(g => g.ToModel()).ToArray() 
                : null,
            Conditions = Conditions.Count > 0 
                ? Conditions.Select(c => c.ToModel()).ToArray() 
                : null
        };
    }

    #endregion
}
