using CommunityToolkit.Mvvm.Input;
using EPConfigTool.Services;
using EventProcessor.Core.Models;
using Microsoft.Extensions.Logging;
using System.Collections.ObjectModel;

namespace EPConfigTool.ViewModels;

/// <summary>
/// 告警配置 ViewModel
/// 包含告警消息预览功能
/// </summary>
public partial class AlarmConfigurationViewModel : ViewModelBase
{
    private readonly ILogger<AlarmConfigurationViewModel>? _logger;
    private AlarmPreviewViewModel? _previewViewModel;

    public AlarmConfigurationViewModel(AlarmConfiguration? model = null, 
                                     IServiceProvider? serviceProvider = null)
    {
        // 初始化集合
        Fields = new ObservableCollection<FieldMappingViewModel>();

        // 初始化命令
        AddFieldCommand = new RelayCommand(AddField);
        RemoveFieldCommand = new RelayCommand<FieldMappingViewModel>(RemoveField);

        // 尝试获取服务
        if (serviceProvider != null)
        {
            _logger = serviceProvider.GetService(typeof(ILogger<AlarmConfigurationViewModel>)) 
                as ILogger<AlarmConfigurationViewModel>;
            
            var previewService = serviceProvider.GetService(typeof(IAlarmPreviewService)) 
                as IAlarmPreviewService;
            var previewLogger = serviceProvider.GetService(typeof(ILogger<AlarmPreviewViewModel>)) 
                as ILogger<AlarmPreviewViewModel>;
            
            if (previewService != null && previewLogger != null)
            {
                _previewViewModel = new AlarmPreviewViewModel(previewService, previewLogger);
            }
        }

        // 从模型加载数据
        if (model != null)
        {
            LoadFromModel(model);
        }

        // 订阅集合变更事件
        Fields.CollectionChanged += (s, e) => 
        {
            OnPropertyChanged(nameof(Fields));
            OnPropertyChanged(nameof(HasFields));
            UpdatePreview();
        };
    }

    #region Properties

    /// <summary>
    /// 字段映射集合
    /// </summary>
    public ObservableCollection<FieldMappingViewModel> Fields { get; }

    /// <summary>
    /// 是否有字段映射
    /// </summary>
    public bool HasFields => Fields.Count > 0;

    /// <summary>
    /// 告警消息预览ViewModel
    /// </summary>
    public AlarmPreviewViewModel? PreviewViewModel => _previewViewModel;

    /// <summary>
    /// 是否启用预览功能
    /// </summary>
    public bool IsPreviewEnabled => _previewViewModel != null;

    #endregion

    #region Commands

    public IRelayCommand AddFieldCommand { get; }
    public IRelayCommand<FieldMappingViewModel> RemoveFieldCommand { get; }

    #endregion

    #region Command Implementations

    private void AddField()
    {
        var newField = new FieldMappingViewModel(new FieldMapping
        {
            AlarmFieldName = "",
            SourceRuleType = "DeviceSignal",
            SourceFieldName = ""
        });

        // 订阅字段变更事件以实时更新预览
        newField.FieldChanged += (s, e) => UpdatePreview();

        Fields.Add(newField);
        OnPropertyChanged(nameof(HasFields));
    }

    private void RemoveField(FieldMappingViewModel? field)
    {
        if (field != null && Fields.Contains(field))
        {
            Fields.Remove(field);
            OnPropertyChanged(nameof(HasFields));
        }
    }

    #endregion

    #region Methods

    /// <summary>
    /// 从模型加载数据
    /// </summary>
    /// <param name="model">告警配置模型</param>
    public void LoadFromModel(AlarmConfiguration model)
    {
        Fields.Clear();

        if (model.Fields != null)
        {
            foreach (var field in model.Fields)
            {
                var fieldViewModel = new FieldMappingViewModel(field);
                // 订阅字段变更事件以实时更新预览
                fieldViewModel.FieldChanged += (s, e) => UpdatePreview();
                Fields.Add(fieldViewModel);
            }
        }

        OnPropertyChanged(nameof(HasFields));
        UpdatePreview(); // 加载后更新预览
    }

    /// <summary>
    /// 转换为模型对象
    /// </summary>
    /// <returns>告警配置模型</returns>
    public AlarmConfiguration ToModel()
    {
        return new AlarmConfiguration
        {
            Fields = Fields.Count > 0 
                ? Fields.Select(f => f.ToModel()).ToArray() 
                : null
        };
    }

    /// <summary>
    /// 更新预览
    /// </summary>
    private void UpdatePreview()
    {
        try
        {
            if (_previewViewModel != null)
            {
                var config = ToModel();
                _previewViewModel.AlarmConfiguration = config;
                
                _logger?.LogDebug("告警配置预览已更新: {FieldCount} 个字段", Fields.Count);
            }
        }
        catch (Exception ex)
        {
            _logger?.LogWarning(ex, "更新告警配置预览失败");
        }
    }

    #endregion
}

/// <summary>
/// 字段映射 ViewModel
/// 支持实时预览更新
/// </summary>
public partial class FieldMappingViewModel : ViewModelBase
{
    private string _alarmFieldName = string.Empty;
    private string _sourceRuleType = "DeviceSignal";
    private string _sourceFieldName = string.Empty;
    private string? _defaultValue;
    private string? _formatTemplate;

    /// <summary>
    /// 字段变更事件，用于通知父ViewModel更新预览
    /// </summary>
    public event EventHandler? FieldChanged;

    public FieldMappingViewModel(FieldMapping? model = null)
    {
        if (model != null)
        {
            LoadFromModel(model);
        }
    }

    #region Properties

    /// <summary>
    /// 告警字段名称
    /// </summary>
    public string AlarmFieldName
    {
        get => _alarmFieldName;
        set 
        { 
            if (SetProperty(ref _alarmFieldName, value))
            {
                OnFieldChanged();
            }
        }
    }

    /// <summary>
    /// 源规则类型
    /// </summary>
    public string SourceRuleType
    {
        get => _sourceRuleType;
        set 
        { 
            if (SetProperty(ref _sourceRuleType, value))
            {
                OnFieldChanged();
            }
        }
    }

    /// <summary>
    /// 源字段名称
    /// </summary>
    public string SourceFieldName
    {
        get => _sourceFieldName;
        set
        {
            if (SetProperty(ref _sourceFieldName, value))
            {
                // 智能提示：当用户输入特殊字段时，自动设置正确的数据源类型
                ApplySmartFieldMapping(value);
                OnFieldChanged();
            }
        }
    }

    /// <summary>
    /// 默认值
    /// </summary>
    public string? DefaultValue
    {
        get => _defaultValue;
        set 
        { 
            if (SetProperty(ref _defaultValue, value))
            {
                OnFieldChanged();
            }
        }
    }

    /// <summary>
    /// 格式化模板
    /// </summary>
    public string? FormatTemplate
    {
        get => _formatTemplate;
        set 
        { 
            if (SetProperty(ref _formatTemplate, value))
            {
                OnFieldChanged();
            }
        }
    }

    /// <summary>
    /// 可用的源规则类型
    /// </summary>
    public static readonly string[] SourceRuleTypes = 
    { 
        "ExclusionRules", 
        "BusinessRules", 
        "AIResultRules", 
        "DeviceSignal" 
    };

    #endregion

    #region Methods

    /// <summary>
    /// 从模型加载数据
    /// </summary>
    /// <param name="model">字段映射模型</param>
    public void LoadFromModel(FieldMapping model)
    {
        AlarmFieldName = model.AlarmFieldName;
        SourceRuleType = model.SourceRuleType;
        SourceFieldName = model.SourceFieldName ?? string.Empty;
        DefaultValue = model.DefaultValue;
        FormatTemplate = model.FormatTemplate;
    }

    /// <summary>
    /// 转换为模型对象
    /// </summary>
    /// <returns>字段映射模型</returns>
    public FieldMapping ToModel()
    {
        return new FieldMapping
        {
            AlarmFieldName = AlarmFieldName,
            SourceRuleType = SourceRuleType,
            SourceFieldName = SourceFieldName,
            DefaultValue = DefaultValue,
            FormatTemplate = FormatTemplate
        };
    }

    /// <summary>
    /// 触发字段变更事件
    /// </summary>
    private void OnFieldChanged()
    {
        FieldChanged?.Invoke(this, EventArgs.Empty);
    }

    /// <summary>
    /// 智能字段映射：根据字段名自动设置正确的数据源类型
    /// </summary>
    private void ApplySmartFieldMapping(string fieldName)
    {
        if (string.IsNullOrWhiteSpace(fieldName)) return;

        // 设备信号字段
        var deviceSignalFields = new[] { "duration", "I1", "I2", "I3", "I4", "holding_duration", "trigger_state" };
        if (deviceSignalFields.Any(field => string.Equals(fieldName, field, StringComparison.OrdinalIgnoreCase)))
        {
            if (!string.Equals(SourceRuleType, "DeviceSignal", StringComparison.OrdinalIgnoreCase))
            {
                SourceRuleType = "DeviceSignal";

                // 特别提示 duration 字段
                if (string.Equals(fieldName, "duration", StringComparison.OrdinalIgnoreCase))
                {
                    // 这里可以触发一个提示事件，但为了简化，我们只在日志中记录
                    System.Diagnostics.Debug.WriteLine($"智能提示：已将 '{fieldName}' 字段的数据源自动设置为 'DeviceSignal'");
                }
            }
        }
        // 业务规则字段
        else
        {
            var businessFields = new[] { "CardType", "log_car_no", "log_user_name", "log_remain_days", "log_end_time" };
            if (businessFields.Any(field => string.Equals(fieldName, field, StringComparison.OrdinalIgnoreCase)))
            {
                if (!string.Equals(SourceRuleType, "BusinessRules", StringComparison.OrdinalIgnoreCase))
                {
                    SourceRuleType = "BusinessRules";
                }
            }
        }
    }

    #endregion
}
