using CommunityToolkit.Mvvm.Input;
using EPConfigTool.Services;
using EventProcessor.Core.Models;
using Microsoft.Extensions.Logging;
using System.Collections.ObjectModel;
using System.Text.Json;
using System.Windows;
using System.Windows.Media;

namespace EPConfigTool.ViewModels;

/// <summary>
/// 告警消息预览 ViewModel
/// </summary>
public partial class AlarmPreviewViewModel : ViewModelBase
{
    private readonly IAlarmPreviewService _previewService;
    private readonly ILogger<AlarmPreviewViewModel> _logger;

    private AlarmConfiguration? _alarmConfiguration;
    private string _jsonPreview = string.Empty;
    private string _previewStatusText = "等待配置数据...";
    private bool _useCustomSampleData = false;
    private string _customSampleDataJson = string.Empty;
    private Dictionary<string, object>? _customSampleData;

    public AlarmPreviewViewModel(IAlarmPreviewService previewService, ILogger<AlarmPreviewViewModel> logger)
    {
        _previewService = previewService;
        _logger = logger;

        // 初始化集合
        PreviewFields = new ObservableCollection<KeyValuePair<string, string>>();
        PreviewErrors = new ObservableCollection<string>();
        PreviewWarnings = new ObservableCollection<string>();

        // 初始化命令
        RefreshPreviewCommand = new RelayCommand(RefreshPreview);
        CopyJsonCommand = new RelayCommand(CopyJson, CanCopyJson);
        ApplyCustomDataCommand = new RelayCommand(ApplyCustomData);
        ResetToDefaultDataCommand = new RelayCommand(ResetToDefaultData);

        // 初始化自定义示例数据
        InitializeCustomSampleData();
    }

    #region Properties

    /// <summary>
    /// 告警配置
    /// </summary>
    public AlarmConfiguration? AlarmConfiguration
    {
        get => _alarmConfiguration;
        set
        {
            if (SetProperty(ref _alarmConfiguration, value))
            {
                RefreshPreview();
            }
        }
    }

    /// <summary>
    /// 预览字段集合
    /// </summary>
    public ObservableCollection<KeyValuePair<string, string>> PreviewFields { get; }

    /// <summary>
    /// JSON预览内容
    /// </summary>
    public string JsonPreview
    {
        get => _jsonPreview;
        set => SetProperty(ref _jsonPreview, value);
    }

    /// <summary>
    /// 预览状态文本
    /// </summary>
    public string PreviewStatusText
    {
        get => _previewStatusText;
        set => SetProperty(ref _previewStatusText, value);
    }

    /// <summary>
    /// 预览错误集合
    /// </summary>
    public ObservableCollection<string> PreviewErrors { get; }

    /// <summary>
    /// 预览警告集合
    /// </summary>
    public ObservableCollection<string> PreviewWarnings { get; }

    /// <summary>
    /// 是否使用自定义示例数据
    /// </summary>
    public bool UseCustomSampleData
    {
        get => _useCustomSampleData;
        set
        {
            if (SetProperty(ref _useCustomSampleData, value))
            {
                RefreshPreview();
            }
        }
    }

    /// <summary>
    /// 自定义示例数据JSON
    /// </summary>
    public string CustomSampleDataJson
    {
        get => _customSampleDataJson;
        set => SetProperty(ref _customSampleDataJson, value);
    }

    /// <summary>
    /// 是否有预览字段
    /// </summary>
    public bool HasPreviewFields => PreviewFields.Count > 0;

    /// <summary>
    /// 是否有JSON预览
    /// </summary>
    public bool HasJsonPreview => !string.IsNullOrEmpty(JsonPreview);

    /// <summary>
    /// 是否有预览错误
    /// </summary>
    public bool HasPreviewErrors => PreviewErrors.Count > 0;

    /// <summary>
    /// 是否有预览警告
    /// </summary>
    public bool HasPreviewWarnings => PreviewWarnings.Count > 0;

    /// <summary>
    /// 预览状态背景色
    /// </summary>
    public Brush PreviewStatusBackground => HasPreviewErrors 
        ? new SolidColorBrush(Color.FromRgb(248, 215, 218))  // 红色背景
        : HasPreviewWarnings 
            ? new SolidColorBrush(Color.FromRgb(255, 249, 196))  // 黄色背景
            : new SolidColorBrush(Color.FromRgb(212, 237, 218)); // 绿色背景

    /// <summary>
    /// 预览状态边框色
    /// </summary>
    public Brush PreviewStatusBorderBrush => HasPreviewErrors 
        ? new SolidColorBrush(Color.FromRgb(220, 53, 69))   // 红色边框
        : HasPreviewWarnings 
            ? new SolidColorBrush(Color.FromRgb(255, 193, 7))   // 黄色边框
            : new SolidColorBrush(Color.FromRgb(25, 135, 84));  // 绿色边框

    /// <summary>
    /// 预览状态前景色
    /// </summary>
    public Brush PreviewStatusForeground => HasPreviewErrors 
        ? new SolidColorBrush(Color.FromRgb(114, 28, 36))   // 深红色
        : HasPreviewWarnings 
            ? new SolidColorBrush(Color.FromRgb(133, 100, 4))   // 深黄色
            : new SolidColorBrush(Color.FromRgb(20, 108, 67));  // 深绿色

    #endregion

    #region Commands

    public IRelayCommand RefreshPreviewCommand { get; }
    public IRelayCommand CopyJsonCommand { get; }
    public IRelayCommand ApplyCustomDataCommand { get; }
    public IRelayCommand ResetToDefaultDataCommand { get; }

    #endregion

    #region Command Implementations

    private void RefreshPreview()
    {
        try
        {
            // 清空之前的状态
            PreviewFields.Clear();
            PreviewErrors.Clear();
            PreviewWarnings.Clear();

            if (AlarmConfiguration == null)
            {
                PreviewStatusText = "未加载告警配置";
                JsonPreview = "{}";
                UpdatePropertyChanged();
                return;
            }

            // 生成预览
            var sampleData = UseCustomSampleData ? _customSampleData : null;
            var previewResult = _previewService.GeneratePreview(AlarmConfiguration, sampleData);

            // 更新预览字段
            foreach (var field in previewResult.Fields)
            {
                PreviewFields.Add(field);
            }

            // 更新JSON预览
            JsonPreview = previewResult.JsonPreview;

            // 更新错误和警告
            if (previewResult.Errors != null)
            {
                foreach (var error in previewResult.Errors)
                {
                    PreviewErrors.Add(error);
                }
            }

            if (previewResult.Warnings != null)
            {
                foreach (var warning in previewResult.Warnings)
                {
                    PreviewWarnings.Add(warning);
                }
            }

            // 更新状态文本
            if (previewResult.IsSuccess)
            {
                PreviewStatusText = $"✅ 预览成功 - 生成了 {previewResult.Fields.Count} 个告警字段";
            }
            else
            {
                PreviewStatusText = $"❌ 预览失败 - 发现 {PreviewErrors.Count} 个错误";
            }

            UpdatePropertyChanged();
            CopyJsonCommand.NotifyCanExecuteChanged();

            _logger.LogInformation("告警预览已刷新: 成功={Success}, 字段数={FieldCount}, 错误数={ErrorCount}", 
                                 previewResult.IsSuccess, previewResult.Fields.Count, PreviewErrors.Count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "刷新告警预览失败");
            PreviewStatusText = $"❌ 预览异常: {ex.Message}";
            PreviewErrors.Add($"预览生成异常: {ex.Message}");
            UpdatePropertyChanged();
        }
    }

    private void CopyJson()
    {
        try
        {
            if (!string.IsNullOrEmpty(JsonPreview))
            {
                Clipboard.SetText(JsonPreview);
                PreviewStatusText = "✅ JSON内容已复制到剪贴板";
                UpdatePropertyChanged();

                _logger.LogInformation("JSON预览内容已复制到剪贴板");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "复制JSON到剪贴板失败");
            PreviewErrors.Add($"复制失败: {ex.Message}");
            UpdatePropertyChanged();
        }
    }

    private bool CanCopyJson()
    {
        return !string.IsNullOrEmpty(JsonPreview) && JsonPreview != "{}";
    }

    private void ApplyCustomData()
    {
        try
        {
            if (string.IsNullOrWhiteSpace(CustomSampleDataJson))
            {
                _customSampleData = null;
                RefreshPreview();
                return;
            }

            // 解析自定义JSON数据
            var customData = JsonSerializer.Deserialize<Dictionary<string, object>>(CustomSampleDataJson);
            _customSampleData = customData;

            RefreshPreview();

            _logger.LogInformation("已应用自定义示例数据: {FieldCount} 个字段", customData?.Count ?? 0);
        }
        catch (JsonException ex)
        {
            _logger.LogWarning(ex, "解析自定义示例数据失败");
            PreviewErrors.Add($"JSON格式错误: {ex.Message}");
            UpdatePropertyChanged();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "应用自定义示例数据失败");
            PreviewErrors.Add($"应用自定义数据失败: {ex.Message}");
            UpdatePropertyChanged();
        }
    }

    private void ResetToDefaultData()
    {
        _customSampleData = null;
        InitializeCustomSampleData();
        RefreshPreview();

        _logger.LogInformation("已重置为默认示例数据");
    }

    #endregion

    #region Helper Methods

    /// <summary>
    /// 初始化自定义示例数据
    /// </summary>
    private void InitializeCustomSampleData()
    {
        var defaultSampleData = new Dictionary<string, object>
        {
            ["CardType"] = "月租卡",
            ["log_car_no"] = "粤A12345",
            ["log_remain_days"] = 15,
            ["user_name"] = "张三",
            ["device_id"] = "BDN8888",
            ["timestamp"] = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
            ["ai_result"] = "检测到违规停车",
            ["confidence"] = 0.95
        };

        var options = new JsonSerializerOptions
        {
            WriteIndented = true,
            Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping
        };

        CustomSampleDataJson = JsonSerializer.Serialize(defaultSampleData, options);
    }

    /// <summary>
    /// 更新相关属性
    /// </summary>
    private void UpdatePropertyChanged()
    {
        OnPropertyChanged(nameof(HasPreviewFields));
        OnPropertyChanged(nameof(HasJsonPreview));
        OnPropertyChanged(nameof(HasPreviewErrors));
        OnPropertyChanged(nameof(HasPreviewWarnings));
        OnPropertyChanged(nameof(PreviewStatusBackground));
        OnPropertyChanged(nameof(PreviewStatusBorderBrush));
        OnPropertyChanged(nameof(PreviewStatusForeground));
    }

    #endregion
}