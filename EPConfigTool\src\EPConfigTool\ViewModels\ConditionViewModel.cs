using EventProcessor.Core.Models;
using System.ComponentModel.DataAnnotations;

namespace EPConfigTool.ViewModels;

/// <summary>
/// 条件 ViewModel
/// </summary>
public partial class ConditionViewModel : ViewModelBase
{
    private string _fieldName = string.Empty;
    private string _dataType = "string";
    private string _operator = "Equals";
    private string _value = string.Empty;
    private string? _description;

    public ConditionViewModel(Condition? model = null)
    {
        if (model != null)
        {
            LoadFromModel(model);
        }
    }

    #region Properties

    /// <summary>
    /// 字段名称
    /// </summary>
    [Required(ErrorMessage = "字段名称不能为空")]
    public string FieldName
    {
        get => _fieldName;
        set => SetProperty(ref _fieldName, value);
    }

    /// <summary>
    /// 数据类型
    /// </summary>
    [Required(ErrorMessage = "数据类型不能为空")]
    public string DataType
    {
        get => _dataType;
        set
        {
            if (SetProperty(ref _dataType, value))
            {
                // 当数据类型改变时，重置操作符为该类型的默认操作符
                Operator = GetDefaultOperatorForDataType(value);
                OnPropertyChanged(nameof(AvailableOperators));
            }
        }
    }

    /// <summary>
    /// 操作符
    /// </summary>
    [Required(ErrorMessage = "操作符不能为空")]
    public string Operator
    {
        get => _operator;
        set => SetProperty(ref _operator, value);
    }

    /// <summary>
    /// 比较值
    /// </summary>
    [Required(ErrorMessage = "比较值不能为空")]
    public string Value
    {
        get => _value;
        set => SetProperty(ref _value, value);
    }

    /// <summary>
    /// 条件描述
    /// </summary>
    public string? Description
    {
        get => _description;
        set => SetProperty(ref _description, value);
    }

    /// <summary>
    /// 可用的数据类型
    /// </summary>
    public static readonly string[] DataTypes = { "string", "number", "datetime" };

    /// <summary>
    /// 当前数据类型可用的操作符
    /// </summary>
    public string[] AvailableOperators => GetOperatorsForDataType(DataType);

    #endregion

    #region Methods

    /// <summary>
    /// 从模型加载数据
    /// </summary>
    /// <param name="model">条件模型</param>
    public void LoadFromModel(Condition model)
    {
        FieldName = model.FieldName;
        DataType = model.DataType;
        Operator = model.Operator;
        Value = model.Value;
        Description = model.Description;
    }

    /// <summary>
    /// 转换为模型对象
    /// </summary>
    /// <returns>条件模型</returns>
    public Condition ToModel()
    {
        return new Condition
        {
            FieldName = FieldName,
            DataType = DataType,
            Operator = Operator,
            Value = Value,
            Description = Description
        };
    }

    /// <summary>
    /// 获取指定数据类型的可用操作符
    /// </summary>
    /// <param name="dataType">数据类型</param>
    /// <returns>操作符数组</returns>
    private static string[] GetOperatorsForDataType(string dataType)
    {
        return dataType switch
        {
            "string" => new[] { "Equals", "NotEquals", "Contains", "NotContains", "StartsWith", "EndsWith", "In", "NotIn" },
            "number" => new[] { "Equals", "NotEquals", "GreaterThan", "GreaterThanOrEqual", "LessThan", "LessThanOrEqual", "In", "NotIn" },
            "datetime" => new[] { "Equals", "NotEquals", "GreaterThan", "GreaterThanOrEqual", "LessThan", "LessThanOrEqual" },
            _ => new[] { "Equals", "NotEquals" }
        };
    }

    /// <summary>
    /// 获取指定数据类型的默认操作符
    /// </summary>
    /// <param name="dataType">数据类型</param>
    /// <returns>默认操作符</returns>
    private static string GetDefaultOperatorForDataType(string dataType)
    {
        return dataType switch
        {
            "string" => "Equals",
            "number" => "Equals",
            "datetime" => "Equals",
            _ => "Equals"
        };
    }

    #endregion
}

/// <summary>
/// 条件组 ViewModel
/// </summary>
public partial class ConditionGroupViewModel : ViewModelBase
{
    private string _logicOperator = "AND";

    public ConditionGroupViewModel(ConditionGroup? model = null)
    {
        Conditions = new System.Collections.ObjectModel.ObservableCollection<ConditionViewModel>();
        
        if (model != null)
        {
            LoadFromModel(model);
        }

        // 订阅集合变更事件
        Conditions.CollectionChanged += (s, e) => OnPropertyChanged(nameof(Conditions));
    }

    #region Properties

    /// <summary>
    /// 逻辑操作符
    /// </summary>
    [Required(ErrorMessage = "逻辑操作符不能为空")]
    public string LogicOperator
    {
        get => _logicOperator;
        set => SetProperty(ref _logicOperator, value);
    }

    /// <summary>
    /// 条件列表
    /// </summary>
    public System.Collections.ObjectModel.ObservableCollection<ConditionViewModel> Conditions { get; }

    /// <summary>
    /// 可用的逻辑操作符
    /// </summary>
    public static readonly string[] LogicOperators = { "AND", "OR", "NOT" };

    #endregion

    #region Methods

    /// <summary>
    /// 从模型加载数据
    /// </summary>
    /// <param name="model">条件组模型</param>
    public void LoadFromModel(ConditionGroup model)
    {
        LogicOperator = model.LogicOperator;
        
        Conditions.Clear();
        if (model.Conditions != null)
        {
            foreach (var condition in model.Conditions)
            {
                Conditions.Add(new ConditionViewModel(condition));
            }
        }
    }

    /// <summary>
    /// 转换为模型对象
    /// </summary>
    /// <returns>条件组模型</returns>
    public ConditionGroup ToModel()
    {
        return new ConditionGroup
        {
            LogicOperator = LogicOperator,
            Conditions = Conditions.Count > 0 
                ? Conditions.Select(c => c.ToModel()).ToArray() 
                : null
        };
    }

    /// <summary>
    /// 添加新条件
    /// </summary>
    public void AddCondition()
    {
        Conditions.Add(new ConditionViewModel());
    }

    /// <summary>
    /// 移除条件
    /// </summary>
    /// <param name="condition">要移除的条件</param>
    public void RemoveCondition(ConditionViewModel condition)
    {
        if (Conditions.Contains(condition))
        {
            Conditions.Remove(condition);
        }
    }

    #endregion
}
