using EventProcessor.Core.Models;
using EPConfigTool.Services;
using System.Collections.ObjectModel;
using System.ComponentModel.DataAnnotations;

namespace EPConfigTool.ViewModels;

/// <summary>
/// 设备信号配置 ViewModel
/// </summary>
public partial class DeviceSignalViewModel : ViewModelBase
{
    private string _topicsText = string.Empty;
    private string _triggerField = string.Empty;
    private string _triggerValuesText = string.Empty;
    private int _holdingTimeoutSec = 20;

    public DeviceSignalViewModel(DeviceSignalConfiguration? model = null, IHelpInfoService? helpInfoService = null)
    {
        if (model != null)
        {
            LoadFromModel(model);
        }
    }

    #region Properties

    /// <summary>
    /// MQTT 主题列表（以逗号分隔的文本形式）
    /// </summary>
    public string TopicsText
    {
        get => _topicsText;
        set
        {
            if (SetProperty(ref _topicsText, value))
            {
                OnPropertyChanged(nameof(Topics));
            }
        }
    }

    /// <summary>
    /// MQTT 主题列表
    /// </summary>
    public string[] Topics
    {
        get
        {
            if (string.IsNullOrWhiteSpace(TopicsText))
                return Array.Empty<string>();

            return TopicsText
                .Split(',', StringSplitOptions.RemoveEmptyEntries)
                .Select(t => t.Trim())
                .Where(t => !string.IsNullOrEmpty(t))
                .ToArray();
        }
    }

    /// <summary>
    /// 触发字段名称
    /// </summary>
    [Required(ErrorMessage = "触发字段不能为空")]
    public string TriggerField
    {
        get => _triggerField;
        set => SetProperty(ref _triggerField, value);
    }

    /// <summary>
    /// 触发值映射（以文本形式显示）
    /// </summary>
    public string TriggerValuesText
    {
        get => _triggerValuesText;
        set
        {
            if (SetProperty(ref _triggerValuesText, value))
            {
                OnPropertyChanged(nameof(TriggerValues));
            }
        }
    }

    /// <summary>
    /// 触发值映射字典
    /// </summary>
    public Dictionary<string, string> TriggerValues
    {
        get
        {
            var result = new Dictionary<string, string>();
            
            if (string.IsNullOrWhiteSpace(TriggerValuesText))
                return result;

            var lines = TriggerValuesText.Split('\n', StringSplitOptions.RemoveEmptyEntries);
            foreach (var line in lines)
            {
                var parts = line.Split(':', 2, StringSplitOptions.RemoveEmptyEntries);
                if (parts.Length == 2)
                {
                    var key = parts[0].Trim().Trim('"');
                    var value = parts[1].Trim().Trim('"');
                    result[key] = value;
                }
            }

            return result;
        }
    }

    /// <summary>
    /// 保持超时时间（秒）
    /// </summary>
    [Range(1, 3600, ErrorMessage = "保持超时时间必须在1-3600秒之间")]
    public int HoldingTimeoutSec
    {
        get => _holdingTimeoutSec;
        set => SetProperty(ref _holdingTimeoutSec, value);
    }

    #endregion

    #region Methods

    /// <summary>
    /// 从模型加载数据
    /// </summary>
    /// <param name="model">设备信号配置模型</param>
    public void LoadFromModel(DeviceSignalConfiguration model)
    {
        TopicsText = model.Topics != null ? string.Join(", ", model.Topics) : string.Empty;
        TriggerField = model.TriggerField ?? string.Empty;
        HoldingTimeoutSec = model.HoldingTimeoutSec;

        // 转换触发值映射为文本格式
        if (model.TriggerValues != null && model.TriggerValues.Count > 0)
        {
            var lines = model.TriggerValues.Select(kvp => $"\"{kvp.Key}\": \"{kvp.Value}\"");
            TriggerValuesText = string.Join("\n", lines);
        }
        else
        {
            TriggerValuesText = string.Empty;
        }
    }

    /// <summary>
    /// 转换为模型对象
    /// </summary>
    /// <returns>设备信号配置模型</returns>
    public DeviceSignalConfiguration ToModel()
    {
        return new DeviceSignalConfiguration
        {
            Topics = Topics,
            TriggerField = TriggerField,
            TriggerValues = TriggerValues,
            HoldingTimeoutSec = HoldingTimeoutSec
        };
    }

    /// <summary>
    /// 添加新的触发值映射
    /// </summary>
    /// <param name="key">键</param>
    /// <param name="value">值</param>
    public void AddTriggerValue(string key, string value)
    {
        if (string.IsNullOrWhiteSpace(key) || string.IsNullOrWhiteSpace(value))
            return;

        var newLine = $"\"{key.Trim()}\": \"{value.Trim()}\"";
        
        if (string.IsNullOrWhiteSpace(TriggerValuesText))
        {
            TriggerValuesText = newLine;
        }
        else
        {
            TriggerValuesText += "\n" + newLine;
        }
    }

    /// <summary>
    /// 清空触发值映射
    /// </summary>
    public void ClearTriggerValues()
    {
        TriggerValuesText = string.Empty;
    }

    /// <summary>
    /// 添加新的主题
    /// </summary>
    /// <param name="topic">主题名称</param>
    public void AddTopic(string topic)
    {
        if (string.IsNullOrWhiteSpace(topic))
            return;

        var currentTopics = Topics.ToList();
        if (!currentTopics.Contains(topic.Trim()))
        {
            currentTopics.Add(topic.Trim());
            TopicsText = string.Join(", ", currentTopics);
        }
    }

    /// <summary>
    /// 移除主题
    /// </summary>
    /// <param name="topic">主题名称</param>
    public void RemoveTopic(string topic)
    {
        if (string.IsNullOrWhiteSpace(topic))
            return;

        var currentTopics = Topics.ToList();
        if (currentTopics.Remove(topic.Trim()))
        {
            TopicsText = string.Join(", ", currentTopics);
        }
    }

    #endregion
}
