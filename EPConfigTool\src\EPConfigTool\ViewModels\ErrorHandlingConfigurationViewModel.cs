using CommunityToolkit.Mvvm.ComponentModel;
using EPConfigTool.Services;
using EventProcessor.Core.Models;
using System.Collections.ObjectModel;

namespace EPConfigTool.ViewModels;

/// <summary>
/// 错误处理配置 ViewModel
/// 封装 ErrorHandlingConfiguration 模型并提供 UI 绑定支持
/// </summary>
public partial class ErrorHandlingConfigurationViewModel : ViewModelBase, IHelpAware
{
    private readonly IHelpInfoService? _helpInfoService;

    [ObservableProperty]
    private string _toleranceLevel = "Normal";

    [ObservableProperty]
    private int _maxRetries = 3;

    [ObservableProperty]
    private int _retryDelay = 1000;

    [ObservableProperty]
    private bool _useExponentialBackoff = true;

    [ObservableProperty]
    private int _maxBackoffDelay = 30000;

    [ObservableProperty]
    private string _onRuleFailure = "ContinueProcessing";

    [ObservableProperty]
    private string _onAIFailure = "UseBusinessOnly";

    [ObservableProperty]
    private string _onTimerFailure = "ImmediateAlarm";

    [ObservableProperty]
    private string _onMqttFailure = "Retry";

    [ObservableProperty]
    private string _errorLogLevel = "Error";

    [ObservableProperty]
    private bool _detailedStackTrace = true;

    [ObservableProperty]
    private bool _includeMessagePayload = false;

    [ObservableProperty]
    private bool _enablePerformanceMonitoring = true;

    [ObservableProperty]
    private double _errorRateThreshold = 0.1;

    [ObservableProperty]
    private string _currentHelpInfo = "错误处理配置。定义 EventProcessor 在遇到各种错误情况时的处理策略。";

    public event HelpInfoUpdatedEventHandler? HelpInfoUpdated;

    public ObservableCollection<string> ToleranceLevels { get; } = new()
    {
        "Strict", "Normal", "Lenient"
    };

    public ObservableCollection<string> RuleFailureStrategies { get; } = new()
    {
        "StopProcessing", "ContinueProcessing"
    };

    public ObservableCollection<string> AIFailureStrategies { get; } = new()
    {
        "SkipEvent", "UseBusinessOnly", "ForceAlarm"
    };

    public ObservableCollection<string> TimerFailureStrategies { get; } = new()
    {
        "ImmediateAlarm", "SkipAlarm"
    };

    public ObservableCollection<string> MqttFailureStrategies { get; } = new()
    {
        "Retry", "Shutdown", "ContinueOffline"
    };

    public ObservableCollection<string> LogLevels { get; } = new()
    {
        "Trace", "Debug", "Information", "Warning", "Error", "Critical"
    };

    public ErrorHandlingConfigurationViewModel(IHelpInfoService? helpInfoService = null)
    {
        _helpInfoService = helpInfoService;
    }

    /// <summary>
    /// 从模型加载数据
    /// </summary>
    /// <param name="model">错误处理配置模型</param>
    public void LoadFromModel(ErrorHandlingConfiguration model)
    {
        ToleranceLevel = model.ToleranceLevel;
        
        MaxRetries = model.RetryPolicy.MaxRetries;
        RetryDelay = model.RetryPolicy.RetryDelay;
        UseExponentialBackoff = model.RetryPolicy.UseExponentialBackoff;
        MaxBackoffDelay = model.RetryPolicy.MaxBackoffDelay;
        
        OnRuleFailure = model.FallbackStrategy.OnRuleFailure;
        OnAIFailure = model.FallbackStrategy.OnAIFailure;
        OnTimerFailure = model.FallbackStrategy.OnTimerFailure;
        OnMqttFailure = model.FallbackStrategy.OnMqttFailure;
        
        ErrorLogLevel = model.Logging.ErrorLogLevel;
        DetailedStackTrace = model.Logging.DetailedStackTrace;
        IncludeMessagePayload = model.Logging.IncludeMessagePayload;
        EnablePerformanceMonitoring = model.Logging.EnablePerformanceMonitoring;
        ErrorRateThreshold = model.Logging.ErrorRateThreshold;
    }

    /// <summary>
    /// 转换为模型对象
    /// </summary>
    /// <returns>错误处理配置模型</returns>
    public ErrorHandlingConfiguration ToModel()
    {
        return new ErrorHandlingConfiguration
        {
            ToleranceLevel = ToleranceLevel,
            RetryPolicy = new RetryPolicyConfiguration
            {
                MaxRetries = MaxRetries,
                RetryDelay = RetryDelay,
                UseExponentialBackoff = UseExponentialBackoff,
                MaxBackoffDelay = MaxBackoffDelay
            },
            FallbackStrategy = new FallbackStrategyConfiguration
            {
                OnRuleFailure = OnRuleFailure,
                OnAIFailure = OnAIFailure,
                OnTimerFailure = OnTimerFailure,
                OnMqttFailure = OnMqttFailure
            },
            Logging = new ErrorLoggingConfiguration
            {
                ErrorLogLevel = ErrorLogLevel,
                DetailedStackTrace = DetailedStackTrace,
                IncludeMessagePayload = IncludeMessagePayload,
                EnablePerformanceMonitoring = EnablePerformanceMonitoring,
                ErrorRateThreshold = ErrorRateThreshold
            }
        };
    }

    /// <summary>
    /// 更新帮助信息
    /// </summary>
    /// <param name="helpKey">帮助键</param>
    public void UpdateHelpInfo(string helpKey)
    {
        if (_helpInfoService != null)
        {
            CurrentHelpInfo = _helpInfoService.GetStatusBarInfo(helpKey);
        }
        else
        {
            CurrentHelpInfo = helpKey switch
            {
                "ErrorHandling.ToleranceLevel" => "错误容忍级别。Strict=严格模式，Normal=正常模式，Lenient=宽松模式。",
                "ErrorHandling.MaxRetries" => "最大重试次数。当操作失败时的最大重试次数，范围：0-10。",
                "ErrorHandling.RetryDelay" => "重试延迟（毫秒）。两次重试之间的延迟时间，范围：100-60000毫秒。",
                "ErrorHandling.UseExponentialBackoff" => "是否使用指数退避。启用后重试延迟会逐渐增加。",
                "ErrorHandling.MaxBackoffDelay" => "最大退避延迟（毫秒）。指数退避的最大延迟时间，范围：1000-300000毫秒。",
                "ErrorHandling.OnRuleFailure" => "规则失败策略。StopProcessing=停止处理，ContinueProcessing=继续处理。",
                "ErrorHandling.OnAIFailure" => "AI失败策略。SkipEvent=跳过事件，UseBusinessOnly=仅使用业务规则，ForceAlarm=强制告警。",
                "ErrorHandling.OnTimerFailure" => "定时器失败策略。ImmediateAlarm=立即告警，SkipAlarm=跳过告警。",
                "ErrorHandling.OnMqttFailure" => "MQTT失败策略。Retry=重试，Shutdown=关闭，ContinueOffline=离线继续。",
                "ErrorHandling.ErrorLogLevel" => "错误日志级别。记录错误信息的最低日志级别。",
                "ErrorHandling.DetailedStackTrace" => "是否包含详细堆栈跟踪。启用后错误日志包含完整的调用堆栈。",
                "ErrorHandling.IncludeMessagePayload" => "是否包含消息负载。启用后错误日志包含消息内容（生产环境建议关闭）。",
                "ErrorHandling.EnablePerformanceMonitoring" => "是否启用性能监控。监控系统性能指标和健康状态。",
                "ErrorHandling.ErrorRateThreshold" => "错误率阈值。超过此阈值时触发告警，范围：0.0-1.0。",
                _ => "错误处理配置参数。定义系统在遇到各种错误情况时的处理策略。"
            };
        }
    }

    /// <summary>
    /// 验证配置
    /// </summary>
    /// <returns>验证错误列表</returns>
    public List<string> Validate()
    {
        var errors = new List<string>();

        if (!ToleranceLevels.Contains(ToleranceLevel))
            errors.Add("错误容忍级别必须是 Strict、Normal 或 Lenient");

        if (MaxRetries < 0 || MaxRetries > 10)
            errors.Add("最大重试次数必须在 0-10 之间");

        if (RetryDelay < 100 || RetryDelay > 60000)
            errors.Add("重试延迟必须在 100-60000 毫秒之间");

        if (MaxBackoffDelay < 1000 || MaxBackoffDelay > 300000)
            errors.Add("最大退避延迟必须在 1000-300000 毫秒之间");

        if (!RuleFailureStrategies.Contains(OnRuleFailure))
            errors.Add("规则失败策略必须是 StopProcessing 或 ContinueProcessing");

        if (!AIFailureStrategies.Contains(OnAIFailure))
            errors.Add("AI失败策略必须是 SkipEvent、UseBusinessOnly 或 ForceAlarm");

        if (!TimerFailureStrategies.Contains(OnTimerFailure))
            errors.Add("定时器失败策略必须是 ImmediateAlarm 或 SkipAlarm");

        if (!MqttFailureStrategies.Contains(OnMqttFailure))
            errors.Add("MQTT失败策略必须是 Retry、Shutdown 或 ContinueOffline");

        if (!LogLevels.Contains(ErrorLogLevel))
            errors.Add("错误日志级别必须是有效的日志级别");

        if (ErrorRateThreshold < 0.0 || ErrorRateThreshold > 1.0)
            errors.Add("错误率阈值必须在 0.0-1.0 之间");

        return errors;
    }

    /// <summary>
    /// 重置为默认值
    /// </summary>
    public void ResetToDefault()
    {
        ToleranceLevel = "Normal";
        MaxRetries = 3;
        RetryDelay = 1000;
        UseExponentialBackoff = true;
        MaxBackoffDelay = 30000;
        OnRuleFailure = "ContinueProcessing";
        OnAIFailure = "UseBusinessOnly";
        OnTimerFailure = "ImmediateAlarm";
        OnMqttFailure = "Retry";
        ErrorLogLevel = "Error";
        DetailedStackTrace = true;
        IncludeMessagePayload = false;
        EnablePerformanceMonitoring = true;
        ErrorRateThreshold = 0.1;
    }
}
