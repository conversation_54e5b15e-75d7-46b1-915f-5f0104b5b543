using EventProcessor.Core.Models;
using EPConfigTool.Services;
using System.Collections.ObjectModel;
using System.ComponentModel.DataAnnotations;

namespace EPConfigTool.ViewModels;

/// <summary>
/// 事件配置 ViewModel
/// 封装 EventConfiguration 模型并提供 UI 绑定支持，包含详细的帮助信息
/// </summary>
public partial class EventViewModel : ViewModelBase, IHelpAware
{
    private string _eventId = string.Empty;
    private string _eventName = string.Empty;
    private string _evaluationStrategy = "BusinessOnly";
    private string _priority = "P3";
    private string _commId = string.Empty;
    private string _positionId = string.Empty;
    private string? _imageCropCoordinates;
    private int _alarmGracePeriodSeconds = 3;
    private bool _enableAlarmCancellation = true;
    private string _correlationTimeWindow = "minute";
    private int? _customTimeWindowMinutes;
    private string? _aiPrompt;
    private int? _aiAnalysisDelaySec;
    private string _currentHelpInfo = "请选择配置项查看详细说明";
    private readonly IHelpInfoService? _helpInfoService;

    public EventViewModel(EventConfiguration model, IHelpInfoService? helpInfoService = null)
    {
        _helpInfoService = helpInfoService;
        LoadFromModel(model);

        // 初始化子 ViewModels
        DeviceSignal = new DeviceSignalViewModel(model.DeviceSignal, helpInfoService);
        RuleConfiguration = new RuleConfigurationViewModel(model.RuleConfiguration, helpInfoService);

        // 订阅子 ViewModel 的属性变更事件
        DeviceSignal.PropertyChanged += (s, e) => OnPropertyChanged(nameof(DeviceSignal));
        RuleConfiguration.PropertyChanged += (s, e) => OnPropertyChanged(nameof(RuleConfiguration));
    }

    #region Properties

    /// <summary>
    /// 事件ID
    /// </summary>
    [Required(ErrorMessage = "事件ID不能为空")]
    [RegularExpression(@"^EV\d{6}$", ErrorMessage = "事件ID格式必须为EV开头的6位数字")]
    public string EventId
    {
        get => _eventId;
        set => SetProperty(ref _eventId, value);
    }

    /// <summary>
    /// 事件名称
    /// </summary>
    [Required(ErrorMessage = "事件名称不能为空")]
    [StringLength(100, ErrorMessage = "事件名称长度不能超过100个字符")]
    public string EventName
    {
        get => _eventName;
        set => SetProperty(ref _eventName, value);
    }

    /// <summary>
    /// 评估策略
    /// </summary>
    [Required(ErrorMessage = "评估策略不能为空")]
    public string EvaluationStrategy
    {
        get => _evaluationStrategy;
        set => SetProperty(ref _evaluationStrategy, value);
    }

    /// <summary>
    /// 优先级
    /// </summary>
    public string Priority
    {
        get => _priority;
        set => SetProperty(ref _priority, value);
    }

    /// <summary>
    /// 小区ID
    /// </summary>
    public string CommId
    {
        get => _commId;
        set => SetProperty(ref _commId, value);
    }

    /// <summary>
    /// 位置ID
    /// </summary>
    public string PositionId
    {
        get => _positionId;
        set => SetProperty(ref _positionId, value);
    }

    /// <summary>
    /// 图片裁剪坐标
    /// </summary>
    [RegularExpression(@"^\d+,\d+,\d+,\d+$", ErrorMessage = "图片裁剪坐标格式必须为x1,y1,x2,y2")]
    public string? ImageCropCoordinates
    {
        get => _imageCropCoordinates;
        set => SetProperty(ref _imageCropCoordinates, value);
    }

    /// <summary>
    /// 告警静默期时长（秒）
    /// </summary>
    [Range(0, 60, ErrorMessage = "告警静默期时长必须在0-60秒之间")]
    public int AlarmGracePeriodSeconds
    {
        get => _alarmGracePeriodSeconds;
        set => SetProperty(ref _alarmGracePeriodSeconds, value);
    }

    /// <summary>
    /// 是否启用告警撤销功能
    /// </summary>
    public bool EnableAlarmCancellation
    {
        get => _enableAlarmCancellation;
        set => SetProperty(ref _enableAlarmCancellation, value);
    }

    /// <summary>
    /// 时间窗口关联策略
    /// </summary>
    [RegularExpression("^(minute|hour|custom)$", ErrorMessage = "时间窗口关联策略必须是minute、hour或custom")]
    public string CorrelationTimeWindow
    {
        get => _correlationTimeWindow;
        set
        {
            if (SetProperty(ref _correlationTimeWindow, value))
            {
                OnPropertyChanged(nameof(IsCustomTimeWindowEnabled));
            }
        }
    }

    /// <summary>
    /// 自定义时间窗口大小（分钟）
    /// </summary>
    [Range(1, 1440, ErrorMessage = "自定义时间窗口大小必须在1-1440分钟之间")]
    public int? CustomTimeWindowMinutes
    {
        get => _customTimeWindowMinutes;
        set => SetProperty(ref _customTimeWindowMinutes, value);
    }

    /// <summary>
    /// 是否启用自定义时间窗口
    /// </summary>
    public bool IsCustomTimeWindowEnabled => CorrelationTimeWindow == "custom";

    /// <summary>
    /// AI提示词（AI模式时必填）
    /// </summary>
    public string? AIPrompt
    {
        get => _aiPrompt;
        set => SetProperty(ref _aiPrompt, value);
    }

    /// <summary>
    /// AI分析延迟时间（秒）
    /// </summary>
    [Range(1, 300, ErrorMessage = "AI分析延迟时间必须在1-300秒之间")]
    public int? AIAnalysisDelaySec
    {
        get => _aiAnalysisDelaySec;
        set => SetProperty(ref _aiAnalysisDelaySec, value);
    }

    /// <summary>
    /// 设备信号配置
    /// </summary>
    public DeviceSignalViewModel DeviceSignal { get; }

    /// <summary>
    /// 规则配置
    /// </summary>
    public RuleConfigurationViewModel RuleConfiguration { get; }

    /// <summary>
    /// 当前帮助信息
    /// </summary>
    public string CurrentHelpInfo
    {
        get => _currentHelpInfo;
        set => SetProperty(ref _currentHelpInfo, value);
    }

    /// <summary>
    /// 帮助信息更新事件
    /// </summary>
    public event HelpInfoUpdatedEventHandler? HelpInfoUpdated;

    /// <summary>
    /// 更新帮助信息
    /// </summary>
    /// <param name="configKey">配置项键名</param>
    public void UpdateHelpInfo(string configKey)
    {
        if (_helpInfoService != null)
        {
            var helpInfo = _helpInfoService.GetStatusBarInfo(configKey);
            CurrentHelpInfo = helpInfo;
            HelpInfoUpdated?.Invoke(this, new HelpInfoEventArgs(configKey, helpInfo));
        }
    }

    /// <summary>
    /// 获取配置项的 ToolTip 文本
    /// </summary>
    /// <param name="configKey">配置项键名</param>
    /// <returns>ToolTip 文本</returns>
    public string GetToolTip(string configKey)
    {
        return _helpInfoService?.GetToolTip(configKey) ?? "暂无帮助信息";
    }

    // ToolTip 属性 - 用于 XAML 绑定
    public string EventIdToolTip => GetToolTip("EventId");
    public string EventNameToolTip => GetToolTip("EventName");
    public string EvaluationStrategyToolTip => GetToolTip("EvaluationStrategy");
    public string PriorityToolTip => GetToolTip("Priority");
    public string CommIdToolTip => GetToolTip("CommId");
    public string PositionIdToolTip => GetToolTip("PositionId");

    /// <summary>
    /// 评估策略选项
    /// </summary>
    public static readonly string[] EvaluationStrategyOptions = { "AI", "BusinessOnly", "AIAndBusiness" };

    /// <summary>
    /// 优先级选项
    /// </summary>
    public static readonly string[] PriorityOptions = { "P1", "P2", "P3", "P4", "P5" };

    /// <summary>
    /// 时间窗口关联策略选项
    /// </summary>
    public static readonly string[] CorrelationTimeWindowOptions = { "minute", "hour", "custom" };

    #endregion

    #region Methods

    /// <summary>
    /// 从模型加载数据
    /// </summary>
    /// <param name="model">事件配置模型</param>
    public void LoadFromModel(EventConfiguration model)
    {
        EventId = model.EventId;
        EventName = model.EventName;
        EvaluationStrategy = model.EvaluationStrategy;
        Priority = model.Priority ?? "P3";
        CommId = model.CommId ?? string.Empty;
        PositionId = model.PositionId ?? string.Empty;
        ImageCropCoordinates = model.ImageCropCoordinates;
        AlarmGracePeriodSeconds = model.AlarmGracePeriodSeconds;
        EnableAlarmCancellation = model.EnableAlarmCancellation;
        CorrelationTimeWindow = model.CorrelationTimeWindow;
        CustomTimeWindowMinutes = model.CustomTimeWindowMinutes;
        AIPrompt = model.AIPrompt;
        AIAnalysisDelaySec = model.AIAnalysisDelaySec;
    }

    /// <summary>
    /// 转换为模型对象
    /// </summary>
    /// <returns>事件配置模型</returns>
    public EventConfiguration ToModel()
    {
        return new EventConfiguration
        {
            EventId = EventId,
            EventName = EventName,
            EvaluationStrategy = EvaluationStrategy,
            Priority = Priority,
            CommId = CommId,
            PositionId = PositionId,
            ImageCropCoordinates = ImageCropCoordinates,
            DeviceSignal = DeviceSignal.ToModel(),
            RuleConfiguration = RuleConfiguration.ToModel(),
            AlarmGracePeriodSeconds = AlarmGracePeriodSeconds,
            EnableAlarmCancellation = EnableAlarmCancellation,
            CorrelationTimeWindow = CorrelationTimeWindow,
            CustomTimeWindowMinutes = CustomTimeWindowMinutes,
            AIPrompt = AIPrompt,
            AIAnalysisDelaySec = AIAnalysisDelaySec
        };
    }

    /// <summary>
    /// 验证当前配置
    /// </summary>
    /// <returns>验证结果</returns>
    public EventValidationResult ValidateConfiguration()
    {
        var validationContext = new ValidationContext(this);
        var validationResults = new List<System.ComponentModel.DataAnnotations.ValidationResult>();

        var isValid = Validator.TryValidateObject(this, validationContext, validationResults, true);

        return new EventValidationResult
        {
            IsValid = isValid,
            Errors = validationResults.Select(vr => vr.ErrorMessage ?? "未知验证错误").ToList()
        };
    }

    #endregion
}

/// <summary>
/// 事件验证结果
/// </summary>
public record EventValidationResult
{
    public bool IsValid { get; init; }
    public List<string> Errors { get; init; } = new();
}
