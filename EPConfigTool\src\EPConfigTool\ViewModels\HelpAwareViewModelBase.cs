using EPConfigTool.Services;
using System.ComponentModel;

namespace EPConfigTool.ViewModels;

/// <summary>
/// 支持帮助信息的 ViewModel 基类
/// 提供 ToolTip 和状态栏信息的统一支持
/// </summary>
public abstract class HelpAwareViewModelBase : ViewModelBase
{
    private readonly IHelpInfoService _helpInfoService;
    private string _currentHelpInfo = "请选择配置项查看详细说明";

    protected HelpAwareViewModelBase(IHelpInfoService helpInfoService)
    {
        _helpInfoService = helpInfoService;
    }

    /// <summary>
    /// 当前选中配置项的帮助信息（用于状态栏显示）
    /// </summary>
    public string CurrentHelpInfo
    {
        get => _currentHelpInfo;
        set => SetProperty(ref _currentHelpInfo, value);
    }

    /// <summary>
    /// 获取指定配置项的 ToolTip 文本
    /// </summary>
    /// <param name="configKey">配置项键名</param>
    /// <returns>ToolTip 文本</returns>
    protected string GetToolTip(string configKey)
    {
        return _helpInfoService.GetToolTip(configKey);
    }

    /// <summary>
    /// 更新当前帮助信息（用于状态栏显示）
    /// </summary>
    /// <param name="configKey">配置项键名</param>
    protected void UpdateCurrentHelpInfo(string configKey)
    {
        CurrentHelpInfo = _helpInfoService.GetStatusBarInfo(configKey);
    }

    /// <summary>
    /// 获取配置项的完整帮助信息
    /// </summary>
    /// <param name="configKey">配置项键名</param>
    /// <returns>完整帮助信息</returns>
    protected ConfigHelpInfo GetHelpInfo(string configKey)
    {
        return _helpInfoService.GetHelpInfo(configKey);
    }

    /// <summary>
    /// 创建带帮助信息的属性包装器
    /// </summary>
    /// <typeparam name="T">属性类型</typeparam>
    /// <param name="configKey">配置项键名</param>
    /// <param name="getValue">获取值的函数</param>
    /// <param name="setValue">设置值的函数</param>
    /// <returns>属性包装器</returns>
    protected HelpAwareProperty<T> CreateHelpAwareProperty<T>(
        string configKey,
        Func<T> getValue,
        Action<T> setValue)
    {
        return new HelpAwareProperty<T>(configKey, getValue, setValue, _helpInfoService);
    }
}

/// <summary>
/// 带帮助信息的属性包装器
/// </summary>
/// <typeparam name="T">属性类型</typeparam>
public class HelpAwareProperty<T> : INotifyPropertyChanged
{
    private readonly string _configKey;
    private readonly Func<T> _getValue;
    private readonly Action<T> _setValue;
    private readonly IHelpInfoService _helpInfoService;

    public HelpAwareProperty(
        string configKey,
        Func<T> getValue,
        Action<T> setValue,
        IHelpInfoService helpInfoService)
    {
        _configKey = configKey;
        _getValue = getValue;
        _setValue = setValue;
        _helpInfoService = helpInfoService;
    }

    /// <summary>
    /// 属性值
    /// </summary>
    public T Value
    {
        get => _getValue();
        set
        {
            _setValue(value);
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(nameof(Value)));
        }
    }

    /// <summary>
    /// ToolTip 文本
    /// </summary>
    public string ToolTip => _helpInfoService.GetToolTip(_configKey);

    /// <summary>
    /// 状态栏帮助信息
    /// </summary>
    public string StatusBarInfo => _helpInfoService.GetStatusBarInfo(_configKey);

    /// <summary>
    /// 完整帮助信息
    /// </summary>
    public ConfigHelpInfo HelpInfo => _helpInfoService.GetHelpInfo(_configKey);

    /// <summary>
    /// 配置项键名
    /// </summary>
    public string ConfigKey => _configKey;

    public event PropertyChangedEventHandler? PropertyChanged;
}

/// <summary>
/// 帮助信息相关的命令和事件参数
/// </summary>
public class HelpInfoEventArgs : EventArgs
{
    public string ConfigKey { get; }
    public string HelpInfo { get; }

    public HelpInfoEventArgs(string configKey, string helpInfo)
    {
        ConfigKey = configKey;
        HelpInfo = helpInfo;
    }
}

/// <summary>
/// 帮助信息更新事件委托
/// </summary>
public delegate void HelpInfoUpdatedEventHandler(object sender, HelpInfoEventArgs e);

/// <summary>
/// 支持帮助信息的接口
/// </summary>
public interface IHelpAware
{
    /// <summary>
    /// 帮助信息更新事件
    /// </summary>
    event HelpInfoUpdatedEventHandler? HelpInfoUpdated;

    /// <summary>
    /// 当前帮助信息
    /// </summary>
    string CurrentHelpInfo { get; }

    /// <summary>
    /// 更新帮助信息
    /// </summary>
    /// <param name="configKey">配置项键名</param>
    void UpdateHelpInfo(string configKey);
}
