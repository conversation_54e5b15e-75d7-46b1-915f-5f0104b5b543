using CommunityToolkit.Mvvm.ComponentModel;
using EPConfigTool.Models;
using EPConfigTool.Services;
using System.Collections.ObjectModel;

namespace EPConfigTool.ViewModels;

/// <summary>
/// 日志配置 ViewModel
/// 封装 LoggingConfiguration 模型并提供 UI 绑定支持
/// </summary>
public partial class LoggingConfigurationViewModel : ViewModelBase, IHelpAware
{
    private readonly IHelpInfoService? _helpInfoService;

    [ObservableProperty]
    private string _defaultLogLevel = "Information";

    [ObservableProperty]
    private string _microsoftLogLevel = "Warning";

    [ObservableProperty]
    private string _microsoftHostingLifetimeLogLevel = "Information";

    [ObservableProperty]
    private string _eventProcessorLogLevel = "Debug";

    [ObservableProperty]
    private string _currentHelpInfo = "日志配置。定义应用程序各组件的日志记录级别。";

    public event HelpInfoUpdatedEventHandler? HelpInfoUpdated;

    public ObservableCollection<string> LogLevels { get; } = new()
    {
        "Trace", "Debug", "Information", "Warning", "Error", "Critical"
    };

    public LoggingConfigurationViewModel(IHelpInfoService? helpInfoService = null)
    {
        _helpInfoService = helpInfoService;
    }

    /// <summary>
    /// 从模型加载数据
    /// </summary>
    /// <param name="model">日志配置模型</param>
    public void LoadFromModel(LoggingConfiguration model)
    {
        if (model.LogLevel != null)
        {
            DefaultLogLevel = model.LogLevel.Default;
            MicrosoftLogLevel = model.LogLevel.Microsoft;
            MicrosoftHostingLifetimeLogLevel = model.LogLevel.MicrosoftHostingLifetime ?? "Information";
            EventProcessorLogLevel = model.LogLevel.EventProcessor;
        }
    }

    /// <summary>
    /// 转换为模型对象
    /// </summary>
    /// <returns>日志配置模型</returns>
    public LoggingConfiguration ToModel()
    {
        return new LoggingConfiguration
        {
            LogLevel = new LogLevelConfiguration
            {
                Default = DefaultLogLevel,
                Microsoft = MicrosoftLogLevel,
                MicrosoftHostingLifetime = MicrosoftHostingLifetimeLogLevel,
                EventProcessor = EventProcessorLogLevel
            }
        };
    }

    /// <summary>
    /// 更新帮助信息
    /// </summary>
    /// <param name="helpKey">帮助键</param>
    public void UpdateHelpInfo(string helpKey)
    {
        if (_helpInfoService != null)
        {
            CurrentHelpInfo = _helpInfoService.GetStatusBarInfo(helpKey);
        }
        else
        {
            CurrentHelpInfo = helpKey switch
            {
                "Logging.Default" => "默认日志级别。应用程序的基础日志记录级别，影响所有未特别指定的组件。",
                "Logging.Microsoft" => "Microsoft 组件日志级别。控制 .NET 框架和 Microsoft 库的日志输出。",
                "Logging.MicrosoftHostingLifetime" => "Microsoft.Hosting.Lifetime 日志级别。控制应用程序生命周期事件的日志输出。",
                "Logging.EventProcessor" => "EventProcessor 日志级别。控制事件处理器核心组件的日志输出。",
                _ => "日志配置参数。控制应用程序各组件的日志记录详细程度。"
            };
        }
    }

    /// <summary>
    /// 验证配置
    /// </summary>
    /// <returns>验证错误列表</returns>
    public List<string> Validate()
    {
        var errors = new List<string>();

        if (!LogLevels.Contains(DefaultLogLevel))
            errors.Add("默认日志级别必须是有效的日志级别");

        if (!LogLevels.Contains(MicrosoftLogLevel))
            errors.Add("Microsoft 日志级别必须是有效的日志级别");

        if (!LogLevels.Contains(MicrosoftHostingLifetimeLogLevel))
            errors.Add("Microsoft.Hosting.Lifetime 日志级别必须是有效的日志级别");

        if (!LogLevels.Contains(EventProcessorLogLevel))
            errors.Add("EventProcessor 日志级别必须是有效的日志级别");

        return errors;
    }

    /// <summary>
    /// 重置为默认值
    /// </summary>
    public void ResetToDefault()
    {
        DefaultLogLevel = "Information";
        MicrosoftLogLevel = "Warning";
        MicrosoftHostingLifetimeLogLevel = "Information";
        EventProcessorLogLevel = "Debug";
    }

    /// <summary>
    /// 设置为开发环境配置
    /// </summary>
    public void SetDevelopmentConfiguration()
    {
        DefaultLogLevel = "Debug";
        MicrosoftLogLevel = "Information";
        MicrosoftHostingLifetimeLogLevel = "Information";
        EventProcessorLogLevel = "Debug";
    }

    /// <summary>
    /// 设置为生产环境配置
    /// </summary>
    public void SetProductionConfiguration()
    {
        DefaultLogLevel = "Information";
        MicrosoftLogLevel = "Warning";
        MicrosoftHostingLifetimeLogLevel = "Warning";
        EventProcessorLogLevel = "Information";
    }

    /// <summary>
    /// 设置为调试配置
    /// </summary>
    public void SetDebugConfiguration()
    {
        DefaultLogLevel = "Trace";
        MicrosoftLogLevel = "Debug";
        MicrosoftHostingLifetimeLogLevel = "Debug";
        EventProcessorLogLevel = "Trace";
    }
}
