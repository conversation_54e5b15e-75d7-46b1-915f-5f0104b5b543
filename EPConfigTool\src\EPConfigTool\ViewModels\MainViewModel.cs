using CommunityToolkit.Mvvm.Input;
using EPConfigTool.Models;
using EPConfigTool.Services;
using EventProcessor.Core.Models;
using Microsoft.Extensions.Logging;
using System.Collections.ObjectModel;
using System.ComponentModel.DataAnnotations;
using System.IO;
using System.Text.RegularExpressions;
using System.Windows;

namespace EPConfigTool.ViewModels;

/// <summary>
/// 主窗口 ViewModel
/// 管理 YAML 配置文件的加载、保存和事件配置的编辑，支持帮助信息显示
/// </summary>
public partial class MainViewModel : ViewModelBase, IHelpAware
{
    private readonly IYamlConfigurationService _configService;
    private readonly IUnifiedConfigurationService _unifiedConfigService;
    private readonly IFileDialogService _fileDialogService;
    private readonly IHelpInfoService _helpInfoService;
    private readonly ILogger<MainViewModel> _logger;

    private EventViewModel? _currentEvent;
    private UnifiedConfigurationViewModel? _currentUnifiedConfig;
    private string? _currentFilePath;
    private bool _hasUnsavedChanges;
    private bool _isUnifiedMode = true; // 默认使用统一配置模式
    private string _currentHelpInfo = "准备就绪。请点击\"打开\"按钮加载 YAML 配置文件，或选择配置项查看详细说明。";

    public MainViewModel(
        IYamlConfigurationService configService,
        IUnifiedConfigurationService unifiedConfigService,
        IFileDialogService fileDialogService,
        IHelpInfoService helpInfoService,
        ILogger<MainViewModel> logger)
    {
        _configService = configService;
        _unifiedConfigService = unifiedConfigService;
        _fileDialogService = fileDialogService;
        _helpInfoService = helpInfoService;
        _logger = logger;

        StatusMessage = "准备就绪。请点击\"打开\"按钮加载 YAML 配置文件。";
        
        // 初始化命令
        LoadCommand = new AsyncRelayCommand(LoadConfigurationAsync);
        SaveCommand = new AsyncRelayCommand(SaveConfigurationAsync, CanSaveConfiguration);
        SaveAsCommand = new AsyncRelayCommand(SaveAsConfigurationAsync, CanSaveConfiguration);
        NewCommand = new RelayCommand(CreateNewConfiguration);
        ValidateCommand = new AsyncRelayCommand(ValidateCurrentConfigurationAsync, CanValidateConfiguration);
        SwitchToUnifiedModeCommand = new RelayCommand(() => IsUnifiedMode = true);
        SwitchToLegacyModeCommand = new RelayCommand(() => IsUnifiedMode = false);
        MigrateToUnifiedCommand = new AsyncRelayCommand(MigrateToUnifiedConfigurationAsync, CanMigrateToUnified);
    }

    #region Properties

    /// <summary>
    /// 当前编辑的事件配置
    /// </summary>
    public EventViewModel? CurrentEvent
    {
        get => _currentEvent;
        set
        {
            if (SetProperty(ref _currentEvent, value))
            {
                OnPropertyChanged(nameof(HasConfiguration));
                OnPropertyChanged(nameof(HasNoConfiguration));
                SaveCommand.NotifyCanExecuteChanged();
                SaveAsCommand.NotifyCanExecuteChanged();
                ValidateCommand.NotifyCanExecuteChanged();
            }
        }
    }

    /// <summary>
    /// 当前编辑的统一配置
    /// </summary>
    public UnifiedConfigurationViewModel? CurrentUnifiedConfig
    {
        get => _currentUnifiedConfig;
        set
        {
            if (SetProperty(ref _currentUnifiedConfig, value))
            {
                OnPropertyChanged(nameof(HasConfiguration));
                OnPropertyChanged(nameof(HasNoConfiguration));
                SaveCommand.NotifyCanExecuteChanged();
                SaveAsCommand.NotifyCanExecuteChanged();
                ValidateCommand.NotifyCanExecuteChanged();
            }
        }
    }

    /// <summary>
    /// 是否使用统一配置模式
    /// </summary>
    public bool IsUnifiedMode
    {
        get => _isUnifiedMode;
        set
        {
            if (SetProperty(ref _isUnifiedMode, value))
            {
                OnPropertyChanged(nameof(IsLegacyMode));
                OnPropertyChanged(nameof(HasConfiguration));
                OnPropertyChanged(nameof(HasNoConfiguration));
            }
        }
    }

    /// <summary>
    /// 是否使用传统配置模式
    /// </summary>
    public bool IsLegacyMode => !IsUnifiedMode;

    /// <summary>
    /// 当前文件路径
    /// </summary>
    public string? CurrentFilePath
    {
        get => _currentFilePath;
        set
        {
            if (SetProperty(ref _currentFilePath, value))
            {
                OnPropertyChanged(nameof(WindowTitle));
            }
        }
    }

    /// <summary>
    /// 是否有未保存的更改
    /// </summary>
    public bool HasUnsavedChanges
    {
        get => _hasUnsavedChanges;
        set
        {
            if (SetProperty(ref _hasUnsavedChanges, value))
            {
                OnPropertyChanged(nameof(WindowTitle));
            }
        }
    }

    /// <summary>
    /// 是否有配置文件
    /// </summary>
    public bool HasConfiguration => IsUnifiedMode ? CurrentUnifiedConfig != null : CurrentEvent != null;

    /// <summary>
    /// 是否没有配置文件
    /// </summary>
    public bool HasNoConfiguration => IsUnifiedMode ? CurrentUnifiedConfig == null : CurrentEvent == null;

    /// <summary>
    /// 窗口标题
    /// </summary>
    public string WindowTitle
    {
        get
        {
            var title = "事件处理器配置工具 v4.1";

            if (!string.IsNullOrEmpty(CurrentFilePath))
            {
                var fileName = Path.GetFileName(CurrentFilePath);
                title += $" - {fileName}";
            }

            if (HasUnsavedChanges)
            {
                title += " *";
            }

            return title;
        }
    }

    /// <summary>
    /// 当前帮助信息（用于状态栏显示）
    /// </summary>
    public string CurrentHelpInfo
    {
        get => _currentHelpInfo;
        set => SetProperty(ref _currentHelpInfo, value);
    }

    /// <summary>
    /// 帮助信息更新事件
    /// </summary>
    public event HelpInfoUpdatedEventHandler? HelpInfoUpdated;

    /// <summary>
    /// 更新帮助信息
    /// </summary>
    /// <param name="configKey">配置项键名</param>
    public void UpdateHelpInfo(string configKey)
    {
        var helpInfo = _helpInfoService.GetStatusBarInfo(configKey);
        CurrentHelpInfo = helpInfo;
        HelpInfoUpdated?.Invoke(this, new HelpInfoEventArgs(configKey, helpInfo));
    }

     /// <summary>
     /// DataAnnotations 基础验证
     /// </summary>
     /// <param name="configuration">配置对象</param>
     /// <param name="errors">错误列表</param>
     private void ValidateWithDataAnnotations(EventConfiguration configuration, List<string> errors)
     {
         var validationContext = new ValidationContext(configuration);
         var validationResults = new List<ValidationResult>();

         if (!Validator.TryValidateObject(configuration, validationContext, validationResults, true))
         {
             errors.AddRange(validationResults.Select(vr => vr.ErrorMessage ?? "未知验证错误"));
         }

         // 验证嵌套对象
         if (configuration.RuleConfiguration != null)
         {
             var ruleValidationContext = new ValidationContext(configuration.RuleConfiguration);
             var ruleValidationResults = new List<ValidationResult>();

             if (!Validator.TryValidateObject(configuration.RuleConfiguration, ruleValidationContext, ruleValidationResults, true))
             {
                 errors.AddRange(ruleValidationResults.Select(vr => vr.ErrorMessage ?? "规则配置验证错误"));
             }
         }

         // MQTT 配置验证已移至统一配置验证中
     }

     /// <summary>
     /// 业务逻辑验证
     /// </summary>
     /// <param name="configuration">配置对象</param>
     /// <param name="errors">错误列表</param>
     /// <param name="warnings">警告列表</param>
     private async Task ValidateBusinessLogicAsync(EventConfiguration configuration, List<string> errors, List<string> warnings)
     {
         // AI策略验证
         ValidateAIStrategy(configuration, errors, warnings);
         
         // 时间窗口验证
         ValidateTimeWindow(configuration, errors, warnings);
         
         // 设备信号配置验证
         ValidateDeviceSignal(configuration.DeviceSignal, errors, warnings);
         
         await Task.CompletedTask;
     }

     /// <summary>
     /// AI策略验证（简化版本）
     /// </summary>
     private void ValidateAIStrategy(EventConfiguration config, List<string> errors, List<string> warnings)
     {
         // 基本的评估策略验证
         var validStrategies = new[] { "AI", "BusinessOnly", "AIAndBusiness" };
         if (!validStrategies.Contains(config.EvaluationStrategy))
         {
             errors.Add($"评估策略 '{config.EvaluationStrategy}' 无效，必须是 AI、BusinessOnly 或 AIAndBusiness");
         }
     }

     /// <summary>
     /// 时间窗口验证（简化版本）
     /// </summary>
     private void ValidateTimeWindow(EventConfiguration config, List<string> errors, List<string> warnings)
     {
         // 基本的时间窗口验证
         var validTimeWindows = new[] { "minute", "hour", "day", "custom" };
         if (!validTimeWindows.Contains(config.CorrelationTimeWindow))
         {
             errors.Add($"时间窗口 '{config.CorrelationTimeWindow}' 无效");
         }

         // 自定义时间窗口验证
         if (config.CorrelationTimeWindow == "custom" && !config.CustomTimeWindowMinutes.HasValue)
         {
             errors.Add("选择自定义时间窗口时，必须设置自定义时间窗口大小");
         }
     }

     /// <summary>
     /// 设备信号配置验证（简化版本）
     /// </summary>
     private void ValidateDeviceSignal(DeviceSignalConfiguration? deviceSignal, List<string> errors, List<string> warnings)
     {
         if (deviceSignal == null)
         {
             warnings.Add("未配置设备信号，事件将无法自动触发");
             return;
         }

         // 基本验证：检查主题配置
         if (deviceSignal.Topics == null || deviceSignal.Topics.Length == 0)
         {
             errors.Add("设备信号主题不能为空");
         }

         // 检查触发字段
         if (string.IsNullOrWhiteSpace(deviceSignal.TriggerField))
         {
             errors.Add("触发字段不能为空");
         }
     }

     /// <summary>
     /// 配置一致性验证（简化版本）
     /// </summary>
     /// <param name="configuration">配置对象</param>
     /// <param name="errors">错误列表</param>
     /// <param name="warnings">警告列表</param>
     private void ValidateConfigurationConsistency(EventConfiguration configuration, List<string> errors, List<string> warnings)
     {
         // 验证规则配置
         ValidateRuleConfiguration(configuration.RuleConfiguration, errors, warnings);

         // 验证事件ID与文件名的一致性（如果有文件路径）
         if (!string.IsNullOrEmpty(CurrentFilePath) && !string.IsNullOrWhiteSpace(configuration.EventId))
         {
             var fileName = Path.GetFileNameWithoutExtension(CurrentFilePath);
             if (!fileName.Contains(configuration.EventId))
             {
                 warnings.Add($"事件ID '{configuration.EventId}' 与文件名 '{fileName}' 不匹配，建议保持一致");
             }
         }
     }

     /// <summary>
     /// 规则配置验证
     /// </summary>
     private void ValidateRuleConfiguration(RuleConfiguration? ruleConfig, List<string> errors, List<string> warnings)
     {
         if (ruleConfig == null)
         {
             errors.Add("规则配置不能为空");
             return;
         }
         
         // 验证排除规则
         ValidateExclusionRules(ruleConfig.ExclusionRules, errors, warnings);
         
         // 验证业务规则
         ValidateBusinessRules(ruleConfig.BusinessRules, errors, warnings);
         
         // 验证AI结果规则
         ValidateAIResultRules(ruleConfig.AIResultRules, errors, warnings);
         
         // 验证告警配置
         ValidateAlarmConfiguration(ruleConfig.AlarmConfig, errors, warnings);
     }

     /// <summary>
     /// 验证排除规则
     /// </summary>
     private void ValidateExclusionRules(ExclusionRuleGroup[]? exclusionRules, List<string> errors, List<string> warnings)
     {
         if (exclusionRules == null || exclusionRules.Length == 0)
         {
             // 排除规则可选，但建议配置
             warnings.Add("未配置排除规则，建议配置以过滤正常业务操作");
             return;
         }

         for (int i = 0; i < exclusionRules.Length; i++)
         {
             var rule = exclusionRules[i];
             if (string.IsNullOrWhiteSpace(rule.SourceTopic))
             {
                 errors.Add($"排除规则组{i + 1}的数据源主题不能为空");
             }
         }
     }

     /// <summary>
     /// 验证业务规则
     /// </summary>
     private void ValidateBusinessRules(BusinessRuleGroup[]? businessRules, List<string> errors, List<string> warnings)
     {
         if (businessRules == null || businessRules.Length == 0)
         {
             errors.Add("业务规则不能为空，至少需要一个规则组");
             return;
         }

         for (int i = 0; i < businessRules.Length; i++)
         {
             var rule = businessRules[i];
             if (string.IsNullOrWhiteSpace(rule.SourceTopic))
             {
                 errors.Add($"业务规则组{i + 1}的数据源主题不能为空");
             }

             // 验证条件组中的字段
             ValidateBusinessRuleConditions(rule, i + 1, errors, warnings);
         }
     }

     /// <summary>
     /// 验证业务规则条件，特别检查 duration 字段的错误使用
     /// </summary>
     private void ValidateBusinessRuleConditions(BusinessRuleGroup ruleGroup, int groupIndex, List<string> errors, List<string> warnings)
     {
         if (ruleGroup.ConditionGroups == null) return;

         foreach (var conditionGroup in ruleGroup.ConditionGroups)
         {
             if (conditionGroup.Conditions == null) continue;

             foreach (var condition in conditionGroup.Conditions)
             {
                 // 检查 duration 字段的错误使用
                 if (string.Equals(condition.FieldName, "duration", StringComparison.OrdinalIgnoreCase))
                 {
                     errors.Add($"业务规则组{groupIndex}中不应包含 'duration' 字段条件。" +
                               "duration 字段由设备信号内部计算产生，应在告警配置中使用，" +
                               "其 SourceRuleType 必须设置为 'DeviceSignal'。" +
                               "请移除此条件，并依赖 DeviceSignal.HoldingTimeoutSec 进行滞留检测。");
                 }

                 // 检查其他常见的设备信号字段误用
                 var deviceSignalFields = new[] { "I1", "I2", "I3", "I4", "holding_duration", "trigger_state" };
                 if (deviceSignalFields.Any(field => string.Equals(condition.FieldName, field, StringComparison.OrdinalIgnoreCase)))
                 {
                     warnings.Add($"业务规则组{groupIndex}中包含疑似设备信号字段 '{condition.FieldName}'。" +
                                "请确认此字段确实来自业务数据源，而非设备信号。");
                 }
             }
         }
     }

     /// <summary>
     /// 验证AI结果规则
     /// </summary>
     private void ValidateAIResultRules(AIResultRuleGroup[]? aiResultRules, List<string> errors, List<string> warnings)
     {
         if (aiResultRules == null || aiResultRules.Length == 0)
         {
             // AI结果规则可选
             return;
         }

         for (int i = 0; i < aiResultRules.Length; i++)
         {
             var rule = aiResultRules[i];
             if (string.IsNullOrWhiteSpace(rule.LogicOperator))
             {
                 errors.Add($"AI结果规则组{i + 1}的逻辑操作符不能为空");
             }
         }
     }

     /// <summary>
     /// 验证告警配置
     /// </summary>
     private void ValidateAlarmConfiguration(AlarmConfiguration? alarmConfig, List<string> errors, List<string> warnings)
     {
         if (alarmConfig == null)
         {
             warnings.Add("未配置告警设置，事件触发时将使用默认告警行为");
             return;
         }

         // 验证告警字段配置
         ValidateAlarmFields(alarmConfig.Fields, errors, warnings);
     }

     /// <summary>
     /// 验证告警字段配置，特别检查 duration 字段的正确配置
     /// </summary>
     private void ValidateAlarmFields(FieldMapping[]? fields, List<string> errors, List<string> warnings)
     {
         if (fields == null || fields.Length == 0)
         {
             warnings.Add("未配置告警字段，告警消息可能缺少关键信息");
             return;
         }

         foreach (var field in fields)
         {
             // 检查 duration 字段的正确配置
             if (string.Equals(field.SourceFieldName, "duration", StringComparison.OrdinalIgnoreCase))
             {
                 if (!string.Equals(field.SourceRuleType, "DeviceSignal", StringComparison.OrdinalIgnoreCase))
                 {
                     errors.Add($"告警字段 '{field.AlarmFieldName}' 使用 duration 字段时，" +
                               "SourceRuleType 必须设置为 'DeviceSignal'，" +
                               $"当前设置为 '{field.SourceRuleType}'。" +
                               "duration 字段由设备信号内部计算产生，不能从业务规则获取。");
                 }
                 else
                 {
                     // 正确配置，给出确认信息
                     warnings.Add($"告警字段 '{field.AlarmFieldName}' 正确配置了 duration 字段，" +
                                "将显示设备检测到的实际滞留时间。");
                 }
             }

             // 检查其他设备信号字段的配置
             var deviceSignalFields = new[] { "I1", "I2", "I3", "I4", "holding_duration", "trigger_state" };
             if (deviceSignalFields.Any(dsField => string.Equals(field.SourceFieldName, dsField, StringComparison.OrdinalIgnoreCase)))
             {
                 if (!string.Equals(field.SourceRuleType, "DeviceSignal", StringComparison.OrdinalIgnoreCase))
                 {
                     warnings.Add($"告警字段 '{field.AlarmFieldName}' 使用疑似设备信号字段 '{field.SourceFieldName}'，" +
                                "建议将 SourceRuleType 设置为 'DeviceSignal'。");
                 }
             }

             // 检查业务字段的配置
             var businessFields = new[] { "CardType", "log_car_no", "log_user_name", "log_remain_days", "log_end_time" };
             if (businessFields.Any(bField => string.Equals(field.SourceFieldName, bField, StringComparison.OrdinalIgnoreCase)))
             {
                 if (!string.Equals(field.SourceRuleType, "BusinessRules", StringComparison.OrdinalIgnoreCase))
                 {
                     warnings.Add($"告警字段 '{field.AlarmFieldName}' 使用业务字段 '{field.SourceFieldName}'，" +
                                "建议将 SourceRuleType 设置为 'BusinessRules'。");
                 }
             }
         }
     }

     /// <summary>
     /// 简化的规则组验证（已移除过时的验证逻辑）
     /// </summary>
     private void ValidateRuleGroup(object ruleGroup, string groupName, List<string> errors, List<string> warnings)
     {
         if (ruleGroup == null)
         {
             errors.Add($"{groupName}不能为空");
             return;
         }

         // 简化验证：仅检查基本的非空条件
         // 详细的规则验证由 EventProcessor.Core 的数据注解处理
     }



     /// <summary>
     /// 复杂结构验证（简化版本）
     /// </summary>
     /// <param name="configuration">配置对象</param>
     /// <param name="errors">错误列表</param>
     /// <param name="warnings">警告列表</param>
     private void ValidateComplexStructures(EventConfiguration configuration, List<string> errors, List<string> warnings)
     {
         // 基本的配置完整性检查
         if (string.IsNullOrWhiteSpace(configuration.EventId))
         {
             errors.Add("事件ID不能为空");
         }

         if (string.IsNullOrWhiteSpace(configuration.EventName))
         {
             errors.Add("事件名称不能为空");
         }
     }







     #endregion

    #region Commands

    public IAsyncRelayCommand LoadCommand { get; }
    public IAsyncRelayCommand SaveCommand { get; }
    public IAsyncRelayCommand SaveAsCommand { get; }
    public IRelayCommand NewCommand { get; }
    public IAsyncRelayCommand ValidateCommand { get; }
    public IRelayCommand SwitchToUnifiedModeCommand { get; }
    public IRelayCommand SwitchToLegacyModeCommand { get; }
    public IAsyncRelayCommand MigrateToUnifiedCommand { get; }

    #endregion

    #region Command Implementations

    private async Task LoadConfigurationAsync()
    {
        try
        {
            var filePath = _fileDialogService.ShowOpenYamlFileDialog("打开 YAML 配置文件");
            if (string.IsNullOrEmpty(filePath))
                return;

            await ExecuteWithBusyStateAsync(
                async () =>
                {
                    // 检测配置文件类型
                    var isUnified = await _unifiedConfigService.IsUnifiedConfigurationFileAsync(filePath);

                    if (isUnified)
                    {
                        // 加载统一配置
                        var unifiedConfig = await _unifiedConfigService.LoadFromYamlFileAsync(filePath);
                        CurrentUnifiedConfig = new UnifiedConfigurationViewModel(_helpInfoService);
                        CurrentUnifiedConfig.LoadFromModel(unifiedConfig);
                        CurrentEvent = null; // 清除传统配置
                        IsUnifiedMode = true;

                        // 订阅属性变更事件
                        CurrentUnifiedConfig.PropertyChanged += OnUnifiedConfigPropertyChanged;
                    }
                    else
                    {
                        // 加载传统配置
                        var configuration = await _configService.LoadFromYamlFileAsync(filePath);
                        CurrentEvent = new EventViewModel(configuration, _helpInfoService);
                        CurrentUnifiedConfig = null; // 清除统一配置
                        IsUnifiedMode = false;

                        // 订阅属性变更事件
                        CurrentEvent.PropertyChanged += OnEventPropertyChanged;
                    }

                    CurrentFilePath = filePath;
                    HasUnsavedChanges = false;
                },
                $"正在从 {Path.GetFileName(filePath)} 加载配置...",
                $"成功加载配置文件: {Path.GetFileName(filePath)}"
            );
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "加载配置文件失败");
            MessageBox.Show($"加载配置文件失败:\n{ex.Message}", "错误",
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private async Task SaveConfigurationAsync()
    {
        if (string.IsNullOrEmpty(CurrentFilePath))
            return;

        if (IsUnifiedMode && CurrentUnifiedConfig == null)
            return;

        if (!IsUnifiedMode && CurrentEvent == null)
            return;

        try
        {
            await ExecuteWithBusyStateAsync(
                async () =>
                {
                    if (IsUnifiedMode)
                    {
                        var unifiedConfig = CurrentUnifiedConfig!.ToModel();
                        await _unifiedConfigService.SaveToYamlFileAsync(CurrentFilePath, unifiedConfig);
                    }
                    else
                    {
                        var configuration = CurrentEvent!.ToModel();
                        await _configService.SaveToYamlFileAsync(CurrentFilePath, configuration);
                    }
                    HasUnsavedChanges = false;
                },
                $"正在保存配置到 {Path.GetFileName(CurrentFilePath)}...",
                "配置已成功保存"
            );
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "保存配置文件失败: {FilePath}", CurrentFilePath);
            MessageBox.Show($"保存配置文件失败:\n{ex.Message}", "错误",
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private async Task SaveAsConfigurationAsync()
    {
        if (IsUnifiedMode && CurrentUnifiedConfig == null)
            return;

        if (!IsUnifiedMode && CurrentEvent == null)
            return;

        try
        {
            string defaultFileName;
            if (IsUnifiedMode)
            {
                var eventId = CurrentUnifiedConfig!.EventConfiguration.EventId;
                defaultFileName = !string.IsNullOrEmpty(eventId)
                    ? $"{eventId}.yaml"
                    : "appsettings.yaml";
            }
            else
            {
                var eventId = CurrentEvent!.EventId;
                defaultFileName = !string.IsNullOrEmpty(eventId)
                    ? $"{eventId}-config.yaml"
                    : "event-config.yaml";
            }

            var filePath = _fileDialogService.ShowSaveYamlFileDialog("另存为 YAML 配置文件", defaultFileName);
            if (string.IsNullOrEmpty(filePath))
                return;

            await ExecuteWithBusyStateAsync(
                async () =>
                {
                    if (IsUnifiedMode)
                    {
                        var unifiedConfig = CurrentUnifiedConfig!.ToModel();
                        await _unifiedConfigService.SaveToYamlFileAsync(filePath, unifiedConfig);
                    }
                    else
                    {
                        var configuration = CurrentEvent!.ToModel();
                        await _configService.SaveToYamlFileAsync(filePath, configuration);
                    }
                    CurrentFilePath = filePath;
                    HasUnsavedChanges = false;
                },
                $"正在保存配置到 {Path.GetFileName(filePath)}...",
                "配置已成功保存"
            );
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "另存为配置文件失败");
            MessageBox.Show($"另存为配置文件失败:\n{ex.Message}", "错误",
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void CreateNewConfiguration()
    {
        if (IsUnifiedMode)
        {
            // 创建默认的统一配置
            CurrentUnifiedConfig = new UnifiedConfigurationViewModel(_helpInfoService);
            CurrentUnifiedConfig.CreateDefault("EV000000", "新建事件");
            CurrentUnifiedConfig.PropertyChanged += OnUnifiedConfigPropertyChanged;
            CurrentEvent = null;
            StatusMessage = "已创建新的统一配置";
        }
        else
        {
            // 创建默认的传统事件配置
            var defaultConfig = UnifiedConfigurationServiceHelpers.CreateDefaultEventConfiguration("EV000000", "新建事件");

            CurrentEvent = new EventViewModel(defaultConfig, _helpInfoService);
            CurrentEvent.PropertyChanged += OnEventPropertyChanged;
            CurrentUnifiedConfig = null;
            StatusMessage = "已创建新的事件配置";
        }

        CurrentFilePath = null;
        HasUnsavedChanges = true;
    }

    private async Task ValidateCurrentConfigurationAsync()
    {
        if (IsUnifiedMode && CurrentUnifiedConfig == null)
            return;

        if (!IsUnifiedMode && CurrentEvent == null)
            return;

        try
        {
            await ExecuteWithBusyStateAsync(
                async () =>
                {
                    if (IsUnifiedMode)
                    {
                        UnifiedConfigurationValidationResult result;

                        // 如果有文件路径，验证文件
                        if (!string.IsNullOrEmpty(CurrentFilePath))
                        {
                            result = await _unifiedConfigService.ValidateYamlFileAsync(CurrentFilePath);
                        }
                        else
                        {
                            // 验证当前内存中的统一配置
                            result = await ValidateInMemoryUnifiedConfigurationAsync();
                        }

                        ShowUnifiedValidationResult(result);
                    }
                    else
                    {
                        YamlValidationResult result;

                        // 如果有文件路径，验证文件
                        if (!string.IsNullOrEmpty(CurrentFilePath))
                        {
                            result = await _configService.ValidateYamlFileAsync(CurrentFilePath);
                        }
                        else
                        {
                            // 验证当前内存中的配置
                            result = await ValidateInMemoryConfigurationAsync();
                        }

                        ShowValidationResult(result);
                    }
                },
                "正在验证配置...",
                "验证完成"
            );
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "验证配置失败");
            MessageBox.Show($"验证配置失败:\n{ex.Message}", "错误",
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    /// <summary>
    /// 验证内存中的配置
    /// 实现完整的四层验证逻辑：DataAnnotations基础验证、业务逻辑验证、配置一致性验证、深度结构验证
    /// </summary>
    /// <returns>验证结果</returns>
    private async Task<YamlValidationResult> ValidateInMemoryConfigurationAsync()
    {
        var errors = new List<string>();
        var warnings = new List<string>();

        try
        {
            // 1. ViewModel → Model 转换
            var configuration = CurrentEvent.ToModel();
            
            // 2. DataAnnotations 基础验证
            ValidateWithDataAnnotations(configuration, errors);
            
            // 3. 业务逻辑验证
            await ValidateBusinessLogicAsync(configuration, errors, warnings);
            
            // 4. 配置一致性验证
            ValidateConfigurationConsistency(configuration, errors, warnings);
            
            // 5. 深度结构验证
            ValidateComplexStructures(configuration, errors, warnings);
            
        }
        catch (Exception ex)
        {
            errors.Add($"验证过程中发生错误: {ex.Message}");
        }

        return new YamlValidationResult 
        { 
            IsValid = errors.Count == 0, 
            Errors = errors, 
            Warnings = warnings 
        };
    }

    #endregion

    #region Command CanExecute

    private bool CanSaveConfiguration() => IsUnifiedMode ? CurrentUnifiedConfig != null : CurrentEvent != null;
    private bool CanValidateConfiguration() => IsUnifiedMode ? CurrentUnifiedConfig != null : CurrentEvent != null;

    #endregion

    #region Event Handlers

    private void OnEventPropertyChanged(object? sender, System.ComponentModel.PropertyChangedEventArgs e)
    {
        HasUnsavedChanges = true;
    }

    private void OnUnifiedConfigPropertyChanged(object? sender, System.ComponentModel.PropertyChangedEventArgs e)
    {
        HasUnsavedChanges = true;
    }

    #endregion

    #region Helper Methods

    private void ShowValidationResult(YamlValidationResult result)
    {
        if (result.IsValid)
        {
            MessageBox.Show("配置验证通过！", "验证结果",
                MessageBoxButton.OK, MessageBoxImage.Information);
        }
        else
        {
            var message = "配置验证失败:\n\n" + string.Join("\n", result.Errors);
            if (result.Warnings.Count > 0)
            {
                message += "\n\n警告:\n" + string.Join("\n", result.Warnings);
            }

            MessageBox.Show(message, "验证结果",
                MessageBoxButton.OK, MessageBoxImage.Warning);
        }
    }

    private void ShowUnifiedValidationResult(UnifiedConfigurationValidationResult result)
    {
        if (result.IsValid)
        {
            MessageBox.Show("统一配置验证通过！", "验证结果",
                MessageBoxButton.OK, MessageBoxImage.Information);
        }
        else
        {
            var message = "统一配置验证失败:\n\n" + string.Join("\n", result.Errors);

            if (result.SectionErrors.Count > 0)
            {
                message += "\n\n分节错误:";
                foreach (var section in result.SectionErrors)
                {
                    message += $"\n[{section.Key}]: " + string.Join(", ", section.Value);
                }
            }

            if (result.Warnings.Count > 0)
            {
                message += "\n\n警告:\n" + string.Join("\n", result.Warnings);
            }

            MessageBox.Show(message, "验证结果",
                MessageBoxButton.OK, MessageBoxImage.Warning);
        }
    }

    private async Task<UnifiedConfigurationValidationResult> ValidateInMemoryUnifiedConfigurationAsync()
    {
        if (CurrentUnifiedConfig == null)
        {
            return new UnifiedConfigurationValidationResult
            {
                IsValid = false,
                Errors = new List<string> { "当前没有统一配置" }
            };
        }

        try
        {
            var unifiedConfig = CurrentUnifiedConfig.ToModel();
            var yamlContent = _unifiedConfigService.SerializeToYamlString(unifiedConfig);
            var parsedConfig = _unifiedConfigService.ParseFromYamlString(yamlContent);

            // 基本验证通过，返回成功结果
            return new UnifiedConfigurationValidationResult
            {
                IsValid = true,
                Errors = new List<string>(),
                Warnings = new List<string>()
            };
        }
        catch (Exception ex)
        {
            return new UnifiedConfigurationValidationResult
            {
                IsValid = false,
                Errors = new List<string> { $"内存配置验证失败: {ex.Message}" }
            };
        }
    }

    #endregion

    #region Unified Configuration Commands

    private async Task MigrateToUnifiedConfigurationAsync()
    {
        try
        {
            if (CurrentEvent == null)
                return;

            var result = MessageBox.Show(
                "是否将当前的传统配置迁移到统一配置格式？\n\n" +
                "迁移后将包含 MQTT 和错误处理配置的默认值。",
                "迁移到统一配置",
                MessageBoxButton.YesNo,
                MessageBoxImage.Question);

            if (result != MessageBoxResult.Yes)
                return;

            await ExecuteWithBusyStateAsync(
                async () =>
                {
                    // 创建统一配置
                    var eventConfig = CurrentEvent.ToModel();
                    var unifiedConfig = _unifiedConfigService.CreateUnifiedConfiguration(eventConfig);

                    // 切换到统一配置模式
                    CurrentUnifiedConfig = new UnifiedConfigurationViewModel(_helpInfoService);
                    CurrentUnifiedConfig.LoadFromModel(unifiedConfig);
                    CurrentUnifiedConfig.PropertyChanged += OnUnifiedConfigPropertyChanged;

                    CurrentEvent = null;
                    IsUnifiedMode = true;
                    HasUnsavedChanges = true;
                },
                "正在迁移配置...",
                "配置迁移完成！现在可以编辑完整的统一配置。"
            );
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "配置迁移失败");
            MessageBox.Show($"配置迁移失败:\n{ex.Message}", "错误",
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private bool CanMigrateToUnified() => !IsUnifiedMode && CurrentEvent != null;

    #endregion
}
