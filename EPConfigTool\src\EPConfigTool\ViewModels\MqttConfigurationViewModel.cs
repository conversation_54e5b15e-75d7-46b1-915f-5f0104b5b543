using CommunityToolkit.Mvvm.ComponentModel;
using EPConfigTool.Services;
using EventProcessor.Core.Models;

namespace EPConfigTool.ViewModels;

/// <summary>
/// MQTT 配置 ViewModel
/// 封装 MqttConfiguration 模型并提供 UI 绑定支持
/// </summary>
public partial class MqttConfigurationViewModel : ViewModelBase, IHelpAware
{
    private readonly IHelpInfoService? _helpInfoService;

    [ObservableProperty]
    private string _brokerHost = "mq.bangdouni.com";

    [ObservableProperty]
    private int _brokerPort = 1883;

    [ObservableProperty]
    private string _clientId = "EP_V4.1_Default";

    [ObservableProperty]
    private string _username = "bdn_event_processor";

    [ObservableProperty]
    private string _password = "Bdn@2024";

    [ObservableProperty]
    private int _keepAliveInterval = 60;

    [ObservableProperty]
    private int _reconnectDelay = 5;

    [ObservableProperty]
    private int _qualityOfServiceLevel = 1;

    [ObservableProperty]
    private string _currentHelpInfo = "MQTT 连接配置。配置 EventProcessor 连接到 MQTT 服务器的参数。";

    public event HelpInfoUpdatedEventHandler? HelpInfoUpdated;

    public MqttConfigurationViewModel(IHelpInfoService? helpInfoService = null)
    {
        _helpInfoService = helpInfoService;
    }

    /// <summary>
    /// 从模型加载数据
    /// </summary>
    /// <param name="model">MQTT 配置模型</param>
    public void LoadFromModel(MqttConfiguration model)
    {
        BrokerHost = model.BrokerHost;
        BrokerPort = model.BrokerPort;
        ClientId = model.ClientId;
        Username = model.Username ?? string.Empty;
        Password = model.Password ?? string.Empty;
        KeepAliveInterval = model.KeepAliveInterval;
        ReconnectDelay = model.ReconnectDelay;
        QualityOfServiceLevel = model.QualityOfServiceLevel;
    }

    /// <summary>
    /// 转换为模型对象
    /// </summary>
    /// <returns>MQTT 配置模型</returns>
    public MqttConfiguration ToModel()
    {
        return new MqttConfiguration
        {
            BrokerHost = BrokerHost,
            BrokerPort = BrokerPort,
            ClientId = ClientId,
            Username = string.IsNullOrEmpty(Username) ? null : Username,
            Password = string.IsNullOrEmpty(Password) ? null : Password,
            KeepAliveInterval = KeepAliveInterval,
            ReconnectDelay = ReconnectDelay,
            QualityOfServiceLevel = QualityOfServiceLevel
        };
    }

    /// <summary>
    /// 更新帮助信息
    /// </summary>
    /// <param name="helpKey">帮助键</param>
    public void UpdateHelpInfo(string helpKey)
    {
        if (_helpInfoService != null)
        {
            CurrentHelpInfo = _helpInfoService.GetStatusBarInfo(helpKey);
        }
        else
        {
            CurrentHelpInfo = helpKey switch
            {
                "Mqtt.BrokerHost" => "MQTT 服务器地址。指定 EventProcessor 连接的 MQTT 代理服务器的主机名或 IP 地址。",
                "Mqtt.BrokerPort" => "MQTT 服务器端口。通常为 1883（非加密）或 8883（SSL/TLS 加密）。",
                "Mqtt.ClientId" => "MQTT 客户端标识符。必须在 MQTT 服务器上唯一。建议包含事件ID以避免冲突。",
                "Mqtt.Username" => "MQTT 连接用户名。如果 MQTT 服务器需要身份验证，请提供用户名。",
                "Mqtt.Password" => "MQTT 连接密码。与用户名配合使用进行身份验证。",
                "Mqtt.KeepAliveInterval" => "保活间隔（秒）。客户端向服务器发送保活消息的间隔时间，范围：10-3600秒。",
                "Mqtt.ReconnectDelay" => "重连延迟（秒）。连接断开后尝试重新连接的延迟时间，范围：1-60秒。",
                "Mqtt.QualityOfServiceLevel" => "服务质量等级。0=最多一次，1=至少一次，2=恰好一次。推荐使用1。",
                _ => "MQTT 配置参数。用于配置 EventProcessor 与 MQTT 服务器的连接。"
            };
        }
    }

    /// <summary>
    /// 生成建议的客户端ID
    /// </summary>
    /// <param name="eventId">事件ID</param>
    public void GenerateClientId(string eventId)
    {
        if (!string.IsNullOrEmpty(eventId))
        {
            ClientId = $"EP_V4.1_{eventId}";
        }
    }

    /// <summary>
    /// 验证配置
    /// </summary>
    /// <returns>验证错误列表</returns>
    public List<string> Validate()
    {
        var errors = new List<string>();

        if (string.IsNullOrWhiteSpace(BrokerHost))
            errors.Add("MQTT 服务器地址不能为空");

        if (BrokerPort < 1 || BrokerPort > 65535)
            errors.Add("MQTT 服务器端口必须在 1-65535 之间");

        if (string.IsNullOrWhiteSpace(ClientId))
            errors.Add("MQTT 客户端ID不能为空");

        if (KeepAliveInterval < 10 || KeepAliveInterval > 3600)
            errors.Add("保活间隔必须在 10-3600 秒之间");

        if (ReconnectDelay < 1 || ReconnectDelay > 60)
            errors.Add("重连延迟必须在 1-60 秒之间");

        if (QualityOfServiceLevel < 0 || QualityOfServiceLevel > 2)
            errors.Add("服务质量等级必须是 0、1 或 2");

        return errors;
    }

    /// <summary>
    /// 重置为默认值
    /// </summary>
    public void ResetToDefault()
    {
        BrokerHost = "mq.bangdouni.com";
        BrokerPort = 1883;
        ClientId = "EP_V4.1_Default";
        Username = "bdn_event_processor";
        Password = "Bdn@2024";
        KeepAliveInterval = 60;
        ReconnectDelay = 5;
        QualityOfServiceLevel = 1;
    }

    /// <summary>
    /// 测试连接配置
    /// </summary>
    /// <returns>连接测试结果</returns>
    public async Task<bool> TestConnectionAsync()
    {
        try
        {
            // 这里可以实现实际的 MQTT 连接测试
            // 目前返回基本验证结果
            var errors = Validate();
            return errors.Count == 0;
        }
        catch
        {
            return false;
        }
    }
}
