using CommunityToolkit.Mvvm.Input;
using EventProcessor.Core.Models;
using EPConfigTool.Services;
using System.Collections.ObjectModel;

namespace EPConfigTool.ViewModels;

/// <summary>
/// 规则配置 ViewModel
/// 管理排除规则、业务规则、AI规则和告警配置
/// </summary>
public partial class RuleConfigurationViewModel : ViewModelBase
{
    public RuleConfigurationViewModel(RuleConfiguration? model = null, IHelpInfoService? helpInfoService = null)
    {
        // 初始化集合
        ExclusionRules = new ObservableCollection<ExclusionRuleGroupViewModel>();
        BusinessRules = new ObservableCollection<BusinessRuleGroupViewModel>();
        AIResultRules = new ObservableCollection<AIResultRuleGroupViewModel>();

        // 初始化告警配置
        AlarmConfig = new AlarmConfigurationViewModel();

        // 初始化命令
        AddExclusionRuleCommand = new RelayCommand(AddExclusionRule);
        RemoveExclusionRuleCommand = new RelayCommand<ExclusionRuleGroupViewModel>(RemoveExclusionRule);
        AddBusinessRuleCommand = new RelayCommand(AddBusinessRule);
        RemoveBusinessRuleCommand = new RelayCommand<BusinessRuleGroupViewModel>(RemoveBusinessRule);
        AddAIResultRuleCommand = new RelayCommand(AddAIResultRule);
        RemoveAIResultRuleCommand = new RelayCommand<AIResultRuleGroupViewModel>(RemoveAIResultRule);

        // 从模型加载数据
        if (model != null)
        {
            LoadFromModel(model);
        }

        // 订阅集合变更事件
        ExclusionRules.CollectionChanged += (s, e) => OnPropertyChanged(nameof(ExclusionRules));
        BusinessRules.CollectionChanged += (s, e) => OnPropertyChanged(nameof(BusinessRules));
        AIResultRules.CollectionChanged += (s, e) => OnPropertyChanged(nameof(AIResultRules));
    }

    #region Properties

    /// <summary>
    /// 排除规则集合
    /// </summary>
    public ObservableCollection<ExclusionRuleGroupViewModel> ExclusionRules { get; }

    /// <summary>
    /// 业务规则集合
    /// </summary>
    public ObservableCollection<BusinessRuleGroupViewModel> BusinessRules { get; }

    /// <summary>
    /// AI结果规则集合
    /// </summary>
    public ObservableCollection<AIResultRuleGroupViewModel> AIResultRules { get; }

    /// <summary>
    /// 告警配置
    /// </summary>
    public AlarmConfigurationViewModel AlarmConfig { get; }

    /// <summary>
    /// 是否有排除规则
    /// </summary>
    public bool HasExclusionRules => ExclusionRules.Count > 0;

    /// <summary>
    /// 是否有业务规则
    /// </summary>
    public bool HasBusinessRules => BusinessRules.Count > 0;

    /// <summary>
    /// 是否有AI规则
    /// </summary>
    public bool HasAIResultRules => AIResultRules.Count > 0;

    #endregion

    #region Commands

    public IRelayCommand AddExclusionRuleCommand { get; }
    public IRelayCommand<ExclusionRuleGroupViewModel> RemoveExclusionRuleCommand { get; }
    public IRelayCommand AddBusinessRuleCommand { get; }
    public IRelayCommand<BusinessRuleGroupViewModel> RemoveBusinessRuleCommand { get; }
    public IRelayCommand AddAIResultRuleCommand { get; }
    public IRelayCommand<AIResultRuleGroupViewModel> RemoveAIResultRuleCommand { get; }

    #endregion

    #region Command Implementations

    private void AddExclusionRule()
    {
        var newRule = new ExclusionRuleGroupViewModel(new ExclusionRuleGroup
        {
            SourceType = "MQTT",
            SourceTopic = "",
            LogicOperator = "AND"
        });

        ExclusionRules.Add(newRule);
        OnPropertyChanged(nameof(HasExclusionRules));
    }

    private void RemoveExclusionRule(ExclusionRuleGroupViewModel? rule)
    {
        if (rule != null && ExclusionRules.Contains(rule))
        {
            ExclusionRules.Remove(rule);
            OnPropertyChanged(nameof(HasExclusionRules));
        }
    }

    private void AddBusinessRule()
    {
        var newRule = new BusinessRuleGroupViewModel(new BusinessRuleGroup
        {
            SourceTopic = "",
            LogicOperator = "AND"
        });

        BusinessRules.Add(newRule);
        OnPropertyChanged(nameof(HasBusinessRules));
    }

    private void RemoveBusinessRule(BusinessRuleGroupViewModel? rule)
    {
        if (rule != null && BusinessRules.Contains(rule))
        {
            BusinessRules.Remove(rule);
            OnPropertyChanged(nameof(HasBusinessRules));
        }
    }

    private void AddAIResultRule()
    {
        var newRule = new AIResultRuleGroupViewModel(new AIResultRuleGroup
        {
            LogicOperator = "AND"
        });

        AIResultRules.Add(newRule);
        OnPropertyChanged(nameof(HasAIResultRules));
    }

    private void RemoveAIResultRule(AIResultRuleGroupViewModel? rule)
    {
        if (rule != null && AIResultRules.Contains(rule))
        {
            AIResultRules.Remove(rule);
            OnPropertyChanged(nameof(HasAIResultRules));
        }
    }

    #endregion

    #region Methods

    /// <summary>
    /// 从模型加载数据
    /// </summary>
    /// <param name="model">规则配置模型</param>
    public void LoadFromModel(RuleConfiguration model)
    {
        // 清空现有数据
        ExclusionRules.Clear();
        BusinessRules.Clear();
        AIResultRules.Clear();

        // 加载排除规则
        if (model.ExclusionRules != null)
        {
            foreach (var rule in model.ExclusionRules)
            {
                ExclusionRules.Add(new ExclusionRuleGroupViewModel(rule));
            }
        }

        // 加载业务规则
        if (model.BusinessRules != null)
        {
            foreach (var rule in model.BusinessRules)
            {
                BusinessRules.Add(new BusinessRuleGroupViewModel(rule));
            }
        }

        // 加载AI规则
        if (model.AIResultRules != null)
        {
            foreach (var rule in model.AIResultRules)
            {
                AIResultRules.Add(new AIResultRuleGroupViewModel(rule));
            }
        }

        // 加载告警配置
        AlarmConfig.LoadFromModel(model.AlarmConfig);

        // 更新属性通知
        OnPropertyChanged(nameof(HasExclusionRules));
        OnPropertyChanged(nameof(HasBusinessRules));
        OnPropertyChanged(nameof(HasAIResultRules));
    }

    /// <summary>
    /// 转换为模型对象
    /// </summary>
    /// <returns>规则配置模型</returns>
    public RuleConfiguration ToModel()
    {
        return new RuleConfiguration
        {
            ExclusionRules = ExclusionRules.Count > 0 
                ? ExclusionRules.Select(r => r.ToModel()).ToArray() 
                : null,
            BusinessRules = BusinessRules.Count > 0 
                ? BusinessRules.Select(r => r.ToModel()).ToArray() 
                : null,
            AIResultRules = AIResultRules.Count > 0 
                ? AIResultRules.Select(r => r.ToModel()).ToArray() 
                : null,
            AlarmConfig = AlarmConfig.ToModel()
        };
    }

    #endregion
}
