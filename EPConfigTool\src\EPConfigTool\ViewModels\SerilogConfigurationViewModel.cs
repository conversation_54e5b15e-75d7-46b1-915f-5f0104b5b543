using CommunityToolkit.Mvvm.ComponentModel;
using EPConfigTool.Models;
using EPConfigTool.Services;
using System.Collections.ObjectModel;

namespace EPConfigTool.ViewModels;

/// <summary>
/// Serilog 配置 ViewModel
/// 封装 SerilogConfiguration 模型并提供 UI 绑定支持
/// </summary>
public partial class SerilogConfigurationViewModel : ViewModelBase, IHelpAware
{
    private readonly IHelpInfoService? _helpInfoService;

    [ObservableProperty]
    private string _defaultMinimumLevel = "Information";

    [ObservableProperty]
    private string _microsoftOverride = "Warning";

    [ObservableProperty]
    private string _systemOverride = "Warning";

    [ObservableProperty]
    private string _eventProcessorOverride = "Debug";

    [ObservableProperty]
    private bool _enableConsoleOutput = true;

    [ObservableProperty]
    private bool _enableFileOutput = true;

    [ObservableProperty]
    private string _logFilePath = "logs/eventprocessor-.log";

    [ObservableProperty]
    private string _rollingInterval = "Day";

    [ObservableProperty]
    private int _retainedFileCountLimit = 30;

    [ObservableProperty]
    private string _outputTemplate = "[{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} {Level:u3}] {SourceContext}: {Message:lj}{NewLine}{Exception}";

    [ObservableProperty]
    private string _currentHelpInfo = "Serilog 配置。定义结构化日志记录的详细设置，包括输出目标和格式。";

    public event HelpInfoUpdatedEventHandler? HelpInfoUpdated;

    public ObservableCollection<string> LogLevels { get; } = new()
    {
        "Verbose", "Debug", "Information", "Warning", "Error", "Fatal"
    };

    public ObservableCollection<string> RollingIntervals { get; } = new()
    {
        "Infinite", "Year", "Month", "Day", "Hour", "Minute"
    };

    public SerilogConfigurationViewModel(IHelpInfoService? helpInfoService = null)
    {
        _helpInfoService = helpInfoService;
    }

    /// <summary>
    /// 从模型加载数据
    /// </summary>
    /// <param name="model">Serilog 配置模型</param>
    public void LoadFromModel(SerilogConfiguration model)
    {
        if (model.MinimumLevel != null)
        {
            DefaultMinimumLevel = model.MinimumLevel.Default;
            
            if (model.MinimumLevel.Override != null)
            {
                MicrosoftOverride = model.MinimumLevel.Override.GetValueOrDefault("Microsoft", "Warning");
                SystemOverride = model.MinimumLevel.Override.GetValueOrDefault("System", "Warning");
                EventProcessorOverride = model.MinimumLevel.Override.GetValueOrDefault("EventProcessor", "Debug");
            }
        }

        if (model.WriteTo != null)
        {
            EnableConsoleOutput = model.WriteTo.Any(w => w.Name == "Console");
            EnableFileOutput = model.WriteTo.Any(w => w.Name == "File");

            var fileConfig = model.WriteTo.FirstOrDefault(w => w.Name == "File");
            if (fileConfig?.Args != null)
            {
                if (fileConfig.Args.TryGetValue("path", out var path))
                    LogFilePath = path.ToString() ?? LogFilePath;
                
                if (fileConfig.Args.TryGetValue("rollingInterval", out var interval))
                    RollingInterval = interval.ToString() ?? RollingInterval;
                
                if (fileConfig.Args.TryGetValue("retainedFileCountLimit", out var limit))
                    RetainedFileCountLimit = Convert.ToInt32(limit);
                
                if (fileConfig.Args.TryGetValue("outputTemplate", out var template))
                    OutputTemplate = template.ToString() ?? OutputTemplate;
            }
        }
    }

    /// <summary>
    /// 转换为模型对象
    /// </summary>
    /// <returns>Serilog 配置模型</returns>
    public SerilogConfiguration ToModel()
    {
        var writeTo = new List<SerilogWriteToConfiguration>();

        if (EnableConsoleOutput)
        {
            writeTo.Add(new SerilogWriteToConfiguration
            {
                Name = "Console",
                Args = new Dictionary<string, object>
                {
                    ["outputTemplate"] = OutputTemplate
                }
            });
        }

        if (EnableFileOutput)
        {
            writeTo.Add(new SerilogWriteToConfiguration
            {
                Name = "File",
                Args = new Dictionary<string, object>
                {
                    ["path"] = LogFilePath,
                    ["rollingInterval"] = RollingInterval,
                    ["retainedFileCountLimit"] = RetainedFileCountLimit,
                    ["outputTemplate"] = OutputTemplate
                }
            });
        }

        return new SerilogConfiguration
        {
            Using = new[] { "Serilog.Sinks.Console", "Serilog.Sinks.File" },
            MinimumLevel = new SerilogMinimumLevelConfiguration
            {
                Default = DefaultMinimumLevel,
                Override = new Dictionary<string, string>
                {
                    ["Microsoft"] = MicrosoftOverride,
                    ["System"] = SystemOverride,
                    ["EventProcessor"] = EventProcessorOverride
                }
            },
            WriteTo = writeTo.ToArray(),
            Enrich = new[] { "FromLogContext", "WithMachineName", "WithThreadId" }
        };
    }

    /// <summary>
    /// 更新帮助信息
    /// </summary>
    /// <param name="helpKey">帮助键</param>
    public void UpdateHelpInfo(string helpKey)
    {
        if (_helpInfoService != null)
        {
            CurrentHelpInfo = _helpInfoService.GetStatusBarInfo(helpKey);
        }
        else
        {
            CurrentHelpInfo = helpKey switch
            {
                "Serilog.DefaultMinimumLevel" => "默认最小日志级别。Serilog 的基础日志记录级别。",
                "Serilog.MicrosoftOverride" => "Microsoft 组件日志级别覆盖。专门控制 Microsoft 库的日志输出。",
                "Serilog.SystemOverride" => "System 组件日志级别覆盖。专门控制 System 库的日志输出。",
                "Serilog.EventProcessorOverride" => "EventProcessor 组件日志级别覆盖。专门控制事件处理器的日志输出。",
                "Serilog.EnableConsoleOutput" => "是否启用控制台输出。将日志输出到控制台窗口。",
                "Serilog.EnableFileOutput" => "是否启用文件输出。将日志保存到文件中。",
                "Serilog.LogFilePath" => "日志文件路径。指定日志文件的保存位置和命名模式。",
                "Serilog.RollingInterval" => "日志文件滚动间隔。控制何时创建新的日志文件。",
                "Serilog.RetainedFileCountLimit" => "保留文件数量限制。控制保留多少个历史日志文件。",
                "Serilog.OutputTemplate" => "输出模板。定义日志条目的格式和包含的信息。",
                _ => "Serilog 配置参数。控制结构化日志记录的详细设置。"
            };
        }
    }

    /// <summary>
    /// 验证配置
    /// </summary>
    /// <returns>验证错误列表</returns>
    public List<string> Validate()
    {
        var errors = new List<string>();

        if (!LogLevels.Contains(DefaultMinimumLevel))
            errors.Add("默认最小日志级别必须是有效的 Serilog 日志级别");

        if (!LogLevels.Contains(MicrosoftOverride))
            errors.Add("Microsoft 覆盖级别必须是有效的 Serilog 日志级别");

        if (!LogLevels.Contains(SystemOverride))
            errors.Add("System 覆盖级别必须是有效的 Serilog 日志级别");

        if (!LogLevels.Contains(EventProcessorOverride))
            errors.Add("EventProcessor 覆盖级别必须是有效的 Serilog 日志级别");

        if (EnableFileOutput)
        {
            if (string.IsNullOrWhiteSpace(LogFilePath))
                errors.Add("启用文件输出时，日志文件路径不能为空");

            if (!RollingIntervals.Contains(RollingInterval))
                errors.Add("滚动间隔必须是有效的间隔值");

            if (RetainedFileCountLimit < 1 || RetainedFileCountLimit > 1000)
                errors.Add("保留文件数量限制必须在 1-1000 之间");
        }

        if (string.IsNullOrWhiteSpace(OutputTemplate))
            errors.Add("输出模板不能为空");

        if (!EnableConsoleOutput && !EnableFileOutput)
            errors.Add("至少需要启用一种输出方式（控制台或文件）");

        return errors;
    }

    /// <summary>
    /// 重置为默认值
    /// </summary>
    public void ResetToDefault()
    {
        DefaultMinimumLevel = "Information";
        MicrosoftOverride = "Warning";
        SystemOverride = "Warning";
        EventProcessorOverride = "Debug";
        EnableConsoleOutput = true;
        EnableFileOutput = true;
        LogFilePath = "logs/eventprocessor-.log";
        RollingInterval = "Day";
        RetainedFileCountLimit = 30;
        OutputTemplate = "[{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} {Level:u3}] {SourceContext}: {Message:lj}{NewLine}{Exception}";
    }

    /// <summary>
    /// 更新日志文件路径以包含事件ID
    /// </summary>
    /// <param name="eventId">事件ID</param>
    public void UpdateLogFilePathForEvent(string eventId)
    {
        if (!string.IsNullOrEmpty(eventId))
        {
            LogFilePath = $"logs/eventprocessor-{eventId}-.log";
        }
    }

    /// <summary>
    /// 设置为开发环境配置
    /// </summary>
    public void SetDevelopmentConfiguration()
    {
        DefaultMinimumLevel = "Debug";
        MicrosoftOverride = "Information";
        SystemOverride = "Information";
        EventProcessorOverride = "Debug";
        EnableConsoleOutput = true;
        EnableFileOutput = true;
    }

    /// <summary>
    /// 设置为生产环境配置
    /// </summary>
    public void SetProductionConfiguration()
    {
        DefaultMinimumLevel = "Information";
        MicrosoftOverride = "Warning";
        SystemOverride = "Warning";
        EventProcessorOverride = "Information";
        EnableConsoleOutput = false;
        EnableFileOutput = true;
        RetainedFileCountLimit = 90; // 生产环境保留更多日志
    }
}
