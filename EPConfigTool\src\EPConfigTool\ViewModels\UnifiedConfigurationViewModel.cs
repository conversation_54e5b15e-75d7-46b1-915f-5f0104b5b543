using CommunityToolkit.Mvvm.ComponentModel;
using EPConfigTool.Models;
using EPConfigTool.Services;
using EventProcessor.Core.Models;

namespace EPConfigTool.ViewModels;

/// <summary>
/// 统一配置 ViewModel
/// 管理包含所有配置节的统一 YAML 配置文件
/// </summary>
public partial class UnifiedConfigurationViewModel : ViewModelBase, IHelpAware
{
    private readonly IHelpInfoService? _helpInfoService;

    [ObservableProperty]
    private EventViewModel _eventConfiguration;

    [ObservableProperty]
    private MqttConfigurationViewModel _mqttConfiguration;

    [ObservableProperty]
    private ErrorHandlingConfigurationViewModel _errorHandlingConfiguration;

    [ObservableProperty]
    private LoggingConfigurationViewModel _loggingConfiguration;

    [ObservableProperty]
    private SerilogConfigurationViewModel _serilogConfiguration;

    [ObservableProperty]
    private string _currentHelpInfo = "统一配置管理器。此界面允许您编辑包含所有配置节的统一 YAML 配置文件。";

    public event HelpInfoUpdatedEventHandler? HelpInfoUpdated;

    public UnifiedConfigurationViewModel(IHelpInfoService? helpInfoService = null)
    {
        _helpInfoService = helpInfoService;

        // 初始化子配置 ViewModels
        var defaultEventConfig = UnifiedConfigurationServiceHelpers.CreateDefaultEventConfiguration("EV000000", "新建事件");
        _eventConfiguration = new EventViewModel(defaultEventConfig, helpInfoService);
        _mqttConfiguration = new MqttConfigurationViewModel(helpInfoService);
        _errorHandlingConfiguration = new ErrorHandlingConfigurationViewModel(helpInfoService);
        _loggingConfiguration = new LoggingConfigurationViewModel(helpInfoService);
        _serilogConfiguration = new SerilogConfigurationViewModel(helpInfoService);

        // 订阅子配置的帮助信息变化
        _eventConfiguration.PropertyChanged += (s, e) => 
        {
            if (e.PropertyName == nameof(EventViewModel.CurrentHelpInfo))
                CurrentHelpInfo = _eventConfiguration.CurrentHelpInfo;
        };

        _mqttConfiguration.PropertyChanged += (s, e) => 
        {
            if (e.PropertyName == nameof(MqttConfigurationViewModel.CurrentHelpInfo))
                CurrentHelpInfo = _mqttConfiguration.CurrentHelpInfo;
        };

        _errorHandlingConfiguration.PropertyChanged += (s, e) => 
        {
            if (e.PropertyName == nameof(ErrorHandlingConfigurationViewModel.CurrentHelpInfo))
                CurrentHelpInfo = _errorHandlingConfiguration.CurrentHelpInfo;
        };
    }

    /// <summary>
    /// 从统一配置模型加载数据
    /// </summary>
    /// <param name="model">统一配置模型</param>
    public void LoadFromModel(UnifiedConfiguration model)
    {
        EventConfiguration.LoadFromModel(model.EventProcessor);
        MqttConfiguration.LoadFromModel(model.Mqtt);
        ErrorHandlingConfiguration.LoadFromModel(model.ErrorHandling);
        
        if (model.Logging != null)
            LoggingConfiguration.LoadFromModel(model.Logging);
        
        if (model.Serilog != null)
            SerilogConfiguration.LoadFromModel(model.Serilog);
    }

    /// <summary>
    /// 转换为统一配置模型对象
    /// </summary>
    /// <returns>统一配置模型</returns>
    public UnifiedConfiguration ToModel()
    {
        return new UnifiedConfiguration
        {
            EventProcessor = EventConfiguration.ToModel(),
            Mqtt = MqttConfiguration.ToModel(),
            ErrorHandling = ErrorHandlingConfiguration.ToModel(),
            Logging = LoggingConfiguration.ToModel(),
            Serilog = SerilogConfiguration.ToModel()
        };
    }

    /// <summary>
    /// 创建默认配置
    /// </summary>
    /// <param name="eventId">事件ID</param>
    /// <param name="eventName">事件名称</param>
    public void CreateDefault(string eventId, string eventName)
    {
        var defaultConfig = UnifiedConfigurationServiceHelpers.CreateDefaultUnifiedConfiguration(eventId, eventName);
        LoadFromModel(defaultConfig);
    }

    /// <summary>
    /// 验证当前配置
    /// </summary>
    /// <returns>验证结果</returns>
    public UnifiedConfigurationValidationResult Validate()
    {
        var model = ToModel();
        return new UnifiedConfigurationValidationResult
        {
            IsValid = true, // 基本验证，详细验证由服务层处理
            Errors = new List<string>(),
            Warnings = new List<string>(),
            SectionErrors = new Dictionary<string, List<string>>()
        };
    }

    /// <summary>
    /// 重置为默认配置
    /// </summary>
    public void ResetToDefault()
    {
        var eventId = EventConfiguration.EventId;
        var eventName = EventConfiguration.EventName;
        
        if (string.IsNullOrEmpty(eventId))
            eventId = "EV000001";
        if (string.IsNullOrEmpty(eventName))
            eventName = "新建事件";

        CreateDefault(eventId, eventName);
    }

    /// <summary>
    /// 更新帮助信息
    /// </summary>
    /// <param name="helpKey">帮助键</param>
    public void UpdateHelpInfo(string helpKey)
    {
        if (_helpInfoService != null)
        {
            CurrentHelpInfo = _helpInfoService.GetStatusBarInfo(helpKey);
        }
    }
}
