using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using System.ComponentModel;

namespace EPConfigTool.ViewModels;

/// <summary>
/// ViewModel 基类
/// 提供属性变更通知和命令支持
/// </summary>
public abstract class ViewModelBase : ObservableObject
{
    private bool _isBusy;
    private string _statusMessage = string.Empty;

    /// <summary>
    /// 是否正在执行操作
    /// </summary>
    public bool IsBusy
    {
        get => _isBusy;
        set => SetProperty(ref _isBusy, value);
    }

    /// <summary>
    /// 状态消息
    /// </summary>
    public string StatusMessage
    {
        get => _statusMessage;
        set => SetProperty(ref _statusMessage, value);
    }

    /// <summary>
    /// 设置忙碌状态并执行异步操作
    /// </summary>
    /// <param name="operation">要执行的异步操作</param>
    /// <param name="busyMessage">忙碌时显示的消息</param>
    /// <param name="successMessage">成功时显示的消息</param>
    protected async Task ExecuteWithBusyStateAsync(
        Func<Task> operation, 
        string busyMessage = "正在处理...", 
        string successMessage = "操作完成")
    {
        if (IsBusy) return;

        try
        {
            IsBusy = true;
            StatusMessage = busyMessage;

            await operation();

            StatusMessage = successMessage;
        }
        catch (Exception ex)
        {
            StatusMessage = $"错误: {ex.Message}";
            throw;
        }
        finally
        {
            IsBusy = false;
        }
    }

    /// <summary>
    /// 设置忙碌状态并执行异步操作（带返回值）
    /// </summary>
    /// <typeparam name="T">返回值类型</typeparam>
    /// <param name="operation">要执行的异步操作</param>
    /// <param name="busyMessage">忙碌时显示的消息</param>
    /// <param name="successMessage">成功时显示的消息</param>
    /// <returns>操作结果</returns>
    protected async Task<T> ExecuteWithBusyStateAsync<T>(
        Func<Task<T>> operation, 
        string busyMessage = "正在处理...", 
        string successMessage = "操作完成")
    {
        if (IsBusy) throw new InvalidOperationException("另一个操作正在进行中");

        try
        {
            IsBusy = true;
            StatusMessage = busyMessage;

            var result = await operation();

            StatusMessage = successMessage;
            return result;
        }
        catch (Exception ex)
        {
            StatusMessage = $"错误: {ex.Message}";
            throw;
        }
        finally
        {
            IsBusy = false;
        }
    }
}
