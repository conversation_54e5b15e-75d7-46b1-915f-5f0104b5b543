<UserControl x:Class="EPConfigTool.Views.AlarmConfigurationView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:EPConfigTool.Views"
             xmlns:vm="clr-namespace:EPConfigTool.ViewModels"
             mc:Ignorable="d" 
             d:DesignHeight="600" d:DesignWidth="800">
    
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="2*"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- 工具栏 -->
        <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="15,15,15,10">
            <TextBlock Text="告警配置" Style="{StaticResource HeaderTextBlockStyle}" VerticalAlignment="Center"/>
            <Button Content="添加字段映射" 
                    Command="{Binding AddFieldCommand}"
                    Style="{StaticResource PrimaryButtonStyle}"
                    Margin="20,0,5,0"
                    ToolTip="添加新的告警字段映射"/>
            <Button Content="删除选中" 
                    Command="{Binding RemoveFieldCommand}"
                    CommandParameter="{Binding ElementName=FieldsDataGrid, Path=SelectedItem}"
                    Style="{StaticResource SecondaryButtonStyle}"
                    ToolTip="删除选中的字段映射"/>
        </StackPanel>

        <!-- 主内容区域 -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto" Margin="15,0,15,5">
            <StackPanel>
                <!-- 无字段映射时的提示 -->
                <Border BorderBrush="#EEEEEE" BorderThickness="1" Padding="20" 
                        Background="#FAFAFA"
                        Visibility="{Binding HasFields, Converter={StaticResource InverseBooleanToVisibilityConverter}}">
                    <StackPanel HorizontalAlignment="Center">
                        <TextBlock Text="暂无告警字段映射" 
                                   FontSize="16" 
                                   Foreground="#999999" 
                                   HorizontalAlignment="Center"/>
                        <TextBlock Text="告警字段映射定义告警消息中各字段的数据来源和格式化规则" 
                                   FontSize="12" 
                                   Foreground="#666666" 
                                   HorizontalAlignment="Center"
                                   Margin="0,5,0,10"/>
                        <Button Content="添加第一个字段映射" 
                                Command="{Binding AddFieldCommand}"
                                Style="{StaticResource PrimaryButtonStyle}"/>
                    </StackPanel>
                </Border>

                <!-- 字段映射列表 -->
                <DataGrid x:Name="FieldsDataGrid"
                          ItemsSource="{Binding Fields}"
                          Style="{StaticResource BaseDataGridStyle}"
                          Visibility="{Binding HasFields, Converter={StaticResource BooleanToVisibilityConverter}}"
                          MinHeight="200"
                          MaxHeight="400">
                    <DataGrid.Columns>
                        <DataGridTextColumn Header="告警字段名" 
                                            Binding="{Binding AlarmFieldName, UpdateSourceTrigger=PropertyChanged}" 
                                            Width="120"/>
                        <DataGridComboBoxColumn Header="源规则类型" 
                                                Width="120">
                            <DataGridComboBoxColumn.ElementStyle>
                                <Style TargetType="ComboBox" BasedOn="{StaticResource BaseComboBoxStyle}">
                                    <Setter Property="ItemsSource" Value="{x:Static vm:FieldMappingViewModel.SourceRuleTypes}"/>
                                    <Setter Property="SelectedItem" Value="{Binding SourceRuleType, UpdateSourceTrigger=PropertyChanged}"/>
                                    <Setter Property="Margin" Value="0"/>
                                </Style>
                            </DataGridComboBoxColumn.ElementStyle>
                            <DataGridComboBoxColumn.EditingElementStyle>
                                <Style TargetType="ComboBox" BasedOn="{StaticResource BaseComboBoxStyle}">
                                    <Setter Property="ItemsSource" Value="{x:Static vm:FieldMappingViewModel.SourceRuleTypes}"/>
                                    <Setter Property="SelectedItem" Value="{Binding SourceRuleType, UpdateSourceTrigger=PropertyChanged}"/>
                                    <Setter Property="Margin" Value="0"/>
                                </Style>
                            </DataGridComboBoxColumn.EditingElementStyle>
                        </DataGridComboBoxColumn>
                        <DataGridTextColumn Header="源字段名" 
                                            Binding="{Binding SourceFieldName, UpdateSourceTrigger=PropertyChanged}" 
                                            Width="150"/>
                        <DataGridTextColumn Header="默认值" 
                                            Binding="{Binding DefaultValue, UpdateSourceTrigger=PropertyChanged}" 
                                            Width="120"/>
                        <DataGridTextColumn Header="格式化模板" 
                                            Binding="{Binding FormatTemplate, UpdateSourceTrigger=PropertyChanged}" 
                                            Width="*"/>
                        <DataGridTemplateColumn Header="操作" Width="80">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <Button Content="删除" 
                                            Command="{Binding DataContext.RemoveFieldCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                            CommandParameter="{Binding}"
                                            Style="{StaticResource SecondaryButtonStyle}"
                                            Padding="5,2"
                                            FontSize="10"
                                            Margin="2"/>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>
                    </DataGrid.Columns>
                </DataGrid>

                <!-- 配置说明 -->
                <Border BorderBrush="#E3F2FD" BorderThickness="1" Padding="15" Background="#E3F2FD" Margin="0,20,0,0">
                    <StackPanel>
                        <TextBlock Text="📋 告警字段映射配置说明" FontWeight="SemiBold" Foreground="{StaticResource PrimaryBrush}" Margin="0,0,0,5"/>
                        <TextBlock TextWrapping="Wrap" Foreground="#666666" FontSize="11">
                            <Run Text="• 告警字段名：告警消息中显示的字段名称"/>
                            <LineBreak/>
                            <Run Text="• 源规则类型：数据来源类型（ExclusionRules、BusinessRules、AIResultRules、DeviceSignal）"/>
                            <LineBreak/>
                            <Run Text="• 源字段名：从数据源中获取哪个字段的值，多个字段用逗号分隔"/>
                            <LineBreak/>
                            <Run Text="• 默认值：当源字段不存在或为空时使用的默认值"/>
                            <LineBreak/>
                            <Run Text="• 格式化模板：使用{字段名}格式引用字段值，如：{CardType}[{log_car_no}]"/>
                        </TextBlock>
                    </StackPanel>
                </Border>

                <!-- 示例配置 -->
                <Border BorderBrush="#FFF3E0" BorderThickness="1" Padding="15" Background="#FFF3E0" Margin="0,10,0,0">
                    <StackPanel>
                        <TextBlock Text="💡 配置示例" FontWeight="SemiBold" Foreground="{StaticResource WarningBrush}" Margin="0,0,0,5"/>
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            
                            <StackPanel Grid.Column="0" Margin="0,0,10,0">
                                <TextBlock Text="常用字段映射：" FontWeight="SemiBold" FontSize="11" Margin="0,0,0,5"/>
                                <TextBlock FontFamily="{StaticResource MonospaceFont}" FontSize="10" Foreground="#666666">
                                    <Run Text="告警字段名: 详情"/>
                                    <LineBreak/>
                                    <Run Text="源规则类型: BusinessRules"/>
                                    <LineBreak/>
                                    <Run Text="源字段名: CardType,log_car_no"/>
                                    <LineBreak/>
                                    <Run Text="格式化模板: [{CardType}][{log_car_no}]"/>
                                </TextBlock>
                            </StackPanel>
                            
                            <StackPanel Grid.Column="1">
                                <TextBlock Text="设备信号字段：" FontWeight="SemiBold" FontSize="11" Margin="0,0,0,5"/>
                                <TextBlock FontFamily="{StaticResource MonospaceFont}" FontSize="10" Foreground="#666666">
                                    <Run Text="告警字段名: 设备"/>
                                    <LineBreak/>
                                    <Run Text="源规则类型: DeviceSignal"/>
                                    <LineBreak/>
                                    <Run Text="源字段名: (留空)"/>
                                    <LineBreak/>
                                    <Run Text="默认值: 帮豆你门岗智能监测"/>
                                </TextBlock>
                            </StackPanel>
                        </Grid>
                    </StackPanel>
                </Border>
            </StackPanel>
        </ScrollViewer>
        
        <!-- 分隔符 -->
        <GridSplitter Grid.Row="2" 
                      Height="3" 
                      HorizontalAlignment="Stretch" 
                      Background="#E0E0E0"
                      Margin="15,5"/>

        <!-- 告警消息预览区域 -->
        <ContentPresenter Grid.Row="3" 
                          Content="{Binding PreviewViewModel}"
                          Visibility="{Binding IsPreviewEnabled, Converter={StaticResource BooleanToVisibilityConverter}}"
                          Margin="15,5,15,15">
            <ContentPresenter.ContentTemplate>
                <DataTemplate>
                    <local:AlarmPreviewPanel/>
                </DataTemplate>
            </ContentPresenter.ContentTemplate>
        </ContentPresenter>
        
        <!-- 预览功能不可用时的提示 -->
        <Border Grid.Row="3" 
                BorderBrush="#EEEEEE" 
                BorderThickness="1" 
                Padding="20" 
                Background="#FAFAFA"
                Margin="15,5,15,15"
                Visibility="{Binding IsPreviewEnabled, Converter={StaticResource InverseBooleanToVisibilityConverter}}">
            <StackPanel HorizontalAlignment="Center">
                <TextBlock Text="告警消息预览功能不可用" 
                           FontSize="14" 
                           Foreground="#999999" 
                           HorizontalAlignment="Center"/>
                <TextBlock Text="预览功能需要相关服务支持，请检查服务配置" 
                           FontSize="11" 
                           Foreground="#666666" 
                           HorizontalAlignment="Center"
                           Margin="0,5,0,0"/>
            </StackPanel>
        </Border>
    </Grid>
</UserControl>
