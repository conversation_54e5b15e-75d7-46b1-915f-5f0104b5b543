<UserControl x:Class="EPConfigTool.Views.AlarmPreviewPanel"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             mc:Ignorable="d" 
             d:DesignHeight="500" d:DesignWidth="600">
    
    <UserControl.Resources>
        <!-- 预览面板样式 -->
        <Style x:Key="PreviewPanelStyle" TargetType="Border">
            <Setter Property="Background" Value="#F8F9FA"/>
            <Setter Property="BorderBrush" Value="#E9ECEF"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="CornerRadius" Value="4"/>
            <Setter Property="Padding" Value="12"/>
            <Setter Property="Margin" Value="5"/>
        </Style>
        
        <Style x:Key="PreviewHeaderStyle" TargetType="TextBlock">
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Foreground" Value="#495057"/>
            <Setter Property="Margin" Value="0,0,0,8"/>
        </Style>
        
        <Style x:Key="JsonTextStyle" TargetType="TextBox">
            <Setter Property="FontFamily" Value="Consolas, Monaco, 'Courier New', monospace"/>
            <Setter Property="FontSize" Value="11"/>
            <Setter Property="Background" Value="#FFFFFF"/>
            <Setter Property="BorderBrush" Value="#DEE2E6"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Padding" Value="8"/>
            <Setter Property="IsReadOnly" Value="True"/>
            <Setter Property="TextWrapping" Value="Wrap"/>
            <Setter Property="AcceptsReturn" Value="True"/>
            <Setter Property="VerticalScrollBarVisibility" Value="Auto"/>
            <Setter Property="HorizontalScrollBarVisibility" Value="Auto"/>
        </Style>
        
        <Style x:Key="FieldValueStyle" TargetType="TextBlock">
            <Setter Property="FontFamily" Value="Consolas, Monaco, 'Courier New', monospace"/>
            <Setter Property="FontSize" Value="11"/>
            <Setter Property="Foreground" Value="#495057"/>
            <Setter Property="TextWrapping" Value="Wrap"/>
        </Style>
        
        <Style x:Key="ErrorTextStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="11"/>
            <Setter Property="Foreground" Value="#DC3545"/>
            <Setter Property="TextWrapping" Value="Wrap"/>
        </Style>
        
        <Style x:Key="WarningTextStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="11"/>
            <Setter Property="Foreground" Value="#FD7E14"/>
            <Setter Property="TextWrapping" Value="Wrap"/>
        </Style>
    </UserControl.Resources>
    
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- 工具栏 -->
        <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,10">
            <TextBlock Text="📋 告警消息预览" 
                       Style="{StaticResource PreviewHeaderStyle}" 
                       VerticalAlignment="Center"/>
            <Button Content="刷新预览" 
                    Command="{Binding RefreshPreviewCommand}"
                    Style="{StaticResource PrimaryButtonStyle}"
                    Margin="15,0,5,0"
                    ToolTip="重新生成告警消息预览"/>
            <Button Content="复制JSON" 
                    Command="{Binding CopyJsonCommand}"
                    Style="{StaticResource SecondaryButtonStyle}"
                    ToolTip="复制完整的JSON预览到剪贴板"/>
            <CheckBox Content="使用自定义数据" 
                      IsChecked="{Binding UseCustomSampleData}"
                      VerticalAlignment="Center"
                      Margin="10,0,0,0"
                      ToolTip="使用自定义示例数据而非默认数据"/>
        </StackPanel>

        <!-- 主预览区域 -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
            <StackPanel>
                <!-- 预览状态 -->
                <Border Style="{StaticResource PreviewPanelStyle}"
                        Background="{Binding PreviewStatusBackground}"
                        BorderBrush="{Binding PreviewStatusBorderBrush}">
                    <StackPanel>
                        <TextBlock Text="{Binding PreviewStatusText}" 
                                   FontWeight="SemiBold" 
                                   Foreground="{Binding PreviewStatusForeground}"/>
                        
                        <!-- 错误信息 -->
                        <ItemsControl ItemsSource="{Binding PreviewErrors}" 
                                      Visibility="{Binding HasPreviewErrors, Converter={StaticResource BooleanToVisibilityConverter}}"
                                      Margin="0,5,0,0">
                            <ItemsControl.ItemTemplate>
                                <DataTemplate>
                                    <TextBlock Text="{Binding}" Style="{StaticResource ErrorTextStyle}" Margin="0,2"/>
                                </DataTemplate>
                            </ItemsControl.ItemTemplate>
                        </ItemsControl>
                        
                        <!-- 警告信息 -->
                        <ItemsControl ItemsSource="{Binding PreviewWarnings}" 
                                      Visibility="{Binding HasPreviewWarnings, Converter={StaticResource BooleanToVisibilityConverter}}"
                                      Margin="0,5,0,0">
                            <ItemsControl.ItemTemplate>
                                <DataTemplate>
                                    <TextBlock Text="{Binding}" Style="{StaticResource WarningTextStyle}" Margin="0,2"/>
                                </DataTemplate>
                            </ItemsControl.ItemTemplate>
                        </ItemsControl>
                    </StackPanel>
                </Border>

                <!-- 字段预览 -->
                <Border Style="{StaticResource PreviewPanelStyle}"
                        Visibility="{Binding HasPreviewFields, Converter={StaticResource BooleanToVisibilityConverter}}">
                    <StackPanel>
                        <TextBlock Text="📝 告警字段预览" Style="{StaticResource PreviewHeaderStyle}"/>
                        
                        <DataGrid ItemsSource="{Binding PreviewFields}"
                                  Style="{StaticResource BaseDataGridStyle}"
                                  IsReadOnly="True"
                                  AutoGenerateColumns="False"
                                  MaxHeight="200">
                            <DataGrid.Columns>
                                <DataGridTextColumn Header="字段名" 
                                                    Binding="{Binding Key}" 
                                                    Width="120"
                                                    FontWeight="SemiBold"/>
                                <DataGridTextColumn Header="预览值" 
                                                    Binding="{Binding Value}" 
                                                    Width="*">
                                    <DataGridTextColumn.ElementStyle>
                                        <Style TargetType="TextBlock" BasedOn="{StaticResource FieldValueStyle}"/>
                                    </DataGridTextColumn.ElementStyle>
                                </DataGridTextColumn>
                            </DataGrid.Columns>
                        </DataGrid>
                    </StackPanel>
                </Border>

                <!-- JSON预览 -->
                <Border Style="{StaticResource PreviewPanelStyle}"
                        Visibility="{Binding HasJsonPreview, Converter={StaticResource BooleanToVisibilityConverter}}">
                    <StackPanel>
                        <TextBlock Text="📄 JSON消息预览" Style="{StaticResource PreviewHeaderStyle}"/>
                        
                        <TextBox Text="{Binding JsonPreview}" 
                                 Style="{StaticResource JsonTextStyle}"
                                 MinHeight="150"
                                 MaxHeight="300"/>
                    </StackPanel>
                </Border>

                <!-- 自定义示例数据编辑器 -->
                <Border Style="{StaticResource PreviewPanelStyle}"
                        Visibility="{Binding UseCustomSampleData, Converter={StaticResource BooleanToVisibilityConverter}}">
                    <StackPanel>
                        <TextBlock Text="🛠️ 自定义示例数据" Style="{StaticResource PreviewHeaderStyle}"/>
                        <TextBlock Text="输入JSON格式的示例数据，用于预览字段映射效果：" 
                                   FontSize="11" 
                                   Foreground="#666666" 
                                   Margin="0,0,0,5"/>
                        
                        <TextBox Text="{Binding CustomSampleDataJson, UpdateSourceTrigger=PropertyChanged}" 
                                 Style="{StaticResource JsonTextStyle}"
                                 MinHeight="100"
                                 MaxHeight="200"/>
                        
                        <StackPanel Orientation="Horizontal" Margin="0,5,0,0">
                            <Button Content="应用数据" 
                                    Command="{Binding ApplyCustomDataCommand}"
                                    Style="{StaticResource PrimaryButtonStyle}"
                                    Padding="8,4"/>
                            <Button Content="重置为默认" 
                                    Command="{Binding ResetToDefaultDataCommand}"
                                    Style="{StaticResource SecondaryButtonStyle}"
                                    Margin="5,0,0,0"
                                    Padding="8,4"/>
                        </StackPanel>
                    </StackPanel>
                </Border>

                <!-- 预览说明 -->
                <Border Style="{StaticResource PreviewPanelStyle}" Background="#E3F2FD" BorderBrush="#BBDEFB">
                    <StackPanel>
                        <TextBlock Text="💡 预览说明" 
                                   FontWeight="SemiBold" 
                                   Foreground="#1976D2" 
                                   Margin="0,0,0,5"/>
                        <TextBlock TextWrapping="Wrap" FontSize="11" Foreground="#424242">
                            <Run Text="• 预览基于EP的实际消息拼接逻辑，显示告警消息的最终格式"/>
                            <LineBreak/>
                            <Run Text="• 未配置默认值的字段将显示为 {源字段名} 占位符"/>
                            <LineBreak/>
                            <Run Text="• 格式化模板支持 {字段名} 占位符和标准.NET格式化语法"/>
                            <LineBreak/>
                            <Run Text="• 多字段映射用逗号分隔，如：CardType,log_car_no"/>
                            <LineBreak/>
                            <Run Text="• 自定义示例数据可以测试不同数据源下的字段映射效果"/>
                        </TextBlock>
                    </StackPanel>
                </Border>
            </StackPanel>
        </ScrollViewer>
    </Grid>
</UserControl>