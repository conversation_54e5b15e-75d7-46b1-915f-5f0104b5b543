<UserControl x:Class="EPConfigTool.Views.BusinessRulesView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:EPConfigTool.Views"
             xmlns:vm="clr-namespace:EPConfigTool.ViewModels"
             mc:Ignorable="d" 
             d:DesignHeight="600" d:DesignWidth="800">
    
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- 工具栏 -->
        <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="15,15,15,10">
            <TextBlock Text="业务规则配置" Style="{StaticResource HeaderTextBlockStyle}" VerticalAlignment="Center"/>
            <Button Content="添加规则组" 
                    Command="{Binding AddBusinessRuleCommand}"
                    Style="{StaticResource PrimaryButtonStyle}"
                    Margin="20,0,5,0"
                    ToolTip="添加新的业务规则组"/>
            <Button Content="删除选中" 
                    Command="{Binding RemoveBusinessRuleCommand}"
                    CommandParameter="{Binding ElementName=BusinessRulesDataGrid, Path=SelectedItem}"
                    Style="{StaticResource SecondaryButtonStyle}"
                    ToolTip="删除选中的业务规则组"/>
        </StackPanel>

        <!-- 主内容区域 -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto" Margin="15,0,15,15">
            <StackPanel>
                <!-- 无规则时的提示 -->
                <Border BorderBrush="#EEEEEE" BorderThickness="1" Padding="20" 
                        Background="#FAFAFA"
                        Visibility="{Binding HasBusinessRules, Converter={StaticResource InverseBooleanToVisibilityConverter}}">
                    <StackPanel HorizontalAlignment="Center">
                        <TextBlock Text="暂无业务规则" 
                                   FontSize="16" 
                                   Foreground="#999999" 
                                   HorizontalAlignment="Center"/>
                        <TextBlock Text="业务规则用于定义纯业务逻辑事件的判断条件" 
                                   FontSize="12" 
                                   Foreground="#666666" 
                                   HorizontalAlignment="Center"
                                   Margin="0,5,0,10"/>
                        <Button Content="添加第一个业务规则" 
                                Command="{Binding AddBusinessRuleCommand}"
                                Style="{StaticResource PrimaryButtonStyle}"/>
                    </StackPanel>
                </Border>

                <!-- 业务规则列表 -->
                <ItemsControl ItemsSource="{Binding BusinessRules}"
                              Visibility="{Binding HasBusinessRules, Converter={StaticResource BooleanToVisibilityConverter}}">
                    <ItemsControl.ItemTemplate>
                        <DataTemplate DataType="{x:Type vm:BusinessRuleGroupViewModel}">
                            <Border BorderBrush="#CCCCCC" BorderThickness="1" Margin="0,0,0,15" Background="White">
                                <Expander IsExpanded="True" Header="{Binding SourceTopic, StringFormat='业务规则: {0}'}">
                                    <Expander.HeaderTemplate>
                                        <DataTemplate>
                                            <StackPanel Orientation="Horizontal">
                                                <TextBlock Text="{Binding}" FontWeight="SemiBold"/>
                                                <Button Content="删除" 
                                                        Command="{Binding DataContext.RemoveBusinessRuleCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                                        CommandParameter="{Binding DataContext, RelativeSource={RelativeSource AncestorType=Expander}}"
                                                        Style="{StaticResource SecondaryButtonStyle}"
                                                        Margin="10,0,0,0"
                                                        Padding="8,4"
                                                        FontSize="10"/>
                                            </StackPanel>
                                        </DataTemplate>
                                    </Expander.HeaderTemplate>
                                    
                                    <StackPanel Margin="15">
                                        <!-- 规则基本信息 -->
                                        <Grid>
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="200"/>
                                            </Grid.ColumnDefinitions>
                                            
                                            <StackPanel Grid.Column="0" Margin="0,0,10,0">
                                                <TextBlock Text="数据源主题 *" Style="{StaticResource LabelTextBlockStyle}"/>
                                                <TextBox Text="{Binding SourceTopic, UpdateSourceTrigger=PropertyChanged}" 
                                                         Style="{StaticResource BaseTextBoxStyle}"
                                                         ToolTip="业务数据的MQTT主题来源"/>
                                            </StackPanel>
                                            
                                            <StackPanel Grid.Column="1">
                                                <TextBlock Text="逻辑操作符 *" Style="{StaticResource LabelTextBlockStyle}"/>
                                                <ComboBox ItemsSource="{x:Static vm:BusinessRuleGroupViewModel.LogicOperators}"
                                                          SelectedItem="{Binding LogicOperator, UpdateSourceTrigger=PropertyChanged}"
                                                          Style="{StaticResource BaseComboBoxStyle}"
                                                          ToolTip="条件组合的逻辑运算符"/>
                                            </StackPanel>
                                        </Grid>

                                        <Separator Margin="0,10"/>

                                        <!-- 条件组管理 -->
                                        <StackPanel>
                                            <StackPanel Orientation="Horizontal" Margin="0,0,0,10">
                                                <TextBlock Text="条件组" Style="{StaticResource LabelTextBlockStyle}" VerticalAlignment="Center"/>
                                                <Button Content="添加条件组" 
                                                        Command="{Binding AddConditionGroupCommand}"
                                                        Style="{StaticResource SecondaryButtonStyle}"
                                                        Margin="10,0,5,0"
                                                        Padding="8,4"
                                                        FontSize="11"/>
                                            </StackPanel>

                                            <!-- 条件组列表 -->
                                            <ItemsControl ItemsSource="{Binding ConditionGroups}">
                                                <ItemsControl.ItemTemplate>
                                                    <DataTemplate DataType="{x:Type vm:ConditionGroupViewModel}">
                                                        <Border BorderBrush="#EEEEEE" BorderThickness="1" Margin="0,0,0,10" Padding="10" Background="#FAFAFA">
                                                            <StackPanel>
                                                                <StackPanel Orientation="Horizontal" Margin="0,0,0,10">
                                                                    <TextBlock Text="逻辑操作符:" VerticalAlignment="Center" Margin="0,0,5,0"/>
                                                                    <ComboBox ItemsSource="{x:Static vm:ConditionGroupViewModel.LogicOperators}"
                                                                              SelectedItem="{Binding LogicOperator, UpdateSourceTrigger=PropertyChanged}"
                                                                              Width="80"
                                                                              Style="{StaticResource BaseComboBoxStyle}"
                                                                              Margin="0"/>
                                                                    <Button Content="删除组" 
                                                                            Command="{Binding DataContext.RemoveConditionGroupCommand, RelativeSource={RelativeSource AncestorType=ItemsControl}}"
                                                                            CommandParameter="{Binding}"
                                                                            Style="{StaticResource SecondaryButtonStyle}"
                                                                            Margin="10,0,0,0"
                                                                            Padding="6,3"
                                                                            FontSize="10"/>
                                                                </StackPanel>
                                                                
                                                                <local:ConditionListView DataContext="{Binding}"/>
                                                            </StackPanel>
                                                        </Border>
                                                    </DataTemplate>
                                                </ItemsControl.ItemTemplate>
                                            </ItemsControl>
                                        </StackPanel>

                                        <Separator Margin="0,10"/>

                                        <!-- 直接条件管理 -->
                                        <StackPanel>
                                            <StackPanel Orientation="Horizontal" Margin="0,0,0,10">
                                                <TextBlock Text="直接条件" Style="{StaticResource LabelTextBlockStyle}" VerticalAlignment="Center"/>
                                                <Button Content="添加条件" 
                                                        Command="{Binding AddConditionCommand}"
                                                        Style="{StaticResource SecondaryButtonStyle}"
                                                        Margin="10,0,5,0"
                                                        Padding="8,4"
                                                        FontSize="11"/>
                                            </StackPanel>

                                            <!-- 直接条件列表 -->
                                            <local:ConditionListView DataContext="{Binding}"/>
                                        </StackPanel>
                                    </StackPanel>
                                </Expander>
                            </Border>
                        </DataTemplate>
                    </ItemsControl.ItemTemplate>
                </ItemsControl>

                <!-- 帮助信息 -->
                <Border BorderBrush="#E3F2FD" BorderThickness="1" Padding="15" Background="#E3F2FD" Margin="0,20,0,0">
                    <StackPanel>
                        <TextBlock Text="💡 业务规则配置说明" FontWeight="SemiBold" Foreground="{StaticResource PrimaryBrush}" Margin="0,0,0,5"/>
                        <TextBlock TextWrapping="Wrap" Foreground="#666666" FontSize="11">
                            <Run Text="• 业务规则用于定义纯业务逻辑事件的判断条件"/>
                            <LineBreak/>
                            <Run Text="• 每个规则组可以包含多个条件组和直接条件"/>
                            <LineBreak/>
                            <Run Text="• 条件组支持嵌套逻辑，最多支持2层嵌套"/>
                            <LineBreak/>
                            <Run Text="• 逻辑操作符决定条件之间的组合方式（AND/OR）"/>
                            <LineBreak/>
                            <Run Text="• 数据源主题指定业务数据的MQTT主题来源"/>
                        </TextBlock>
                    </StackPanel>
                </Border>
            </StackPanel>
        </ScrollViewer>
    </Grid>
</UserControl>
