<UserControl x:Class="EPConfigTool.Views.ConditionListView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:EPConfigTool.Views"
             xmlns:vm="clr-namespace:EPConfigTool.ViewModels"
             mc:Ignorable="d" 
             d:DesignHeight="300" d:DesignWidth="800">
    
    <StackPanel>
        <!-- 条件列表 -->
        <DataGrid ItemsSource="{Binding Conditions}"
                  Style="{StaticResource BaseDataGridStyle}"
                  MaxHeight="200"
                  Margin="0,0,0,10">
            <DataGrid.Columns>
                <DataGridTextColumn Header="字段名" 
                                    Binding="{Binding FieldName, UpdateSourceTrigger=PropertyChanged}" 
                                    Width="150"/>
                <DataGridComboBoxColumn Header="数据类型" 
                                        Width="100">
                    <DataGridComboBoxColumn.ElementStyle>
                        <Style TargetType="ComboBox" BasedOn="{StaticResource BaseComboBoxStyle}">
                            <Setter Property="ItemsSource" Value="{x:Static vm:ConditionViewModel.DataTypes}"/>
                            <Setter Property="SelectedItem" Value="{Binding DataType, UpdateSourceTrigger=PropertyChanged}"/>
                            <Setter Property="Margin" Value="0"/>
                        </Style>
                    </DataGridComboBoxColumn.ElementStyle>
                    <DataGridComboBoxColumn.EditingElementStyle>
                        <Style TargetType="ComboBox" BasedOn="{StaticResource BaseComboBoxStyle}">
                            <Setter Property="ItemsSource" Value="{x:Static vm:ConditionViewModel.DataTypes}"/>
                            <Setter Property="SelectedItem" Value="{Binding DataType, UpdateSourceTrigger=PropertyChanged}"/>
                            <Setter Property="Margin" Value="0"/>
                        </Style>
                    </DataGridComboBoxColumn.EditingElementStyle>
                </DataGridComboBoxColumn>
                <DataGridComboBoxColumn Header="操作符" 
                                        Width="120">
                    <DataGridComboBoxColumn.ElementStyle>
                        <Style TargetType="ComboBox" BasedOn="{StaticResource BaseComboBoxStyle}">
                            <Setter Property="ItemsSource" Value="{Binding AvailableOperators}"/>
                            <Setter Property="SelectedItem" Value="{Binding Operator, UpdateSourceTrigger=PropertyChanged}"/>
                            <Setter Property="Margin" Value="0"/>
                        </Style>
                    </DataGridComboBoxColumn.ElementStyle>
                    <DataGridComboBoxColumn.EditingElementStyle>
                        <Style TargetType="ComboBox" BasedOn="{StaticResource BaseComboBoxStyle}">
                            <Setter Property="ItemsSource" Value="{Binding AvailableOperators}"/>
                            <Setter Property="SelectedItem" Value="{Binding Operator, UpdateSourceTrigger=PropertyChanged}"/>
                            <Setter Property="Margin" Value="0"/>
                        </Style>
                    </DataGridComboBoxColumn.EditingElementStyle>
                </DataGridComboBoxColumn>
                <DataGridTextColumn Header="值" 
                                    Binding="{Binding Value, UpdateSourceTrigger=PropertyChanged}" 
                                    Width="150"/>
                <DataGridTextColumn Header="描述" 
                                    Binding="{Binding Description, UpdateSourceTrigger=PropertyChanged}" 
                                    Width="*"/>
                <DataGridTemplateColumn Header="操作" Width="80">
                    <DataGridTemplateColumn.CellTemplate>
                        <DataTemplate>
                            <Button Content="删除" 
                                    Command="{Binding DataContext.RemoveConditionCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                    CommandParameter="{Binding}"
                                    Style="{StaticResource SecondaryButtonStyle}"
                                    Padding="5,2"
                                    FontSize="10"
                                    Margin="2"/>
                        </DataTemplate>
                    </DataGridTemplateColumn.CellTemplate>
                </DataGridTemplateColumn>
            </DataGrid.Columns>
        </DataGrid>

        <!-- 无条件时的提示 -->
        <Border BorderBrush="#EEEEEE" BorderThickness="1" Padding="15" Background="#FAFAFA"
                Visibility="{Binding HasConditions, Converter={StaticResource InverseBooleanToVisibilityConverter}}">
            <StackPanel HorizontalAlignment="Center">
                <TextBlock Text="暂无条件" 
                           FontSize="12" 
                           Foreground="#999999" 
                           HorizontalAlignment="Center"/>
                <TextBlock Text="点击&quot;添加条件&quot;按钮添加判断条件"
                           FontSize="11" 
                           Foreground="#666666" 
                           HorizontalAlignment="Center"
                           Margin="0,2,0,8"/>
                <Button Content="添加条件" 
                        Command="{Binding AddConditionCommand}"
                        Style="{StaticResource PrimaryButtonStyle}"
                        Padding="10,5"
                        FontSize="11"/>
            </StackPanel>
        </Border>

        <!-- 条件配置说明 -->
        <Border BorderBrush="#FFF3E0" BorderThickness="1" Padding="10" Background="#FFF3E0" 
                Margin="0,10,0,0"
                Visibility="{Binding HasConditions, Converter={StaticResource BooleanToVisibilityConverter}}">
            <StackPanel>
                <TextBlock Text="📋 条件配置说明" FontWeight="SemiBold" Foreground="{StaticResource WarningBrush}" FontSize="11" Margin="0,0,0,3"/>
                <TextBlock TextWrapping="Wrap" Foreground="#666666" FontSize="10">
                    <Run Text="• 字段名：数据中要检查的字段名称"/>
                    <LineBreak/>
                    <Run Text="• 数据类型：string(文本)、number(数字)、datetime(日期时间)"/>
                    <LineBreak/>
                    <Run Text="• 操作符：根据数据类型提供不同的比较操作"/>
                    <LineBreak/>
                    <Run Text="• 值：用于比较的具体值，In/NotIn操作符支持用|分隔多个值"/>
                </TextBlock>
            </StackPanel>
        </Border>
    </StackPanel>
</UserControl>
