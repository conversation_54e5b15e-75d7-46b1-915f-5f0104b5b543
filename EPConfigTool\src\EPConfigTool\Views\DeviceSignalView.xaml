<UserControl x:Class="EPConfigTool.Views.DeviceSignalView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:EPConfigTool.Views"
             mc:Ignorable="d" 
             d:DesignHeight="450" d:DesignWidth="800">
    
    <ScrollViewer VerticalScrollBarVisibility="Auto" Padding="15">
        <StackPanel MaxWidth="600" HorizontalAlignment="Left">
            <TextBlock Text="设备信号配置" Style="{StaticResource HeaderTextBlockStyle}"/>
            
            <TextBlock Text="MQTT 主题列表 *" Style="{StaticResource LabelTextBlockStyle}"/>
            <TextBox Text="{Binding TopicsText, UpdateSourceTrigger=PropertyChanged}" 
                     Style="{StaticResource BaseTextBoxStyle}"
                     Height="60"
                     TextWrapping="Wrap"
                     AcceptsReturn="True"
                     VerticalScrollBarVisibility="Auto"
                     ToolTip="每行一个主题，或用逗号分隔"/>
            
            <TextBlock Text="触发字段名称 *" Style="{StaticResource LabelTextBlockStyle}"/>
            <TextBox Text="{Binding TriggerField, UpdateSourceTrigger=PropertyChanged}" 
                     Style="{StaticResource BaseTextBoxStyle}"
                     ToolTip="设备信号中的触发字段名称"/>
            
            <TextBlock Text="触发值映射" Style="{StaticResource LabelTextBlockStyle}"/>
            <TextBox Text="{Binding TriggerValuesText, UpdateSourceTrigger=PropertyChanged}" 
                     Style="{StaticResource BaseTextBoxStyle}"
                     Height="80"
                     TextWrapping="Wrap"
                     AcceptsReturn="True"
                     VerticalScrollBarVisibility="Auto"
                     FontFamily="{StaticResource MonospaceFont}"
                     ToolTip="格式：&quot;key&quot;: &quot;value&quot;，每行一个映射"/>
            
            <TextBlock Text="保持超时时间（秒）" Style="{StaticResource LabelTextBlockStyle}"/>
            <TextBox Text="{Binding HoldingTimeoutSec, UpdateSourceTrigger=PropertyChanged}" 
                     Style="{StaticResource BaseTextBoxStyle}"
                     ToolTip="设备信号保持超时时间，范围：1-3600秒"/>
            
            <TextBlock Text="示例配置：" Style="{StaticResource LabelTextBlockStyle}" Margin="0,20,0,5"/>
            <Border BorderBrush="#EEEEEE" BorderThickness="1" Padding="10" Background="#FAFAFA">
                <TextBlock FontFamily="{StaticResource MonospaceFont}" FontSize="11" Foreground="#666666">
                    <Run Text="MQTT 主题："/>
                    <LineBreak/>
                    <Run Text="device/BDN861290073715232/event"/>
                    <LineBreak/>
                    <LineBreak/>
                    <Run Text="触发字段：I2"/>
                    <LineBreak/>
                    <LineBreak/>
                    <Run Text="触发值映射："/>
                    <LineBreak/>
                    <Run Text="&quot;true&quot;: &quot;0&quot;"/>
                    <LineBreak/>
                    <Run Text="&quot;false&quot;: &quot;1&quot;"/>
                </TextBlock>
            </Border>
        </StackPanel>
    </ScrollViewer>
</UserControl>
