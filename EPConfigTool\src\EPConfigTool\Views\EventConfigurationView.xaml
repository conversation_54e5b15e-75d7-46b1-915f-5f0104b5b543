<UserControl x:Class="EPConfigTool.Views.EventConfigurationView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:EPConfigTool.Views"
             mc:Ignorable="d" 
             d:DesignHeight="600" d:DesignWidth="800">
    
    <TabControl Style="{StaticResource BaseTabControlStyle}">
        <!-- 基本信息标签页 -->
        <TabItem Header="基本信息">
            <ScrollViewer VerticalScrollBarVisibility="Auto" Padding="15">
                <StackPanel MaxWidth="600" HorizontalAlignment="Left">
                    <TextBlock Text="事件基本信息" Style="{StaticResource HeaderTextBlockStyle}"/>
                    
                    <TextBlock Text="事件ID *" Style="{StaticResource LabelTextBlockStyle}"/>
                    <TextBox Text="{Binding EventId, UpdateSourceTrigger=PropertyChanged}"
                             Style="{StaticResource BaseTextBoxStyle}"
                             ToolTip="{Binding EventIdToolTip}"
                             GotFocus="OnConfigItemGotFocus"
                             Tag="EventId"/>
                    
                    <TextBlock Text="事件名称 *" Style="{StaticResource LabelTextBlockStyle}"/>
                    <TextBox Text="{Binding EventName, UpdateSourceTrigger=PropertyChanged}"
                             Style="{StaticResource BaseTextBoxStyle}"
                             ToolTip="{Binding EventNameToolTip}"
                             GotFocus="OnConfigItemGotFocus"
                             Tag="EventName"/>
                    
                    <TextBlock Text="评估策略 *" Style="{StaticResource LabelTextBlockStyle}"/>
                    <ComboBox ItemsSource="{x:Static local:EventConfigurationView.EvaluationStrategyOptions}"
                              SelectedItem="{Binding EvaluationStrategy, UpdateSourceTrigger=PropertyChanged}"
                              Style="{StaticResource BaseComboBoxStyle}"
                              ToolTip="{Binding EvaluationStrategyToolTip}"
                              GotFocus="OnConfigItemGotFocus"
                              Tag="EvaluationStrategy"/>
                    
                    <TextBlock Text="优先级" Style="{StaticResource LabelTextBlockStyle}"/>
                    <ComboBox ItemsSource="{x:Static local:EventConfigurationView.PriorityOptions}"
                              SelectedItem="{Binding Priority, UpdateSourceTrigger=PropertyChanged}"
                              Style="{StaticResource BaseComboBoxStyle}"
                              ToolTip="{Binding PriorityToolTip}"
                              GotFocus="OnConfigItemGotFocus"
                              Tag="Priority"/>
                    
                    <TextBlock Text="小区ID" Style="{StaticResource LabelTextBlockStyle}"/>
                    <TextBox Text="{Binding CommId, UpdateSourceTrigger=PropertyChanged}"
                             Style="{StaticResource BaseTextBoxStyle}"
                             ToolTip="{Binding CommIdToolTip}"
                             GotFocus="OnConfigItemGotFocus"
                             Tag="CommId"/>
                    
                    <TextBlock Text="位置ID" Style="{StaticResource LabelTextBlockStyle}"/>
                    <TextBox Text="{Binding PositionId, UpdateSourceTrigger=PropertyChanged}"
                             Style="{StaticResource BaseTextBoxStyle}"
                             ToolTip="{Binding PositionIdToolTip}"
                             GotFocus="OnConfigItemGotFocus"
                             Tag="PositionId"/>
                    
                    <TextBlock Text="图片裁剪坐标" Style="{StaticResource LabelTextBlockStyle}"/>
                    <TextBox Text="{Binding ImageCropCoordinates, UpdateSourceTrigger=PropertyChanged}" 
                             Style="{StaticResource BaseTextBoxStyle}"
                             ToolTip="格式：x1,y1,x2,y2"/>
                    
                    <Separator Margin="0,20"/>
                    
                    <TextBlock Text="高级设置" Style="{StaticResource HeaderTextBlockStyle}"/>
                    
                    <TextBlock Text="告警静默期（秒）" Style="{StaticResource LabelTextBlockStyle}"/>
                    <TextBox Text="{Binding AlarmGracePeriodSeconds, UpdateSourceTrigger=PropertyChanged}" 
                             Style="{StaticResource BaseTextBoxStyle}"
                             ToolTip="告警静默期时长，范围：0-60秒"/>
                    
                    <CheckBox Content="启用告警撤销功能" 
                              IsChecked="{Binding EnableAlarmCancellation, UpdateSourceTrigger=PropertyChanged}"
                              Margin="0,5,0,10"
                              ToolTip="是否启用告警撤销功能"/>
                    
                    <TextBlock Text="时间窗口关联策略" Style="{StaticResource LabelTextBlockStyle}"/>
                    <ComboBox ItemsSource="{x:Static local:EventConfigurationView.CorrelationTimeWindowOptions}"
                              SelectedItem="{Binding CorrelationTimeWindow, UpdateSourceTrigger=PropertyChanged}"
                              Style="{StaticResource BaseComboBoxStyle}"
                              ToolTip="时间窗口关联策略"/>
                    
                    <TextBlock Text="自定义时间窗口（分钟）" 
                               Style="{StaticResource LabelTextBlockStyle}"
                               Visibility="{Binding IsCustomTimeWindowEnabled, Converter={StaticResource BooleanToVisibilityConverter}}"/>
                    <TextBox Text="{Binding CustomTimeWindowMinutes, UpdateSourceTrigger=PropertyChanged}" 
                             Style="{StaticResource BaseTextBoxStyle}"
                             Visibility="{Binding IsCustomTimeWindowEnabled, Converter={StaticResource BooleanToVisibilityConverter}}"
                             ToolTip="自定义时间窗口大小，范围：1-1440分钟"/>
                </StackPanel>
            </ScrollViewer>
        </TabItem>
        
        <!-- 设备信号标签页 -->
        <TabItem Header="设备信号">
            <local:DeviceSignalView DataContext="{Binding DeviceSignal}"/>
        </TabItem>
        
        <!-- 排除规则标签页 -->
        <TabItem Header="排除规则">
            <local:ExclusionRulesView DataContext="{Binding RuleConfiguration}"/>
        </TabItem>
        
        <!-- 业务规则标签页 -->
        <TabItem Header="业务规则">
            <local:BusinessRulesView DataContext="{Binding RuleConfiguration}"/>
        </TabItem>
        
        <!-- AI规则标签页 -->
        <TabItem Header="AI规则">
            <local:AIResultRulesView DataContext="{Binding RuleConfiguration}"/>
        </TabItem>
        
        <!-- 告警配置标签页 -->
        <TabItem Header="告警配置">
            <local:AlarmConfigurationView DataContext="{Binding RuleConfiguration.AlarmConfig}"/>
        </TabItem>
    </TabControl>
</UserControl>
