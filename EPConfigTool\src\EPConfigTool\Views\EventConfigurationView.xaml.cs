using System.Windows;
using System.Windows.Controls;
using EPConfigTool.ViewModels;

namespace EPConfigTool.Views;

/// <summary>
/// EventConfigurationView.xaml 的交互逻辑
/// </summary>
public partial class EventConfigurationView : UserControl
{
    public EventConfigurationView()
    {
        InitializeComponent();
    }

    /// <summary>
    /// 评估策略选项
    /// </summary>
    public static readonly string[] EvaluationStrategyOptions = { "AI", "BusinessOnly", "AIAndBusiness" };

    /// <summary>
    /// 优先级选项
    /// </summary>
    public static readonly string[] PriorityOptions = { "P1", "P2", "P3", "P4", "P5" };

    /// <summary>
    /// 时间窗口关联策略选项
    /// </summary>
    public static readonly string[] CorrelationTimeWindowOptions = { "minute", "hour", "custom" };

    /// <summary>
    /// 配置项获得焦点时更新帮助信息
    /// </summary>
    /// <param name="sender">事件发送者</param>
    /// <param name="e">事件参数</param>
    private void OnConfigItemGotFocus(object sender, RoutedEventArgs e)
    {
        if (sender is FrameworkElement element &&
            element.Tag is string configKey &&
            DataContext is IHelpAware helpAware)
        {
            helpAware.UpdateHelpInfo(configKey);
        }
    }
}
