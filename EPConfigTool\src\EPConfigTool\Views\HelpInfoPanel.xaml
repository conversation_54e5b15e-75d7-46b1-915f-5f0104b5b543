<UserControl x:Class="EPConfigTool.Views.HelpInfoPanel"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             mc:Ignorable="d" 
             d:DesignHeight="300" d:DesignWidth="400">
    
    <UserControl.Resources>
        <!-- 帮助信息面板样式 -->
        <Style x:Key="HelpPanelStyle" TargetType="Border">
            <Setter Property="Background" Value="#F8F9FA"/>
            <Setter Property="BorderBrush" Value="#E9ECEF"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="CornerRadius" Value="4"/>
            <Setter Property="Padding" Value="12"/>
            <Setter Property="Margin" Value="5"/>
        </Style>
        
        <Style x:Key="HelpTitleStyle" TargetType="TextBlock">
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Foreground" Value="#495057"/>
            <Setter Property="Margin" Value="0,0,0,8"/>
        </Style>
        
        <Style x:Key="HelpContentStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="Foreground" Value="#6C757D"/>
            <Setter Property="TextWrapping" Value="Wrap"/>
            <Setter Property="LineHeight" Value="18"/>
            <Setter Property="Margin" Value="0,0,0,6"/>
        </Style>
        
        <Style x:Key="HelpSectionStyle" TargetType="TextBlock">
            <Setter Property="FontWeight" Value="Medium"/>
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="Foreground" Value="#343A40"/>
            <Setter Property="Margin" Value="0,8,0,4"/>
        </Style>
    </UserControl.Resources>
    
    <ScrollViewer VerticalScrollBarVisibility="Auto" HorizontalScrollBarVisibility="Disabled">
        <Border Style="{StaticResource HelpPanelStyle}">
            <StackPanel>
                <!-- 标题区域 -->
                <Grid Margin="0,0,0,12">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>
                    
                    <TextBlock Grid.Column="0" 
                               Text="{Binding DisplayName}" 
                               Style="{StaticResource HelpTitleStyle}"/>
                    
                    <TextBlock Grid.Column="1" 
                               Text="{Binding EnglishName}" 
                               FontSize="11" 
                               Foreground="#ADB5BD"
                               VerticalAlignment="Top"/>
                </Grid>
                
                <!-- 必需标识 -->
                <StackPanel Orientation="Horizontal" Margin="0,0,0,8" 
                            Visibility="{Binding IsRequired, Converter={StaticResource BooleanToVisibilityConverter}}">
                    <Border Background="#DC3545" CornerRadius="2" Padding="4,2">
                        <TextBlock Text="必需" Foreground="White" FontSize="10" FontWeight="Bold"/>
                    </Border>
                    <Border Background="#6C757D" CornerRadius="2" Padding="4,2" Margin="4,0,0,0">
                        <TextBlock Text="{Binding Category}" Foreground="White" FontSize="10"/>
                    </Border>
                </StackPanel>
                
                <!-- 功能描述 -->
                <TextBlock Text="功能描述" Style="{StaticResource HelpSectionStyle}"/>
                <TextBlock Text="{Binding FunctionDescription}" Style="{StaticResource HelpContentStyle}"/>
                
                <!-- 有效值范围 -->
                <TextBlock Text="有效值范围" Style="{StaticResource HelpSectionStyle}"/>
                <TextBlock Text="{Binding ValidValues}" Style="{StaticResource HelpContentStyle}"/>
                
                <!-- 示例值 -->
                <TextBlock Text="示例值" Style="{StaticResource HelpSectionStyle}"/>
                <Border Background="#F8F9FA" BorderBrush="#DEE2E6" BorderThickness="1" 
                        CornerRadius="3" Padding="8" Margin="0,0,0,6">
                    <TextBlock Text="{Binding ExampleValue}" 
                               FontFamily="Consolas" 
                               FontSize="11" 
                               Foreground="#495057"
                               TextWrapping="Wrap"/>
                </Border>
                
                <!-- 使用场景 -->
                <TextBlock Text="使用场景" Style="{StaticResource HelpSectionStyle}"/>
                <TextBlock Text="{Binding UsageScenario}" Style="{StaticResource HelpContentStyle}"/>
                
                <!-- 注意事项 -->
                <TextBlock Text="注意事项" Style="{StaticResource HelpSectionStyle}"/>
                <Border Background="#FFF3CD" BorderBrush="#FFEAA7" BorderThickness="1" 
                        CornerRadius="3" Padding="8" Margin="0,0,0,6">
                    <TextBlock Text="{Binding Notes}" 
                               FontSize="11" 
                               Foreground="#856404"
                               TextWrapping="Wrap"/>
                </Border>
                
                <!-- 相关配置项 -->
                <TextBlock Text="相关配置项" Style="{StaticResource HelpSectionStyle}"/>
                <TextBlock Text="{Binding RelatedConfigs}" Style="{StaticResource HelpContentStyle}"/>

                <!-- 常见错误 -->
                <TextBlock Text="常见错误" Style="{StaticResource HelpSectionStyle}"/>
                <Border Background="#F8D7DA" BorderBrush="#F5C6CB" BorderThickness="1"
                        CornerRadius="3" Padding="8" Margin="0,0,0,6">
                    <TextBlock Text="{Binding CommonErrors}"
                               FontSize="11"
                               Foreground="#721C24"
                               TextWrapping="Wrap"/>
                </Border>

                <!-- 最佳实践 -->
                <TextBlock Text="最佳实践" Style="{StaticResource HelpSectionStyle}"/>
                <Border Background="#D1ECF1" BorderBrush="#BEE5EB" BorderThickness="1"
                        CornerRadius="3" Padding="8" Margin="0,0,0,6">
                    <TextBlock Text="{Binding BestPractices}"
                               FontSize="11"
                               Foreground="#0C5460"
                               TextWrapping="Wrap"/>
                </Border>
            </StackPanel>
        </Border>
    </ScrollViewer>
</UserControl>
