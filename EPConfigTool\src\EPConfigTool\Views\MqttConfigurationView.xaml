<UserControl x:Class="EPConfigTool.Views.MqttConfigurationView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             mc:Ignorable="d" 
             d:DesignHeight="500" d:DesignWidth="600">

    <UserControl.Resources>
        <Style TargetType="GroupBox">
            <Setter Property="Margin" Value="0,0,0,15" />
            <Setter Property="Padding" Value="10" />
            <Setter Property="FontWeight" Value="SemiBold" />
        </Style>
        
        <Style TargetType="Label">
            <Setter Property="FontWeight" Value="Normal" />
            <Setter Property="VerticalAlignment" Value="Center" />
            <Setter Property="Margin" Value="0,0,10,0" />
            <Setter Property="MinWidth" Value="120" />
        </Style>
        
        <Style TargetType="TextBox">
            <Setter Property="Margin" Value="0,2" />
            <Setter Property="Padding" Value="5" />
            <Setter Property="VerticalAlignment" Value="Center" />
        </Style>
        
        <Style TargetType="ComboBox">
            <Setter Property="Margin" Value="0,2" />
            <Setter Property="Padding" Value="5" />
            <Setter Property="VerticalAlignment" Value="Center" />
        </Style>
    </UserControl.Resources>

    <ScrollViewer VerticalScrollBarVisibility="Auto">
        <StackPanel Margin="10">
            
            <!-- 连接配置 -->
            <GroupBox Header="📡 MQTT 服务器连接">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <!-- 服务器地址 -->
                    <Label Grid.Row="0" Grid.Column="0" Content="服务器地址:"/>
                    <TextBox Grid.Row="0" Grid.Column="1" 
                            Text="{Binding BrokerHost, UpdateSourceTrigger=PropertyChanged}"
                            ToolTip="MQTT 服务器的主机名或 IP 地址"/>
                    <Button Grid.Row="0" Grid.Column="2" Content="🔗 测试连接" 
                           Margin="10,0,0,0" Padding="8,4"
                           Click="TestConnection_Click"/>

                    <!-- 端口 -->
                    <Label Grid.Row="1" Grid.Column="0" Content="端口:"/>
                    <TextBox Grid.Row="1" Grid.Column="1" 
                            Text="{Binding BrokerPort, UpdateSourceTrigger=PropertyChanged}"
                            ToolTip="MQTT 服务器端口，通常为 1883（非加密）或 8883（SSL/TLS）"/>

                    <!-- 客户端ID -->
                    <Label Grid.Row="2" Grid.Column="0" Content="客户端ID:"/>
                    <TextBox Grid.Row="2" Grid.Column="1" 
                            Text="{Binding ClientId, UpdateSourceTrigger=PropertyChanged}"
                            ToolTip="MQTT 客户端标识符，必须在服务器上唯一"/>
                    <Button Grid.Row="2" Grid.Column="2" Content="🔄 自动生成" 
                           Margin="10,0,0,0" Padding="8,4"
                           Click="GenerateClientId_Click"/>

                    <!-- 用户名 -->
                    <Label Grid.Row="3" Grid.Column="0" Content="用户名:"/>
                    <TextBox Grid.Row="3" Grid.Column="1" 
                            Text="{Binding Username, UpdateSourceTrigger=PropertyChanged}"
                            ToolTip="MQTT 连接用户名（如果服务器需要身份验证）"/>

                    <!-- 密码 -->
                    <Label Grid.Row="4" Grid.Column="0" Content="密码:"/>
                    <PasswordBox Grid.Row="4" Grid.Column="1" x:Name="PasswordBox"
                                PasswordChanged="PasswordBox_PasswordChanged"
                                ToolTip="MQTT 连接密码"/>
                </Grid>
            </GroupBox>

            <!-- 连接参数 -->
            <GroupBox Header="⚙️ 连接参数">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <!-- 保活间隔 -->
                    <Label Grid.Row="0" Grid.Column="0" Content="保活间隔(秒):"/>
                    <TextBox Grid.Row="0" Grid.Column="1" 
                            Text="{Binding KeepAliveInterval, UpdateSourceTrigger=PropertyChanged}"
                            ToolTip="客户端向服务器发送保活消息的间隔时间（10-3600秒）"/>

                    <!-- 重连延迟 -->
                    <Label Grid.Row="0" Grid.Column="2" Content="重连延迟(秒):" Margin="20,0,10,0"/>
                    <TextBox Grid.Row="0" Grid.Column="3" 
                            Text="{Binding ReconnectDelay, UpdateSourceTrigger=PropertyChanged}"
                            ToolTip="连接断开后尝试重新连接的延迟时间（1-60秒）"/>

                    <!-- 服务质量等级 -->
                    <Label Grid.Row="1" Grid.Column="0" Content="QoS 等级:"/>
                    <ComboBox Grid.Row="1" Grid.Column="1" 
                             SelectedValue="{Binding QualityOfServiceLevel}"
                             SelectedValuePath="Tag"
                             ToolTip="MQTT 消息服务质量等级">
                        <ComboBoxItem Content="0 - 最多一次" Tag="0"/>
                        <ComboBoxItem Content="1 - 至少一次" Tag="1"/>
                        <ComboBoxItem Content="2 - 恰好一次" Tag="2"/>
                    </ComboBox>
                </Grid>
            </GroupBox>

            <!-- 快速配置 -->
            <GroupBox Header="🚀 快速配置">
                <StackPanel Orientation="Horizontal">
                    <Button Content="📋 默认配置" Padding="10,5" Margin="0,0,10,0"
                           Click="SetDefaultConfig_Click"
                           ToolTip="恢复为默认的 MQTT 配置"/>
                    <Button Content="🏭 生产环境" Padding="10,5" Margin="0,0,10,0"
                           Click="SetProductionConfig_Click"
                           ToolTip="设置为生产环境推荐配置"/>
                    <Button Content="🧪 测试环境" Padding="10,5"
                           Click="SetTestConfig_Click"
                           ToolTip="设置为测试环境配置"/>
                </StackPanel>
            </GroupBox>

            <!-- 配置验证 -->
            <GroupBox Header="✅ 配置验证">
                <StackPanel>
                    <TextBlock x:Name="ValidationResultText" 
                              Text="点击验证按钮检查配置是否正确"
                              Margin="0,0,0,10"
                              TextWrapping="Wrap"/>
                    <Button Content="🔍 验证配置" Padding="10,5" HorizontalAlignment="Left"
                           Click="ValidateConfig_Click"/>
                </StackPanel>
            </GroupBox>

        </StackPanel>
    </ScrollViewer>
</UserControl>
