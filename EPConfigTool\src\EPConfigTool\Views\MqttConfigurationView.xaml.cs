using EPConfigTool.ViewModels;
using System.Windows;
using System.Windows.Controls;

namespace EPConfigTool.Views;

/// <summary>
/// MQTT 配置视图
/// 提供编辑 MQTT 连接配置的界面
/// </summary>
public partial class MqttConfigurationView : UserControl
{
    public MqttConfigurationView()
    {
        InitializeComponent();
        DataContextChanged += OnDataContextChanged;
    }

    private void OnDataContextChanged(object sender, DependencyPropertyChangedEventArgs e)
    {
        if (e.NewValue is MqttConfigurationViewModel viewModel)
        {
            // 同步密码框
            PasswordBox.Password = viewModel.Password;
        }
    }

    private void PasswordBox_PasswordChanged(object sender, RoutedEventArgs e)
    {
        if (DataContext is MqttConfigurationViewModel viewModel)
        {
            viewModel.Password = PasswordBox.Password;
        }
    }

    private void TestConnection_Click(object sender, RoutedEventArgs e)
    {
        if (DataContext is MqttConfigurationViewModel viewModel)
        {
            try
            {
                var errors = viewModel.Validate();
                if (errors.Count == 0)
                {
                    MessageBox.Show("MQTT 配置验证通过！\n\n注意：这只是基本的配置验证，实际连接测试需要网络环境支持。", 
                        "连接测试", MessageBoxButton.OK, MessageBoxImage.Information);
                }
                else
                {
                    var message = "配置验证失败：\n\n" + string.Join("\n", errors);
                    MessageBox.Show(message, "连接测试", MessageBoxButton.OK, MessageBoxImage.Warning);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"连接测试失败：{ex.Message}", "错误", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
    }

    private void GenerateClientId_Click(object sender, RoutedEventArgs e)
    {
        if (DataContext is MqttConfigurationViewModel viewModel)
        {
            // 尝试从父级获取事件ID
            var eventId = TryGetEventIdFromParent();
            if (!string.IsNullOrEmpty(eventId))
            {
                viewModel.GenerateClientId(eventId);
            }
            else
            {
                // 使用时间戳生成唯一ID
                var timestamp = DateTime.Now.ToString("yyyyMMddHHmmss");
                viewModel.ClientId = $"EP_V4.1_{timestamp}";
            }
        }
    }

    private void SetDefaultConfig_Click(object sender, RoutedEventArgs e)
    {
        if (DataContext is MqttConfigurationViewModel viewModel)
        {
            viewModel.ResetToDefault();
            PasswordBox.Password = viewModel.Password;
            MessageBox.Show("已恢复为默认配置", "配置重置", MessageBoxButton.OK, MessageBoxImage.Information);
        }
    }

    private void SetProductionConfig_Click(object sender, RoutedEventArgs e)
    {
        if (DataContext is MqttConfigurationViewModel viewModel)
        {
            viewModel.BrokerHost = "mq.bangdouni.com";
            viewModel.BrokerPort = 1883;
            viewModel.Username = "bdn_event_processor";
            viewModel.Password = "Bdn@2024";
            viewModel.KeepAliveInterval = 60;
            viewModel.ReconnectDelay = 5;
            viewModel.QualityOfServiceLevel = 1;
            
            PasswordBox.Password = viewModel.Password;
            MessageBox.Show("已设置为生产环境配置", "配置设置", MessageBoxButton.OK, MessageBoxImage.Information);
        }
    }

    private void SetTestConfig_Click(object sender, RoutedEventArgs e)
    {
        if (DataContext is MqttConfigurationViewModel viewModel)
        {
            viewModel.BrokerHost = "localhost";
            viewModel.BrokerPort = 1883;
            viewModel.Username = "test_user";
            viewModel.Password = "test_password";
            viewModel.KeepAliveInterval = 30;
            viewModel.ReconnectDelay = 3;
            viewModel.QualityOfServiceLevel = 0;
            
            PasswordBox.Password = viewModel.Password;
            MessageBox.Show("已设置为测试环境配置", "配置设置", MessageBoxButton.OK, MessageBoxImage.Information);
        }
    }

    private void ValidateConfig_Click(object sender, RoutedEventArgs e)
    {
        if (DataContext is MqttConfigurationViewModel viewModel)
        {
            var errors = viewModel.Validate();
            
            if (errors.Count == 0)
            {
                ValidationResultText.Text = "✅ 配置验证通过！所有参数都符合要求。";
                ValidationResultText.Foreground = System.Windows.Media.Brushes.Green;
            }
            else
            {
                ValidationResultText.Text = "❌ 配置验证失败：\n" + string.Join("\n", errors);
                ValidationResultText.Foreground = System.Windows.Media.Brushes.Red;
            }
        }
    }

    private string? TryGetEventIdFromParent()
    {
        // 尝试从父级 UnifiedConfigurationViewModel 获取事件ID
        var parent = this.DataContext;
        while (parent != null)
        {
            if (parent is UnifiedConfigurationViewModel unifiedViewModel)
            {
                return unifiedViewModel.EventConfiguration?.EventId;
            }
            
            // 这里可以添加更多的父级查找逻辑
            break;
        }
        
        return null;
    }
}
