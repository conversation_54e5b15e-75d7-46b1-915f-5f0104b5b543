<UserControl x:Class="EPConfigTool.Views.UnifiedConfigurationView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:EPConfigTool.Views"
             mc:Ignorable="d" 
             d:DesignHeight="600" d:DesignWidth="800">
    
    <UserControl.Resources>
        <Style TargetType="TabItem">
            <Setter Property="Header" Value="{Binding Header}" />
            <Setter Property="FontWeight" Value="SemiBold" />
            <Setter Property="Padding" Value="12,8" />
        </Style>
        
        <Style TargetType="GroupBox">
            <Setter Property="Margin" Value="5" />
            <Setter Property="Padding" Value="10" />
            <Setter Property="FontWeight" Value="SemiBold" />
        </Style>
    </UserControl.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- 标题栏 -->
        <Border Grid.Row="0" Background="#f0f0f0" Padding="10,5">
            <StackPanel Orientation="Horizontal">
                <TextBlock Text="📋" FontSize="16" VerticalAlignment="Center" Margin="0,0,8,0"/>
                <TextBlock Text="统一配置编辑器" FontSize="14" FontWeight="Bold" VerticalAlignment="Center"/>
                <TextBlock Text="- 包含所有配置节的完整 YAML 配置文件" FontSize="12" 
                          Foreground="Gray" VerticalAlignment="Center" Margin="10,0,0,0"/>
            </StackPanel>
        </Border>

        <!-- 主要内容 -->
        <TabControl Grid.Row="1" Margin="5">
            
            <!-- 事件配置标签页 -->
            <TabItem Header="🎯 事件配置">
                <ScrollViewer VerticalScrollBarVisibility="Auto">
                    <local:EventConfigurationView DataContext="{Binding EventConfiguration}" 
                                                 Margin="10"/>
                </ScrollViewer>
            </TabItem>

            <!-- MQTT 配置标签页 -->
            <TabItem Header="📡 MQTT 配置">
                <ScrollViewer VerticalScrollBarVisibility="Auto">
                    <local:MqttConfigurationView DataContext="{Binding MqttConfiguration}" 
                                                Margin="10"/>
                </ScrollViewer>
            </TabItem>

            <!-- 错误处理配置标签页 -->
            <TabItem Header="⚠️ 错误处理">
                <ScrollViewer VerticalScrollBarVisibility="Auto">
                    <TextBlock Text="错误处理配置界面开发中..."
                              HorizontalAlignment="Center"
                              VerticalAlignment="Center"
                              FontSize="16"
                              Foreground="Gray"
                              Margin="10"/>
                </ScrollViewer>
            </TabItem>

            <!-- 日志配置标签页 -->
            <TabItem Header="📝 日志配置">
                <ScrollViewer VerticalScrollBarVisibility="Auto">
                    <StackPanel Margin="10">
                        <!-- 基础日志配置 -->
                        <GroupBox Header="基础日志配置">
                            <TextBlock Text="基础日志配置界面开发中..."
                                      HorizontalAlignment="Center"
                                      VerticalAlignment="Center"
                                      FontSize="14"
                                      Foreground="Gray"/>
                        </GroupBox>

                        <!-- Serilog 配置 -->
                        <GroupBox Header="Serilog 结构化日志配置" Margin="0,10,0,0">
                            <TextBlock Text="Serilog 配置界面开发中..."
                                      HorizontalAlignment="Center"
                                      VerticalAlignment="Center"
                                      FontSize="14"
                                      Foreground="Gray"/>
                        </GroupBox>
                    </StackPanel>
                </ScrollViewer>
            </TabItem>

            <!-- 配置预览标签页 -->
            <TabItem Header="👁️ 配置预览">
                <Grid Margin="10">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <!-- 预览工具栏 -->
                    <Border Grid.Row="0" Background="#f8f8f8" Padding="10,5" Margin="0,0,0,10">
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="📄" FontSize="14" VerticalAlignment="Center" Margin="0,0,5,0"/>
                            <TextBlock Text="YAML 配置预览" FontWeight="Bold" VerticalAlignment="Center"/>
                            <Button Content="🔄 刷新预览" Margin="20,0,0,0" Padding="8,4" 
                                   Click="RefreshPreview_Click"/>
                            <Button Content="📋 复制到剪贴板" Margin="10,0,0,0" Padding="8,4" 
                                   Click="CopyToClipboard_Click"/>
                        </StackPanel>
                    </Border>

                    <!-- YAML 内容预览 -->
                    <Border Grid.Row="1" BorderBrush="#ddd" BorderThickness="1">
                        <TextBox x:Name="YamlPreviewTextBox" 
                                IsReadOnly="True"
                                FontFamily="Consolas, Courier New, monospace"
                                FontSize="12"
                                Background="#fafafa"
                                VerticalScrollBarVisibility="Auto"
                                HorizontalScrollBarVisibility="Auto"
                                TextWrapping="NoWrap"
                                AcceptsReturn="True"
                                Padding="10"/>
                    </Border>

                    <!-- 预览状态栏 -->
                    <Border Grid.Row="2" Background="#f0f0f0" Padding="10,5" Margin="0,10,0,0">
                        <StackPanel Orientation="Horizontal">
                            <TextBlock x:Name="PreviewStatusText" Text="准备就绪" VerticalAlignment="Center"/>
                            <TextBlock x:Name="PreviewSizeText" Text="" Margin="20,0,0,0" 
                                      Foreground="Gray" VerticalAlignment="Center"/>
                        </StackPanel>
                    </Border>
                </Grid>
            </TabItem>

        </TabControl>
    </Grid>
</UserControl>
