using EPConfigTool.Services;
using EPConfigTool.ViewModels;
using System.Windows;
using System.Windows.Controls;

namespace EPConfigTool.Views;

/// <summary>
/// 统一配置视图
/// 提供编辑包含所有配置节的统一 YAML 配置文件的界面
/// </summary>
public partial class UnifiedConfigurationView : UserControl
{
    public UnifiedConfigurationView()
    {
        InitializeComponent();
        DataContextChanged += OnDataContextChanged;
    }

    private void OnDataContextChanged(object sender, DependencyPropertyChangedEventArgs e)
    {
        if (e.NewValue is UnifiedConfigurationViewModel viewModel)
        {
            // 初始化时刷新预览
            RefreshPreview();
        }
    }

    private void RefreshPreview_Click(object sender, RoutedEventArgs e)
    {
        RefreshPreview();
    }

    private void RefreshPreview()
    {
        try
        {
            if (DataContext is UnifiedConfigurationViewModel viewModel)
            {
                // 获取统一配置服务（这里需要通过某种方式获取，比如服务定位器或依赖注入）
                // 为了简化，我们直接创建一个实例
                var configService = new UnifiedConfigurationService(null!, null!);
                
                var unifiedConfig = viewModel.ToModel();
                var yamlContent = configService.SerializeToYamlString(unifiedConfig);
                
                YamlPreviewTextBox.Text = yamlContent;
                PreviewStatusText.Text = "预览已更新";
                PreviewSizeText.Text = $"大小: {yamlContent.Length} 字符, {yamlContent.Split('\n').Length} 行";
            }
            else
            {
                YamlPreviewTextBox.Text = "# 没有可预览的配置";
                PreviewStatusText.Text = "无配置数据";
                PreviewSizeText.Text = "";
            }
        }
        catch (Exception ex)
        {
            YamlPreviewTextBox.Text = $"# 预览生成失败\n# 错误: {ex.Message}";
            PreviewStatusText.Text = "预览失败";
            PreviewSizeText.Text = "";
        }
    }

    private void CopyToClipboard_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            if (!string.IsNullOrEmpty(YamlPreviewTextBox.Text))
            {
                Clipboard.SetText(YamlPreviewTextBox.Text);
                PreviewStatusText.Text = "已复制到剪贴板";
                
                // 2秒后恢复状态文本
                var timer = new System.Windows.Threading.DispatcherTimer
                {
                    Interval = TimeSpan.FromSeconds(2)
                };
                timer.Tick += (s, args) =>
                {
                    timer.Stop();
                    PreviewStatusText.Text = "预览已更新";
                };
                timer.Start();
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"复制到剪贴板失败: {ex.Message}", "错误", 
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }
}
