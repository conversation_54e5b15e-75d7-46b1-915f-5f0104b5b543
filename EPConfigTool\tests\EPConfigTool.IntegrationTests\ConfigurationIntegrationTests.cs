using FluentAssertions;
using Microsoft.Extensions.DependencyInjection;
using System.IO;
using System.Threading.Tasks;
using Xunit;
using EPConfigTool.Services;
using EPConfigTool.Models;
using EventProcessor.Core.Models;
using System;
using System.Collections.Generic;

namespace EPConfigTool.IntegrationTests;

/// <summary>
/// 配置文件处理的集成测试
/// </summary>
public class ConfigurationIntegrationTests : IntegrationTestBase
{
    protected override void ConfigureServices(IServiceCollection services)
    {
        base.ConfigureServices(services);
        
        // 注册EPConfigTool的服务
        services.AddSingleton<IYamlConfigurationService, YamlConfigurationService>();
        services.AddSingleton<IUnifiedConfigurationService, UnifiedConfigurationService>();
    }

    [Fact]
    public async Task LoadAndSaveConfiguration_ShouldPreserveData()
    {
        // Arrange
        var yamlContent = @"
eventId: ""EV001001""
eventName: ""Test Event Configuration""
evaluationStrategy: ""BusinessOnly""
priority: ""P1""
commId: ""COMM001""
positionId: ""POS001""
ruleConfiguration:
  businessRules: []
  exclusionRules: []
  aiResultRules: []
  alarmConfig:
    enabled: true
    threshold: 0.8
";

        var configFilePath = await CreateTestConfigFileAsync("test_config.yaml", yamlContent);
        var yamlService = Services.GetRequiredService<IYamlConfigurationService>();
        var unifiedService = Services.GetRequiredService<IUnifiedConfigurationService>();

        // Act - 加载配置
        var config = await yamlService.LoadFromYamlFileAsync(configFilePath);
        config.Should().NotBeNull();

        // 验证加载的数据 - 需要根据实际的EventConfiguration结构调整
        // 注意：EventConfiguration可能不包含MqttConfiguration等，需要根据实际结构调整
        config.Should().NotBeNull();
        config.EventId.Should().NotBeNullOrEmpty();
        config.EventName.Should().NotBeNullOrEmpty();

        // Act - 创建修改后的配置（因为EventConfiguration是record类型，需要使用with表达式）
        var modifiedConfig = config with 
        {
            EventName = "Modified Event Name",
            Priority = "P2"
        };

        // Act - 保存配置
        var saveFilePath = Path.Combine(TempDirectory, "modified_config.yaml");
        await yamlService.SaveToYamlFileAsync(saveFilePath, modifiedConfig);

        // Act - 重新加载保存的配置
        var reloadedConfig = await yamlService.LoadFromYamlFileAsync(saveFilePath);
        reloadedConfig.Should().NotBeNull();

        // Assert - 验证修改后的数据
        reloadedConfig.EventName.Should().Be("Modified Event Name");
        reloadedConfig.Priority.Should().Be("P2");
        reloadedConfig.EventId.Should().Be(modifiedConfig.EventId); // 未修改的值应保持不变
    }

    [Fact]
    public async Task UnifiedConfigurationService_ShouldHandleCompleteWorkflow()
    {
        // Arrange
        var unifiedService = Services.GetRequiredService<IUnifiedConfigurationService>();
        var testConfigPath = Path.Combine(TestDataDirectory, "unified_test.yaml");

        // 创建初始配置
        var initialConfig = new UnifiedConfiguration
        {
            EventProcessor = new EventConfiguration
            {
                EventId = "EV001001",
                EventName = "Test Event",
                EvaluationStrategy = "BusinessOnly",
                Priority = "P1",
                CommId = "COMM001",
                PositionId = "POS001",
                RuleConfiguration = new RuleConfiguration
                {
                    BusinessRules = new BusinessRuleGroup[0]
                }
            },
            Mqtt = new MqttConfiguration
            {
                BrokerHost = "test.broker.com",
                BrokerPort = 1883,
                Username = "testuser",
                Password = "testpass",
                ClientId = "test_client",
                KeepAliveInterval = 60
            },
            Logging = new LoggingConfiguration
            {
                LogLevel = new LogLevelConfiguration
                {
                    Default = "Warning",
                    Microsoft = "Warning",
                    EventProcessor = "Debug"
                }
            },
            ErrorHandling = new ErrorHandlingConfiguration
            {
                ToleranceLevel = "Normal",
                RetryPolicy = new RetryPolicyConfiguration
                {
                    MaxRetries = 5,
                    RetryDelay = 2000,
                    UseExponentialBackoff = true
                },
                FallbackStrategy = new FallbackStrategyConfiguration
                {
                    OnRuleFailure = "ContinueProcessing",
                    OnAIFailure = "UseBusinessOnly"
                }
            }
        };

        // Act & Assert - 保存配置
        await unifiedService.SaveToYamlFileAsync(testConfigPath, initialConfig);
        File.Exists(testConfigPath).Should().BeTrue();

        // Act & Assert - 加载配置
        var loadedConfig = await unifiedService.LoadFromYamlFileAsync(testConfigPath);
        loadedConfig.Should().NotBeNull();
        loadedConfig.Mqtt.BrokerHost.Should().Be("test.broker.com");
        loadedConfig.Logging?.LogLevel?.Default.Should().Be("Warning");
        loadedConfig.ErrorHandling.ToleranceLevel.Should().Be("Normal");

        // Act & Assert - 验证配置
        var validationResult = await unifiedService.ValidateYamlFileAsync(testConfigPath);
        validationResult.IsValid.Should().BeTrue();
    }

    [Fact]
    public async Task InvalidConfigurationFile_ShouldReturnError()
    {
        // Arrange
        var invalidYaml = @"
invalid_yaml_content:
  - item1
    - item2  # 错误的缩进
  missing_colon
    value
";

        var invalidConfigPath = await CreateTestConfigFileAsync("invalid_config.yaml", invalidYaml);
        var yamlService = Services.GetRequiredService<IYamlConfigurationService>();

        // Act & Assert
        var act = async () => await yamlService.LoadFromYamlFileAsync(invalidConfigPath);
        
        // 应该抛出异常，因为YAML格式无效
        await act.Should().ThrowAsync<InvalidDataException>();
    }
}