using System;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using Xunit;
using FluentAssertions;
using Microsoft.Extensions.Logging;
using YamlDotNet.Core;
using EPConfigTool.Tests.TestData;
using EPConfigTool.Tests.TestData.Models;

namespace EPConfigTool.IntegrationTests
{
    /// <summary>
    /// 配置文件加载和保存的端到端集成测试
    /// </summary>
    public class ConfigurationLoadSaveTests : IDisposable
    {
        private readonly TestDataManager _testDataManager;
        private readonly ILogger<ConfigurationLoadSaveTests> _logger;
        private readonly List<string> _tempFilesToCleanup;

        public ConfigurationLoadSaveTests()
        {
            var loggerFactory = LoggerFactory.Create(builder => builder.AddConsole());
            _logger = loggerFactory.CreateLogger<ConfigurationLoadSaveTests>();
            _testDataManager = new TestDataManager(loggerFactory.CreateLogger<TestDataManager>());
            _tempFilesToCleanup = new List<string>();
        }

        [Fact]
        public async Task LoadUnifiedConfig_ValidFile_ShouldSucceed()
        {
            // Arrange
            const string configFileName = "unified-config-sample.yaml";

            // Act
            var config = await _testDataManager.DeserializeConfigAsync<TestUnifiedConfig>(configFileName);

            // Assert
            config.Should().NotBeNull();
            config.Version.Should().Be("4.1");
            config.Metadata.Should().NotBeNull();
            config.Metadata!.Name.Should().Be("测试配置");
            config.Metadata.Description.Should().Be("用于集成测试的示例配置");
            config.Metadata.Author.Should().Be("EPConfigTool Test Suite");

            // 验证MQTT配置
            config.Mqtt.Should().NotBeNull();
            config.Mqtt!.Broker.Should().NotBeNull();
            config.Mqtt.Broker!.Host.Should().Be("localhost");
            config.Mqtt.Broker.Port.Should().Be(1883);
            config.Mqtt.Broker.Username.Should().Be("test_user");
            config.Mqtt.Broker.Password.Should().Be("test_password");

            // 验证事件配置
            config.Events.Should().NotBeNull().And.HaveCount(2);
            var firstEvent = config.Events[0];
            firstEvent.Id.Should().Be("EV001");
            firstEvent.Name.Should().Be("车辆进入测试");
            firstEvent.Conditions.Should().HaveCount(2);
            firstEvent.Actions.Should().HaveCount(2);

            // 验证设备配置
            config.Devices.Should().NotBeNull().And.HaveCount(2);
            var firstDevice = config.Devices[0];
            firstDevice.Id.Should().Be("DEV001");
            firstDevice.Name.Should().Be("入口摄像头1");
            firstDevice.Type.Should().Be("camera");
            firstDevice.Enabled.Should().BeTrue();

            // 验证告警配置
            config.Alarms.Should().NotBeNull();
            config.Alarms!.Levels.Should().ContainKey("info");
            config.Alarms.Levels.Should().ContainKey("warning");
            config.Alarms.Levels.Should().ContainKey("error");
            config.Alarms.Retention.Should().NotBeNull();
            config.Alarms.Retention!.Days.Should().Be(30);
            config.Alarms.Retention.MaxCount.Should().Be(10000);
        }

        [Fact]
        public async Task LoadEventConfig_ValidFile_ShouldSucceed()
        {
            // Arrange
            const string configFileName = "event-config-sample.yaml";

            // Act
            var config = await _testDataManager.DeserializeConfigAsync<TestEventConfig>(configFileName);

            // Assert
            config.Should().NotBeNull();
            config.Version.Should().Be("4.1");
            config.Metadata.Should().NotBeNull();
            config.Metadata!.Name.Should().Be("事件配置测试");

            // 验证事件定义
            config.Events.Should().NotBeNull().And.HaveCount(2);
            config.Events.Should().ContainKey("EV001");
            config.Events.Should().ContainKey("EV002");

            var ev001 = config.Events["EV001"];
            ev001.Name.Should().Be("车辆进入事件");
            ev001.TriggerConditions.Should().HaveCount(2);
            ev001.ProcessingRules.Should().HaveCount(2);
            ev001.OutputActions.Should().HaveCount(3);

            // 验证全局设置
            config.GlobalSettings.Should().NotBeNull();
            config.GlobalSettings!.Timezone.Should().Be("Asia/Shanghai");
            config.GlobalSettings.MaxProcessingTime.Should().Be(5000);
            config.GlobalSettings.RetryAttempts.Should().Be(3);

            // 验证验证规则
            config.ValidationRules.Should().NotBeNull().And.HaveCount(2);
            config.ValidationRules.Should().ContainKey("vehicle_id_required");
            config.ValidationRules.Should().ContainKey("timestamp_valid");

            // 验证设备映射
            config.DeviceMapping.Should().NotBeNull().And.HaveCount(5);
            config.DeviceMapping.Should().ContainKey("camera_001");
            config.DeviceMapping["camera_001"].Should().Be("主入口摄像头");
        }

        [Fact]
        public async Task SaveAndLoadConfig_RoundTrip_ShouldPreserveData()
        {
            // Arrange
            var originalConfig = new TestUnifiedConfig
            {
                Version = "4.1",
                Metadata = new TestMetadata
                {
                    Name = "测试往返配置",
                    Description = "用于测试保存和加载的往返配置",
                    Created = DateTime.UtcNow.ToString("yyyy-MM-ddTHH:mm:ssZ"),
                    Author = "集成测试"
                },
                Mqtt = new TestMqttConfig
                {
                    Broker = new TestMqttBroker
                    {
                        Host = "test.mqtt.broker",
                        Port = 1883,
                        Username = "testuser",
                        Password = "testpass"
                    },
                    Topics = new TestMqttTopics
                    {
                        EventInput = "test/events/input",
                        AlarmOutput = "test/alarms/output",
                        ControlInput = "test/control/input"
                    }
                },
                Events = new List<TestEvent>
                {
                    new TestEvent
                    {
                        Id = "TEST001",
                        Name = "测试事件",
                        Description = "用于往返测试的事件",
                        Conditions = new List<TestCondition>
                        {
                            new TestCondition
                            {
                                Field = "event_type",
                                Operator = "equals",
                                Value = "test_event"
                            }
                        },
                        Actions = new List<TestAction>
                        {
                            new TestAction
                            {
                                Type = "log",
                                Level = "info",
                                Message = "测试事件触发"
                            }
                        }
                    }
                },
                Devices = new List<TestDevice>
                {
                    new TestDevice
                    {
                        Id = "TESTDEV001",
                        Name = "测试设备",
                        Type = "sensor",
                        Location = "测试位置",
                        Enabled = true
                    }
                }
            };

            // Act - 序列化为YAML
            var yamlContent = _testDataManager.SerializeToYaml(originalConfig);
            yamlContent.Should().NotBeNullOrEmpty();

            // 创建临时文件
            var tempFilePath = _testDataManager.CreateTempTestFile(yamlContent);
            _tempFilesToCleanup.Add(tempFilePath);

            // 从临时文件反序列化
            var yamlFromFile = await File.ReadAllTextAsync(tempFilePath);
            var deserializer = new YamlDotNet.Serialization.DeserializerBuilder()
                .WithNamingConvention(YamlDotNet.Serialization.NamingConventions.UnderscoredNamingConvention.Instance)
                .IgnoreUnmatchedProperties()
                .Build();
            var loadedConfig = deserializer.Deserialize<TestUnifiedConfig>(yamlFromFile);

            // Assert - 验证往返数据完整性
            loadedConfig.Should().NotBeNull();
            loadedConfig.Version.Should().Be(originalConfig.Version);
            loadedConfig.Metadata.Should().NotBeNull();
            loadedConfig.Metadata!.Name.Should().Be(originalConfig.Metadata.Name);
            loadedConfig.Metadata.Description.Should().Be(originalConfig.Metadata.Description);
            loadedConfig.Metadata.Author.Should().Be(originalConfig.Metadata.Author);

            // 验证MQTT配置
            loadedConfig.Mqtt.Should().NotBeNull();
            loadedConfig.Mqtt!.Broker.Should().NotBeNull();
            loadedConfig.Mqtt.Broker!.Host.Should().Be(originalConfig.Mqtt.Broker.Host);
            loadedConfig.Mqtt.Broker.Port.Should().Be(originalConfig.Mqtt.Broker.Port);

            // 验证事件配置
            loadedConfig.Events.Should().HaveCount(originalConfig.Events.Count);
            var loadedEvent = loadedConfig.Events[0];
            var originalEvent = originalConfig.Events[0];
            loadedEvent.Id.Should().Be(originalEvent.Id);
            loadedEvent.Name.Should().Be(originalEvent.Name);
            loadedEvent.Conditions.Should().HaveCount(originalEvent.Conditions.Count);
            loadedEvent.Actions.Should().HaveCount(originalEvent.Actions.Count);

            // 验证设备配置
            loadedConfig.Devices.Should().HaveCount(originalConfig.Devices.Count);
            var loadedDevice = loadedConfig.Devices[0];
            var originalDevice = originalConfig.Devices[0];
            loadedDevice.Id.Should().Be(originalDevice.Id);
            loadedDevice.Name.Should().Be(originalDevice.Name);
            loadedDevice.Type.Should().Be(originalDevice.Type);
            loadedDevice.Enabled.Should().Be(originalDevice.Enabled);
        }

        [Fact]
        public async Task LoadInvalidConfig_ShouldThrowException()
        {
            // Arrange
            const string invalidConfigFileName = "invalid-config-sample.yaml";

            // Act & Assert
            var exception = await Assert.ThrowsAsync<YamlException>(async () =>
            {
                await _testDataManager.DeserializeConfigAsync<TestUnifiedConfig>(invalidConfigFileName);
            });

            exception.Should().NotBeNull();
            _logger.LogInformation("预期的异常被正确抛出: {ExceptionMessage}", exception.Message);
        }

        [Fact]
        public void ValidateYamlFormat_ValidContent_ShouldReturnTrue()
        {
            // Arrange
            const string validYaml = @"
version: '4.1'
metadata:
  name: '测试配置'
  description: '有效的YAML格式'
events: []
devices: []
";

            // Act
            var isValid = _testDataManager.IsValidYaml(validYaml);

            // Assert
            isValid.Should().BeTrue();
        }

        [Fact]
        public void ValidateYamlFormat_InvalidContent_ShouldReturnFalse()
        {
            // Arrange
            const string invalidYaml = @"
version: '4.1'
metadata:
  name: '测试配置'
  description: '无效的YAML格式
  # 缺少引号结束
events: [
devices: # 缺少值
";

            // Act
            var isValid = _testDataManager.IsValidYaml(invalidYaml);

            // Assert
            isValid.Should().BeFalse();
        }

        [Fact]
        public void GetAllSampleConfigFiles_ShouldReturnExpectedFiles()
        {
            // Act
            var configFiles = _testDataManager.GetAllSampleConfigFiles();

            // Assert
            configFiles.Should().NotBeNull();
            configFiles.Should().Contain("unified-config-sample.yaml");
            configFiles.Should().Contain("event-config-sample.yaml");
            configFiles.Should().Contain("invalid-config-sample.yaml");
            configFiles.Length.Should().BeGreaterOrEqualTo(3);
        }

        public void Dispose()
        {
            // 清理临时文件
            foreach (var tempFile in _tempFilesToCleanup)
            {
                _testDataManager.CleanupTempFile(tempFile);
            }
        }
    }
}