using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using System;
using System.IO;
using System.Threading.Tasks;
using Xunit;

namespace EPConfigTool.IntegrationTests;

/// <summary>
/// 集成测试基类，提供测试环境的初始化和清理
/// </summary>
public abstract class IntegrationTestBase : IAsyncLifetime
{
    protected IHost? Host { get; private set; }
    protected IServiceProvider Services => Host?.Services ?? throw new InvalidOperationException("Host not initialized");
    protected ILogger<IntegrationTestBase> Logger => Services.GetRequiredService<ILogger<IntegrationTestBase>>();
    
    protected string TestDataDirectory { get; private set; } = string.Empty;
    protected string TempDirectory { get; private set; } = string.Empty;

    public virtual async Task InitializeAsync()
    {
        // 创建测试数据目录
        TestDataDirectory = Path.Combine(Path.GetTempPath(), "EPConfigTool_IntegrationTests", Guid.NewGuid().ToString());
        TempDirectory = Path.Combine(TestDataDirectory, "temp");
        
        Directory.CreateDirectory(TestDataDirectory);
        Directory.CreateDirectory(TempDirectory);

        // 初始化Host
        Host = CreateHost();
        await Host.StartAsync();
    }

    public virtual async Task DisposeAsync()
    {
        if (Host != null)
        {
            await Host.StopAsync();
            Host.Dispose();
        }

        // 清理测试数据
        if (Directory.Exists(TestDataDirectory))
        {
            try
            {
                Directory.Delete(TestDataDirectory, true);
            }
            catch
            {
                // 忽略清理错误
            }
        }
    }

    protected virtual IHost CreateHost()
    {
        return Microsoft.Extensions.Hosting.Host.CreateDefaultBuilder()
            .ConfigureServices(ConfigureServices)
            .ConfigureLogging(logging =>
            {
                logging.ClearProviders();
                logging.AddConsole();
                logging.SetMinimumLevel(LogLevel.Debug);
            })
            .Build();
    }

    protected virtual void ConfigureServices(IServiceCollection services)
    {
        // 子类可以重写此方法来配置特定的服务
    }

    protected string CreateTestConfigFile(string fileName, string content)
    {
        var filePath = Path.Combine(TestDataDirectory, fileName);
        File.WriteAllText(filePath, content);
        return filePath;
    }

    protected async Task<string> CreateTestConfigFileAsync(string fileName, string content)
    {
        var filePath = Path.Combine(TestDataDirectory, fileName);
        await File.WriteAllTextAsync(filePath, content);
        return filePath;
    }
}