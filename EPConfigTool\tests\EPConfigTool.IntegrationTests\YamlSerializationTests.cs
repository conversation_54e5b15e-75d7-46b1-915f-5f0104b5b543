using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Text;
using Xunit;
using FluentAssertions;
using YamlDotNet.Serialization;
using YamlDotNet.Serialization.NamingConventions;
using Microsoft.Extensions.Logging;
using EPConfigTool.Tests.TestData;
using EPConfigTool.Tests.TestData.Models;

namespace EPConfigTool.IntegrationTests
{
    /// <summary>
    /// YAML序列化和反序列化的集成测试
    /// </summary>
    public class YamlSerializationTests : IDisposable
    {
        private readonly ISerializer _yamlSerializer;
        private readonly IDeserializer _yamlDeserializer;
        private readonly TestDataManager _testDataManager;
        private readonly ILogger<YamlSerializationTests> _logger;
        private readonly List<string> _tempFilesToCleanup;

        public YamlSerializationTests()
        {
            var loggerFactory = LoggerFactory.Create(builder => builder.AddConsole());
            _logger = loggerFactory.CreateLogger<YamlSerializationTests>();
            _testDataManager = new TestDataManager(loggerFactory.CreateLogger<TestDataManager>());
            _tempFilesToCleanup = new List<string>();

            // 配置YAML序列化器
            _yamlSerializer = new SerializerBuilder()
                .WithNamingConvention(UnderscoredNamingConvention.Instance)
                .ConfigureDefaultValuesHandling(DefaultValuesHandling.Preserve)
                .Build();

            _yamlDeserializer = new DeserializerBuilder()
                .WithNamingConvention(UnderscoredNamingConvention.Instance)
                .IgnoreUnmatchedProperties()
                .Build();
        }

        [Fact]
        public void SerializeUnifiedConfig_ComplexObject_ShouldProduceValidYaml()
        {
            // Arrange
            var config = new TestUnifiedConfig
            {
                Version = "4.1",
                Metadata = new TestMetadata
                {
                    Name = "序列化测试配置",
                    Description = "用于测试YAML序列化功能的配置",
                    Created = "2025-01-04T10:00:00Z",
                    Author = "YAML序列化测试"
                },
                Mqtt = new TestMqttConfig
                {
                    Broker = new TestMqttBroker
                    {
                        Host = "mqtt.test.com",
                        Port = 1883,
                        Username = "yaml_test_user",
                        Password = "yaml_test_pass"
                    },
                    Topics = new TestMqttTopics
                    {
                        EventInput = "yaml/test/events",
                        AlarmOutput = "yaml/test/alarms",
                        ControlInput = "yaml/test/control"
                    }
                },
                Events = new List<TestEvent>
                {
                    new TestEvent
                    {
                        Id = "YAML001",
                        Name = "YAML序列化测试事件",
                        Description = "用于测试YAML序列化的事件定义",
                        Conditions = new List<TestCondition>
                        {
                            new TestCondition
                            {
                                Field = "event_type",
                                Operator = "equals",
                                Value = "yaml_test"
                            },
                            new TestCondition
                            {
                                Field = "priority",
                                Operator = "greater_than",
                                Value = 5
                            }
                        },
                        TimeWindow = new TestTimeWindow
                        {
                            Duration = 300,
                            Unit = "seconds"
                        },
                        Actions = new List<TestAction>
                        {
                            new TestAction
                            {
                                Type = "log",
                                Level = "info",
                                Message = "YAML测试事件: {event_id}"
                            },
                            new TestAction
                            {
                                Type = "alarm",
                                Level = "warning",
                                Message = "YAML序列化测试告警"
                            }
                        }
                    }
                },
                Devices = new List<TestDevice>
                {
                    new TestDevice
                    {
                        Id = "YAML_DEV001",
                        Name = "YAML测试设备1",
                        Type = "sensor",
                        Location = "YAML测试区域A",
                        Enabled = true
                    },
                    new TestDevice
                    {
                        Id = "YAML_DEV002",
                        Name = "YAML测试设备2",
                        Type = "camera",
                        Location = "YAML测试区域B",
                        Enabled = false
                    }
                },
                Alarms = new TestAlarmConfig
                {
                    Levels = new Dictionary<string, TestAlarmLevel>
                    {
                        ["info"] = new TestAlarmLevel { Color = "#17a2b8", Sound = false },
                        ["warning"] = new TestAlarmLevel { Color = "#ffc107", Sound = true },
                        ["error"] = new TestAlarmLevel { Color = "#dc3545", Sound = true }
                    },
                    Retention = new TestAlarmRetention
                    {
                        Days = 30,
                        MaxCount = 10000
                    }
                }
            };

            // Act
            var yamlContent = _yamlSerializer.Serialize(config);

            // Assert
            yamlContent.Should().NotBeNullOrEmpty();
            yamlContent.Should().Contain("version: 4.1");
            yamlContent.Should().Contain("序列化测试配置");
            yamlContent.Should().Contain("mqtt.test.com");
            yamlContent.Should().Contain("YAML001");
            yamlContent.Should().Contain("YAML_DEV001");
            yamlContent.Should().Contain("duration: 300");
            yamlContent.Should().Contain("enabled: true");
            yamlContent.Should().Contain("enabled: false");

            // 验证生成的YAML是有效的
            _testDataManager.IsValidYaml(yamlContent).Should().BeTrue();

            _logger.LogInformation("生成的YAML内容:\n{YamlContent}", yamlContent);
        }

        [Fact]
        public void DeserializeUnifiedConfig_ValidYaml_ShouldProduceCorrectObject()
        {
            // Arrange
            const string yamlContent = @"
version: 4.1
metadata:
  name: 反序列化测试配置
  description: 用于测试YAML反序列化功能的配置
  created: 2025-01-04T10:00:00Z
  author: YAML反序列化测试
mqtt:
  broker:
    host: deserialize.test.com
    port: 1883
    username: deserialize_user
    password: deserialize_pass
  topics:
    event_input: deserialize/events
    alarm_output: deserialize/alarms
    control_input: deserialize/control
events:
- id: DESER001
  name: YAML反序列化测试事件
  description: 用于测试YAML反序列化的事件
  conditions:
  - field: event_type
    operator: equals
    value: deserialize_test
  - field: level
    operator: in
    value: [1, 2, 3]
  time_window:
    duration: 600
    unit: seconds
  actions:
  - type: log
    level: debug
    message: 反序列化测试日志
devices:
- id: DESER_DEV001
  name: 反序列化测试设备
  type: actuator
  location: 反序列化测试位置
  enabled: true
alarms:
  levels:
    info:
      color: '#17a2b8'
      sound: false
    warning:
      color: '#ffc107'
      sound: true
  retention:
    days: 60
    max_count: 20000
";

            // Act
            var config = _yamlDeserializer.Deserialize<TestUnifiedConfig>(yamlContent);

            // Assert
            config.Should().NotBeNull();
            config.Version.Should().Be("4.1");
            config.Metadata.Should().NotBeNull();
            config.Metadata!.Name.Should().Be("反序列化测试配置");
            config.Metadata.Description.Should().Be("用于测试YAML反序列化功能的配置");
            config.Metadata.Author.Should().Be("YAML反序列化测试");

            // 验证MQTT配置
            config.Mqtt.Should().NotBeNull();
            config.Mqtt!.Broker.Should().NotBeNull();
            config.Mqtt.Broker!.Host.Should().Be("deserialize.test.com");
            config.Mqtt.Broker.Port.Should().Be(1883);
            config.Mqtt.Broker.Username.Should().Be("deserialize_user");
            config.Mqtt.Broker.Password.Should().Be("deserialize_pass");

            config.Mqtt.Topics.Should().NotBeNull();
            config.Mqtt.Topics!.EventInput.Should().Be("deserialize/events");
            config.Mqtt.Topics.AlarmOutput.Should().Be("deserialize/alarms");
            config.Mqtt.Topics.ControlInput.Should().Be("deserialize/control");

            // 验证事件配置
            config.Events.Should().NotBeNull().And.HaveCount(1);
            var event1 = config.Events[0];
            event1.Id.Should().Be("DESER001");
            event1.Name.Should().Be("YAML反序列化测试事件");
            event1.Conditions.Should().HaveCount(2);
            event1.Conditions[0].Field.Should().Be("event_type");
            event1.Conditions[0].Operator.Should().Be("equals");
            event1.Conditions[0].Value.Should().Be("deserialize_test");
            event1.TimeWindow.Should().NotBeNull();
            event1.TimeWindow!.Duration.Should().Be(600);
            event1.TimeWindow.Unit.Should().Be("seconds");
            event1.Actions.Should().HaveCount(1);
            event1.Actions[0].Type.Should().Be("log");
            event1.Actions[0].Level.Should().Be("debug");

            // 验证设备配置
            config.Devices.Should().NotBeNull().And.HaveCount(1);
            var device1 = config.Devices[0];
            device1.Id.Should().Be("DESER_DEV001");
            device1.Name.Should().Be("反序列化测试设备");
            device1.Type.Should().Be("actuator");
            device1.Location.Should().Be("反序列化测试位置");
            device1.Enabled.Should().BeTrue();

            // 验证告警配置
            config.Alarms.Should().NotBeNull();
            config.Alarms!.Levels.Should().HaveCount(2);
            config.Alarms.Levels.Should().ContainKey("info");
            config.Alarms.Levels.Should().ContainKey("warning");
            config.Alarms.Levels["info"].Color.Should().Be("#17a2b8");
            config.Alarms.Levels["info"].Sound.Should().BeFalse();
            config.Alarms.Levels["warning"].Sound.Should().BeTrue();
            config.Alarms.Retention.Should().NotBeNull();
            config.Alarms.Retention!.Days.Should().Be(60);
            config.Alarms.Retention.MaxCount.Should().Be(20000);
        }

        [Fact]
        public void SerializeDeserialize_RoundTrip_ShouldPreserveAllData()
        {
            // Arrange
            var originalConfig = new TestEventConfig
            {
                Version = "4.1",
                Metadata = new TestMetadata
                {
                    Name = "往返测试事件配置",
                    Description = "用于测试YAML往返序列化的事件配置",
                    Created = "2025-01-04T10:00:00Z"
                },
                Events = new Dictionary<string, TestEventDefinition>
                {
                    ["RT001"] = new TestEventDefinition
                    {
                        Name = "往返测试事件1",
                        Description = "第一个往返测试事件",
                        TriggerConditions = new List<TestCondition>
                        {
                            new TestCondition
                            {
                                Field = "event_type",
                                Operator = "==",
                                Value = "roundtrip_test"
                            },
                            new TestCondition
                            {
                                Field = "confidence",
                                Operator = ">=",
                                Value = 0.95
                            }
                        },
                        ProcessingRules = new List<TestProcessingRule>
                        {
                            new TestProcessingRule
                            {
                                Type = "validation",
                                Rules = new List<string> { "required_fields", "data_format" },
                                Actions = new List<string> { "validate_input", "sanitize_data" }
                            },
                            new TestProcessingRule
                            {
                                Type = "correlation",
                                MatchField = "session_id",
                                TimeWindow = 3600
                            }
                        },
                        OutputActions = new List<TestOutputAction>
                        {
                            new TestOutputAction
                            {
                                Type = "mqtt_publish",
                                Topic = "roundtrip/test/output",
                                Qos = 2
                            },
                            new TestOutputAction
                            {
                                Type = "database_insert",
                                Table = "roundtrip_events"
                            },
                            new TestOutputAction
                            {
                                Type = "alarm_trigger",
                                Condition = "high_priority",
                                Level = "warning"
                            }
                        }
                    },
                    ["RT002"] = new TestEventDefinition
                    {
                        Name = "往返测试事件2",
                        Description = "第二个往返测试事件",
                        TriggerConditions = new List<TestCondition>
                        {
                            new TestCondition
                            {
                                Field = "event_category",
                                Operator = "in",
                                Value = new List<string> { "category1", "category2", "category3" }
                            }
                        },
                        ProcessingRules = new List<TestProcessingRule>
                        {
                            new TestProcessingRule
                            {
                                Type = "enrichment",
                                Actions = new List<string> { "add_metadata", "calculate_metrics" }
                            }
                        },
                        OutputActions = new List<TestOutputAction>
                        {
                            new TestOutputAction
                            {
                                Type = "log",
                                Level = "info"
                            }
                        }
                    }
                },
                GlobalSettings = new TestGlobalSettings
                {
                    Timezone = "Asia/Shanghai",
                    DateFormat = "yyyy-MM-dd HH:mm:ss",
                    MaxProcessingTime = 10000,
                    RetryAttempts = 5
                },
                ValidationRules = new Dictionary<string, TestValidationRule>
                {
                    ["required_fields"] = new TestValidationRule
                    {
                        Field = "event_id",
                        Type = "not_empty",
                        ErrorMessage = "事件ID不能为空"
                    },
                    ["data_format"] = new TestValidationRule
                    {
                        Field = "timestamp",
                        Type = "datetime_range",
                        MinAge = 0,
                        MaxAge = 600,
                        ErrorMessage = "时间戳格式无效或超出范围"
                    }
                },
                DeviceMapping = new Dictionary<string, string>
                {
                    ["rt_camera_001"] = "往返测试摄像头1",
                    ["rt_sensor_001"] = "往返测试传感器1",
                    ["rt_actuator_001"] = "往返测试执行器1"
                }
            };

            // Act - 序列化
            var yamlContent = _yamlSerializer.Serialize(originalConfig);
            yamlContent.Should().NotBeNullOrEmpty();

            // 反序列化
            var deserializedConfig = _yamlDeserializer.Deserialize<TestEventConfig>(yamlContent);

            // Assert - 验证所有数据完整性
            deserializedConfig.Should().NotBeNull();
            deserializedConfig.Version.Should().Be(originalConfig.Version);
            deserializedConfig.Metadata.Should().NotBeNull();
            deserializedConfig.Metadata!.Name.Should().Be(originalConfig.Metadata.Name);
            deserializedConfig.Metadata.Description.Should().Be(originalConfig.Metadata.Description);

            // 验证事件定义
            deserializedConfig.Events.Should().HaveCount(originalConfig.Events.Count);
            deserializedConfig.Events.Should().ContainKey("RT001");
            deserializedConfig.Events.Should().ContainKey("RT002");

            var rt001 = deserializedConfig.Events["RT001"];
            var originalRt001 = originalConfig.Events["RT001"];
            rt001.Name.Should().Be(originalRt001.Name);
            rt001.Description.Should().Be(originalRt001.Description);
            rt001.TriggerConditions.Should().HaveCount(originalRt001.TriggerConditions.Count);
            rt001.ProcessingRules.Should().HaveCount(originalRt001.ProcessingRules.Count);
            rt001.OutputActions.Should().HaveCount(originalRt001.OutputActions.Count);

            // 验证处理规则
            var processingRule = rt001.ProcessingRules[0];
            var originalProcessingRule = originalRt001.ProcessingRules[0];
            processingRule.Type.Should().Be(originalProcessingRule.Type);
            processingRule.Rules.Should().BeEquivalentTo(originalProcessingRule.Rules);
            processingRule.Actions.Should().BeEquivalentTo(originalProcessingRule.Actions);

            // 验证输出动作
            var outputAction = rt001.OutputActions[0];
            var originalOutputAction = originalRt001.OutputActions[0];
            outputAction.Type.Should().Be(originalOutputAction.Type);
            outputAction.Topic.Should().Be(originalOutputAction.Topic);
            outputAction.Qos.Should().Be(originalOutputAction.Qos);

            // 验证全局设置
            deserializedConfig.GlobalSettings.Should().NotBeNull();
            deserializedConfig.GlobalSettings!.Timezone.Should().Be(originalConfig.GlobalSettings.Timezone);
            deserializedConfig.GlobalSettings.DateFormat.Should().Be(originalConfig.GlobalSettings.DateFormat);
            deserializedConfig.GlobalSettings.MaxProcessingTime.Should().Be(originalConfig.GlobalSettings.MaxProcessingTime);
            deserializedConfig.GlobalSettings.RetryAttempts.Should().Be(originalConfig.GlobalSettings.RetryAttempts);

            // 验证验证规则
            deserializedConfig.ValidationRules.Should().HaveCount(originalConfig.ValidationRules.Count);
            deserializedConfig.ValidationRules.Should().ContainKey("required_fields");
            deserializedConfig.ValidationRules.Should().ContainKey("data_format");

            // 验证设备映射
            deserializedConfig.DeviceMapping.Should().HaveCount(originalConfig.DeviceMapping.Count);
            deserializedConfig.DeviceMapping.Should().BeEquivalentTo(originalConfig.DeviceMapping);

            _logger.LogInformation("往返序列化测试成功完成，所有数据完整性验证通过");
        }

        [Theory]
        [InlineData("version: '4.1'\nmetadata:\n  name: '测试'\n", true)]
        [InlineData("version: '4.1'\nmetadata:\n  name: '测试\n  # 缺少引号结束\n", false)]
        [InlineData("version: 4.1\nmetadata: null\n", true)]
        [InlineData("invalid: yaml: content: [\n", false)]
        [InlineData("", false)]
        [InlineData("   \n\n   \n", false)]
        public void ValidateYamlContent_VariousInputs_ShouldReturnExpectedResults(string yamlContent, bool expectedValid)
        {
            // Act
            var isValid = _testDataManager.IsValidYaml(yamlContent);

            // Assert
            isValid.Should().Be(expectedValid);
        }

        [Fact]
        public void SerializeWithSpecialCharacters_ShouldHandleCorrectly()
        {
            // Arrange
            var config = new TestUnifiedConfig
            {
                Version = "4.1",
                Metadata = new TestMetadata
                {
                    Name = "特殊字符测试: \"引号\" & 'apostrophe' & <tags> & 换行\n制表\t符",
                    Description = "包含各种特殊字符的配置: @#$%^&*()_+-=[]{}|;:,.<>?/~`",
                    Author = "测试用户 <<EMAIL>>"
                }
            };

            // Act
            var yamlContent = _yamlSerializer.Serialize(config);
            var deserializedConfig = _yamlDeserializer.Deserialize<TestUnifiedConfig>(yamlContent);

            // Assert
            yamlContent.Should().NotBeNullOrEmpty();
            deserializedConfig.Should().NotBeNull();
            deserializedConfig.Metadata.Should().NotBeNull();
            deserializedConfig.Metadata!.Name.Should().Be(config.Metadata.Name);
            deserializedConfig.Metadata.Description.Should().Be(config.Metadata.Description);
            deserializedConfig.Metadata.Author.Should().Be(config.Metadata.Author);
        }

        public void Dispose()
        {
            // 清理临时文件
            foreach (var tempFile in _tempFilesToCleanup)
            {
                _testDataManager.CleanupTempFile(tempFile);
            }
        }
    }
}