using FluentAssertions;
using Xunit;

namespace EPConfigTool.Tests.BasicTests;

/// <summary>
/// 基础测试示例
/// 验证测试框架工作正常
/// </summary>
public class SampleTest
{
    [Fact]
    public void BasicTest_ShouldPass()
    {
        // Arrange
        var expected = "Hello, World!";
        
        // Act
        var actual = "Hello, World!";
        
        // Assert
        actual.Should().Be(expected);
    }

    [Theory]
    [InlineData(1, 2, 3)]
    [InlineData(5, 10, 15)]
    [InlineData(-1, -2, -3)]
    public void AddNumbers_ShouldReturnCorrectSum(int a, int b, int expected)
    {
        // Act
        var result = a + b;
        
        // Assert
        result.Should().Be(expected);
    }

    [Fact]
    public void TestFramework_ShouldSupportFluentAssertions()
    {
        // Arrange
        var list = new List<string> { "apple", "banana", "cherry" };
        
        // Assert
        list.Should().HaveCount(3);
        list.Should().Contain("banana");
        list.Should().NotContain("orange");
    }

    [Fact]
    public async Task AsyncTest_ShouldWork()
    {
        // Arrange
        var delay = TimeSpan.FromMilliseconds(10);
        
        // Act
        await Task.Delay(delay);
        var result = DateTime.Now;
        
        // Assert
        result.Should().BeCloseTo(DateTime.Now, TimeSpan.FromSeconds(1));
    }
}
