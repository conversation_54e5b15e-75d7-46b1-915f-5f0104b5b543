using EPConfigTool.Services;
using Microsoft.Extensions.Logging;
using Moq;
using System.IO;

namespace EPConfigTool.Tests.Helpers;

/// <summary>
/// 测试辅助类
/// 提供测试中常用的工具方法和模拟对象
/// </summary>
public static class TestHelper
{
    /// <summary>
    /// 创建模拟的 ILogger
    /// </summary>
    public static Mock<ILogger<T>> CreateMockLogger<T>()
    {
        return new Mock<ILogger<T>>();
    }

    /// <summary>
    /// 创建模拟的 IHelpInfoService
    /// </summary>
    public static Mock<IHelpInfoService> CreateMockHelpInfoService()
    {
        var mock = new Mock<IHelpInfoService>();
        
        // 设置默认的帮助信息返回
        mock.Setup(x => x.GetStatusBarInfo(It.IsAny<string>()))
            .Returns((string key) => $"帮助信息: {key}");
            
        mock.Setup(x => x.GetHelpInfo(It.IsAny<string>()))
            .Returns((string key) => new ConfigHelpInfo
            {
                Key = key,
                DisplayName = $"标题: {key}",
                ShortDescription = $"描述: {key}",
                ExampleValue = $"示例: {key}",
                ValidValues = $"验证规则: {key}"
            });
            
        return mock;
    }

    /// <summary>
    /// 创建模拟的 IFileDialogService
    /// </summary>
    public static Mock<IFileDialogService> CreateMockFileDialogService()
    {
        var mock = new Mock<IFileDialogService>();
        
        // 设置默认的文件对话框行为
        mock.Setup(x => x.ShowOpenYamlFileDialog(It.IsAny<string>(), It.IsAny<string>()))
            .Returns((string?)null); // 默认返回 null（用户取消）
            
        mock.Setup(x => x.ShowSaveYamlFileDialog(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<string>()))
            .Returns((string?)null); // 默认返回 null（用户取消）
            
        return mock;
    }

    /// <summary>
    /// 创建临时测试文件
    /// </summary>
    public static string CreateTempTestFile(string content, string extension = ".yaml")
    {
        var tempFile = Path.Combine(Path.GetTempPath(), $"test_{Guid.NewGuid()}{extension}");
        File.WriteAllText(tempFile, content);
        return tempFile;
    }

    /// <summary>
    /// 清理临时测试文件
    /// </summary>
    public static void CleanupTempFile(string filePath)
    {
        if (File.Exists(filePath))
        {
            try
            {
                File.Delete(filePath);
            }
            catch
            {
                // 忽略删除失败的情况
            }
        }
    }

    /// <summary>
    /// 验证 YAML 内容是否包含指定的键值对
    /// </summary>
    public static bool YamlContains(string yamlContent, string key, string value)
    {
        var lines = yamlContent.Split('\n', StringSplitOptions.RemoveEmptyEntries);
        var targetLine = $"{key}: {value}";
        return lines.Any(line => line.Trim().Equals(targetLine, StringComparison.OrdinalIgnoreCase));
    }

    /// <summary>
    /// 验证 YAML 内容是否包含指定的键
    /// </summary>
    public static bool YamlContainsKey(string yamlContent, string key)
    {
        var lines = yamlContent.Split('\n', StringSplitOptions.RemoveEmptyEntries);
        return lines.Any(line => line.Trim().StartsWith($"{key}:", StringComparison.OrdinalIgnoreCase));
    }

    /// <summary>
    /// 从 YAML 内容中提取指定键的值
    /// </summary>
    public static string? ExtractYamlValue(string yamlContent, string key)
    {
        var lines = yamlContent.Split('\n', StringSplitOptions.RemoveEmptyEntries);
        var targetLine = lines.FirstOrDefault(line => 
            line.Trim().StartsWith($"{key}:", StringComparison.OrdinalIgnoreCase));
            
        if (targetLine == null) return null;
        
        var colonIndex = targetLine.IndexOf(':');
        if (colonIndex == -1) return null;
        
        return targetLine.Substring(colonIndex + 1).Trim().Trim('"');
    }

    /// <summary>
    /// 创建测试用的统一配置 YAML 内容
    /// </summary>
    public static string CreateTestUnifiedConfigYaml()
    {
        return @"# EventProcessor V4.1 统一配置文件
Logging:
  LogLevel:
    Default: Information
    Microsoft: Warning
    EventProcessor: Debug

Serilog:
  Using:
    - Serilog.Sinks.Console
    - Serilog.Sinks.File
  MinimumLevel:
    Default: Information
    Override:
      Microsoft: Warning
      EventProcessor: Debug
  WriteTo:
    - Name: Console
      Args:
        outputTemplate: '[{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} {Level:u3}] {Message:lj}{NewLine}{Exception}'
    - Name: File
      Args:
        path: 'logs/test-.log'
        rollingInterval: Day
        retainedFileCountLimit: 30

EventProcessor:
  EventId: EV001001
  EventName: 测试事件
  EvaluationStrategy: BusinessOnly
  Priority: P3
  CommId: '101013'
  PositionId: P001
  AlarmGracePeriodSeconds: 3
  EnableAlarmCancellation: true
  CorrelationTimeWindow: minute
  DeviceSignal:
    Topics:
      - device/test/event
    TriggerField: I2
    TriggerValues:
      'true': '0'
      'false': '1'
    HoldingTimeoutSec: 20
  RuleConfiguration:
    AlarmConfig: {}

Mqtt:
  BrokerHost: localhost
  BrokerPort: 1883
  ClientId: EP_V4.1_Test
  Username: test_user
  Password: test_password
  KeepAliveInterval: 60
  ReconnectDelay: 5
  QualityOfServiceLevel: 1

ErrorHandling:
  ToleranceLevel: Normal
  RetryPolicy:
    MaxRetries: 3
    RetryDelay: 1000
    UseExponentialBackoff: true
    MaxBackoffDelay: 30000
  FallbackStrategy:
    OnRuleFailure: ContinueProcessing
    OnAIFailure: UseBusinessOnly
    OnTimerFailure: ImmediateAlarm
    OnMqttFailure: Retry
  Logging:
    ErrorLogLevel: Error
    DetailedStackTrace: true
    IncludeMessagePayload: false
    EnablePerformanceMonitoring: true
    ErrorRateThreshold: 0.1";
    }

    /// <summary>
    /// 创建测试用的传统配置 YAML 内容
    /// </summary>
    public static string CreateTestLegacyConfigYaml()
    {
        return @"EventId: EV001001
EventName: 测试事件
EvaluationStrategy: BusinessOnly
Priority: P3
CommId: '101013'
PositionId: P001
AlarmGracePeriodSeconds: 3
EnableAlarmCancellation: true
CorrelationTimeWindow: minute
DeviceSignal:
  Topics:
    - device/test/event
  TriggerField: I2
  TriggerValues:
    'true': '0'
    'false': '1'
  HoldingTimeoutSec: 20
RuleConfiguration:
  AlarmConfig: {}";
    }

    /// <summary>
    /// 验证两个对象的属性是否相等（用于测试模型转换）
    /// </summary>
    public static bool ArePropertiesEqual<T>(T obj1, T obj2, params string[] excludeProperties)
    {
        if (obj1 == null && obj2 == null) return true;
        if (obj1 == null || obj2 == null) return false;

        var properties = typeof(T).GetProperties()
            .Where(p => !excludeProperties.Contains(p.Name))
            .ToArray();

        foreach (var property in properties)
        {
            var value1 = property.GetValue(obj1);
            var value2 = property.GetValue(obj2);

            if (!Equals(value1, value2))
            {
                return false;
            }
        }

        return true;
    }
}
