using EPConfigTool.Services;
using FluentAssertions;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;

namespace EPConfigTool.Tests.Services;

/// <summary>
/// HelpInfoService 集成测试
/// 测试帮助信息的获取和上下文相关性
/// </summary>
public class HelpInfoServiceTests
{
    private readonly Mock<ILogger<HelpInfoService>> _mockLogger;
    private readonly HelpInfoService _service;

    public HelpInfoServiceTests()
    {
        _service = new HelpInfoService();
    }

    #region 状态栏帮助信息测试

    [Theory]
    [InlineData("EventId", "事件ID")]
    [InlineData("EventName", "事件名称")]
    [InlineData("EvaluationStrategy", "评估策略")]
    [InlineData("Priority", "优先级")]
    [InlineData("CommId", "小区ID")]
    [InlineData("PositionId", "位置ID")]
    public void GetStatusBarInfo_WithKnownEventKeys_ShouldReturnCorrectInfo(string key, string expectedContent)
    {
        // Act
        var result = _service.GetStatusBarInfo(key);

        // Assert
        result.Should().NotBeNullOrEmpty();
        result.Should().Contain(expectedContent);
    }

    [Theory]
    [InlineData("Mqtt.BrokerHost", "MQTT 服务器地址")]
    [InlineData("Mqtt.BrokerPort", "MQTT 服务器端口")]
    [InlineData("Mqtt.ClientId", "MQTT 客户端标识符")]
    [InlineData("Mqtt.Username", "MQTT 连接用户名")]
    [InlineData("Mqtt.Password", "MQTT 连接密码")]
    public void GetStatusBarInfo_WithKnownMqttKeys_ShouldReturnCorrectInfo(string key, string expectedContent)
    {
        // Act
        var result = _service.GetStatusBarInfo(key);

        // Assert
        result.Should().NotBeNullOrEmpty();
        result.Should().Contain(expectedContent);
    }

    [Theory]
    [InlineData("ErrorHandling.ToleranceLevel", "错误容忍级别")]
    [InlineData("ErrorHandling.MaxRetries", "最大重试次数")]
    [InlineData("ErrorHandling.RetryDelay", "重试延迟")]
    [InlineData("ErrorHandling.OnRuleFailure", "规则失败策略")]
    [InlineData("ErrorHandling.OnAIFailure", "AI失败策略")]
    public void GetStatusBarInfo_WithKnownErrorHandlingKeys_ShouldReturnCorrectInfo(string key, string expectedContent)
    {
        // Act
        var result = _service.GetStatusBarInfo(key);

        // Assert
        result.Should().NotBeNullOrEmpty();
        result.Should().Contain(expectedContent);
    }

    [Theory]
    [InlineData("Logging.Default", "默认日志级别")]
    [InlineData("Logging.Microsoft", "Microsoft 组件日志级别")]
    [InlineData("Logging.EventProcessor", "EventProcessor 日志级别")]
    public void GetStatusBarInfo_WithKnownLoggingKeys_ShouldReturnCorrectInfo(string key, string expectedContent)
    {
        // Act
        var result = _service.GetStatusBarInfo(key);

        // Assert
        result.Should().NotBeNullOrEmpty();
        result.Should().Contain(expectedContent);
    }

    [Theory]
    [InlineData("Serilog.DefaultMinimumLevel", "默认最小日志级别")]
    [InlineData("Serilog.EnableConsoleOutput", "是否启用控制台输出")]
    [InlineData("Serilog.EnableFileOutput", "是否启用文件输出")]
    [InlineData("Serilog.LogFilePath", "日志文件路径")]
    public void GetStatusBarInfo_WithKnownSerilogKeys_ShouldReturnCorrectInfo(string key, string expectedContent)
    {
        // Act
        var result = _service.GetStatusBarInfo(key);

        // Assert
        result.Should().NotBeNullOrEmpty();
        result.Should().Contain(expectedContent);
    }

    [Fact]
    public void GetStatusBarInfo_WithUnknownKey_ShouldReturnGenericMessage()
    {
        // Arrange
        var unknownKey = "Unknown.Key.That.Does.Not.Exist";

        // Act
        var result = _service.GetStatusBarInfo(unknownKey);

        // Assert
        result.Should().Be($"配置项: {unknownKey}");
    }

    [Theory]
    [InlineData("")]
    [InlineData("   ")]
    [InlineData(null)]
    public void GetStatusBarInfo_WithInvalidKey_ShouldReturnDefaultMessage(string invalidKey)
    {
        // Act
        var result = _service.GetStatusBarInfo(invalidKey);

        // Assert
        result.Should().Be("请选择配置项以查看帮助信息");
    }

    #endregion

    #region 详细帮助信息测试

    [Fact]
    public void GetHelpInfo_WithEventIdKey_ShouldReturnDetailedInfo()
    {
        // Act
        var result = _service.GetHelpInfo("EventId");

        // Assert
        result.Should().NotBeNull();
        result.DisplayName.Should().Be("事件ID");
        result.ShortDescription.Should().NotBeNullOrEmpty();
        result.ExampleValue.Should().NotBeNullOrEmpty();
        result.ValidValues.Should().NotBeNullOrEmpty();
        result.ValidValues.Should().Contain("EV");
        result.ValidValues.Should().Contain("6位数字");
    }

    [Fact]
    public void GetHelpInfo_WithEvaluationStrategyKey_ShouldReturnDetailedInfo()
    {
        // Act
        var result = _service.GetHelpInfo("EvaluationStrategy");

        // Assert
        result.Should().NotBeNull();
        result.DisplayName.Should().Be("评估策略");
        result.DetailedDescription.Should().NotBeNullOrEmpty();
        result.ExampleValue.Should().NotBeNullOrEmpty();
        result.ValidValues.Should().NotBeNullOrEmpty();
        result.ValidValues.Should().Contain("BusinessOnly");
        result.ValidValues.Should().Contain("AI");
        result.ValidValues.Should().Contain("AIAndBusiness");
    }

    [Fact]
    public void GetHelpInfo_WithMqttBrokerHostKey_ShouldReturnDetailedInfo()
    {
        // Act
        var result = _service.GetHelpInfo("Mqtt.BrokerHost");

        // Assert
        result.Should().NotBeNull();
        result.DisplayName.Should().Be("MQTT 服务器地址");
        result.DetailedDescription.Should().NotBeNullOrEmpty();
        result.ExampleValue.Should().NotBeNullOrEmpty();
        result.ValidValues.Should().NotBeNullOrEmpty();
        result.ValidValues.Should().Contain("不能为空");
    }

    [Fact]
    public void GetHelpInfo_WithErrorHandlingToleranceLevelKey_ShouldReturnDetailedInfo()
    {
        // Act
        var result = _service.GetHelpInfo("ErrorHandling.ToleranceLevel");

        // Assert
        result.Should().NotBeNull();
        result.DisplayName.Should().Be("错误容忍级别");
        result.DetailedDescription.Should().NotBeNullOrEmpty();
        result.ExampleValue.Should().NotBeNullOrEmpty();
        result.ValidValues.Should().NotBeNullOrEmpty();
        result.ValidValues.Should().Contain("Strict");
        result.ValidValues.Should().Contain("Normal");
        result.ValidValues.Should().Contain("Lenient");
    }

    [Fact]
    public void GetHelpInfo_WithUnknownKey_ShouldReturnGenericInfo()
    {
        // Arrange
        var unknownKey = "Unknown.Key.That.Does.Not.Exist";

        // Act
        var result = _service.GetHelpInfo(unknownKey);

        // Assert
        result.Should().NotBeNull();
        result.DisplayName.Should().Be($"配置项: {unknownKey}");
        result.DetailedDescription.Should().Be("暂无详细帮助信息");
        result.ExampleValue.Should().Be("暂无示例");
        result.ValidValues.Should().BeEmpty();
    }

    [Theory]
    [InlineData("")]
    [InlineData("   ")]
    [InlineData(null)]
    public void GetHelpInfo_WithInvalidKey_ShouldReturnDefaultInfo(string invalidKey)
    {
        // Act
        var result = _service.GetHelpInfo(invalidKey);

        // Assert
        result.Should().NotBeNull();
        result.DisplayName.Should().Be("帮助信息");
        result.DetailedDescription.Should().Be("请选择配置项以查看详细帮助信息");
        result.ExampleValue.Should().Be("");
        result.ValidValues.Should().BeEmpty();
    }

    #endregion

    #region 配置键获取测试

    [Fact]
    public void GetAllConfigKeys_ShouldReturnAllAvailableKeys()
    {
        // Act
        var result = _service.GetAllConfigKeys();

        // Assert
        result.Should().NotBeEmpty();
        result.Should().Contain("EventId");
        result.Should().Contain("EventName");
    }

    #endregion

    #region 帮助信息缓存测试

    [Fact]
    public void GetHelpInfo_CalledMultipleTimes_ShouldReturnConsistentResults()
    {
        // Arrange
        var key = "EventId";

        // Act
        var result1 = _service.GetHelpInfo(key);
        var result2 = _service.GetHelpInfo(key);
        var result3 = _service.GetHelpInfo(key);

        // Assert
        result1.Should().BeEquivalentTo(result2);
        result2.Should().BeEquivalentTo(result3);
    }

    [Fact]
    public void GetStatusBarInfo_CalledMultipleTimes_ShouldReturnConsistentResults()
    {
        // Arrange
        var key = "EventName";

        // Act
        var result1 = _service.GetStatusBarInfo(key);
        var result2 = _service.GetStatusBarInfo(key);
        var result3 = _service.GetStatusBarInfo(key);

        // Assert
        result1.Should().Be(result2);
        result2.Should().Be(result3);
    }

    #endregion

    #region 本地化支持测试

    [Fact]
    public void GetHelpInfo_ShouldReturnChineseContent()
    {
        // Act
        var result = _service.GetHelpInfo("EventId");

        // Assert
        result.DisplayName.Should().Contain("事件");
        result.DetailedDescription.Should().Contain("唯一标识");
        result.ValidValues.Should().Contain("必须");
    }

    [Fact]
    public void GetStatusBarInfo_ShouldReturnChineseContent()
    {
        // Act
        var result = _service.GetStatusBarInfo("EventName");

        // Assert
        result.Should().Contain("事件名称");
        result.Should().Contain("描述");
    }

    #endregion

    #region 性能测试

    [Fact]
    public void GetHelpInfo_WithManyRequests_ShouldPerformWell()
    {
        // Arrange
        var keys = new[] { "EventId", "EventName" };
        var stopwatch = System.Diagnostics.Stopwatch.StartNew();

        // Act
        for (int i = 0; i < 1000; i++)
        {
            foreach (var key in keys)
            {
                _service.GetHelpInfo(key);
            }
        }

        stopwatch.Stop();

        // Assert
        stopwatch.ElapsedMilliseconds.Should().BeLessThan(1000); // 应该在1秒内完成
    }

    #endregion
}
