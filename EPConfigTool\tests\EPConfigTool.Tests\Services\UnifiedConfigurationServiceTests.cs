using EPConfigTool.Models;
using EPConfigTool.Services;
using EPConfigTool.Tests.Helpers;
using EPConfigTool.Tests.TestData;
using FluentAssertions;
using Microsoft.Extensions.Logging;
using Moq;
using System.IO;
using Xunit;

namespace EPConfigTool.Tests.Services;

/// <summary>
/// UnifiedConfigurationService 集成测试
/// 测试统一配置文件的加载、保存、验证、序列化/反序列化功能
/// </summary>
public class UnifiedConfigurationServiceTests : IDisposable
{
    private readonly Mock<ILogger<UnifiedConfigurationService>> _mockLogger;
    private readonly Mock<IYamlConfigurationService> _mockYamlConfigService;
    private readonly UnifiedConfigurationService _service;
    private readonly List<string> _tempFiles;

    public UnifiedConfigurationServiceTests()
    {
        _mockLogger = TestHelper.CreateMockLogger<UnifiedConfigurationService>();
        _mockYamlConfigService = new Mock<IYamlConfigurationService>();
        _service = new UnifiedConfigurationService(_mockLogger.Object, _mockYamlConfigService.Object);
        _tempFiles = new List<string>();
    }

    public void Dispose()
    {
        // 清理临时文件
        foreach (var file in _tempFiles)
        {
            TestHelper.CleanupTempFile(file);
        }
    }

    #region 文件检测测试

    [Fact]
    public async Task IsUnifiedConfigurationFileAsync_WithUnifiedConfigFile_ShouldReturnTrue()
    {
        // Arrange
        var yamlContent = TestHelper.CreateTestUnifiedConfigYaml();
        var testFile = TestHelper.CreateTempTestFile(yamlContent);
        _tempFiles.Add(testFile);

        // Act
        var result = await _service.IsUnifiedConfigurationFileAsync(testFile);

        // Assert
        result.Should().BeTrue();
    }

    [Fact]
    public async Task IsUnifiedConfigurationFileAsync_WithLegacyConfigFile_ShouldReturnFalse()
    {
        // Arrange
        var yamlContent = TestHelper.CreateTestLegacyConfigYaml();
        var testFile = TestHelper.CreateTempTestFile(yamlContent);
        _tempFiles.Add(testFile);

        // Act
        var result = await _service.IsUnifiedConfigurationFileAsync(testFile);

        // Assert
        result.Should().BeFalse();
    }

    [Fact]
    public async Task IsUnifiedConfigurationFileAsync_WithNonExistentFile_ShouldThrowFileNotFoundException()
    {
        // Arrange
        var nonExistentFile = "non-existent-file.yaml";

        // Act & Assert
        await Assert.ThrowsAsync<FileNotFoundException>(() => 
            _service.IsUnifiedConfigurationFileAsync(nonExistentFile));
    }

    [Fact]
    public async Task IsUnifiedConfigurationFileAsync_WithInvalidYamlFile_ShouldReturnFalse()
    {
        // Arrange
        var invalidYaml = "invalid: yaml: content: [unclosed";
        var testFile = TestHelper.CreateTempTestFile(invalidYaml);
        _tempFiles.Add(testFile);

        // Act
        var result = await _service.IsUnifiedConfigurationFileAsync(testFile);

        // Assert
        result.Should().BeFalse();
    }

    #endregion

    #region 加载测试

    [Fact]
    public async Task LoadFromYamlFileAsync_WithValidUnifiedConfig_ShouldReturnUnifiedConfiguration()
    {
        // Arrange
        var yamlContent = TestHelper.CreateTestUnifiedConfigYaml();
        var testFile = TestHelper.CreateTempTestFile(yamlContent);
        _tempFiles.Add(testFile);

        // Act
        var result = await _service.LoadFromYamlFileAsync(testFile);

        // Assert
        result.Should().NotBeNull();
        result.EventProcessor.Should().NotBeNull();
        result.Mqtt.Should().NotBeNull();
        result.ErrorHandling.Should().NotBeNull();
        result.Logging.Should().NotBeNull();
        result.Serilog.Should().NotBeNull();

        // 验证关键属性
        result.EventProcessor.EventId.Should().Be("EV001001");
        result.EventProcessor.EventName.Should().Be("测试事件");
        result.Mqtt.BrokerHost.Should().Be("localhost");
        result.Mqtt.BrokerPort.Should().Be(1883);
        result.ErrorHandling.ToleranceLevel.Should().Be("Normal");
    }

    [Fact]
    public async Task LoadFromYamlFileAsync_WithNonExistentFile_ShouldThrowFileNotFoundException()
    {
        // Arrange
        var nonExistentFile = "non-existent-file.yaml";

        // Act & Assert
        await Assert.ThrowsAsync<FileNotFoundException>(() => 
            _service.LoadFromYamlFileAsync(nonExistentFile));
    }

    [Fact]
    public async Task LoadFromYamlFileAsync_WithInvalidYamlFile_ShouldThrowException()
    {
        // Arrange
        var invalidYaml = "invalid: yaml: content: [unclosed";
        var testFile = TestHelper.CreateTempTestFile(invalidYaml);
        _tempFiles.Add(testFile);

        // Act & Assert
        await Assert.ThrowsAsync<Exception>(() => 
            _service.LoadFromYamlFileAsync(testFile));
    }

    #endregion

    #region 保存测试

    [Fact]
    public async Task SaveToYamlFileAsync_WithValidConfiguration_ShouldSaveFile()
    {
        // Arrange
        var unifiedConfig = TestDataFactory.CreateDefaultUnifiedConfiguration();
        var testFile = Path.GetTempFileName();
        _tempFiles.Add(testFile);

        // Act
        await _service.SaveToYamlFileAsync(testFile, unifiedConfig);

        // Assert
        File.Exists(testFile).Should().BeTrue();
        var content = await File.ReadAllTextAsync(testFile);
        content.Should().NotBeEmpty();
        
        // 验证 YAML 内容包含关键配置节
        TestHelper.YamlContainsKey(content, "EventProcessor").Should().BeTrue();
        TestHelper.YamlContainsKey(content, "Mqtt").Should().BeTrue();
        TestHelper.YamlContainsKey(content, "ErrorHandling").Should().BeTrue();
        TestHelper.YamlContainsKey(content, "Logging").Should().BeTrue();
        TestHelper.YamlContainsKey(content, "Serilog").Should().BeTrue();
    }

    [Fact]
    public async Task SaveToYamlFileAsync_WithNullConfiguration_ShouldThrowArgumentNullException()
    {
        // Arrange
        var testFile = Path.GetTempFileName();
        _tempFiles.Add(testFile);

        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => 
            _service.SaveToYamlFileAsync(testFile, null!));
    }

    [Fact]
    public async Task SaveToYamlFileAsync_WithInvalidFilePath_ShouldThrowException()
    {
        // Arrange
        var unifiedConfig = TestDataFactory.CreateDefaultUnifiedConfiguration();
        var invalidPath = "invalid/path/that/does/not/exist/file.yaml";

        // Act & Assert
        await Assert.ThrowsAsync<DirectoryNotFoundException>(() => 
            _service.SaveToYamlFileAsync(invalidPath, unifiedConfig));
    }

    #endregion

    #region 序列化测试

    [Fact]
    public void SerializeToYamlString_WithValidConfiguration_ShouldReturnYamlString()
    {
        // Arrange
        var unifiedConfig = TestDataFactory.CreateDefaultUnifiedConfiguration();

        // Act
        var result = _service.SerializeToYamlString(unifiedConfig);

        // Assert
        result.Should().NotBeNullOrEmpty();
        TestHelper.YamlContainsKey(result, "EventProcessor").Should().BeTrue();
        TestHelper.YamlContainsKey(result, "Mqtt").Should().BeTrue();
        TestHelper.YamlContainsKey(result, "ErrorHandling").Should().BeTrue();
        TestHelper.YamlContainsKey(result, "Logging").Should().BeTrue();
        TestHelper.YamlContainsKey(result, "Serilog").Should().BeTrue();
    }

    [Fact]
    public void SerializeToYamlString_WithNullConfiguration_ShouldThrowArgumentNullException()
    {
        // Act & Assert
        Assert.Throws<ArgumentNullException>(() => 
            _service.SerializeToYamlString(null!));
    }

    [Fact]
    public void ParseFromYamlString_WithValidYaml_ShouldReturnUnifiedConfiguration()
    {
        // Arrange
        var yamlContent = TestHelper.CreateTestUnifiedConfigYaml();

        // Act
        var result = _service.ParseFromYamlString(yamlContent);

        // Assert
        result.Should().NotBeNull();
        result.EventProcessor.Should().NotBeNull();
        result.Mqtt.Should().NotBeNull();
        result.ErrorHandling.Should().NotBeNull();
        result.Logging.Should().NotBeNull();
        result.Serilog.Should().NotBeNull();
    }

    [Fact]
    public void ParseFromYamlString_WithInvalidYaml_ShouldThrowException()
    {
        // Arrange
        var invalidYaml = "invalid: yaml: content: [unclosed";

        // Act & Assert
        Assert.Throws<Exception>(() => 
            _service.ParseFromYamlString(invalidYaml));
    }

    [Theory]
    [InlineData("")]
    [InlineData("   ")]
    [InlineData(null)]
    public void ParseFromYamlString_WithEmptyOrNullYaml_ShouldThrowArgumentException(string invalidYaml)
    {
        // Act & Assert
        Assert.Throws<ArgumentException>(() => 
            _service.ParseFromYamlString(invalidYaml));
    }

    #endregion

    #region 验证测试

    [Fact]
    public async Task ValidateYamlFileAsync_WithValidFile_ShouldReturnValidResult()
    {
        // Arrange
        var yamlContent = TestHelper.CreateTestUnifiedConfigYaml();
        var testFile = TestHelper.CreateTempTestFile(yamlContent);
        _tempFiles.Add(testFile);

        // Act
        var result = await _service.ValidateYamlFileAsync(testFile);

        // Assert
        result.Should().NotBeNull();
        result.IsValid.Should().BeTrue();
        result.Errors.Should().BeEmpty();
        result.SectionErrors.Should().BeEmpty();
    }

    [Fact]
    public async Task ValidateYamlFileAsync_WithInvalidFile_ShouldReturnInvalidResult()
    {
        // Arrange
        var invalidYaml = "invalid: yaml: content: [unclosed";
        var testFile = TestHelper.CreateTempTestFile(invalidYaml);
        _tempFiles.Add(testFile);

        // Act
        var result = await _service.ValidateYamlFileAsync(testFile);

        // Assert
        result.Should().NotBeNull();
        result.IsValid.Should().BeFalse();
        result.Errors.Should().NotBeEmpty();
    }

    [Fact]
    public async Task ValidateYamlFileAsync_WithNonExistentFile_ShouldReturnInvalidResult()
    {
        // Arrange
        var nonExistentFile = "non-existent-file.yaml";

        // Act
        var result = await _service.ValidateYamlFileAsync(nonExistentFile);

        // Assert
        result.Should().NotBeNull();
        result.IsValid.Should().BeFalse();
        result.Errors.Should().Contain(error => error.Contains("文件不存在"));
    }

    #endregion

    #region 配置创建测试

    [Fact]
    public void CreateUnifiedConfiguration_WithEventConfiguration_ShouldReturnUnifiedConfiguration()
    {
        // Arrange
        var eventConfig = TestDataFactory.CreateDefaultEventConfiguration();

        // Act
        var result = _service.CreateUnifiedConfiguration(eventConfig);

        // Assert
        result.Should().NotBeNull();
        result.EventProcessor.Should().Be(eventConfig);
        result.Mqtt.Should().NotBeNull();
        result.ErrorHandling.Should().NotBeNull();
        result.Logging.Should().NotBeNull();
        result.Serilog.Should().NotBeNull();

        // 验证 MQTT 客户端ID包含事件ID
        result.Mqtt.ClientId.Should().Contain(eventConfig.EventId);
    }

    [Fact]
    public void CreateUnifiedConfiguration_WithCustomMqttConfig_ShouldUseCustomMqttConfig()
    {
        // Arrange
        var eventConfig = TestDataFactory.CreateDefaultEventConfiguration();
        var customMqttConfig = TestDataFactory.CreateProductionMqttConfiguration();

        // Act
        var result = _service.CreateUnifiedConfiguration(eventConfig, customMqttConfig);

        // Assert
        result.Mqtt.Should().Be(customMqttConfig);
    }

    [Fact]
    public void CreateUnifiedConfiguration_WithCustomErrorHandlingConfig_ShouldUseCustomErrorHandlingConfig()
    {
        // Arrange
        var eventConfig = TestDataFactory.CreateDefaultEventConfiguration();
        var customErrorConfig = TestDataFactory.CreateDefaultErrorHandlingConfiguration() with { ToleranceLevel = "Strict" };

        // Act
        var result = _service.CreateUnifiedConfiguration(eventConfig, null, customErrorConfig);

        // Assert
        result.ErrorHandling.Should().Be(customErrorConfig);
        result.ErrorHandling.ToleranceLevel.Should().Be("Strict");
    }

    [Fact]
    public void CreateDefaultUnifiedConfiguration_ShouldReturnValidConfiguration()
    {
        // Arrange
        var eventId = "EV999999";
        var eventName = "默认测试事件";

        // Act
        var result = _service.CreateDefaultUnifiedConfiguration(eventId, eventName);

        // Assert
        result.Should().NotBeNull();
        result.EventProcessor.EventId.Should().Be(eventId);
        result.EventProcessor.EventName.Should().Be(eventName);
        result.Mqtt.ClientId.Should().Contain(eventId);
        result.ErrorHandling.ToleranceLevel.Should().Be("Normal");
        result.Logging.LogLevel.Default.Should().Be("Information");
        result.Serilog.MinimumLevel.Default.Should().Be("Information");
    }

    #endregion

    #region 往返测试（序列化-反序列化）

    [Fact]
    public void SerializeAndParse_ShouldPreserveConfiguration()
    {
        // Arrange
        var originalConfig = TestDataFactory.CreateAIUnifiedConfiguration();

        // Act
        var yamlString = _service.SerializeToYamlString(originalConfig);
        var parsedConfig = _service.ParseFromYamlString(yamlString);

        // Assert
        parsedConfig.Should().NotBeNull();
        parsedConfig.EventProcessor.EventId.Should().Be(originalConfig.EventProcessor.EventId);
        parsedConfig.EventProcessor.EventName.Should().Be(originalConfig.EventProcessor.EventName);
        parsedConfig.EventProcessor.EvaluationStrategy.Should().Be(originalConfig.EventProcessor.EvaluationStrategy);
        parsedConfig.Mqtt.BrokerHost.Should().Be(originalConfig.Mqtt.BrokerHost);
        parsedConfig.Mqtt.BrokerPort.Should().Be(originalConfig.Mqtt.BrokerPort);
        parsedConfig.ErrorHandling.ToleranceLevel.Should().Be(originalConfig.ErrorHandling.ToleranceLevel);
    }

    [Fact]
    public async Task SaveAndLoad_ShouldPreserveConfiguration()
    {
        // Arrange
        var originalConfig = TestDataFactory.CreateAIUnifiedConfiguration();
        var testFile = Path.GetTempFileName();
        _tempFiles.Add(testFile);

        // Act
        await _service.SaveToYamlFileAsync(testFile, originalConfig);
        var loadedConfig = await _service.LoadFromYamlFileAsync(testFile);

        // Assert
        loadedConfig.Should().NotBeNull();
        loadedConfig.EventProcessor.EventId.Should().Be(originalConfig.EventProcessor.EventId);
        loadedConfig.EventProcessor.EventName.Should().Be(originalConfig.EventProcessor.EventName);
        loadedConfig.EventProcessor.EvaluationStrategy.Should().Be(originalConfig.EventProcessor.EvaluationStrategy);
        loadedConfig.Mqtt.BrokerHost.Should().Be(originalConfig.Mqtt.BrokerHost);
        loadedConfig.Mqtt.BrokerPort.Should().Be(originalConfig.Mqtt.BrokerPort);
        loadedConfig.ErrorHandling.ToleranceLevel.Should().Be(originalConfig.ErrorHandling.ToleranceLevel);
    }

    #endregion

    #region 文件扩展名验证测试

    [Theory]
    [InlineData("config.yaml")]
    [InlineData("config.yml")]
    [InlineData("CONFIG.YAML")]
    [InlineData("CONFIG.YML")]
    public void ValidateYamlFileExtension_WithValidExtensions_ShouldNotThrow(string fileName)
    {
        // Act & Assert
        var action = () => _service.GetType()
            .GetMethod("ValidateYamlFileExtension", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Static)
            ?.Invoke(null, new object[] { fileName });
        
        action.Should().NotThrow();
    }

    [Theory]
    [InlineData("config.txt")]
    [InlineData("config.json")]
    [InlineData("config.xml")]
    [InlineData("config")]
    public void ValidateYamlFileExtension_WithInvalidExtensions_ShouldThrow(string fileName)
    {
        // Act & Assert
        var action = () => _service.GetType()
            .GetMethod("ValidateYamlFileExtension", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Static)
            ?.Invoke(null, new object[] { fileName });
        
        action.Should().Throw<System.Reflection.TargetInvocationException>();
    }

    #endregion
}
