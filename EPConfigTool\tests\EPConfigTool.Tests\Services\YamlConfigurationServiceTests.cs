using EPConfigTool.Services;
using EPConfigTool.Tests.Helpers;
using EPConfigTool.Tests.TestData;
using EventProcessor.Core.Models;
using FluentAssertions;
using Microsoft.Extensions.Logging;
using Moq;
using System.IO;
using Xunit;

namespace EPConfigTool.Tests.Services;

/// <summary>
/// YamlConfigurationService 集成测试
/// 测试传统配置文件的处理和向后兼容性
/// </summary>
public class YamlConfigurationServiceTests : IDisposable
{
    private readonly Mock<ILogger<YamlConfigurationService>> _mockLogger;
    private readonly YamlConfigurationService _service;
    private readonly List<string> _tempFiles;

    public YamlConfigurationServiceTests()
    {
        _mockLogger = TestHelper.CreateMockLogger<YamlConfigurationService>();
        _service = new YamlConfigurationService(_mockLogger.Object);
        _tempFiles = new List<string>();
    }

    public void Dispose()
    {
        // 清理临时文件
        foreach (var file in _tempFiles)
        {
            TestHelper.CleanupTempFile(file);
        }
    }

    #region 加载测试

    [Fact]
    public async Task LoadFromYamlFileAsync_WithValidLegacyConfig_ShouldReturnEventConfiguration()
    {
        // Arrange
        var yamlContent = TestHelper.CreateTestLegacyConfigYaml();
        var testFile = TestHelper.CreateTempTestFile(yamlContent);
        _tempFiles.Add(testFile);

        // Act
        var result = await _service.LoadFromYamlFileAsync(testFile);

        // Assert
        result.Should().NotBeNull();
        result.EventId.Should().Be("EV001001");
        result.EventName.Should().Be("测试事件");
        result.EvaluationStrategy.Should().Be("BusinessOnly");
        result.Priority.Should().Be("P3");
        result.CommId.Should().Be("101013");
        result.PositionId.Should().Be("P001");
        result.AlarmGracePeriodSeconds.Should().Be(3);
        result.EnableAlarmCancellation.Should().BeTrue();
        result.CorrelationTimeWindow.Should().Be("minute");
    }

    [Fact]
    public async Task LoadFromYamlFileAsync_WithNonExistentFile_ShouldThrowFileNotFoundException()
    {
        // Arrange
        var nonExistentFile = "non-existent-file.yaml";

        // Act & Assert
        await Assert.ThrowsAsync<FileNotFoundException>(() => 
            _service.LoadFromYamlFileAsync(nonExistentFile));
    }

    [Fact]
    public async Task LoadFromYamlFileAsync_WithInvalidYamlFile_ShouldThrowException()
    {
        // Arrange
        var invalidYaml = "invalid: yaml: content: [unclosed";
        var testFile = TestHelper.CreateTempTestFile(invalidYaml);
        _tempFiles.Add(testFile);

        // Act & Assert
        await Assert.ThrowsAsync<Exception>(() => 
            _service.LoadFromYamlFileAsync(testFile));
    }

    [Fact]
    public async Task LoadFromYamlFileAsync_WithEmptyFile_ShouldThrowException()
    {
        // Arrange
        var emptyFile = TestHelper.CreateTempTestFile("");
        _tempFiles.Add(emptyFile);

        // Act & Assert
        await Assert.ThrowsAsync<Exception>(() => 
            _service.LoadFromYamlFileAsync(emptyFile));
    }

    #endregion

    #region 保存测试

    [Fact]
    public async Task SaveToYamlFileAsync_WithValidConfiguration_ShouldSaveFile()
    {
        // Arrange
        var eventConfig = TestDataFactory.CreateDefaultEventConfiguration();
        var testFile = Path.GetTempFileName();
        _tempFiles.Add(testFile);

        // Act
        await _service.SaveToYamlFileAsync(testFile, eventConfig);

        // Assert
        File.Exists(testFile).Should().BeTrue();
        var content = await File.ReadAllTextAsync(testFile);
        content.Should().NotBeEmpty();
        
        // 验证 YAML 内容包含关键属性
        TestHelper.YamlContains(content, "EventId", eventConfig.EventId).Should().BeTrue();
        TestHelper.YamlContains(content, "EventName", eventConfig.EventName).Should().BeTrue();
        TestHelper.YamlContains(content, "EvaluationStrategy", eventConfig.EvaluationStrategy).Should().BeTrue();
        TestHelper.YamlContains(content, "Priority", eventConfig.Priority).Should().BeTrue();
    }

    [Fact]
    public async Task SaveToYamlFileAsync_WithNullConfiguration_ShouldThrowArgumentNullException()
    {
        // Arrange
        var testFile = Path.GetTempFileName();
        _tempFiles.Add(testFile);

        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => 
            _service.SaveToYamlFileAsync(testFile, null!));
    }

    [Fact]
    public async Task SaveToYamlFileAsync_WithInvalidFilePath_ShouldThrowException()
    {
        // Arrange
        var eventConfig = TestDataFactory.CreateDefaultEventConfiguration();
        var invalidPath = "invalid/path/that/does/not/exist/file.yaml";

        // Act & Assert
        await Assert.ThrowsAsync<DirectoryNotFoundException>(() => 
            _service.SaveToYamlFileAsync(invalidPath, eventConfig));
    }

    #endregion

    #region 验证测试

    [Fact]
    public async Task ValidateYamlFileAsync_WithValidFile_ShouldReturnValidResult()
    {
        // Arrange
        var yamlContent = TestHelper.CreateTestLegacyConfigYaml();
        var testFile = TestHelper.CreateTempTestFile(yamlContent);
        _tempFiles.Add(testFile);

        // Act
        var result = await _service.ValidateYamlFileAsync(testFile);

        // Assert
        result.Should().NotBeNull();
        result.IsValid.Should().BeTrue();
        result.Errors.Should().BeEmpty();
        result.Warnings.Should().BeEmpty();
    }

    [Fact]
    public async Task ValidateYamlFileAsync_WithInvalidFile_ShouldReturnInvalidResult()
    {
        // Arrange
        var invalidYaml = "invalid: yaml: content: [unclosed";
        var testFile = TestHelper.CreateTempTestFile(invalidYaml);
        _tempFiles.Add(testFile);

        // Act
        var result = await _service.ValidateYamlFileAsync(testFile);

        // Assert
        result.Should().NotBeNull();
        result.IsValid.Should().BeFalse();
        result.Errors.Should().NotBeEmpty();
    }

    [Fact]
    public async Task ValidateYamlFileAsync_WithNonExistentFile_ShouldReturnInvalidResult()
    {
        // Arrange
        var nonExistentFile = "non-existent-file.yaml";

        // Act
        var result = await _service.ValidateYamlFileAsync(nonExistentFile);

        // Assert
        result.Should().NotBeNull();
        result.IsValid.Should().BeFalse();
        result.Errors.Should().Contain(error => error.Contains("文件不存在"));
    }

    [Fact]
    public async Task ValidateYamlFileAsync_WithMissingRequiredFields_ShouldReturnWarnings()
    {
        // Arrange
        var incompleteYaml = @"EventId: EV001001
EventName: 测试事件
# 缺少其他必需字段";
        var testFile = TestHelper.CreateTempTestFile(incompleteYaml);
        _tempFiles.Add(testFile);

        // Act
        var result = await _service.ValidateYamlFileAsync(testFile);

        // Assert
        result.Should().NotBeNull();
        // 基本的 YAML 解析应该成功，但可能有警告
        result.IsValid.Should().BeTrue(); // 基本解析成功
    }

    #endregion

    #region 序列化测试

    [Fact]
    public void SerializeToYamlString_WithValidConfiguration_ShouldReturnYamlString()
    {
        // Arrange
        var eventConfig = TestDataFactory.CreateDefaultEventConfiguration();

        // Act
        var result = _service.SerializeToYamlString(eventConfig);

        // Assert
        result.Should().NotBeNullOrEmpty();
        TestHelper.YamlContains(result, "EventId", eventConfig.EventId).Should().BeTrue();
        TestHelper.YamlContains(result, "EventName", eventConfig.EventName).Should().BeTrue();
        TestHelper.YamlContains(result, "EvaluationStrategy", eventConfig.EvaluationStrategy).Should().BeTrue();
        TestHelper.YamlContains(result, "Priority", eventConfig.Priority).Should().BeTrue();
    }

    [Fact]
    public void SerializeToYamlString_WithNullConfiguration_ShouldThrowArgumentNullException()
    {
        // Act & Assert
        Assert.Throws<ArgumentNullException>(() => 
            _service.SerializeToYamlString(null!));
    }

    [Fact]
    public void ParseFromYamlString_WithValidYaml_ShouldReturnEventConfiguration()
    {
        // Arrange
        var yamlContent = TestHelper.CreateTestLegacyConfigYaml();

        // Act
        var result = _service.ParseFromYamlString(yamlContent);

        // Assert
        result.Should().NotBeNull();
        result.EventId.Should().Be("EV001001");
        result.EventName.Should().Be("测试事件");
        result.EvaluationStrategy.Should().Be("BusinessOnly");
        result.Priority.Should().Be("P3");
    }

    [Fact]
    public void ParseFromYamlString_WithInvalidYaml_ShouldThrowException()
    {
        // Arrange
        var invalidYaml = "invalid: yaml: content: [unclosed";

        // Act & Assert
        Assert.Throws<Exception>(() => 
            _service.ParseFromYamlString(invalidYaml));
    }

    [Theory]
    [InlineData("")]
    [InlineData("   ")]
    [InlineData(null)]
    public void ParseFromYamlString_WithEmptyOrNullYaml_ShouldThrowArgumentException(string invalidYaml)
    {
        // Act & Assert
        Assert.Throws<ArgumentException>(() => 
            _service.ParseFromYamlString(invalidYaml));
    }

    #endregion

    #region 往返测试（序列化-反序列化）

    [Fact]
    public void SerializeAndParse_ShouldPreserveConfiguration()
    {
        // Arrange
        var originalConfig = TestDataFactory.CreateAIEventConfiguration();

        // Act
        var yamlString = _service.SerializeToYamlString(originalConfig);
        var parsedConfig = _service.ParseFromYamlString(yamlString);

        // Assert
        parsedConfig.Should().NotBeNull();
        parsedConfig.EventId.Should().Be(originalConfig.EventId);
        parsedConfig.EventName.Should().Be(originalConfig.EventName);
        parsedConfig.EvaluationStrategy.Should().Be(originalConfig.EvaluationStrategy);
        parsedConfig.Priority.Should().Be(originalConfig.Priority);
        parsedConfig.CommId.Should().Be(originalConfig.CommId);
        parsedConfig.PositionId.Should().Be(originalConfig.PositionId);
        parsedConfig.AIPrompt.Should().Be(originalConfig.AIPrompt);
        parsedConfig.AIAnalysisDelaySec.Should().Be(originalConfig.AIAnalysisDelaySec);
    }

    [Fact]
    public async Task SaveAndLoad_ShouldPreserveConfiguration()
    {
        // Arrange
        var originalConfig = TestDataFactory.CreateAIEventConfiguration();
        var testFile = Path.GetTempFileName();
        _tempFiles.Add(testFile);

        // Act
        await _service.SaveToYamlFileAsync(testFile, originalConfig);
        var loadedConfig = await _service.LoadFromYamlFileAsync(testFile);

        // Assert
        loadedConfig.Should().NotBeNull();
        loadedConfig.EventId.Should().Be(originalConfig.EventId);
        loadedConfig.EventName.Should().Be(originalConfig.EventName);
        loadedConfig.EvaluationStrategy.Should().Be(originalConfig.EvaluationStrategy);
        loadedConfig.Priority.Should().Be(originalConfig.Priority);
        loadedConfig.CommId.Should().Be(originalConfig.CommId);
        loadedConfig.PositionId.Should().Be(originalConfig.PositionId);
        loadedConfig.AIPrompt.Should().Be(originalConfig.AIPrompt);
        loadedConfig.AIAnalysisDelaySec.Should().Be(originalConfig.AIAnalysisDelaySec);
    }

    #endregion

    #region 复杂配置测试

    [Fact]
    public async Task LoadFromYamlFileAsync_WithComplexConfiguration_ShouldPreserveAllData()
    {
        // Arrange
        var complexConfig = TestDataFactory.CreateAIEventConfiguration();
        var testFile = Path.GetTempFileName();
        _tempFiles.Add(testFile);

        // 先保存复杂配置
        await _service.SaveToYamlFileAsync(testFile, complexConfig);

        // Act
        var loadedConfig = await _service.LoadFromYamlFileAsync(testFile);

        // Assert
        loadedConfig.Should().NotBeNull();
        loadedConfig.EventId.Should().Be(complexConfig.EventId);
        loadedConfig.EventName.Should().Be(complexConfig.EventName);
        loadedConfig.EvaluationStrategy.Should().Be(complexConfig.EvaluationStrategy);
        loadedConfig.AIPrompt.Should().Be(complexConfig.AIPrompt);
        loadedConfig.AIAnalysisDelaySec.Should().Be(complexConfig.AIAnalysisDelaySec);
        
        // 验证设备信号配置
        loadedConfig.DeviceSignal.Should().NotBeNull();
        loadedConfig.DeviceSignal.Topics.Should().BeEquivalentTo(complexConfig.DeviceSignal.Topics);
        loadedConfig.DeviceSignal.TriggerField.Should().Be(complexConfig.DeviceSignal.TriggerField);
        loadedConfig.DeviceSignal.HoldingTimeoutSec.Should().Be(complexConfig.DeviceSignal.HoldingTimeoutSec);
        
        // 验证规则配置
        loadedConfig.RuleConfiguration.Should().NotBeNull();
        loadedConfig.RuleConfiguration.AlarmConfig.Should().NotBeNull();
    }

    #endregion

    #region 错误处理测试

    [Fact]
    public async Task LoadFromYamlFileAsync_WithCorruptedFile_ShouldLogErrorAndThrow()
    {
        // Arrange
        var corruptedYaml = "EventId: EV001001\nEventName: 测试事件\n[invalid yaml structure";
        var testFile = TestHelper.CreateTempTestFile(corruptedYaml);
        _tempFiles.Add(testFile);

        // Act & Assert
        await Assert.ThrowsAsync<Exception>(() => 
            _service.LoadFromYamlFileAsync(testFile));

        // 验证日志记录了错误
        _mockLogger.Verify(
            x => x.Log(
                LogLevel.Error,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("加载配置文件失败")),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
            Times.Once);
    }

    [Fact]
    public async Task SaveToYamlFileAsync_WithReadOnlyFile_ShouldThrowException()
    {
        // Arrange
        var eventConfig = TestDataFactory.CreateDefaultEventConfiguration();
        var testFile = TestHelper.CreateTempTestFile("existing content");
        _tempFiles.Add(testFile);

        // 设置文件为只读
        File.SetAttributes(testFile, FileAttributes.ReadOnly);

        try
        {
            // Act & Assert
            await Assert.ThrowsAsync<UnauthorizedAccessException>(() => 
                _service.SaveToYamlFileAsync(testFile, eventConfig));
        }
        finally
        {
            // 清理：移除只读属性
            File.SetAttributes(testFile, FileAttributes.Normal);
        }
    }

    #endregion
}
