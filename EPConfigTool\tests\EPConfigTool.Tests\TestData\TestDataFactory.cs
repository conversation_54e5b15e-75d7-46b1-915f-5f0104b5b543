using EPConfigTool.Models;
using EventProcessor.Core.Models;

namespace EPConfigTool.Tests.TestData;

/// <summary>
/// 测试数据工厂类
/// 提供各种测试场景所需的配置数据
/// </summary>
public static class TestDataFactory
{
    /// <summary>
    /// 创建默认的事件配置
    /// </summary>
    public static EventConfiguration CreateDefaultEventConfiguration()
    {
        return new EventConfiguration
        {
            EventId = "EV001001",
            EventName = "测试事件",
            EvaluationStrategy = "BusinessOnly",
            Priority = "P3",
            CommId = "101013",
            PositionId = "P001",
            AlarmGracePeriodSeconds = 3,
            EnableAlarmCancellation = true,
            CorrelationTimeWindow = "minute",
            DeviceSignal = CreateDefaultDeviceSignalConfiguration(),
            RuleConfiguration = CreateDefaultRuleConfiguration()
        };
    }

    /// <summary>
    /// 创建带 AI 配置的事件配置
    /// </summary>
    public static EventConfiguration CreateAIEventConfiguration()
    {
        return new EventConfiguration
        {
            EventId = "EV001002",
            EventName = "AI测试事件",
            EvaluationStrategy = "AI",
            Priority = "P2",
            CommId = "101013",
            PositionId = "P002",
            AIPrompt = "分析车辆异常行为",
            AIAnalysisDelaySec = 5,
            AlarmGracePeriodSeconds = 5,
            EnableAlarmCancellation = true,
            CorrelationTimeWindow = "hour",
            DeviceSignal = CreateDefaultDeviceSignalConfiguration(),
            RuleConfiguration = CreateDefaultRuleConfiguration()
        };
    }

    /// <summary>
    /// 创建默认的设备信号配置
    /// </summary>
    public static DeviceSignalConfiguration CreateDefaultDeviceSignalConfiguration()
    {
        return new DeviceSignalConfiguration
        {
            Topics = new[] { "device/test/event" },
            TriggerField = "I2",
            TriggerValues = new Dictionary<string, string>
            {
                ["true"] = "0",
                ["false"] = "1"
            },
            HoldingTimeoutSec = 20
        };
    }

    /// <summary>
    /// 创建默认的规则配置
    /// </summary>
    public static RuleConfiguration CreateDefaultRuleConfiguration()
    {
        return new RuleConfiguration
        {
            BusinessRules = CreateDefaultBusinessRules(),
            ExclusionRules = CreateDefaultExclusionRules(),
            AIResultRules = CreateDefaultAIResultRules(),
            AlarmConfig = CreateDefaultAlarmConfiguration()
        };
    }

    /// <summary>
    /// 创建默认的业务规则
    /// </summary>
    public static BusinessRuleGroup[] CreateDefaultBusinessRules()
    {
        return new[]
        {
            new BusinessRuleGroup
            {
                SourceTopic = "business/data",
                LogicOperator = "AND",
                Conditions = new[]
                {
                    new Condition
                    {
                        FieldName = "status",
                        DataType = "string",
                        Operator = "equals",
                        Value = "active"
                    }
                }
            }
        };
    }

    /// <summary>
    /// 创建默认的排除规则
    /// </summary>
    public static ExclusionRuleGroup[] CreateDefaultExclusionRules()
    {
        return new[]
        {
            new ExclusionRuleGroup
            {
                SourceType = "MQTT",
                SourceTopic = "exclusion/data",
                LogicOperator = "OR",
                Conditions = new[]
                {
                    new Condition
                    {
                        FieldName = "maintenance",
                        DataType = "string",
                        Operator = "equals",
                        Value = "true"
                    }
                }
            }
        };
    }

    /// <summary>
    /// 创建默认的 AI 结果规则
    /// </summary>
    public static AIResultRuleGroup[] CreateDefaultAIResultRules()
    {
        return new[]
        {
            new AIResultRuleGroup
            {
                LogicOperator = "AND",
                Conditions = new[]
                {
                    new Condition
                    {
                        FieldName = "confidence",
                        DataType = "number",
                        Operator = ">=",
                        Value = "0.8"
                    }
                }
            }
        };
    }

    /// <summary>
    /// 创建默认的告警配置
    /// </summary>
    public static AlarmConfiguration CreateDefaultAlarmConfiguration()
    {
        return new AlarmConfiguration
        {
            Fields = new[]
            {
                new FieldMapping
                {
                    AlarmFieldName = "EventId",
                    SourceRuleType = "DeviceSignal",
                    SourceFieldName = "EventId"
                },
                new FieldMapping
                {
                    AlarmFieldName = "EventName",
                    SourceRuleType = "DeviceSignal",
                    SourceFieldName = "EventName"
                }
            }
        };
    }

    /// <summary>
    /// 创建默认的 MQTT 配置
    /// </summary>
    public static MqttConfiguration CreateDefaultMqttConfiguration()
    {
        return new MqttConfiguration
        {
            BrokerHost = "localhost",
            BrokerPort = 1883,
            ClientId = "EP_V4.1_Test",
            Username = "test_user",
            Password = "test_password",
            KeepAliveInterval = 60,
            ReconnectDelay = 5,
            QualityOfServiceLevel = 1
        };
    }

    /// <summary>
    /// 创建生产环境的 MQTT 配置
    /// </summary>
    public static MqttConfiguration CreateProductionMqttConfiguration()
    {
        return new MqttConfiguration
        {
            BrokerHost = "mq.bangdouni.com",
            BrokerPort = 1883,
            ClientId = "EP_V4.1_EV001001",
            Username = "bdn_event_processor",
            Password = "Bdn@2024",
            KeepAliveInterval = 60,
            ReconnectDelay = 5,
            QualityOfServiceLevel = 1
        };
    }

    /// <summary>
    /// 创建默认的错误处理配置
    /// </summary>
    public static ErrorHandlingConfiguration CreateDefaultErrorHandlingConfiguration()
    {
        return new ErrorHandlingConfiguration
        {
            ToleranceLevel = "Normal",
            RetryPolicy = new RetryPolicyConfiguration
            {
                MaxRetries = 3,
                RetryDelay = 1000,
                UseExponentialBackoff = true,
                MaxBackoffDelay = 30000
            },
            FallbackStrategy = new FallbackStrategyConfiguration
            {
                OnRuleFailure = "ContinueProcessing",
                OnAIFailure = "UseBusinessOnly",
                OnTimerFailure = "ImmediateAlarm",
                OnMqttFailure = "Retry"
            },
            Logging = new ErrorLoggingConfiguration
            {
                ErrorLogLevel = "Error",
                DetailedStackTrace = true,
                IncludeMessagePayload = false,
                EnablePerformanceMonitoring = true,
                ErrorRateThreshold = 0.1
            }
        };
    }

    /// <summary>
    /// 创建默认的日志配置
    /// </summary>
    public static LoggingConfiguration CreateDefaultLoggingConfiguration()
    {
        return new LoggingConfiguration
        {
            LogLevel = new LogLevelConfiguration
            {
                Default = "Information",
                Microsoft = "Warning",
                MicrosoftHostingLifetime = "Information",
                EventProcessor = "Debug"
            }
        };
    }

    /// <summary>
    /// 创建默认的 Serilog 配置
    /// </summary>
    public static SerilogConfiguration CreateDefaultSerilogConfiguration()
    {
        return new SerilogConfiguration
        {
            Using = new[] { "Serilog.Sinks.Console", "Serilog.Sinks.File" },
            MinimumLevel = new SerilogMinimumLevelConfiguration
            {
                Default = "Information",
                Override = new Dictionary<string, string>
                {
                    ["Microsoft"] = "Warning",
                    ["System"] = "Warning",
                    ["EventProcessor"] = "Debug"
                }
            },
            WriteTo = new[]
            {
                new SerilogWriteToConfiguration
                {
                    Name = "Console",
                    Args = new Dictionary<string, object>
                    {
                        ["outputTemplate"] = "[{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} {Level:u3}] {Message:lj}{NewLine}{Exception}"
                    }
                },
                new SerilogWriteToConfiguration
                {
                    Name = "File",
                    Args = new Dictionary<string, object>
                    {
                        ["path"] = "logs/test-.log",
                        ["rollingInterval"] = "Day",
                        ["retainedFileCountLimit"] = 30
                    }
                }
            },
            Enrich = new[] { "FromLogContext", "WithMachineName", "WithThreadId" }
        };
    }

    /// <summary>
    /// 创建完整的统一配置
    /// </summary>
    public static UnifiedConfiguration CreateDefaultUnifiedConfiguration()
    {
        return new UnifiedConfiguration
        {
            Logging = CreateDefaultLoggingConfiguration(),
            Serilog = CreateDefaultSerilogConfiguration(),
            EventProcessor = CreateDefaultEventConfiguration(),
            Mqtt = CreateDefaultMqttConfiguration(),
            ErrorHandling = CreateDefaultErrorHandlingConfiguration()
        };
    }

    /// <summary>
    /// 创建带 AI 的统一配置
    /// </summary>
    public static UnifiedConfiguration CreateAIUnifiedConfiguration()
    {
        return new UnifiedConfiguration
        {
            Logging = CreateDefaultLoggingConfiguration(),
            Serilog = CreateDefaultSerilogConfiguration(),
            EventProcessor = CreateAIEventConfiguration(),
            Mqtt = CreateProductionMqttConfiguration(),
            ErrorHandling = CreateDefaultErrorHandlingConfiguration()
        };
    }
}
