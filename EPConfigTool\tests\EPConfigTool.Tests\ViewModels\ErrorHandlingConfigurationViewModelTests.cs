using EPConfigTool.Services;
using EPConfigTool.Tests.Helpers;
using EPConfigTool.Tests.TestData;
using EPConfigTool.ViewModels;
using EventProcessor.Core.Models;
using FluentAssertions;
using Moq;
using Xunit;

namespace EPConfigTool.Tests.ViewModels;

/// <summary>
/// ErrorHandlingConfigurationViewModel 单元测试
/// 测试错误处理配置视图模型的策略配置和验证逻辑
/// </summary>
public class ErrorHandlingConfigurationViewModelTests
{
    private readonly Mock<IHelpInfoService> _mockHelpInfoService;
    private readonly ErrorHandlingConfigurationViewModel _viewModel;

    public ErrorHandlingConfigurationViewModelTests()
    {
        _mockHelpInfoService = TestHelper.CreateMockHelpInfoService();
        _viewModel = new ErrorHandlingConfigurationViewModel(_mockHelpInfoService.Object);
    }

    #region 初始化测试

    [Fact]
    public void Constructor_ShouldInitializeWithDefaultValues()
    {
        // Assert
        _viewModel.ToleranceLevel.Should().Be("Normal");
        _viewModel.MaxRetries.Should().Be(3);
        _viewModel.RetryDelay.Should().Be(1000);
        _viewModel.UseExponentialBackoff.Should().BeTrue();
        _viewModel.MaxBackoffDelay.Should().Be(30000);
        _viewModel.OnRuleFailure.Should().Be("ContinueProcessing");
        _viewModel.OnAIFailure.Should().Be("UseBusinessOnly");
        _viewModel.OnTimerFailure.Should().Be("ImmediateAlarm");
        _viewModel.OnMqttFailure.Should().Be("Retry");
        _viewModel.ErrorLogLevel.Should().Be("Error");
        _viewModel.DetailedStackTrace.Should().BeTrue();
        _viewModel.IncludeMessagePayload.Should().BeFalse();
        _viewModel.EnablePerformanceMonitoring.Should().BeTrue();
        _viewModel.ErrorRateThreshold.Should().Be(0.1);
    }

    [Fact]
    public void Constructor_ShouldInitializeCollections()
    {
        // Assert
        _viewModel.ToleranceLevels.Should().Contain(new[] { "Strict", "Normal", "Lenient" });
        _viewModel.RuleFailureStrategies.Should().Contain(new[] { "StopProcessing", "ContinueProcessing" });
        _viewModel.AIFailureStrategies.Should().Contain(new[] { "SkipEvent", "UseBusinessOnly", "ForceAlarm" });
        _viewModel.TimerFailureStrategies.Should().Contain(new[] { "ImmediateAlarm", "SkipAlarm" });
        _viewModel.MqttFailureStrategies.Should().Contain(new[] { "Retry", "Shutdown", "ContinueOffline" });
        _viewModel.LogLevels.Should().Contain(new[] { "Trace", "Debug", "Information", "Warning", "Error", "Critical" });
    }

    #endregion

    #region 属性绑定测试

    [Fact]
    public void ToleranceLevel_WhenChanged_ShouldUpdateProperty()
    {
        // Arrange
        var newLevel = "Strict";

        // Act
        _viewModel.ToleranceLevel = newLevel;

        // Assert
        _viewModel.ToleranceLevel.Should().Be(newLevel);
    }

    [Fact]
    public void MaxRetries_WhenChanged_ShouldUpdateProperty()
    {
        // Arrange
        var newMaxRetries = 5;

        // Act
        _viewModel.MaxRetries = newMaxRetries;

        // Assert
        _viewModel.MaxRetries.Should().Be(newMaxRetries);
    }

    [Fact]
    public void RetryDelay_WhenChanged_ShouldUpdateProperty()
    {
        // Arrange
        var newDelay = 2000;

        // Act
        _viewModel.RetryDelay = newDelay;

        // Assert
        _viewModel.RetryDelay.Should().Be(newDelay);
    }

    [Fact]
    public void UseExponentialBackoff_WhenChanged_ShouldUpdateProperty()
    {
        // Arrange
        var newValue = false;

        // Act
        _viewModel.UseExponentialBackoff = newValue;

        // Assert
        _viewModel.UseExponentialBackoff.Should().Be(newValue);
    }

    [Fact]
    public void MaxBackoffDelay_WhenChanged_ShouldUpdateProperty()
    {
        // Arrange
        var newDelay = 60000;

        // Act
        _viewModel.MaxBackoffDelay = newDelay;

        // Assert
        _viewModel.MaxBackoffDelay.Should().Be(newDelay);
    }

    [Fact]
    public void OnRuleFailure_WhenChanged_ShouldUpdateProperty()
    {
        // Arrange
        var newStrategy = "StopProcessing";

        // Act
        _viewModel.OnRuleFailure = newStrategy;

        // Assert
        _viewModel.OnRuleFailure.Should().Be(newStrategy);
    }

    [Fact]
    public void OnAIFailure_WhenChanged_ShouldUpdateProperty()
    {
        // Arrange
        var newStrategy = "ForceAlarm";

        // Act
        _viewModel.OnAIFailure = newStrategy;

        // Assert
        _viewModel.OnAIFailure.Should().Be(newStrategy);
    }

    [Fact]
    public void OnTimerFailure_WhenChanged_ShouldUpdateProperty()
    {
        // Arrange
        var newStrategy = "SkipAlarm";

        // Act
        _viewModel.OnTimerFailure = newStrategy;

        // Assert
        _viewModel.OnTimerFailure.Should().Be(newStrategy);
    }

    [Fact]
    public void OnMqttFailure_WhenChanged_ShouldUpdateProperty()
    {
        // Arrange
        var newStrategy = "Shutdown";

        // Act
        _viewModel.OnMqttFailure = newStrategy;

        // Assert
        _viewModel.OnMqttFailure.Should().Be(newStrategy);
    }

    [Fact]
    public void ErrorLogLevel_WhenChanged_ShouldUpdateProperty()
    {
        // Arrange
        var newLevel = "Critical";

        // Act
        _viewModel.ErrorLogLevel = newLevel;

        // Assert
        _viewModel.ErrorLogLevel.Should().Be(newLevel);
    }

    [Fact]
    public void DetailedStackTrace_WhenChanged_ShouldUpdateProperty()
    {
        // Arrange
        var newValue = false;

        // Act
        _viewModel.DetailedStackTrace = newValue;

        // Assert
        _viewModel.DetailedStackTrace.Should().Be(newValue);
    }

    [Fact]
    public void IncludeMessagePayload_WhenChanged_ShouldUpdateProperty()
    {
        // Arrange
        var newValue = true;

        // Act
        _viewModel.IncludeMessagePayload = newValue;

        // Assert
        _viewModel.IncludeMessagePayload.Should().Be(newValue);
    }

    [Fact]
    public void EnablePerformanceMonitoring_WhenChanged_ShouldUpdateProperty()
    {
        // Arrange
        var newValue = false;

        // Act
        _viewModel.EnablePerformanceMonitoring = newValue;

        // Assert
        _viewModel.EnablePerformanceMonitoring.Should().Be(newValue);
    }

    [Fact]
    public void ErrorRateThreshold_WhenChanged_ShouldUpdateProperty()
    {
        // Arrange
        var newThreshold = 0.05;

        // Act
        _viewModel.ErrorRateThreshold = newThreshold;

        // Assert
        _viewModel.ErrorRateThreshold.Should().Be(newThreshold);
    }

    #endregion

    #region 模型转换测试

    [Fact]
    public void LoadFromModel_ShouldUpdateAllProperties()
    {
        // Arrange
        var errorConfig = TestDataFactory.CreateDefaultErrorHandlingConfiguration() with
        {
            ToleranceLevel = "Strict",
            RetryPolicy = new RetryPolicyConfiguration
            {
                MaxRetries = 5,
                RetryDelay = 2000,
                UseExponentialBackoff = false,
                MaxBackoffDelay = 60000
            },
            FallbackStrategy = new FallbackStrategyConfiguration
            {
                OnRuleFailure = "StopProcessing",
                OnAIFailure = "ForceAlarm",
                OnTimerFailure = "SkipAlarm",
                OnMqttFailure = "Shutdown"
            },
            Logging = new ErrorLoggingConfiguration
            {
                ErrorLogLevel = "Critical",
                DetailedStackTrace = false,
                IncludeMessagePayload = true,
                EnablePerformanceMonitoring = false,
                ErrorRateThreshold = 0.05
            }
        };

        // Act
        _viewModel.LoadFromModel(errorConfig);

        // Assert
        _viewModel.ToleranceLevel.Should().Be("Strict");
        _viewModel.MaxRetries.Should().Be(5);
        _viewModel.RetryDelay.Should().Be(2000);
        _viewModel.UseExponentialBackoff.Should().BeFalse();
        _viewModel.MaxBackoffDelay.Should().Be(60000);
        _viewModel.OnRuleFailure.Should().Be("StopProcessing");
        _viewModel.OnAIFailure.Should().Be("ForceAlarm");
        _viewModel.OnTimerFailure.Should().Be("SkipAlarm");
        _viewModel.OnMqttFailure.Should().Be("Shutdown");
        _viewModel.ErrorLogLevel.Should().Be("Critical");
        _viewModel.DetailedStackTrace.Should().BeFalse();
        _viewModel.IncludeMessagePayload.Should().BeTrue();
        _viewModel.EnablePerformanceMonitoring.Should().BeFalse();
        _viewModel.ErrorRateThreshold.Should().Be(0.05);
    }

    [Fact]
    public void ToModel_ShouldReturnCorrectErrorHandlingConfiguration()
    {
        // Arrange
        _viewModel.ToleranceLevel = "Lenient";
        _viewModel.MaxRetries = 7;
        _viewModel.RetryDelay = 3000;
        _viewModel.UseExponentialBackoff = false;
        _viewModel.MaxBackoffDelay = 90000;
        _viewModel.OnRuleFailure = "StopProcessing";
        _viewModel.OnAIFailure = "SkipEvent";
        _viewModel.OnTimerFailure = "SkipAlarm";
        _viewModel.OnMqttFailure = "ContinueOffline";
        _viewModel.ErrorLogLevel = "Warning";
        _viewModel.DetailedStackTrace = false;
        _viewModel.IncludeMessagePayload = true;
        _viewModel.EnablePerformanceMonitoring = false;
        _viewModel.ErrorRateThreshold = 0.2;

        // Act
        var result = _viewModel.ToModel();

        // Assert
        result.ToleranceLevel.Should().Be("Lenient");
        result.RetryPolicy.MaxRetries.Should().Be(7);
        result.RetryPolicy.RetryDelay.Should().Be(3000);
        result.RetryPolicy.UseExponentialBackoff.Should().BeFalse();
        result.RetryPolicy.MaxBackoffDelay.Should().Be(90000);
        result.FallbackStrategy.OnRuleFailure.Should().Be("StopProcessing");
        result.FallbackStrategy.OnAIFailure.Should().Be("SkipEvent");
        result.FallbackStrategy.OnTimerFailure.Should().Be("SkipAlarm");
        result.FallbackStrategy.OnMqttFailure.Should().Be("ContinueOffline");
        result.Logging.ErrorLogLevel.Should().Be("Warning");
        result.Logging.DetailedStackTrace.Should().BeFalse();
        result.Logging.IncludeMessagePayload.Should().BeTrue();
        result.Logging.EnablePerformanceMonitoring.Should().BeFalse();
        result.Logging.ErrorRateThreshold.Should().Be(0.2);
    }

    #endregion

    #region 配置验证测试

    [Fact]
    public void Validate_WithValidConfiguration_ShouldReturnNoErrors()
    {
        // Arrange
        _viewModel.LoadFromModel(TestDataFactory.CreateDefaultErrorHandlingConfiguration());

        // Act
        var errors = _viewModel.Validate();

        // Assert
        errors.Should().BeEmpty();
    }

    [Fact]
    public void Validate_WithInvalidToleranceLevel_ShouldReturnError()
    {
        // Arrange
        _viewModel.ToleranceLevel = "Invalid";

        // Act
        var errors = _viewModel.Validate();

        // Assert
        errors.Should().Contain("错误容忍级别必须是 Strict、Normal 或 Lenient");
    }

    [Theory]
    [InlineData(-1)]
    [InlineData(11)]
    public void Validate_WithInvalidMaxRetries_ShouldReturnError(int invalidRetries)
    {
        // Arrange
        _viewModel.MaxRetries = invalidRetries;

        // Act
        var errors = _viewModel.Validate();

        // Assert
        errors.Should().Contain("最大重试次数必须在 0-10 之间");
    }

    [Theory]
    [InlineData(99)]
    [InlineData(60001)]
    public void Validate_WithInvalidRetryDelay_ShouldReturnError(int invalidDelay)
    {
        // Arrange
        _viewModel.RetryDelay = invalidDelay;

        // Act
        var errors = _viewModel.Validate();

        // Assert
        errors.Should().Contain("重试延迟必须在 100-60000 毫秒之间");
    }

    [Theory]
    [InlineData(999)]
    [InlineData(300001)]
    public void Validate_WithInvalidMaxBackoffDelay_ShouldReturnError(int invalidDelay)
    {
        // Arrange
        _viewModel.MaxBackoffDelay = invalidDelay;

        // Act
        var errors = _viewModel.Validate();

        // Assert
        errors.Should().Contain("最大退避延迟必须在 1000-300000 毫秒之间");
    }

    [Fact]
    public void Validate_WithInvalidRuleFailureStrategy_ShouldReturnError()
    {
        // Arrange
        _viewModel.OnRuleFailure = "Invalid";

        // Act
        var errors = _viewModel.Validate();

        // Assert
        errors.Should().Contain("规则失败策略必须是 StopProcessing 或 ContinueProcessing");
    }

    [Fact]
    public void Validate_WithInvalidAIFailureStrategy_ShouldReturnError()
    {
        // Arrange
        _viewModel.OnAIFailure = "Invalid";

        // Act
        var errors = _viewModel.Validate();

        // Assert
        errors.Should().Contain("AI失败策略必须是 SkipEvent、UseBusinessOnly 或 ForceAlarm");
    }

    [Fact]
    public void Validate_WithInvalidTimerFailureStrategy_ShouldReturnError()
    {
        // Arrange
        _viewModel.OnTimerFailure = "Invalid";

        // Act
        var errors = _viewModel.Validate();

        // Assert
        errors.Should().Contain("定时器失败策略必须是 ImmediateAlarm 或 SkipAlarm");
    }

    [Fact]
    public void Validate_WithInvalidMqttFailureStrategy_ShouldReturnError()
    {
        // Arrange
        _viewModel.OnMqttFailure = "Invalid";

        // Act
        var errors = _viewModel.Validate();

        // Assert
        errors.Should().Contain("MQTT失败策略必须是 Retry、Shutdown 或 ContinueOffline");
    }

    [Fact]
    public void Validate_WithInvalidErrorLogLevel_ShouldReturnError()
    {
        // Arrange
        _viewModel.ErrorLogLevel = "Invalid";

        // Act
        var errors = _viewModel.Validate();

        // Assert
        errors.Should().Contain("错误日志级别必须是有效的日志级别");
    }

    [Theory]
    [InlineData(-0.1)]
    [InlineData(1.1)]
    public void Validate_WithInvalidErrorRateThreshold_ShouldReturnError(double invalidThreshold)
    {
        // Arrange
        _viewModel.ErrorRateThreshold = invalidThreshold;

        // Act
        var errors = _viewModel.Validate();

        // Assert
        errors.Should().Contain("错误率阈值必须在 0.0-1.0 之间");
    }

    #endregion

    #region 重置功能测试

    [Fact]
    public void ResetToDefault_ShouldRestoreDefaultValues()
    {
        // Arrange
        _viewModel.ToleranceLevel = "Strict";
        _viewModel.MaxRetries = 10;
        _viewModel.RetryDelay = 5000;
        _viewModel.UseExponentialBackoff = false;
        _viewModel.MaxBackoffDelay = 60000;
        _viewModel.OnRuleFailure = "StopProcessing";
        _viewModel.OnAIFailure = "ForceAlarm";
        _viewModel.OnTimerFailure = "SkipAlarm";
        _viewModel.OnMqttFailure = "Shutdown";
        _viewModel.ErrorLogLevel = "Critical";
        _viewModel.DetailedStackTrace = false;
        _viewModel.IncludeMessagePayload = true;
        _viewModel.EnablePerformanceMonitoring = false;
        _viewModel.ErrorRateThreshold = 0.5;

        // Act
        _viewModel.ResetToDefault();

        // Assert
        _viewModel.ToleranceLevel.Should().Be("Normal");
        _viewModel.MaxRetries.Should().Be(3);
        _viewModel.RetryDelay.Should().Be(1000);
        _viewModel.UseExponentialBackoff.Should().BeTrue();
        _viewModel.MaxBackoffDelay.Should().Be(30000);
        _viewModel.OnRuleFailure.Should().Be("ContinueProcessing");
        _viewModel.OnAIFailure.Should().Be("UseBusinessOnly");
        _viewModel.OnTimerFailure.Should().Be("ImmediateAlarm");
        _viewModel.OnMqttFailure.Should().Be("Retry");
        _viewModel.ErrorLogLevel.Should().Be("Error");
        _viewModel.DetailedStackTrace.Should().BeTrue();
        _viewModel.IncludeMessagePayload.Should().BeFalse();
        _viewModel.EnablePerformanceMonitoring.Should().BeTrue();
        _viewModel.ErrorRateThreshold.Should().Be(0.1);
    }

    #endregion

    #region 帮助信息测试

    [Fact]
    public void UpdateHelpInfo_ShouldCallHelpInfoService()
    {
        // Arrange
        var helpKey = "ErrorHandling.ToleranceLevel";

        // Act
        _viewModel.UpdateHelpInfo(helpKey);

        // Assert
        _mockHelpInfoService.Verify(x => x.GetStatusBarInfo(helpKey), Times.Once);
    }

    [Theory]
    [InlineData("ErrorHandling.ToleranceLevel", "错误容忍级别")]
    [InlineData("ErrorHandling.MaxRetries", "最大重试次数")]
    [InlineData("ErrorHandling.RetryDelay", "重试延迟")]
    [InlineData("ErrorHandling.UseExponentialBackoff", "是否使用指数退避")]
    [InlineData("ErrorHandling.OnRuleFailure", "规则失败策略")]
    [InlineData("ErrorHandling.OnAIFailure", "AI失败策略")]
    [InlineData("ErrorHandling.ErrorLogLevel", "错误日志级别")]
    public void UpdateHelpInfo_WithKnownKeys_ShouldReturnCorrectHelpText(string helpKey, string expectedContent)
    {
        // Arrange
        var viewModelWithoutService = new ErrorHandlingConfigurationViewModel(null);

        // Act
        viewModelWithoutService.UpdateHelpInfo(helpKey);

        // Assert
        viewModelWithoutService.CurrentHelpInfo.Should().Contain(expectedContent);
    }

    #endregion
}
