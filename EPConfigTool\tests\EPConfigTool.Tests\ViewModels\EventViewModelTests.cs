using EPConfigTool.Services;
using EPConfigTool.Tests.Helpers;
using EPConfigTool.Tests.TestData;
using EPConfigTool.ViewModels;
using EventProcessor.Core.Models;
using FluentAssertions;
using Moq;
using Xunit;

namespace EPConfigTool.Tests.ViewModels;

/// <summary>
/// EventViewModel 单元测试
/// 测试事件配置视图模型的属性绑定、数据验证和模型转换功能
/// </summary>
public class EventViewModelTests
{
    private readonly Mock<IHelpInfoService> _mockHelpInfoService;
    private readonly EventConfiguration _testEventConfig;
    private readonly EventViewModel _viewModel;

    public EventViewModelTests()
    {
        _mockHelpInfoService = TestHelper.CreateMockHelpInfoService();
        _testEventConfig = TestDataFactory.CreateDefaultEventConfiguration();
        _viewModel = new EventViewModel(_testEventConfig, _mockHelpInfoService.Object);
    }

    #region 初始化测试

    [Fact]
    public void Constructor_WithEventConfiguration_ShouldInitializeProperties()
    {
        // Assert
        _viewModel.EventId.Should().Be(_testEventConfig.EventId);
        _viewModel.EventName.Should().Be(_testEventConfig.EventName);
        _viewModel.EvaluationStrategy.Should().Be(_testEventConfig.EvaluationStrategy);
        _viewModel.Priority.Should().Be(_testEventConfig.Priority);
        _viewModel.CommId.Should().Be(_testEventConfig.CommId);
        _viewModel.PositionId.Should().Be(_testEventConfig.PositionId);
        _viewModel.AlarmGracePeriodSeconds.Should().Be(_testEventConfig.AlarmGracePeriodSeconds);
        _viewModel.EnableAlarmCancellation.Should().Be(_testEventConfig.EnableAlarmCancellation);
        _viewModel.CorrelationTimeWindow.Should().Be(_testEventConfig.CorrelationTimeWindow);
    }

    [Fact]
    public void Constructor_WithAIConfiguration_ShouldInitializeAIProperties()
    {
        // Arrange
        var aiConfig = TestDataFactory.CreateAIEventConfiguration();
        var aiViewModel = new EventViewModel(aiConfig, _mockHelpInfoService.Object);

        // Assert
        aiViewModel.AIPrompt.Should().Be(aiConfig.AIPrompt);
        aiViewModel.AIAnalysisDelaySec.Should().Be(aiConfig.AIAnalysisDelaySec);
    }

    #endregion

    #region 属性绑定测试

    [Fact]
    public void EventId_WhenChanged_ShouldUpdateProperty()
    {
        // Arrange
        var newEventId = "EV002002";

        // Act
        _viewModel.EventId = newEventId;

        // Assert
        _viewModel.EventId.Should().Be(newEventId);
    }

    [Fact]
    public void EventName_WhenChanged_ShouldUpdateProperty()
    {
        // Arrange
        var newEventName = "新的测试事件";

        // Act
        _viewModel.EventName = newEventName;

        // Assert
        _viewModel.EventName.Should().Be(newEventName);
    }

    [Fact]
    public void EvaluationStrategy_WhenChanged_ShouldUpdateProperty()
    {
        // Arrange
        var newStrategy = "AI";

        // Act
        _viewModel.EvaluationStrategy = newStrategy;

        // Assert
        _viewModel.EvaluationStrategy.Should().Be(newStrategy);
    }

    [Fact]
    public void Priority_WhenChanged_ShouldUpdateProperty()
    {
        // Arrange
        var newPriority = "P1";

        // Act
        _viewModel.Priority = newPriority;

        // Assert
        _viewModel.Priority.Should().Be(newPriority);
    }

    [Fact]
    public void AlarmGracePeriodSeconds_WhenChanged_ShouldUpdateProperty()
    {
        // Arrange
        var newGracePeriod = 10;

        // Act
        _viewModel.AlarmGracePeriodSeconds = newGracePeriod;

        // Assert
        _viewModel.AlarmGracePeriodSeconds.Should().Be(newGracePeriod);
    }

    [Fact]
    public void EnableAlarmCancellation_WhenChanged_ShouldUpdateProperty()
    {
        // Arrange
        var newValue = false;

        // Act
        _viewModel.EnableAlarmCancellation = newValue;

        // Assert
        _viewModel.EnableAlarmCancellation.Should().Be(newValue);
    }

    [Fact]
    public void CorrelationTimeWindow_WhenChanged_ShouldUpdateProperty()
    {
        // Arrange
        var newTimeWindow = "hour";

        // Act
        _viewModel.CorrelationTimeWindow = newTimeWindow;

        // Assert
        _viewModel.CorrelationTimeWindow.Should().Be(newTimeWindow);
    }

    [Fact]
    public void CustomTimeWindowMinutes_WhenChanged_ShouldUpdateProperty()
    {
        // Arrange
        var newCustomWindow = 30;

        // Act
        _viewModel.CustomTimeWindowMinutes = newCustomWindow;

        // Assert
        _viewModel.CustomTimeWindowMinutes.Should().Be(newCustomWindow);
    }

    #endregion

    #region AI 配置测试

    [Fact]
    public void AIPrompt_WhenChanged_ShouldUpdateProperty()
    {
        // Arrange
        var newPrompt = "新的AI提示词";

        // Act
        _viewModel.AIPrompt = newPrompt;

        // Assert
        _viewModel.AIPrompt.Should().Be(newPrompt);
    }

    [Fact]
    public void AIAnalysisDelaySec_WhenChanged_ShouldUpdateProperty()
    {
        // Arrange
        var newDelay = 10;

        // Act
        _viewModel.AIAnalysisDelaySec = newDelay;

        // Assert
        _viewModel.AIAnalysisDelaySec.Should().Be(newDelay);
    }

    [Fact]
    public void ImageCropCoordinates_WhenChanged_ShouldUpdateProperty()
    {
        // Arrange
        var newCoordinates = "100,200,300,400";

        // Act
        _viewModel.ImageCropCoordinates = newCoordinates;

        // Assert
        _viewModel.ImageCropCoordinates.Should().Be(newCoordinates);
    }

    #endregion

    #region 模型转换测试

    [Fact]
    public void ToModel_ShouldReturnCorrectEventConfiguration()
    {
        // Arrange
        _viewModel.EventId = "EV003003";
        _viewModel.EventName = "转换测试事件";
        _viewModel.EvaluationStrategy = "AIAndBusiness";
        _viewModel.Priority = "P2";
        _viewModel.CommId = "202024";
        _viewModel.PositionId = "P003";
        _viewModel.AlarmGracePeriodSeconds = 5;
        _viewModel.EnableAlarmCancellation = false;
        _viewModel.CorrelationTimeWindow = "custom";
        _viewModel.CustomTimeWindowMinutes = 45;
        _viewModel.AIPrompt = "测试AI提示";
        _viewModel.AIAnalysisDelaySec = 8;

        // Act
        var result = _viewModel.ToModel();

        // Assert
        result.EventId.Should().Be("EV003003");
        result.EventName.Should().Be("转换测试事件");
        result.EvaluationStrategy.Should().Be("AIAndBusiness");
        result.Priority.Should().Be("P2");
        result.CommId.Should().Be("202024");
        result.PositionId.Should().Be("P003");
        result.AlarmGracePeriodSeconds.Should().Be(5);
        result.EnableAlarmCancellation.Should().BeFalse();
        result.CorrelationTimeWindow.Should().Be("custom");
        result.CustomTimeWindowMinutes.Should().Be(45);
        result.AIPrompt.Should().Be("测试AI提示");
        result.AIAnalysisDelaySec.Should().Be(8);
    }

    [Fact]
    public void LoadFromModel_ShouldUpdateAllProperties()
    {
        // Arrange
        var newConfig = new EventConfiguration
        {
            EventId = "EV004004",
            EventName = "加载测试事件",
            EvaluationStrategy = "AI",
            Priority = "P1",
            CommId = "303035",
            PositionId = "P004",
            AlarmGracePeriodSeconds = 7,
            EnableAlarmCancellation = true,
            CorrelationTimeWindow = "hour",
            CustomTimeWindowMinutes = 60,
            AIPrompt = "加载测试AI提示",
            AIAnalysisDelaySec = 12,
            ImageCropCoordinates = "50,100,150,200",
            DeviceSignal = TestDataFactory.CreateDefaultDeviceSignalConfiguration(),
            RuleConfiguration = TestDataFactory.CreateDefaultRuleConfiguration()
        };

        // Act
        _viewModel.LoadFromModel(newConfig);

        // Assert
        _viewModel.EventId.Should().Be("EV004004");
        _viewModel.EventName.Should().Be("加载测试事件");
        _viewModel.EvaluationStrategy.Should().Be("AI");
        _viewModel.Priority.Should().Be("P1");
        _viewModel.CommId.Should().Be("303035");
        _viewModel.PositionId.Should().Be("P004");
        _viewModel.AlarmGracePeriodSeconds.Should().Be(7);
        _viewModel.EnableAlarmCancellation.Should().BeTrue();
        _viewModel.CorrelationTimeWindow.Should().Be("hour");
        _viewModel.CustomTimeWindowMinutes.Should().Be(60);
        _viewModel.AIPrompt.Should().Be("加载测试AI提示");
        _viewModel.AIAnalysisDelaySec.Should().Be(12);
        _viewModel.ImageCropCoordinates.Should().Be("50,100,150,200");
    }

    #endregion

    #region 数据验证测试

    [Theory]
    [InlineData("")]
    [InlineData("   ")]
    [InlineData(null)]
    public void EventId_WithInvalidValue_ShouldHaveValidationError(string invalidEventId)
    {
        // Act
        _viewModel.EventId = invalidEventId;
        var validationResult = _viewModel.ValidateConfiguration();

        // Assert
        validationResult.IsValid.Should().BeFalse();
        validationResult.Errors.Should().Contain(error => error.Contains("事件ID"));
    }

    [Theory]
    [InlineData("EV001001")]
    [InlineData("EV999999")]
    public void EventId_WithValidValue_ShouldNotHaveValidationError(string validEventId)
    {
        // Act
        _viewModel.EventId = validEventId;
        _viewModel.EventName = "Valid Event Name"; // 确保其他必填字段有效
        var validationResult = _viewModel.ValidateConfiguration();

        // Assert
        validationResult.IsValid.Should().BeTrue();
    }

    [Theory]
    [InlineData("")]
    [InlineData("   ")]
    [InlineData(null)]
    public void EventName_WithInvalidValue_ShouldHaveValidationError(string invalidEventName)
    {
        // Act
        _viewModel.EventName = invalidEventName;
        var validationResult = _viewModel.ValidateConfiguration();

        // Assert
        validationResult.IsValid.Should().BeFalse();
        validationResult.Errors.Should().Contain(error => error.Contains("事件名称"));
    }

    [Theory]
    [InlineData("测试事件")]
    [InlineData("Test Event")]
    [InlineData("事件123")]
    public void EventName_WithValidValue_ShouldNotHaveValidationError(string validEventName)
    {
        // Act
        _viewModel.EventId = "EV001001"; // 确保其他必填字段有效
        _viewModel.EventName = validEventName;
        var validationResult = _viewModel.ValidateConfiguration();

        // Assert
        validationResult.IsValid.Should().BeTrue();
    }

    [Theory]
    [InlineData(-1)]
    [InlineData(61)]
    [InlineData(100)]
    public void AlarmGracePeriodSeconds_WithInvalidValue_ShouldHaveValidationError(int invalidValue)
    {
        // Act
        _viewModel.AlarmGracePeriodSeconds = invalidValue;
        var validationResult = _viewModel.ValidateConfiguration();

        // Assert
        validationResult.IsValid.Should().BeFalse();
        validationResult.Errors.Should().Contain(error => error.Contains("告警静默期"));
    }

    [Theory]
    [InlineData(0)]
    [InlineData(30)]
    [InlineData(60)]
    public void AlarmGracePeriodSeconds_WithValidValue_ShouldNotHaveValidationError(int validValue)
    {
        // Act
        _viewModel.EventId = "EV001001"; // 确保其他必填字段有效
        _viewModel.EventName = "Valid Event Name";
        _viewModel.AlarmGracePeriodSeconds = validValue;
        var validationResult = _viewModel.ValidateConfiguration();

        // Assert
        validationResult.IsValid.Should().BeTrue();
    }

    #endregion

    #region 帮助信息测试

    [Fact]
    public void UpdateHelpInfo_ShouldCallHelpInfoService()
    {
        // Arrange
        var helpKey = "EventId";

        // Act
        _viewModel.UpdateHelpInfo(helpKey);

        // Assert
        _mockHelpInfoService.Verify(x => x.GetStatusBarInfo(helpKey), Times.Once);
    }

    [Fact]
    public void UpdateHelpInfo_ShouldUpdateCurrentHelpInfo()
    {
        // Arrange
        var helpKey = "EventName";
        var expectedHelpInfo = $"帮助信息: {helpKey}";

        // Act
        _viewModel.UpdateHelpInfo(helpKey);

        // Assert
        _viewModel.CurrentHelpInfo.Should().Be(expectedHelpInfo);
    }

    #endregion
}
