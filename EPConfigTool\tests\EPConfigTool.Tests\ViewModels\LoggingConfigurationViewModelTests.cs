using EPConfigTool.Services;
using EPConfigTool.Tests.Helpers;
using EPConfigTool.Tests.TestData;
using EPConfigTool.ViewModels;
using FluentAssertions;
using Moq;
using Xunit;

namespace EPConfigTool.Tests.ViewModels;

/// <summary>
/// LoggingConfigurationViewModel 单元测试
/// 测试日志配置视图模型的设置和环境切换功能
/// </summary>
public class LoggingConfigurationViewModelTests
{
    private readonly Mock<IHelpInfoService> _mockHelpInfoService;
    private readonly LoggingConfigurationViewModel _viewModel;

    public LoggingConfigurationViewModelTests()
    {
        _mockHelpInfoService = TestHelper.CreateMockHelpInfoService();
        _viewModel = new LoggingConfigurationViewModel(_mockHelpInfoService.Object);
    }

    #region 初始化测试

    [Fact]
    public void Constructor_ShouldInitializeWithDefaultValues()
    {
        // Assert
        _viewModel.DefaultLogLevel.Should().Be("Information");
        _viewModel.MicrosoftLogLevel.Should().Be("Warning");
        _viewModel.MicrosoftHostingLifetimeLogLevel.Should().Be("Information");
        _viewModel.EventProcessorLogLevel.Should().Be("Debug");
    }

    [Fact]
    public void Constructor_ShouldInitializeLogLevelsCollection()
    {
        // Assert
        _viewModel.LogLevels.Should().Contain(new[] { "Trace", "Debug", "Information", "Warning", "Error", "Critical" });
    }

    [Fact]
    public void Constructor_ShouldSetDefaultHelpInfo()
    {
        // Assert
        _viewModel.CurrentHelpInfo.Should().Be("日志配置。定义应用程序各组件的日志记录级别。");
    }

    #endregion

    #region 属性绑定测试

    [Fact]
    public void DefaultLogLevel_WhenChanged_ShouldUpdateProperty()
    {
        // Arrange
        var newLevel = "Debug";

        // Act
        _viewModel.DefaultLogLevel = newLevel;

        // Assert
        _viewModel.DefaultLogLevel.Should().Be(newLevel);
    }

    [Fact]
    public void MicrosoftLogLevel_WhenChanged_ShouldUpdateProperty()
    {
        // Arrange
        var newLevel = "Error";

        // Act
        _viewModel.MicrosoftLogLevel = newLevel;

        // Assert
        _viewModel.MicrosoftLogLevel.Should().Be(newLevel);
    }

    [Fact]
    public void MicrosoftHostingLifetimeLogLevel_WhenChanged_ShouldUpdateProperty()
    {
        // Arrange
        var newLevel = "Warning";

        // Act
        _viewModel.MicrosoftHostingLifetimeLogLevel = newLevel;

        // Assert
        _viewModel.MicrosoftHostingLifetimeLogLevel.Should().Be(newLevel);
    }

    [Fact]
    public void EventProcessorLogLevel_WhenChanged_ShouldUpdateProperty()
    {
        // Arrange
        var newLevel = "Trace";

        // Act
        _viewModel.EventProcessorLogLevel = newLevel;

        // Assert
        _viewModel.EventProcessorLogLevel.Should().Be(newLevel);
    }

    #endregion

    #region 模型转换测试

    [Fact]
    public void LoadFromModel_ShouldUpdateAllProperties()
    {
        // Arrange
        var loggingConfig = TestDataFactory.CreateDefaultLoggingConfiguration() with
        {
            LogLevel = TestDataFactory.CreateDefaultLoggingConfiguration().LogLevel with
            {
                Default = "Debug",
                Microsoft = "Error",
                MicrosoftHostingLifetime = "Warning",
                EventProcessor = "Trace"
            }
        };

        // Act
        _viewModel.LoadFromModel(loggingConfig);

        // Assert
        _viewModel.DefaultLogLevel.Should().Be("Debug");
        _viewModel.MicrosoftLogLevel.Should().Be("Error");
        _viewModel.MicrosoftHostingLifetimeLogLevel.Should().Be("Warning");
        _viewModel.EventProcessorLogLevel.Should().Be("Trace");
    }

    [Fact]
    public void LoadFromModel_WithNullLogLevel_ShouldHandleGracefully()
    {
        // Arrange
        var loggingConfig = TestDataFactory.CreateDefaultLoggingConfiguration() with
        {
            LogLevel = null
        };

        // Act
        var action = () => _viewModel.LoadFromModel(loggingConfig);

        // Assert
        action.Should().NotThrow();
    }

    [Fact]
    public void ToModel_ShouldReturnCorrectLoggingConfiguration()
    {
        // Arrange
        _viewModel.DefaultLogLevel = "Debug";
        _viewModel.MicrosoftLogLevel = "Error";
        _viewModel.MicrosoftHostingLifetimeLogLevel = "Warning";
        _viewModel.EventProcessorLogLevel = "Trace";

        // Act
        var result = _viewModel.ToModel();

        // Assert
        result.Should().NotBeNull();
        result.LogLevel.Should().NotBeNull();
        result.LogLevel.Default.Should().Be("Debug");
        result.LogLevel.Microsoft.Should().Be("Error");
        result.LogLevel.MicrosoftHostingLifetime.Should().Be("Warning");
        result.LogLevel.EventProcessor.Should().Be("Trace");
    }

    #endregion

    #region 配置验证测试

    [Fact]
    public void Validate_WithValidConfiguration_ShouldReturnNoErrors()
    {
        // Arrange
        _viewModel.LoadFromModel(TestDataFactory.CreateDefaultLoggingConfiguration());

        // Act
        var errors = _viewModel.Validate();

        // Assert
        errors.Should().BeEmpty();
    }

    [Fact]
    public void Validate_WithInvalidDefaultLogLevel_ShouldReturnError()
    {
        // Arrange
        _viewModel.DefaultLogLevel = "Invalid";

        // Act
        var errors = _viewModel.Validate();

        // Assert
        errors.Should().Contain("默认日志级别必须是有效的日志级别");
    }

    [Fact]
    public void Validate_WithInvalidMicrosoftLogLevel_ShouldReturnError()
    {
        // Arrange
        _viewModel.MicrosoftLogLevel = "Invalid";

        // Act
        var errors = _viewModel.Validate();

        // Assert
        errors.Should().Contain("Microsoft 日志级别必须是有效的日志级别");
    }

    [Fact]
    public void Validate_WithInvalidMicrosoftHostingLifetimeLogLevel_ShouldReturnError()
    {
        // Arrange
        _viewModel.MicrosoftHostingLifetimeLogLevel = "Invalid";

        // Act
        var errors = _viewModel.Validate();

        // Assert
        errors.Should().Contain("Microsoft.Hosting.Lifetime 日志级别必须是有效的日志级别");
    }

    [Fact]
    public void Validate_WithInvalidEventProcessorLogLevel_ShouldReturnError()
    {
        // Arrange
        _viewModel.EventProcessorLogLevel = "Invalid";

        // Act
        var errors = _viewModel.Validate();

        // Assert
        errors.Should().Contain("EventProcessor 日志级别必须是有效的日志级别");
    }

    #endregion

    #region 重置功能测试

    [Fact]
    public void ResetToDefault_ShouldRestoreDefaultValues()
    {
        // Arrange
        _viewModel.DefaultLogLevel = "Trace";
        _viewModel.MicrosoftLogLevel = "Critical";
        _viewModel.MicrosoftHostingLifetimeLogLevel = "Error";
        _viewModel.EventProcessorLogLevel = "Warning";

        // Act
        _viewModel.ResetToDefault();

        // Assert
        _viewModel.DefaultLogLevel.Should().Be("Information");
        _viewModel.MicrosoftLogLevel.Should().Be("Warning");
        _viewModel.MicrosoftHostingLifetimeLogLevel.Should().Be("Information");
        _viewModel.EventProcessorLogLevel.Should().Be("Debug");
    }

    #endregion

    #region 环境配置测试

    [Fact]
    public void SetDevelopmentConfiguration_ShouldSetDevelopmentLogLevels()
    {
        // Act
        _viewModel.SetDevelopmentConfiguration();

        // Assert
        _viewModel.DefaultLogLevel.Should().Be("Debug");
        _viewModel.MicrosoftLogLevel.Should().Be("Information");
        _viewModel.MicrosoftHostingLifetimeLogLevel.Should().Be("Information");
        _viewModel.EventProcessorLogLevel.Should().Be("Debug");
    }

    [Fact]
    public void SetProductionConfiguration_ShouldSetProductionLogLevels()
    {
        // Act
        _viewModel.SetProductionConfiguration();

        // Assert
        _viewModel.DefaultLogLevel.Should().Be("Information");
        _viewModel.MicrosoftLogLevel.Should().Be("Warning");
        _viewModel.MicrosoftHostingLifetimeLogLevel.Should().Be("Warning");
        _viewModel.EventProcessorLogLevel.Should().Be("Information");
    }

    [Fact]
    public void SetDebugConfiguration_ShouldSetDebugLogLevels()
    {
        // Act
        _viewModel.SetDebugConfiguration();

        // Assert
        _viewModel.DefaultLogLevel.Should().Be("Trace");
        _viewModel.MicrosoftLogLevel.Should().Be("Debug");
        _viewModel.MicrosoftHostingLifetimeLogLevel.Should().Be("Debug");
        _viewModel.EventProcessorLogLevel.Should().Be("Trace");
    }

    #endregion

    #region 帮助信息测试

    [Fact]
    public void UpdateHelpInfo_ShouldCallHelpInfoService()
    {
        // Arrange
        var helpKey = "Logging.Default";

        // Act
        _viewModel.UpdateHelpInfo(helpKey);

        // Assert
        _mockHelpInfoService.Verify(x => x.GetStatusBarInfo(helpKey), Times.Once);
    }

    [Theory]
    [InlineData("Logging.Default", "默认日志级别")]
    [InlineData("Logging.Microsoft", "Microsoft 组件日志级别")]
    [InlineData("Logging.MicrosoftHostingLifetime", "Microsoft.Hosting.Lifetime 日志级别")]
    [InlineData("Logging.EventProcessor", "EventProcessor 日志级别")]
    public void UpdateHelpInfo_WithKnownKeys_ShouldReturnCorrectHelpText(string helpKey, string expectedContent)
    {
        // Arrange
        var viewModelWithoutService = new LoggingConfigurationViewModel(null);

        // Act
        viewModelWithoutService.UpdateHelpInfo(helpKey);

        // Assert
        viewModelWithoutService.CurrentHelpInfo.Should().Contain(expectedContent);
    }

    #endregion
}
