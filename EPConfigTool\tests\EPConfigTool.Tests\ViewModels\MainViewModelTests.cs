using EPConfigTool.Models;
using EPConfigTool.Services;
using EPConfigTool.Tests.Helpers;
using EPConfigTool.Tests.TestData;
using EPConfigTool.ViewModels;
using EventProcessor.Core.Models;
using FluentAssertions;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;

namespace EPConfigTool.Tests.ViewModels;

/// <summary>
/// MainViewModel 单元测试
/// 测试主视图模型的配置加载、保存、验证和模式切换功能
/// </summary>
public class MainViewModelTests : IDisposable
{
    private readonly Mock<IYamlConfigurationService> _mockConfigService;
    private readonly Mock<IUnifiedConfigurationService> _mockUnifiedConfigService;
    private readonly Mock<IFileDialogService> _mockFileDialogService;
    private readonly Mock<IHelpInfoService> _mockHelpInfoService;
    private readonly Mock<ILogger<MainViewModel>> _mockLogger;
    private readonly MainViewModel _viewModel;
    private readonly List<string> _tempFiles;

    public MainViewModelTests()
    {
        _mockConfigService = new Mock<IYamlConfigurationService>();
        _mockUnifiedConfigService = new Mock<IUnifiedConfigurationService>();
        _mockFileDialogService = TestHelper.CreateMockFileDialogService();
        _mockHelpInfoService = TestHelper.CreateMockHelpInfoService();
        _mockLogger = TestHelper.CreateMockLogger<MainViewModel>();
        _tempFiles = new List<string>();

        _viewModel = new MainViewModel(
            _mockConfigService.Object,
            _mockUnifiedConfigService.Object,
            _mockFileDialogService.Object,
            _mockHelpInfoService.Object,
            _mockLogger.Object);
    }

    public void Dispose()
    {
        // 清理临时文件
        foreach (var file in _tempFiles)
        {
            TestHelper.CleanupTempFile(file);
        }
    }

    #region 初始化测试

    [Fact]
    public void Constructor_ShouldInitializeWithDefaultValues()
    {
        // Assert
        _viewModel.IsUnifiedMode.Should().BeTrue();
        _viewModel.IsLegacyMode.Should().BeFalse();
        _viewModel.HasConfiguration.Should().BeFalse();
        _viewModel.HasNoConfiguration.Should().BeTrue();
        _viewModel.CurrentEvent.Should().BeNull();
        _viewModel.CurrentUnifiedConfig.Should().BeNull();
    }

    [Fact]
    public void Commands_ShouldBeInitialized()
    {
        // Assert
        _viewModel.LoadCommand.Should().NotBeNull();
        _viewModel.SaveCommand.Should().NotBeNull();
        _viewModel.SaveAsCommand.Should().NotBeNull();
        _viewModel.NewCommand.Should().NotBeNull();
        _viewModel.ValidateCommand.Should().NotBeNull();
        _viewModel.SwitchToUnifiedModeCommand.Should().NotBeNull();
        _viewModel.SwitchToLegacyModeCommand.Should().NotBeNull();
        _viewModel.MigrateToUnifiedCommand.Should().NotBeNull();
    }

    #endregion

    #region 模式切换测试

    [Fact]
    public void SwitchToUnifiedMode_ShouldUpdateModeProperties()
    {
        // Arrange
        _viewModel.IsUnifiedMode = false;

        // Act
        _viewModel.SwitchToUnifiedModeCommand.Execute(null);

        // Assert
        _viewModel.IsUnifiedMode.Should().BeTrue();
        _viewModel.IsLegacyMode.Should().BeFalse();
    }

    [Fact]
    public void SwitchToLegacyMode_ShouldUpdateModeProperties()
    {
        // Arrange
        _viewModel.IsUnifiedMode = true;

        // Act
        _viewModel.SwitchToLegacyModeCommand.Execute(null);

        // Assert
        _viewModel.IsUnifiedMode.Should().BeFalse();
        _viewModel.IsLegacyMode.Should().BeTrue();
    }

    #endregion

    #region 新建配置测试

    [Fact]
    public void NewCommand_InUnifiedMode_ShouldCreateUnifiedConfiguration()
    {
        // Arrange
        _viewModel.IsUnifiedMode = true;

        // Act
        _viewModel.NewCommand.Execute(null);

        // Assert
        _viewModel.CurrentUnifiedConfig.Should().NotBeNull();
        _viewModel.CurrentEvent.Should().BeNull();
        _viewModel.HasConfiguration.Should().BeTrue();
        _viewModel.HasUnsavedChanges.Should().BeTrue();
    }

    [Fact]
    public void NewCommand_InLegacyMode_ShouldCreateEventConfiguration()
    {
        // Arrange
        _viewModel.IsUnifiedMode = false;

        // Act
        _viewModel.NewCommand.Execute(null);

        // Assert
        _viewModel.CurrentEvent.Should().NotBeNull();
        _viewModel.CurrentUnifiedConfig.Should().BeNull();
        _viewModel.HasConfiguration.Should().BeTrue();
        _viewModel.HasUnsavedChanges.Should().BeTrue();
    }

    #endregion

    #region 配置加载测试

    [Fact]
    public async Task LoadCommand_WithUnifiedConfigFile_ShouldLoadUnifiedConfiguration()
    {
        // Arrange
        var testFilePath = TestHelper.CreateTempTestFile(TestHelper.CreateTestUnifiedConfigYaml());
        _tempFiles.Add(testFilePath);

        var unifiedConfig = TestDataFactory.CreateDefaultUnifiedConfiguration();

        _mockFileDialogService.Setup(x => x.ShowOpenYamlFileDialog(It.IsAny<string>(), It.IsAny<string?>()))
            .Returns(testFilePath);
        _mockUnifiedConfigService.Setup(x => x.IsUnifiedConfigurationFileAsync(testFilePath))
            .ReturnsAsync(true);
        _mockUnifiedConfigService.Setup(x => x.LoadFromYamlFileAsync(testFilePath))
            .ReturnsAsync(unifiedConfig);

        // Act
        await _viewModel.LoadCommand.ExecuteAsync(null);

        // Assert
        _viewModel.IsUnifiedMode.Should().BeTrue();
        _viewModel.CurrentUnifiedConfig.Should().NotBeNull();
        _viewModel.CurrentEvent.Should().BeNull();
        _viewModel.HasConfiguration.Should().BeTrue();
        _viewModel.HasUnsavedChanges.Should().BeFalse();
    }

    [Fact]
    public async Task LoadCommand_WithLegacyConfigFile_ShouldLoadEventConfiguration()
    {
        // Arrange
        var testFilePath = TestHelper.CreateTempTestFile(TestHelper.CreateTestLegacyConfigYaml());
        _tempFiles.Add(testFilePath);

        var eventConfig = TestDataFactory.CreateDefaultEventConfiguration();

        _mockFileDialogService.Setup(x => x.ShowOpenYamlFileDialog(It.IsAny<string>(), It.IsAny<string?>()))
            .Returns(testFilePath);
        _mockUnifiedConfigService.Setup(x => x.IsUnifiedConfigurationFileAsync(testFilePath))
            .ReturnsAsync(false);
        _mockConfigService.Setup(x => x.LoadFromYamlFileAsync(testFilePath))
            .ReturnsAsync(eventConfig);

        // Act
        await _viewModel.LoadCommand.ExecuteAsync(null);

        // Assert
        _viewModel.IsUnifiedMode.Should().BeFalse();
        _viewModel.CurrentEvent.Should().NotBeNull();
        _viewModel.CurrentUnifiedConfig.Should().BeNull();
        _viewModel.HasConfiguration.Should().BeTrue();
        _viewModel.HasUnsavedChanges.Should().BeFalse();
    }

    [Fact]
    public async Task LoadCommand_WhenUserCancels_ShouldNotChangeState()
    {
        // Arrange
        _mockFileDialogService.Setup(x => x.ShowOpenYamlFileDialog(It.IsAny<string>(), It.IsAny<string?>()))
            .Returns((string?)null);

        var originalMode = _viewModel.IsUnifiedMode;
        var originalConfig = _viewModel.CurrentUnifiedConfig;
        var originalEvent = _viewModel.CurrentEvent;

        // Act
        await _viewModel.LoadCommand.ExecuteAsync(null);

        // Assert
        _viewModel.IsUnifiedMode.Should().Be(originalMode);
        _viewModel.CurrentUnifiedConfig.Should().Be(originalConfig);
        _viewModel.CurrentEvent.Should().Be(originalEvent);
    }

    #endregion

    #region 配置保存测试

    [Fact]
    public async Task SaveCommand_WithUnifiedConfig_ShouldSaveUnifiedConfiguration()
    {
        // Arrange
        var testFilePath = TestHelper.CreateTempTestFile("");
        _tempFiles.Add(testFilePath);

        _viewModel.IsUnifiedMode = true;
        _viewModel.NewCommand.Execute(null); // 创建配置
        _viewModel.CurrentFilePath = testFilePath;

        _mockUnifiedConfigService.Setup(x => x.SaveToYamlFileAsync(testFilePath, It.IsAny<UnifiedConfiguration>()))
            .Returns(Task.CompletedTask);

        // Act
        await _viewModel.SaveCommand.ExecuteAsync(null);

        // Assert
        _mockUnifiedConfigService.Verify(x => x.SaveToYamlFileAsync(testFilePath, It.IsAny<UnifiedConfiguration>()), Times.Once);
        _viewModel.HasUnsavedChanges.Should().BeFalse();
    }

    [Fact]
    public async Task SaveCommand_WithEventConfig_ShouldSaveEventConfiguration()
    {
        // Arrange
        var testFilePath = TestHelper.CreateTempTestFile("");
        _tempFiles.Add(testFilePath);

        _viewModel.IsUnifiedMode = false;
        _viewModel.NewCommand.Execute(null); // 创建配置
        _viewModel.CurrentFilePath = testFilePath;

        _mockConfigService.Setup(x => x.SaveToYamlFileAsync(testFilePath, It.IsAny<EventConfiguration>()))
            .Returns(Task.CompletedTask);

        // Act
        await _viewModel.SaveCommand.ExecuteAsync(null);

        // Assert
        _mockConfigService.Verify(x => x.SaveToYamlFileAsync(testFilePath, It.IsAny<EventConfiguration>()), Times.Once);
        _viewModel.HasUnsavedChanges.Should().BeFalse();
    }

    #endregion

    #region 配置验证测试

    [Fact]
    public async Task ValidateCommand_WithUnifiedConfig_ShouldValidateUnifiedConfiguration()
    {
        // Arrange
        _viewModel.IsUnifiedMode = true;
        _viewModel.NewCommand.Execute(null); // 创建配置

        var validationResult = new UnifiedConfigurationValidationResult
        {
            IsValid = true,
            Errors = new List<string>(),
            Warnings = new List<string>(),
            SectionErrors = new Dictionary<string, List<string>>()
        };

        _mockUnifiedConfigService.Setup(x => x.ValidateYamlFileAsync(It.IsAny<string>()))
            .ReturnsAsync(validationResult);

        // Act
        await _viewModel.ValidateCommand.ExecuteAsync(null);

        // Assert - 验证应该被调用（通过内存验证）
        _viewModel.Should().NotBeNull(); // 基本验证，实际验证逻辑在私有方法中
    }

    [Fact]
    public async Task ValidateCommand_WithEventConfig_ShouldValidateEventConfiguration()
    {
        // Arrange
        _viewModel.IsUnifiedMode = false;
        _viewModel.NewCommand.Execute(null); // 创建配置

        var validationResult = new YamlValidationResult
        {
            IsValid = true,
            Errors = new List<string>(),
            Warnings = new List<string>()
        };

        _mockConfigService.Setup(x => x.ValidateYamlFileAsync(It.IsAny<string>()))
            .ReturnsAsync(validationResult);

        // Act
        await _viewModel.ValidateCommand.ExecuteAsync(null);

        // Assert - 验证应该被调用（通过内存验证）
        _viewModel.Should().NotBeNull(); // 基本验证，实际验证逻辑在私有方法中
    }

    #endregion

    #region 命令可执行性测试

    [Fact]
    public void SaveCommand_WithoutConfiguration_ShouldNotBeExecutable()
    {
        // Arrange - 确保没有配置
        _viewModel.CurrentEvent = null;
        _viewModel.CurrentUnifiedConfig = null;

        // Act & Assert
        _viewModel.SaveCommand.CanExecute(null).Should().BeFalse();
    }

    [Fact]
    public void SaveCommand_WithConfiguration_ShouldBeExecutable()
    {
        // Arrange
        _viewModel.NewCommand.Execute(null); // 创建配置

        // Act & Assert
        _viewModel.SaveCommand.CanExecute(null).Should().BeTrue();
    }

    [Fact]
    public void ValidateCommand_WithoutConfiguration_ShouldNotBeExecutable()
    {
        // Arrange - 确保没有配置
        _viewModel.CurrentEvent = null;
        _viewModel.CurrentUnifiedConfig = null;

        // Act & Assert
        _viewModel.ValidateCommand.CanExecute(null).Should().BeFalse();
    }

    [Fact]
    public void ValidateCommand_WithConfiguration_ShouldBeExecutable()
    {
        // Arrange
        _viewModel.NewCommand.Execute(null); // 创建配置

        // Act & Assert
        _viewModel.ValidateCommand.CanExecute(null).Should().BeTrue();
    }

    [Fact]
    public void MigrateToUnifiedCommand_InUnifiedMode_ShouldNotBeExecutable()
    {
        // Arrange
        _viewModel.IsUnifiedMode = true;

        // Act & Assert
        _viewModel.MigrateToUnifiedCommand.CanExecute(null).Should().BeFalse();
    }

    [Fact]
    public void MigrateToUnifiedCommand_InLegacyModeWithConfig_ShouldBeExecutable()
    {
        // Arrange
        _viewModel.IsUnifiedMode = false;
        _viewModel.NewCommand.Execute(null); // 创建传统配置

        // Act & Assert
        _viewModel.MigrateToUnifiedCommand.CanExecute(null).Should().BeTrue();
    }

    #endregion
}
