using EPConfigTool.Services;
using EPConfigTool.Tests.Helpers;
using EPConfigTool.Tests.TestData;
using EPConfigTool.ViewModels;
using FluentAssertions;
using Moq;
using Xunit;

namespace EPConfigTool.Tests.ViewModels;

/// <summary>
/// MqttConfigurationViewModel 单元测试
/// 测试 MQTT 配置视图模型的属性验证、连接测试和快速配置功能
/// </summary>
public class MqttConfigurationViewModelTests
{
    private readonly Mock<IHelpInfoService> _mockHelpInfoService;
    private readonly MqttConfigurationViewModel _viewModel;

    public MqttConfigurationViewModelTests()
    {
        _mockHelpInfoService = TestHelper.CreateMockHelpInfoService();
        _viewModel = new MqttConfigurationViewModel(_mockHelpInfoService.Object);
    }

    #region 初始化测试

    [Fact]
    public void Constructor_ShouldInitializeWithDefaultValues()
    {
        // Assert
        _viewModel.BrokerHost.Should().Be("mq.bangdouni.com");
        _viewModel.BrokerPort.Should().Be(1883);
        _viewModel.ClientId.Should().Be("EP_V4.1_Default");
        _viewModel.Username.Should().Be("bdn_event_processor");
        _viewModel.Password.Should().Be("Bdn@2024");
        _viewModel.KeepAliveInterval.Should().Be(60);
        _viewModel.ReconnectDelay.Should().Be(5);
        _viewModel.QualityOfServiceLevel.Should().Be(1);
    }

    [Fact]
    public void Constructor_ShouldSetDefaultHelpInfo()
    {
        // Assert
        _viewModel.CurrentHelpInfo.Should().Be("MQTT 连接配置。配置 EventProcessor 连接到 MQTT 服务器的参数。");
    }

    #endregion

    #region 属性绑定测试

    [Fact]
    public void BrokerHost_WhenChanged_ShouldUpdateProperty()
    {
        // Arrange
        var newHost = "test.mqtt.com";

        // Act
        _viewModel.BrokerHost = newHost;

        // Assert
        _viewModel.BrokerHost.Should().Be(newHost);
    }

    [Fact]
    public void BrokerPort_WhenChanged_ShouldUpdateProperty()
    {
        // Arrange
        var newPort = 8883;

        // Act
        _viewModel.BrokerPort = newPort;

        // Assert
        _viewModel.BrokerPort.Should().Be(newPort);
    }

    [Fact]
    public void ClientId_WhenChanged_ShouldUpdateProperty()
    {
        // Arrange
        var newClientId = "EP_V4.1_TestClient";

        // Act
        _viewModel.ClientId = newClientId;

        // Assert
        _viewModel.ClientId.Should().Be(newClientId);
    }

    [Fact]
    public void Username_WhenChanged_ShouldUpdateProperty()
    {
        // Arrange
        var newUsername = "test_user";

        // Act
        _viewModel.Username = newUsername;

        // Assert
        _viewModel.Username.Should().Be(newUsername);
    }

    [Fact]
    public void Password_WhenChanged_ShouldUpdateProperty()
    {
        // Arrange
        var newPassword = "test_password";

        // Act
        _viewModel.Password = newPassword;

        // Assert
        _viewModel.Password.Should().Be(newPassword);
    }

    [Fact]
    public void KeepAliveInterval_WhenChanged_ShouldUpdateProperty()
    {
        // Arrange
        var newInterval = 120;

        // Act
        _viewModel.KeepAliveInterval = newInterval;

        // Assert
        _viewModel.KeepAliveInterval.Should().Be(newInterval);
    }

    [Fact]
    public void ReconnectDelay_WhenChanged_ShouldUpdateProperty()
    {
        // Arrange
        var newDelay = 10;

        // Act
        _viewModel.ReconnectDelay = newDelay;

        // Assert
        _viewModel.ReconnectDelay.Should().Be(newDelay);
    }

    [Fact]
    public void QualityOfServiceLevel_WhenChanged_ShouldUpdateProperty()
    {
        // Arrange
        var newQoS = 2;

        // Act
        _viewModel.QualityOfServiceLevel = newQoS;

        // Assert
        _viewModel.QualityOfServiceLevel.Should().Be(newQoS);
    }

    #endregion

    #region 模型转换测试

    [Fact]
    public void LoadFromModel_ShouldUpdateAllProperties()
    {
        // Arrange
        var mqttConfig = TestDataFactory.CreateProductionMqttConfiguration();

        // Act
        _viewModel.LoadFromModel(mqttConfig);

        // Assert
        _viewModel.BrokerHost.Should().Be(mqttConfig.BrokerHost);
        _viewModel.BrokerPort.Should().Be(mqttConfig.BrokerPort);
        _viewModel.ClientId.Should().Be(mqttConfig.ClientId);
        _viewModel.Username.Should().Be(mqttConfig.Username);
        _viewModel.Password.Should().Be(mqttConfig.Password);
        _viewModel.KeepAliveInterval.Should().Be(mqttConfig.KeepAliveInterval);
        _viewModel.ReconnectDelay.Should().Be(mqttConfig.ReconnectDelay);
        _viewModel.QualityOfServiceLevel.Should().Be(mqttConfig.QualityOfServiceLevel);
    }

    [Fact]
    public void LoadFromModel_WithNullCredentials_ShouldHandleGracefully()
    {
        // Arrange
        var mqttConfig = TestDataFactory.CreateDefaultMqttConfiguration() with
        {
            Username = null,
            Password = null
        };

        // Act
        _viewModel.LoadFromModel(mqttConfig);

        // Assert
        _viewModel.Username.Should().BeEmpty();
        _viewModel.Password.Should().BeEmpty();
    }

    [Fact]
    public void ToModel_ShouldReturnCorrectMqttConfiguration()
    {
        // Arrange
        _viewModel.BrokerHost = "custom.mqtt.com";
        _viewModel.BrokerPort = 8883;
        _viewModel.ClientId = "CustomClient";
        _viewModel.Username = "custom_user";
        _viewModel.Password = "custom_password";
        _viewModel.KeepAliveInterval = 90;
        _viewModel.ReconnectDelay = 3;
        _viewModel.QualityOfServiceLevel = 2;

        // Act
        var result = _viewModel.ToModel();

        // Assert
        result.BrokerHost.Should().Be("custom.mqtt.com");
        result.BrokerPort.Should().Be(8883);
        result.ClientId.Should().Be("CustomClient");
        result.Username.Should().Be("custom_user");
        result.Password.Should().Be("custom_password");
        result.KeepAliveInterval.Should().Be(90);
        result.ReconnectDelay.Should().Be(3);
        result.QualityOfServiceLevel.Should().Be(2);
    }

    [Fact]
    public void ToModel_WithEmptyCredentials_ShouldReturnNull()
    {
        // Arrange
        _viewModel.Username = "";
        _viewModel.Password = "";

        // Act
        var result = _viewModel.ToModel();

        // Assert
        result.Username.Should().BeNull();
        result.Password.Should().BeNull();
    }

    #endregion

    #region 配置验证测试

    [Fact]
    public void Validate_WithValidConfiguration_ShouldReturnNoErrors()
    {
        // Arrange
        _viewModel.LoadFromModel(TestDataFactory.CreateDefaultMqttConfiguration());

        // Act
        var errors = _viewModel.Validate();

        // Assert
        errors.Should().BeEmpty();
    }

    [Theory]
    [InlineData("")]
    [InlineData("   ")]
    [InlineData(null)]
    public void Validate_WithInvalidBrokerHost_ShouldReturnError(string invalidHost)
    {
        // Arrange
        _viewModel.BrokerHost = invalidHost;

        // Act
        var errors = _viewModel.Validate();

        // Assert
        errors.Should().Contain("MQTT 服务器地址不能为空");
    }

    [Theory]
    [InlineData(0)]
    [InlineData(-1)]
    [InlineData(65536)]
    public void Validate_WithInvalidBrokerPort_ShouldReturnError(int invalidPort)
    {
        // Arrange
        _viewModel.BrokerPort = invalidPort;

        // Act
        var errors = _viewModel.Validate();

        // Assert
        errors.Should().Contain("MQTT 服务器端口必须在 1-65535 之间");
    }

    [Theory]
    [InlineData("")]
    [InlineData("   ")]
    [InlineData(null)]
    public void Validate_WithInvalidClientId_ShouldReturnError(string invalidClientId)
    {
        // Arrange
        _viewModel.ClientId = invalidClientId;

        // Act
        var errors = _viewModel.Validate();

        // Assert
        errors.Should().Contain("MQTT 客户端ID不能为空");
    }

    [Theory]
    [InlineData(9)]
    [InlineData(3601)]
    public void Validate_WithInvalidKeepAliveInterval_ShouldReturnError(int invalidInterval)
    {
        // Arrange
        _viewModel.KeepAliveInterval = invalidInterval;

        // Act
        var errors = _viewModel.Validate();

        // Assert
        errors.Should().Contain("保活间隔必须在 10-3600 秒之间");
    }

    [Theory]
    [InlineData(0)]
    [InlineData(61)]
    public void Validate_WithInvalidReconnectDelay_ShouldReturnError(int invalidDelay)
    {
        // Arrange
        _viewModel.ReconnectDelay = invalidDelay;

        // Act
        var errors = _viewModel.Validate();

        // Assert
        errors.Should().Contain("重连延迟必须在 1-60 秒之间");
    }

    [Theory]
    [InlineData(-1)]
    [InlineData(3)]
    public void Validate_WithInvalidQoSLevel_ShouldReturnError(int invalidQoS)
    {
        // Arrange
        _viewModel.QualityOfServiceLevel = invalidQoS;

        // Act
        var errors = _viewModel.Validate();

        // Assert
        errors.Should().Contain("服务质量等级必须是 0、1 或 2");
    }

    #endregion

    #region 客户端ID生成测试

    [Fact]
    public void GenerateClientId_WithEventId_ShouldGenerateCorrectClientId()
    {
        // Arrange
        var eventId = "EV123456";

        // Act
        _viewModel.GenerateClientId(eventId);

        // Assert
        _viewModel.ClientId.Should().Be($"EP_V4.1_{eventId}");
    }

    [Theory]
    [InlineData("")]
    [InlineData(null)]
    public void GenerateClientId_WithInvalidEventId_ShouldNotChangeClientId(string invalidEventId)
    {
        // Arrange
        var originalClientId = _viewModel.ClientId;

        // Act
        _viewModel.GenerateClientId(invalidEventId);

        // Assert
        _viewModel.ClientId.Should().Be(originalClientId);
    }

    #endregion

    #region 重置功能测试

    [Fact]
    public void ResetToDefault_ShouldRestoreDefaultValues()
    {
        // Arrange
        _viewModel.BrokerHost = "custom.host.com";
        _viewModel.BrokerPort = 8883;
        _viewModel.ClientId = "CustomClient";
        _viewModel.Username = "custom_user";
        _viewModel.Password = "custom_password";
        _viewModel.KeepAliveInterval = 120;
        _viewModel.ReconnectDelay = 10;
        _viewModel.QualityOfServiceLevel = 2;

        // Act
        _viewModel.ResetToDefault();

        // Assert
        _viewModel.BrokerHost.Should().Be("mq.bangdouni.com");
        _viewModel.BrokerPort.Should().Be(1883);
        _viewModel.ClientId.Should().Be("EP_V4.1_Default");
        _viewModel.Username.Should().Be("bdn_event_processor");
        _viewModel.Password.Should().Be("Bdn@2024");
        _viewModel.KeepAliveInterval.Should().Be(60);
        _viewModel.ReconnectDelay.Should().Be(5);
        _viewModel.QualityOfServiceLevel.Should().Be(1);
    }

    #endregion

    #region 连接测试功能测试

    [Fact]
    public async Task TestConnectionAsync_WithValidConfiguration_ShouldReturnTrue()
    {
        // Arrange
        _viewModel.LoadFromModel(TestDataFactory.CreateDefaultMqttConfiguration());

        // Act
        var result = await _viewModel.TestConnectionAsync();

        // Assert
        result.Should().BeTrue();
    }

    [Fact]
    public async Task TestConnectionAsync_WithInvalidConfiguration_ShouldReturnFalse()
    {
        // Arrange
        _viewModel.BrokerHost = ""; // 无效配置

        // Act
        var result = await _viewModel.TestConnectionAsync();

        // Assert
        result.Should().BeFalse();
    }

    #endregion

    #region 帮助信息测试

    [Fact]
    public void UpdateHelpInfo_ShouldCallHelpInfoService()
    {
        // Arrange
        var helpKey = "Mqtt.BrokerHost";

        // Act
        _viewModel.UpdateHelpInfo(helpKey);

        // Assert
        _mockHelpInfoService.Verify(x => x.GetStatusBarInfo(helpKey), Times.Once);
    }

    [Fact]
    public void UpdateHelpInfo_WithoutHelpInfoService_ShouldUseDefaultHelpText()
    {
        // Arrange
        var viewModelWithoutService = new MqttConfigurationViewModel(null);
        var helpKey = "Mqtt.BrokerHost";

        // Act
        viewModelWithoutService.UpdateHelpInfo(helpKey);

        // Assert
        viewModelWithoutService.CurrentHelpInfo.Should().Contain("MQTT 服务器地址");
    }

    [Theory]
    [InlineData("Mqtt.BrokerHost", "MQTT 服务器地址")]
    [InlineData("Mqtt.BrokerPort", "MQTT 服务器端口")]
    [InlineData("Mqtt.ClientId", "MQTT 客户端标识符")]
    [InlineData("Mqtt.Username", "MQTT 连接用户名")]
    [InlineData("Mqtt.Password", "MQTT 连接密码")]
    [InlineData("Mqtt.KeepAliveInterval", "保活间隔")]
    [InlineData("Mqtt.ReconnectDelay", "重连延迟")]
    [InlineData("Mqtt.QualityOfServiceLevel", "服务质量等级")]
    public void UpdateHelpInfo_WithKnownKeys_ShouldReturnCorrectHelpText(string helpKey, string expectedContent)
    {
        // Arrange
        var viewModelWithoutService = new MqttConfigurationViewModel(null);

        // Act
        viewModelWithoutService.UpdateHelpInfo(helpKey);

        // Assert
        viewModelWithoutService.CurrentHelpInfo.Should().Contain(expectedContent);
    }

    #endregion
}
