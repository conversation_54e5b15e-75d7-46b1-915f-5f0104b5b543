using EPConfigTool.Services;
using EPConfigTool.Tests.Helpers;
using EPConfigTool.Tests.TestData;
using EPConfigTool.ViewModels;
using FluentAssertions;
using Moq;
using Xunit;

namespace EPConfigTool.Tests.ViewModels;

/// <summary>
/// SerilogConfigurationViewModel 单元测试
/// 测试 Serilog 配置视图模型的输出目标、日志格式配置和环境切换功能
/// </summary>
public class SerilogConfigurationViewModelTests
{
    private readonly Mock<IHelpInfoService> _mockHelpInfoService;
    private readonly SerilogConfigurationViewModel _viewModel;

    public SerilogConfigurationViewModelTests()
    {
        _mockHelpInfoService = TestHelper.CreateMockHelpInfoService();
        _viewModel = new SerilogConfigurationViewModel(_mockHelpInfoService.Object);
    }

    #region 初始化测试

    [Fact]
    public void Constructor_ShouldInitializeWithDefaultValues()
    {
        // Assert
        _viewModel.DefaultMinimumLevel.Should().Be("Information");
        _viewModel.MicrosoftOverride.Should().Be("Warning");
        _viewModel.SystemOverride.Should().Be("Warning");
        _viewModel.EventProcessorOverride.Should().Be("Debug");
        _viewModel.EnableConsoleOutput.Should().BeTrue();
        _viewModel.EnableFileOutput.Should().BeTrue();
        _viewModel.LogFilePath.Should().Be("logs/eventprocessor-.log");
        _viewModel.RollingInterval.Should().Be("Day");
        _viewModel.RetainedFileCountLimit.Should().Be(30);
        _viewModel.OutputTemplate.Should().Be("[{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} {Level:u3}] {SourceContext}: {Message:lj}{NewLine}{Exception}");
    }

    [Fact]
    public void Constructor_ShouldInitializeCollections()
    {
        // Assert
        _viewModel.LogLevels.Should().Contain(new[] { "Verbose", "Debug", "Information", "Warning", "Error", "Fatal" });
        _viewModel.RollingIntervals.Should().Contain(new[] { "Infinite", "Year", "Month", "Day", "Hour", "Minute" });
    }

    [Fact]
    public void Constructor_ShouldSetDefaultHelpInfo()
    {
        // Assert
        _viewModel.CurrentHelpInfo.Should().Be("Serilog 配置。定义结构化日志记录的详细设置，包括输出目标和格式。");
    }

    #endregion

    #region 属性绑定测试

    [Fact]
    public void DefaultMinimumLevel_WhenChanged_ShouldUpdateProperty()
    {
        // Arrange
        var newLevel = "Debug";

        // Act
        _viewModel.DefaultMinimumLevel = newLevel;

        // Assert
        _viewModel.DefaultMinimumLevel.Should().Be(newLevel);
    }

    [Fact]
    public void MicrosoftOverride_WhenChanged_ShouldUpdateProperty()
    {
        // Arrange
        var newLevel = "Error";

        // Act
        _viewModel.MicrosoftOverride = newLevel;

        // Assert
        _viewModel.MicrosoftOverride.Should().Be(newLevel);
    }

    [Fact]
    public void SystemOverride_WhenChanged_ShouldUpdateProperty()
    {
        // Arrange
        var newLevel = "Error";

        // Act
        _viewModel.SystemOverride = newLevel;

        // Assert
        _viewModel.SystemOverride.Should().Be(newLevel);
    }

    [Fact]
    public void EventProcessorOverride_WhenChanged_ShouldUpdateProperty()
    {
        // Arrange
        var newLevel = "Verbose";

        // Act
        _viewModel.EventProcessorOverride = newLevel;

        // Assert
        _viewModel.EventProcessorOverride.Should().Be(newLevel);
    }

    [Fact]
    public void EnableConsoleOutput_WhenChanged_ShouldUpdateProperty()
    {
        // Arrange
        var newValue = false;

        // Act
        _viewModel.EnableConsoleOutput = newValue;

        // Assert
        _viewModel.EnableConsoleOutput.Should().Be(newValue);
    }

    [Fact]
    public void EnableFileOutput_WhenChanged_ShouldUpdateProperty()
    {
        // Arrange
        var newValue = false;

        // Act
        _viewModel.EnableFileOutput = newValue;

        // Assert
        _viewModel.EnableFileOutput.Should().Be(newValue);
    }

    [Fact]
    public void LogFilePath_WhenChanged_ShouldUpdateProperty()
    {
        // Arrange
        var newPath = "logs/custom-.log";

        // Act
        _viewModel.LogFilePath = newPath;

        // Assert
        _viewModel.LogFilePath.Should().Be(newPath);
    }

    [Fact]
    public void RollingInterval_WhenChanged_ShouldUpdateProperty()
    {
        // Arrange
        var newInterval = "Hour";

        // Act
        _viewModel.RollingInterval = newInterval;

        // Assert
        _viewModel.RollingInterval.Should().Be(newInterval);
    }

    [Fact]
    public void RetainedFileCountLimit_WhenChanged_ShouldUpdateProperty()
    {
        // Arrange
        var newLimit = 60;

        // Act
        _viewModel.RetainedFileCountLimit = newLimit;

        // Assert
        _viewModel.RetainedFileCountLimit.Should().Be(newLimit);
    }

    [Fact]
    public void OutputTemplate_WhenChanged_ShouldUpdateProperty()
    {
        // Arrange
        var newTemplate = "[{Timestamp:HH:mm:ss} {Level:u3}] {Message:lj}{NewLine}{Exception}";

        // Act
        _viewModel.OutputTemplate = newTemplate;

        // Assert
        _viewModel.OutputTemplate.Should().Be(newTemplate);
    }

    #endregion

    #region 模型转换测试

    [Fact]
    public void LoadFromModel_ShouldUpdateAllProperties()
    {
        // Arrange
        var serilogConfig = TestDataFactory.CreateDefaultSerilogConfiguration() with
        {
            MinimumLevel = TestDataFactory.CreateDefaultSerilogConfiguration().MinimumLevel with
            {
                Default = "Debug",
                Override = new Dictionary<string, string>
                {
                    ["Microsoft"] = "Error",
                    ["System"] = "Error",
                    ["EventProcessor"] = "Verbose"
                }
            }
        };

        // 修改 WriteTo 配置
        var fileConfig = serilogConfig.WriteTo.First(w => w.Name == "File");
        fileConfig.Args["path"] = "logs/custom-.log";
        fileConfig.Args["rollingInterval"] = "Hour";
        fileConfig.Args["retainedFileCountLimit"] = 60;

        // Act
        _viewModel.LoadFromModel(serilogConfig);

        // Assert
        _viewModel.DefaultMinimumLevel.Should().Be("Debug");
        _viewModel.MicrosoftOverride.Should().Be("Error");
        _viewModel.SystemOverride.Should().Be("Error");
        _viewModel.EventProcessorOverride.Should().Be("Verbose");
        _viewModel.EnableConsoleOutput.Should().BeTrue();
        _viewModel.EnableFileOutput.Should().BeTrue();
        _viewModel.LogFilePath.Should().Be("logs/custom-.log");
        _viewModel.RollingInterval.Should().Be("Hour");
        _viewModel.RetainedFileCountLimit.Should().Be(60);
    }

    [Fact]
    public void LoadFromModel_WithNullMinimumLevel_ShouldHandleGracefully()
    {
        // Arrange
        var serilogConfig = TestDataFactory.CreateDefaultSerilogConfiguration() with
        {
            MinimumLevel = null
        };

        // Act
        var action = () => _viewModel.LoadFromModel(serilogConfig);

        // Assert
        action.Should().NotThrow();
    }

    [Fact]
    public void LoadFromModel_WithNullWriteTo_ShouldHandleGracefully()
    {
        // Arrange
        var serilogConfig = TestDataFactory.CreateDefaultSerilogConfiguration() with
        {
            WriteTo = null
        };

        // Act
        var action = () => _viewModel.LoadFromModel(serilogConfig);

        // Assert
        action.Should().NotThrow();
    }

    [Fact]
    public void ToModel_ShouldReturnCorrectSerilogConfiguration()
    {
        // Arrange
        _viewModel.DefaultMinimumLevel = "Debug";
        _viewModel.MicrosoftOverride = "Error";
        _viewModel.SystemOverride = "Error";
        _viewModel.EventProcessorOverride = "Verbose";
        _viewModel.EnableConsoleOutput = true;
        _viewModel.EnableFileOutput = true;
        _viewModel.LogFilePath = "logs/custom-.log";
        _viewModel.RollingInterval = "Hour";
        _viewModel.RetainedFileCountLimit = 60;
        _viewModel.OutputTemplate = "[{Timestamp:HH:mm:ss}] {Message}{NewLine}";

        // Act
        var result = _viewModel.ToModel();

        // Assert
        result.Should().NotBeNull();
        result.MinimumLevel.Default.Should().Be("Debug");
        result.MinimumLevel.Override["Microsoft"].Should().Be("Error");
        result.MinimumLevel.Override["System"].Should().Be("Error");
        result.MinimumLevel.Override["EventProcessor"].Should().Be("Verbose");
        result.WriteTo.Should().HaveCount(2);
        result.WriteTo.Should().Contain(w => w.Name == "Console");
        result.WriteTo.Should().Contain(w => w.Name == "File");

        var fileConfig = result.WriteTo.First(w => w.Name == "File");
        fileConfig.Args["path"].Should().Be("logs/custom-.log");
        fileConfig.Args["rollingInterval"].Should().Be("Hour");
        fileConfig.Args["retainedFileCountLimit"].Should().Be(60);
    }

    [Fact]
    public void ToModel_WithOnlyConsoleOutput_ShouldReturnOnlyConsoleConfig()
    {
        // Arrange
        _viewModel.EnableConsoleOutput = true;
        _viewModel.EnableFileOutput = false;

        // Act
        var result = _viewModel.ToModel();

        // Assert
        result.WriteTo.Should().HaveCount(1);
        result.WriteTo.Should().Contain(w => w.Name == "Console");
        result.WriteTo.Should().NotContain(w => w.Name == "File");
    }

    [Fact]
    public void ToModel_WithOnlyFileOutput_ShouldReturnOnlyFileConfig()
    {
        // Arrange
        _viewModel.EnableConsoleOutput = false;
        _viewModel.EnableFileOutput = true;

        // Act
        var result = _viewModel.ToModel();

        // Assert
        result.WriteTo.Should().HaveCount(1);
        result.WriteTo.Should().Contain(w => w.Name == "File");
        result.WriteTo.Should().NotContain(w => w.Name == "Console");
    }

    #endregion

    #region 配置验证测试

    [Fact]
    public void Validate_WithValidConfiguration_ShouldReturnNoErrors()
    {
        // Arrange
        _viewModel.LoadFromModel(TestDataFactory.CreateDefaultSerilogConfiguration());

        // Act
        var errors = _viewModel.Validate();

        // Assert
        errors.Should().BeEmpty();
    }

    [Fact]
    public void Validate_WithInvalidDefaultMinimumLevel_ShouldReturnError()
    {
        // Arrange
        _viewModel.DefaultMinimumLevel = "Invalid";

        // Act
        var errors = _viewModel.Validate();

        // Assert
        errors.Should().Contain("默认最小日志级别必须是有效的 Serilog 日志级别");
    }

    [Fact]
    public void Validate_WithInvalidMicrosoftOverride_ShouldReturnError()
    {
        // Arrange
        _viewModel.MicrosoftOverride = "Invalid";

        // Act
        var errors = _viewModel.Validate();

        // Assert
        errors.Should().Contain("Microsoft 覆盖级别必须是有效的 Serilog 日志级别");
    }

    [Fact]
    public void Validate_WithInvalidSystemOverride_ShouldReturnError()
    {
        // Arrange
        _viewModel.SystemOverride = "Invalid";

        // Act
        var errors = _viewModel.Validate();

        // Assert
        errors.Should().Contain("System 覆盖级别必须是有效的 Serilog 日志级别");
    }

    [Fact]
    public void Validate_WithInvalidEventProcessorOverride_ShouldReturnError()
    {
        // Arrange
        _viewModel.EventProcessorOverride = "Invalid";

        // Act
        var errors = _viewModel.Validate();

        // Assert
        errors.Should().Contain("EventProcessor 覆盖级别必须是有效的 Serilog 日志级别");
    }

    [Fact]
    public void Validate_WithFileOutputEnabledButEmptyPath_ShouldReturnError()
    {
        // Arrange
        _viewModel.EnableFileOutput = true;
        _viewModel.LogFilePath = "";

        // Act
        var errors = _viewModel.Validate();

        // Assert
        errors.Should().Contain("启用文件输出时，日志文件路径不能为空");
    }

    [Fact]
    public void Validate_WithInvalidRollingInterval_ShouldReturnError()
    {
        // Arrange
        _viewModel.EnableFileOutput = true;
        _viewModel.RollingInterval = "Invalid";

        // Act
        var errors = _viewModel.Validate();

        // Assert
        errors.Should().Contain("滚动间隔必须是有效的间隔值");
    }

    [Theory]
    [InlineData(0)]
    [InlineData(1001)]
    public void Validate_WithInvalidRetainedFileCountLimit_ShouldReturnError(int invalidLimit)
    {
        // Arrange
        _viewModel.EnableFileOutput = true;
        _viewModel.RetainedFileCountLimit = invalidLimit;

        // Act
        var errors = _viewModel.Validate();

        // Assert
        errors.Should().Contain("保留文件数量限制必须在 1-1000 之间");
    }

    [Fact]
    public void Validate_WithEmptyOutputTemplate_ShouldReturnError()
    {
        // Arrange
        _viewModel.OutputTemplate = "";

        // Act
        var errors = _viewModel.Validate();

        // Assert
        errors.Should().Contain("输出模板不能为空");
    }

    [Fact]
    public void Validate_WithNoOutputEnabled_ShouldReturnError()
    {
        // Arrange
        _viewModel.EnableConsoleOutput = false;
        _viewModel.EnableFileOutput = false;

        // Act
        var errors = _viewModel.Validate();

        // Assert
        errors.Should().Contain("至少需要启用一种输出方式（控制台或文件）");
    }

    #endregion

    #region 重置功能测试

    [Fact]
    public void ResetToDefault_ShouldRestoreDefaultValues()
    {
        // Arrange
        _viewModel.DefaultMinimumLevel = "Verbose";
        _viewModel.MicrosoftOverride = "Fatal";
        _viewModel.SystemOverride = "Fatal";
        _viewModel.EventProcessorOverride = "Fatal";
        _viewModel.EnableConsoleOutput = false;
        _viewModel.EnableFileOutput = false;
        _viewModel.LogFilePath = "custom/path.log";
        _viewModel.RollingInterval = "Minute";
        _viewModel.RetainedFileCountLimit = 100;
        _viewModel.OutputTemplate = "Custom template";

        // Act
        _viewModel.ResetToDefault();

        // Assert
        _viewModel.DefaultMinimumLevel.Should().Be("Information");
        _viewModel.MicrosoftOverride.Should().Be("Warning");
        _viewModel.SystemOverride.Should().Be("Warning");
        _viewModel.EventProcessorOverride.Should().Be("Debug");
        _viewModel.EnableConsoleOutput.Should().BeTrue();
        _viewModel.EnableFileOutput.Should().BeTrue();
        _viewModel.LogFilePath.Should().Be("logs/eventprocessor-.log");
        _viewModel.RollingInterval.Should().Be("Day");
        _viewModel.RetainedFileCountLimit.Should().Be(30);
        _viewModel.OutputTemplate.Should().Be("[{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} {Level:u3}] {SourceContext}: {Message:lj}{NewLine}{Exception}");
    }

    #endregion

    #region 事件ID更新测试

    [Fact]
    public void UpdateLogFilePathForEvent_ShouldUpdateLogFilePath()
    {
        // Arrange
        var eventId = "EV123456";

        // Act
        _viewModel.UpdateLogFilePathForEvent(eventId);

        // Assert
        _viewModel.LogFilePath.Should().Be($"logs/eventprocessor-{eventId}-.log");
    }

    [Theory]
    [InlineData("")]
    [InlineData(null)]
    public void UpdateLogFilePathForEvent_WithInvalidEventId_ShouldNotChangeLogFilePath(string invalidEventId)
    {
        // Arrange
        var originalPath = _viewModel.LogFilePath;

        // Act
        _viewModel.UpdateLogFilePathForEvent(invalidEventId);

        // Assert
        _viewModel.LogFilePath.Should().Be(originalPath);
    }

    #endregion

    #region 环境配置测试

    [Fact]
    public void SetDevelopmentConfiguration_ShouldSetDevelopmentLogLevels()
    {
        // Act
        _viewModel.SetDevelopmentConfiguration();

        // Assert
        _viewModel.DefaultMinimumLevel.Should().Be("Debug");
        _viewModel.MicrosoftOverride.Should().Be("Information");
        _viewModel.SystemOverride.Should().Be("Information");
        _viewModel.EventProcessorOverride.Should().Be("Debug");
        _viewModel.EnableConsoleOutput.Should().BeTrue();
        _viewModel.EnableFileOutput.Should().BeTrue();
    }

    [Fact]
    public void SetProductionConfiguration_ShouldSetProductionLogLevels()
    {
        // Act
        _viewModel.SetProductionConfiguration();

        // Assert
        _viewModel.DefaultMinimumLevel.Should().Be("Information");
        _viewModel.MicrosoftOverride.Should().Be("Warning");
        _viewModel.SystemOverride.Should().Be("Warning");
        _viewModel.EventProcessorOverride.Should().Be("Information");
        _viewModel.EnableConsoleOutput.Should().BeFalse();
        _viewModel.EnableFileOutput.Should().BeTrue();
        _viewModel.RetainedFileCountLimit.Should().Be(90);
    }

    #endregion

    #region 帮助信息测试

    [Fact]
    public void UpdateHelpInfo_ShouldCallHelpInfoService()
    {
        // Arrange
        var helpKey = "Serilog.DefaultMinimumLevel";

        // Act
        _viewModel.UpdateHelpInfo(helpKey);

        // Assert
        _mockHelpInfoService.Verify(x => x.GetStatusBarInfo(helpKey), Times.Once);
    }

    [Theory]
    [InlineData("Serilog.DefaultMinimumLevel", "默认最小日志级别")]
    [InlineData("Serilog.MicrosoftOverride", "Microsoft 组件日志级别覆盖")]
    [InlineData("Serilog.EnableConsoleOutput", "是否启用控制台输出")]
    [InlineData("Serilog.EnableFileOutput", "是否启用文件输出")]
    [InlineData("Serilog.LogFilePath", "日志文件路径")]
    [InlineData("Serilog.RollingInterval", "日志文件滚动间隔")]
    public void UpdateHelpInfo_WithKnownKeys_ShouldReturnCorrectHelpText(string helpKey, string expectedContent)
    {
        // Arrange
        var viewModelWithoutService = new SerilogConfigurationViewModel(null);

        // Act
        viewModelWithoutService.UpdateHelpInfo(helpKey);

        // Assert
        viewModelWithoutService.CurrentHelpInfo.Should().Contain(expectedContent);
    }

    #endregion
}
