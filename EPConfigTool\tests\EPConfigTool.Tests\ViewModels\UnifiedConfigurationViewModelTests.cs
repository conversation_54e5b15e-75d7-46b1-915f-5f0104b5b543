using EPConfigTool.Models;
using EPConfigTool.Services;
using EPConfigTool.Tests.Helpers;
using EPConfigTool.Tests.TestData;
using EPConfigTool.ViewModels;
using FluentAssertions;
using Moq;
using Xunit;

namespace EPConfigTool.Tests.ViewModels;

/// <summary>
/// UnifiedConfigurationViewModel 单元测试
/// 测试统一配置视图模型的创建、加载、验证和子配置管理功能
/// </summary>
public class UnifiedConfigurationViewModelTests
{
    private readonly Mock<IHelpInfoService> _mockHelpInfoService;
    private readonly UnifiedConfigurationViewModel _viewModel;

    public UnifiedConfigurationViewModelTests()
    {
        _mockHelpInfoService = TestHelper.CreateMockHelpInfoService();
        _viewModel = new UnifiedConfigurationViewModel(_mockHelpInfoService.Object);
    }

    #region 初始化测试

    [Fact]
    public void Constructor_ShouldInitializeSubViewModels()
    {
        // Assert
        _viewModel.EventConfiguration.Should().NotBeNull();
        _viewModel.MqttConfiguration.Should().NotBeNull();
        _viewModel.ErrorHandlingConfiguration.Should().NotBeNull();
        _viewModel.LoggingConfiguration.Should().NotBeNull();
        _viewModel.SerilogConfiguration.Should().NotBeNull();
    }

    [Fact]
    public void Constructor_ShouldSetDefaultHelpInfo()
    {
        // Assert
        _viewModel.CurrentHelpInfo.Should().Be("统一配置管理器。此界面允许您编辑包含所有配置节的统一 YAML 配置文件。");
    }

    #endregion

    #region 配置加载测试

    [Fact]
    public void LoadFromModel_ShouldUpdateAllSubConfigurations()
    {
        // Arrange
        var unifiedConfig = TestDataFactory.CreateDefaultUnifiedConfiguration();

        // Act
        _viewModel.LoadFromModel(unifiedConfig);

        // Assert
        _viewModel.EventConfiguration.EventId.Should().Be(unifiedConfig.EventProcessor.EventId);
        _viewModel.EventConfiguration.EventName.Should().Be(unifiedConfig.EventProcessor.EventName);
        _viewModel.MqttConfiguration.BrokerHost.Should().Be(unifiedConfig.Mqtt.BrokerHost);
        _viewModel.MqttConfiguration.BrokerPort.Should().Be(unifiedConfig.Mqtt.BrokerPort);
        _viewModel.ErrorHandlingConfiguration.ToleranceLevel.Should().Be(unifiedConfig.ErrorHandling.ToleranceLevel);
        _viewModel.LoggingConfiguration.DefaultLogLevel.Should().Be(unifiedConfig.Logging.LogLevel.Default);
        _viewModel.SerilogConfiguration.DefaultMinimumLevel.Should().Be(unifiedConfig.Serilog.MinimumLevel.Default);
    }

    [Fact]
    public void LoadFromModel_WithNullLogging_ShouldHandleGracefully()
    {
        // Arrange
        var unifiedConfig = TestDataFactory.CreateDefaultUnifiedConfiguration() with
        {
            Logging = null
        };

        // Act
        var action = () => _viewModel.LoadFromModel(unifiedConfig);

        // Assert
        action.Should().NotThrow();
    }

    [Fact]
    public void LoadFromModel_WithNullSerilog_ShouldHandleGracefully()
    {
        // Arrange
        var unifiedConfig = TestDataFactory.CreateDefaultUnifiedConfiguration() with
        {
            Serilog = null
        };

        // Act
        var action = () => _viewModel.LoadFromModel(unifiedConfig);

        // Assert
        action.Should().NotThrow();
    }

    #endregion

    #region 模型转换测试

    [Fact]
    public void ToModel_ShouldReturnCorrectUnifiedConfiguration()
    {
        // Arrange
        var originalConfig = TestDataFactory.CreateDefaultUnifiedConfiguration();
        _viewModel.LoadFromModel(originalConfig);

        // Act
        var result = _viewModel.ToModel();

        // Assert
        result.Should().NotBeNull();
        result.EventProcessor.Should().NotBeNull();
        result.Mqtt.Should().NotBeNull();
        result.ErrorHandling.Should().NotBeNull();
        result.Logging.Should().NotBeNull();
        result.Serilog.Should().NotBeNull();

        // 验证关键属性
        result.EventProcessor.EventId.Should().Be(originalConfig.EventProcessor.EventId);
        result.EventProcessor.EventName.Should().Be(originalConfig.EventProcessor.EventName);
        result.Mqtt.BrokerHost.Should().Be(originalConfig.Mqtt.BrokerHost);
        result.Mqtt.BrokerPort.Should().Be(originalConfig.Mqtt.BrokerPort);
        result.ErrorHandling.ToleranceLevel.Should().Be(originalConfig.ErrorHandling.ToleranceLevel);
    }

    [Fact]
    public void ToModel_AfterPropertyChanges_ShouldReflectChanges()
    {
        // Arrange
        _viewModel.LoadFromModel(TestDataFactory.CreateDefaultUnifiedConfiguration());
        
        // 修改一些属性
        _viewModel.EventConfiguration.EventId = "EV999999";
        _viewModel.EventConfiguration.EventName = "修改后的事件";
        _viewModel.MqttConfiguration.BrokerHost = "new.broker.com";
        _viewModel.MqttConfiguration.BrokerPort = 8883;
        _viewModel.ErrorHandlingConfiguration.ToleranceLevel = "Strict";

        // Act
        var result = _viewModel.ToModel();

        // Assert
        result.EventProcessor.EventId.Should().Be("EV999999");
        result.EventProcessor.EventName.Should().Be("修改后的事件");
        result.Mqtt.BrokerHost.Should().Be("new.broker.com");
        result.Mqtt.BrokerPort.Should().Be(8883);
        result.ErrorHandling.ToleranceLevel.Should().Be("Strict");
    }

    #endregion

    #region 默认配置创建测试

    [Fact]
    public void CreateDefault_ShouldCreateValidConfiguration()
    {
        // Arrange
        var eventId = "EV888888";
        var eventName = "默认测试事件";

        // Act
        _viewModel.CreateDefault(eventId, eventName);

        // Assert
        _viewModel.EventConfiguration.EventId.Should().Be(eventId);
        _viewModel.EventConfiguration.EventName.Should().Be(eventName);
        _viewModel.MqttConfiguration.BrokerHost.Should().NotBeNullOrEmpty();
        _viewModel.ErrorHandlingConfiguration.ToleranceLevel.Should().NotBeNullOrEmpty();
        _viewModel.LoggingConfiguration.DefaultLogLevel.Should().NotBeNullOrEmpty();
        _viewModel.SerilogConfiguration.DefaultMinimumLevel.Should().NotBeNullOrEmpty();
    }

    [Fact]
    public void CreateDefault_ShouldSetMqttClientIdWithEventId()
    {
        // Arrange
        var eventId = "EV777777";
        var eventName = "MQTT测试事件";

        // Act
        _viewModel.CreateDefault(eventId, eventName);

        // Assert
        _viewModel.MqttConfiguration.ClientId.Should().Contain(eventId);
    }

    #endregion

    #region 配置验证测试

    [Fact]
    public void Validate_WithValidConfiguration_ShouldReturnValidResult()
    {
        // Arrange
        _viewModel.CreateDefault("EV666666", "验证测试事件");

        // Act
        var result = _viewModel.Validate();

        // Assert
        result.Should().NotBeNull();
        result.IsValid.Should().BeTrue();
        result.Errors.Should().BeEmpty();
        result.Warnings.Should().BeEmpty();
        result.SectionErrors.Should().BeEmpty();
    }

    #endregion

    #region 重置功能测试

    [Fact]
    public void ResetToDefault_ShouldResetToDefaultConfiguration()
    {
        // Arrange
        _viewModel.CreateDefault("EV555555", "重置测试事件");
        
        // 修改一些属性
        _viewModel.EventConfiguration.Priority = "P1";
        _viewModel.MqttConfiguration.BrokerHost = "modified.broker.com";
        _viewModel.ErrorHandlingConfiguration.ToleranceLevel = "Lenient";

        var originalEventId = _viewModel.EventConfiguration.EventId;
        var originalEventName = _viewModel.EventConfiguration.EventName;

        // Act
        _viewModel.ResetToDefault();

        // Assert
        _viewModel.EventConfiguration.EventId.Should().Be(originalEventId);
        _viewModel.EventConfiguration.EventName.Should().Be(originalEventName);
        // 其他属性应该重置为默认值
        _viewModel.EventConfiguration.Priority.Should().Be("P3"); // 默认优先级
        _viewModel.MqttConfiguration.BrokerHost.Should().Be("mq.bangdouni.com"); // 默认主机
        _viewModel.ErrorHandlingConfiguration.ToleranceLevel.Should().Be("Normal"); // 默认容错级别
    }

    [Fact]
    public void ResetToDefault_WithEmptyEventInfo_ShouldUseDefaultValues()
    {
        // Arrange
        _viewModel.EventConfiguration.EventId = "";
        _viewModel.EventConfiguration.EventName = "";

        // Act
        _viewModel.ResetToDefault();

        // Assert
        _viewModel.EventConfiguration.EventId.Should().Be("EV000001");
        _viewModel.EventConfiguration.EventName.Should().Be("新建事件");
    }

    #endregion

    #region 帮助信息测试

    [Fact]
    public void UpdateHelpInfo_ShouldCallHelpInfoService()
    {
        // Arrange
        var helpKey = "UnifiedConfig.Overview";

        // Act
        _viewModel.UpdateHelpInfo(helpKey);

        // Assert
        _mockHelpInfoService.Verify(x => x.GetStatusBarInfo(helpKey), Times.Once);
    }

    [Fact]
    public void UpdateHelpInfo_ShouldUpdateCurrentHelpInfo()
    {
        // Arrange
        var helpKey = "UnifiedConfig.EventProcessor";
        var expectedHelpInfo = $"帮助信息: {helpKey}";

        // Act
        _viewModel.UpdateHelpInfo(helpKey);

        // Assert
        _viewModel.CurrentHelpInfo.Should().Be(expectedHelpInfo);
    }

    #endregion

    #region 子配置帮助信息传播测试

    [Fact]
    public void EventConfiguration_HelpInfoChange_ShouldPropagateToParent()
    {
        // Arrange
        var newHelpInfo = "事件配置帮助信息";

        // Act
        _viewModel.EventConfiguration.CurrentHelpInfo = newHelpInfo;

        // Assert
        _viewModel.CurrentHelpInfo.Should().Be(newHelpInfo);
    }

    [Fact]
    public void MqttConfiguration_HelpInfoChange_ShouldPropagateToParent()
    {
        // Arrange
        var newHelpInfo = "MQTT配置帮助信息";

        // Act
        _viewModel.MqttConfiguration.CurrentHelpInfo = newHelpInfo;

        // Assert
        _viewModel.CurrentHelpInfo.Should().Be(newHelpInfo);
    }

    [Fact]
    public void ErrorHandlingConfiguration_HelpInfoChange_ShouldPropagateToParent()
    {
        // Arrange
        var newHelpInfo = "错误处理配置帮助信息";

        // Act
        _viewModel.ErrorHandlingConfiguration.CurrentHelpInfo = newHelpInfo;

        // Assert
        _viewModel.CurrentHelpInfo.Should().Be(newHelpInfo);
    }

    #endregion
}
