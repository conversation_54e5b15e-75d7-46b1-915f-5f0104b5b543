using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace EPConfigTool.Tests.TestData.Models
{
    /// <summary>
    /// 测试用的统一配置模型
    /// </summary>
    public class TestUnifiedConfig
    {
        public string Version { get; set; } = string.Empty;
        public TestMetadata? Metadata { get; set; }
        public TestMqttConfig? Mqtt { get; set; }
        public List<TestEvent> Events { get; set; } = new();
        public List<TestDevice> Devices { get; set; } = new();
        public TestAlarmConfig? Alarms { get; set; }
    }

    /// <summary>
    /// 测试用的元数据模型
    /// </summary>
    public class TestMetadata
    {
        public string Name { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string Created { get; set; } = string.Empty;
        public string Author { get; set; } = string.Empty;
    }

    /// <summary>
    /// 测试用的MQTT配置模型
    /// </summary>
    public class TestMqttConfig
    {
        public TestMqttBroker? Broker { get; set; }
        public TestMqttTopics? Topics { get; set; }
    }

    /// <summary>
    /// 测试用的MQTT代理配置
    /// </summary>
    public class TestMqttBroker
    {
        public string Host { get; set; } = string.Empty;
        public int Port { get; set; }
        public string Username { get; set; } = string.Empty;
        public string Password { get; set; } = string.Empty;
    }

    /// <summary>
    /// 测试用的MQTT主题配置
    /// </summary>
    public class TestMqttTopics
    {
        public string EventInput { get; set; } = string.Empty;
        public string AlarmOutput { get; set; } = string.Empty;
        public string ControlInput { get; set; } = string.Empty;
    }

    /// <summary>
    /// 测试用的事件模型
    /// </summary>
    public class TestEvent
    {
        public string Id { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public List<TestCondition> Conditions { get; set; } = new();
        public TestTimeWindow? TimeWindow { get; set; }
        public List<TestAction> Actions { get; set; } = new();
    }

    /// <summary>
    /// 测试用的条件模型
    /// </summary>
    public class TestCondition
    {
        public string Field { get; set; } = string.Empty;
        public string Operator { get; set; } = string.Empty;
        public object? Value { get; set; }
    }

    /// <summary>
    /// 测试用的时间窗口模型
    /// </summary>
    public class TestTimeWindow
    {
        public int Duration { get; set; }
        public string Unit { get; set; } = string.Empty;
    }

    /// <summary>
    /// 测试用的动作模型
    /// </summary>
    public class TestAction
    {
        public string Type { get; set; } = string.Empty;
        public string Level { get; set; } = string.Empty;
        public string Message { get; set; } = string.Empty;
    }

    /// <summary>
    /// 测试用的设备模型
    /// </summary>
    public class TestDevice
    {
        public string Id { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public string Type { get; set; } = string.Empty;
        public string Location { get; set; } = string.Empty;
        public bool Enabled { get; set; }
    }

    /// <summary>
    /// 测试用的告警配置模型
    /// </summary>
    public class TestAlarmConfig
    {
        public Dictionary<string, TestAlarmLevel> Levels { get; set; } = new();
        public TestAlarmRetention? Retention { get; set; }
    }

    /// <summary>
    /// 测试用的告警级别模型
    /// </summary>
    public class TestAlarmLevel
    {
        public string Color { get; set; } = string.Empty;
        public bool Sound { get; set; }
    }

    /// <summary>
    /// 测试用的告警保留策略模型
    /// </summary>
    public class TestAlarmRetention
    {
        public int Days { get; set; }
        public int MaxCount { get; set; }
    }

    /// <summary>
    /// 测试用的事件配置模型
    /// </summary>
    public class TestEventConfig
    {
        public string Version { get; set; } = string.Empty;
        public TestMetadata? Metadata { get; set; }
        public Dictionary<string, TestEventDefinition> Events { get; set; } = new();
        public TestGlobalSettings? GlobalSettings { get; set; }
        public Dictionary<string, TestValidationRule> ValidationRules { get; set; } = new();
        public Dictionary<string, string> DeviceMapping { get; set; } = new();
    }

    /// <summary>
    /// 测试用的事件定义模型
    /// </summary>
    public class TestEventDefinition
    {
        public string Name { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public List<TestCondition> TriggerConditions { get; set; } = new();
        public List<TestProcessingRule> ProcessingRules { get; set; } = new();
        public List<TestOutputAction> OutputActions { get; set; } = new();
    }

    /// <summary>
    /// 测试用的处理规则模型
    /// </summary>
    public class TestProcessingRule
    {
        public string Type { get; set; } = string.Empty;
        public List<string> Rules { get; set; } = new();
        public List<string> Actions { get; set; } = new();
        public string MatchField { get; set; } = string.Empty;
        public int TimeWindow { get; set; }
    }

    /// <summary>
    /// 测试用的输出动作模型
    /// </summary>
    public class TestOutputAction
    {
        public string Type { get; set; } = string.Empty;
        public string Topic { get; set; } = string.Empty;
        public int Qos { get; set; }
        public string Table { get; set; } = string.Empty;
        public string Condition { get; set; } = string.Empty;
        public string Level { get; set; } = string.Empty;
    }

    /// <summary>
    /// 测试用的全局设置模型
    /// </summary>
    public class TestGlobalSettings
    {
        public string Timezone { get; set; } = string.Empty;
        public string DateFormat { get; set; } = string.Empty;
        public int MaxProcessingTime { get; set; }
        public int RetryAttempts { get; set; }
    }

    /// <summary>
    /// 测试用的验证规则模型
    /// </summary>
    public class TestValidationRule
    {
        public string Field { get; set; } = string.Empty;
        public string Type { get; set; } = string.Empty;
        public string ErrorMessage { get; set; } = string.Empty;
        public int MinAge { get; set; }
        public int MaxAge { get; set; }
    }
}