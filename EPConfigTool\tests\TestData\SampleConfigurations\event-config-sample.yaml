# 事件配置示例文件 - 用于集成测试
version: "4.1"
metadata:
  name: "事件配置测试"
  description: "用于测试事件配置加载和保存功能"
  created: "2025-01-04T10:00:00Z"

# 事件定义
events:
  EV001:
    name: "车辆进入事件"
    description: "检测车辆进入停车场"
    trigger_conditions:
      - field: "event_type"
        operator: "=="
        value: "vehicle_enter"
      - field: "confidence"
        operator: ">="
        value: 0.8
    processing_rules:
      - type: "validation"
        rules:
          - "vehicle_id_required"
          - "timestamp_valid"
      - type: "enrichment"
        actions:
          - "add_location_info"
          - "calculate_duration"
    output_actions:
      - type: "mqtt_publish"
        topic: "events/processed"
        qos: 1
      - type: "database_insert"
        table: "vehicle_events"
      - type: "alarm_trigger"
        condition: "first_time_visitor"
        level: "info"

  EV002:
    name: "车辆离开事件"
    description: "检测车辆离开停车场"
    trigger_conditions:
      - field: "event_type"
        operator: "=="
        value: "vehicle_exit"
    processing_rules:
      - type: "correlation"
        match_field: "vehicle_id"
        time_window: 86400  # 24小时
      - type: "calculation"
        actions:
          - "calculate_parking_duration"
          - "calculate_parking_fee"
    output_actions:
      - type: "mqtt_publish"
        topic: "events/exit"
      - type: "billing_trigger"
        condition: "fee_required"

# 全局配置
global_settings:
  timezone: "Asia/Shanghai"
  date_format: "yyyy-MM-dd HH:mm:ss"
  max_processing_time: 5000  # 毫秒
  retry_attempts: 3
  
# 验证规则
validation_rules:
  vehicle_id_required:
    field: "vehicle_id"
    type: "not_empty"
    error_message: "车辆ID不能为空"
  
  timestamp_valid:
    field: "timestamp"
    type: "datetime_range"
    min_age: 0
    max_age: 300  # 5分钟内的事件才有效
    error_message: "事件时间戳无效"

# 设备映射
device_mapping:
  camera_001: "主入口摄像头"
  camera_002: "副入口摄像头"
  camera_003: "出口摄像头"
  sensor_001: "地磁传感器1"
  sensor_002: "地磁传感器2"