# 无效配置示例文件 - 用于测试错误处理
version: "4.1"
metadata:
  name: "无效配置测试"
  # 缺少必需的description字段
  created: "invalid-date-format"  # 无效的日期格式

# 语法错误的YAML
events:
  - id: "EV001"
    name: "测试事件"
    conditions:
      - field: "event_type"
        operator: "invalid_operator"  # 无效的操作符
        value: 
      # 缺少value值
    actions:
      - type: "unknown_action"  # 未知的动作类型
        level: "invalid_level"  # 无效的告警级别

# 重复的ID
  - id: "EV001"  # 重复的事件ID
    name: "重复事件"

# 无效的设备配置
devices:
  - id: ""  # 空的设备ID
    name: "无效设备"
    type: "unknown_type"  # 未知的设备类型
    enabled: not_a_boolean  # 应该是布尔值

# 无效的MQTT配置
mqtt:
  broker:
    host: ""  # 空的主机地址
    port: "not_a_number"  # 端口应该是数字
    username: null
    password: 123  # 密码应该是字符串

# 无效的时间窗口配置
time_windows:
  - duration: -1  # 负数持续时间
    unit: "invalid_unit"  # 无效的时间单位

# 循环引用（如果支持引用的话）
references:
  ref1: &ref1
    name: "引用1"
    depends_on: *ref2  # 引用ref2
  ref2: &ref2
    name: "引用2"
    depends_on: *ref1  # 循环引用ref1

# 超出范围的值
limits:
  max_events: 999999999999999999999  # 超大数值
  timeout: -100  # 负数超时
  retry_count: 1.5  # 应该是整数