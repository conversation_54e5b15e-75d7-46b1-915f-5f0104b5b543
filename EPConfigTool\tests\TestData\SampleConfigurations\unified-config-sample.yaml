# 统一配置示例文件 - 用于集成测试
version: "4.1"
metadata:
  name: "测试配置"
  description: "用于集成测试的示例配置"
  created: "2025-01-04T10:00:00Z"
  author: "EPConfigTool Test Suite"

# MQTT配置
mqtt:
  broker:
    host: "localhost"
    port: 1883
    username: "test_user"
    password: "test_password"
  topics:
    event_input: "events/input"
    alarm_output: "alarms/output"
    control_input: "control/input"

# 事件处理规则
events:
  - id: "EV001"
    name: "车辆进入测试"
    description: "测试车辆进入事件处理"
    conditions:
      - field: "event_type"
        operator: "equals"
        value: "vehicle_enter"
      - field: "device_id"
        operator: "in"
        value: ["DEV001", "DEV002"]
    actions:
      - type: "alarm"
        level: "info"
        message: "车辆进入: {vehicle_id}"
      - type: "log"
        message: "记录车辆进入事件"

  - id: "EV002"
    name: "车辆超时测试"
    description: "测试车辆超时告警"
    conditions:
      - field: "event_type"
        operator: "equals"
        value: "vehicle_timeout"
    time_window:
      duration: 300  # 5分钟
      unit: "seconds"
    actions:
      - type: "alarm"
        level: "warning"
        message: "车辆超时: {vehicle_id}"

# 设备配置
devices:
  - id: "DEV001"
    name: "入口摄像头1"
    type: "camera"
    location: "主入口"
    enabled: true
  - id: "DEV002"
    name: "入口摄像头2"
    type: "camera"
    location: "副入口"
    enabled: true

# 告警配置
alarms:
  levels:
    info:
      color: "#17a2b8"
      sound: false
    warning:
      color: "#ffc107"
      sound: true
    error:
      color: "#dc3545"
      sound: true
  retention:
    days: 30
    max_count: 10000