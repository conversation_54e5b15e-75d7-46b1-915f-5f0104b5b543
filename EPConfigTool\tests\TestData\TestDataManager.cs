using System;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Threading.Tasks;
using YamlDotNet.Serialization;
using YamlDotNet.Serialization.NamingConventions;
using Microsoft.Extensions.Logging;

namespace EPConfigTool.Tests.TestData
{
    /// <summary>
    /// 测试数据管理器，用于管理测试过程中的配置文件和测试数据
    /// </summary>
    public class TestDataManager
    {
        private readonly ILogger<TestDataManager> _logger;
        private readonly string _testDataRoot;
        private readonly ISerializer _yamlSerializer;
        private readonly IDeserializer _yamlDeserializer;

        public TestDataManager(ILogger<TestDataManager> logger = null)
        {
            _logger = logger;
            _testDataRoot = GetTestDataRootPath();
            
            // 配置YAML序列化器
            _yamlSerializer = new SerializerBuilder()
                .WithNamingConvention(UnderscoredNamingConvention.Instance)
                .Build();
                
            _yamlDeserializer = new DeserializerBuilder()
                .WithNamingConvention(UnderscoredNamingConvention.Instance)
                .IgnoreUnmatchedProperties()
                .Build();
        }

        /// <summary>
        /// 获取测试数据根目录路径
        /// </summary>
        /// <returns>测试数据根目录的绝对路径</returns>
        private static string GetTestDataRootPath()
        {
            var assemblyLocation = Assembly.GetExecutingAssembly().Location;
            var assemblyDirectory = Path.GetDirectoryName(assemblyLocation);
            
            // 向上查找到tests目录
            var currentDir = assemblyDirectory;
            while (currentDir != null && !Path.GetFileName(currentDir).Equals("tests", StringComparison.OrdinalIgnoreCase))
            {
                currentDir = Directory.GetParent(currentDir)?.FullName;
            }
            
            if (currentDir == null)
            {
                throw new DirectoryNotFoundException("无法找到tests目录");
            }
            
            return Path.Combine(currentDir, "TestData");
        }

        /// <summary>
        /// 获取示例配置文件路径
        /// </summary>
        /// <param name="fileName">配置文件名</param>
        /// <returns>配置文件的完整路径</returns>
        public string GetSampleConfigPath(string fileName)
        {
            return Path.Combine(_testDataRoot, "SampleConfigurations", fileName);
        }

        /// <summary>
        /// 读取示例配置文件内容
        /// </summary>
        /// <param name="fileName">配置文件名</param>
        /// <returns>配置文件内容</returns>
        public async Task<string> ReadSampleConfigAsync(string fileName)
        {
            var filePath = GetSampleConfigPath(fileName);
            if (!File.Exists(filePath))
            {
                throw new FileNotFoundException($"示例配置文件不存在: {filePath}");
            }
            
            _logger?.LogDebug("读取示例配置文件: {FilePath}", filePath);
            return await File.ReadAllTextAsync(filePath);
        }

        /// <summary>
        /// 反序列化YAML配置文件
        /// </summary>
        /// <typeparam name="T">目标类型</typeparam>
        /// <param name="fileName">配置文件名</param>
        /// <returns>反序列化后的对象</returns>
        public async Task<T> DeserializeConfigAsync<T>(string fileName)
        {
            var yamlContent = await ReadSampleConfigAsync(fileName);
            
            try
            {
                return _yamlDeserializer.Deserialize<T>(yamlContent);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "反序列化配置文件失败: {FileName}", fileName);
                throw;
            }
        }

        /// <summary>
        /// 序列化对象为YAML格式
        /// </summary>
        /// <typeparam name="T">对象类型</typeparam>
        /// <param name="obj">要序列化的对象</param>
        /// <returns>YAML格式的字符串</returns>
        public string SerializeToYaml<T>(T obj)
        {
            try
            {
                return _yamlSerializer.Serialize(obj);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "序列化对象为YAML失败: {ObjectType}", typeof(T).Name);
                throw;
            }
        }

        /// <summary>
        /// 创建临时测试文件
        /// </summary>
        /// <param name="content">文件内容</param>
        /// <param name="extension">文件扩展名（默认为.yaml）</param>
        /// <returns>临时文件路径</returns>
        public string CreateTempTestFile(string content, string extension = ".yaml")
        {
            var tempFileName = $"temp_test_{Guid.NewGuid():N}{extension}";
            var tempFilePath = Path.Combine(Path.GetTempPath(), tempFileName);
            
            File.WriteAllText(tempFilePath, content);
            _logger?.LogDebug("创建临时测试文件: {TempFilePath}", tempFilePath);
            
            return tempFilePath;
        }

        /// <summary>
        /// 清理临时测试文件
        /// </summary>
        /// <param name="filePath">要删除的文件路径</param>
        public void CleanupTempFile(string filePath)
        {
            try
            {
                if (File.Exists(filePath))
                {
                    File.Delete(filePath);
                    _logger?.LogDebug("清理临时测试文件: {FilePath}", filePath);
                }
            }
            catch (Exception ex)
            {
                _logger?.LogWarning(ex, "清理临时文件失败: {FilePath}", filePath);
            }
        }

        /// <summary>
        /// 验证配置文件是否为有效的YAML格式
        /// </summary>
        /// <param name="yamlContent">YAML内容</param>
        /// <returns>是否为有效的YAML</returns>
        public bool IsValidYaml(string yamlContent)
        {
            if (string.IsNullOrWhiteSpace(yamlContent))
            {
                return false;
            }
            
            try
            {
                var result = _yamlDeserializer.Deserialize<object>(yamlContent);
                // 如果反序列化结果为null，说明YAML内容为空或只包含空白字符
                return result != null;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 获取所有示例配置文件列表
        /// </summary>
        /// <returns>示例配置文件名列表</returns>
        public string[] GetAllSampleConfigFiles()
        {
            var sampleConfigDir = Path.Combine(_testDataRoot, "SampleConfigurations");
            if (!Directory.Exists(sampleConfigDir))
            {
                return Array.Empty<string>();
            }
            
            return Directory.GetFiles(sampleConfigDir, "*.yaml")
                .Select(Path.GetFileName)
                .ToArray();
        }
    }
}