# EPConfigTool 校验功能修复计划

## 🎯 **项目背景**

EPConfigTool 的 `ValidateCurrentConfigurationAsync` 方法存在验证缺陷：
- **文件验证路径**：通过 `_configService.ValidateYamlFileAsync` 正确验证已保存文件
- **内存验证路径**：仅进行序列化循环测试，直接返回 `IsValid = true`，未执行实际验证

**目标**：建立完整、一致的配置验证体系，确保新建配置与文件配置使用相同的验证标准。

---

## 📋 **修复范围与内容**

### **阶段一：核心验证逻辑修复** ⚠️ **关键修复**

#### **1.1 MainViewModel.ValidateCurrentConfigurationAsync 重构**

**问题分析**：
```csharp
// ❌ 当前问题代码
var configuration = CurrentEvent.ToModel();
var yamlContent = _configService.SerializeToYamlString(configuration);
var parsedConfig = _configService.ParseFromYamlString(yamlContent);
var result = new YamlValidationResult { IsValid = true }; // 直接返回true
```

**修复方案**：
```csharp
//  修复后的代码结构
private async Task<YamlValidationResult> ValidateInMemoryConfigurationAsync()
{
    var errors = new List<string>();
    var warnings = new List<string>();

    try
    {
        // 1. ViewModel → Model 转换
        var configuration = CurrentEvent.ToModel();
        
        // 2. DataAnnotations 基础验证
        ValidateWithDataAnnotations(configuration, errors);
        
        // 3. 业务逻辑验证
        await ValidateBusinessLogicAsync(configuration, errors, warnings);
        
        // 4. 配置一致性验证
        ValidateConfigurationConsistency(configuration, errors, warnings);
        
        // 5. 深度结构验证
        ValidateComplexStructures(configuration, errors, warnings);
        
    }
    catch (Exception ex)
    {
        errors.Add($"验证过程中发生错误: {ex.Message}");
    }

    return new YamlValidationResult 
    { 
        IsValid = errors.Count == 0, 
        Errors = errors, 
        Warnings = warnings 
    };
}
```

**实现文件**：
- `EPConfigTool/ViewModels/MainViewModel.cs`
- 新增方法：`ValidateInMemoryConfigurationAsync`
- 修改方法：`ValidateCurrentConfigurationAsync`

---

### **阶段二：验证规则体系** 📐 **验证规则**

#### **2.1 基础字段验证规则**

| 字段 | 验证规则 | 错误消息 | 实现状态 |
|------|----------|----------|----------|
| **EventId** | `^EV\d{6}$` | 事件ID格式必须为EV开头的6位数字 |   |
| **EventName** | 必填，长度≤100 | 事件名称不能为空且长度不能超过100个字符 |   |
| **EvaluationStrategy** | `AI\|BusinessOnly\|AIAndBusiness` | 评估策略必须是AI、BusinessOnly或AIAndBusiness |   |
| **Priority** | `^P[1-5]$` | 优先级必须是P1-P5 |   |
| **CommId** | 必填，非空 | 小区ID不能为空 |   |
| **PositionId** | 必填，非空 | 位置ID不能为空 |   |
| **AIAnalysisDelaySec** | 1-300秒 | AI分析延迟时间必须在1-300秒之间 |   |
| **ImageCropCoordinates** | `^\d+,\d+,\d+,\d+$` | 图片裁剪坐标格式必须为x1,y1,x2,y2 |   |
| **AlarmGracePeriodSeconds** | 0-60秒 | 告警静默期时长必须在0-60秒之间 |   |
| **CustomTimeWindowMinutes** | 1-1440分钟 | 自定义时间窗口大小必须在1-1440分钟之间 |   |

#### **2.2 业务逻辑验证规则** 

**AI策略验证**：
```csharp
private void ValidateAIStrategy(EventConfiguration config, List<string> errors, List<string> warnings)
{
    // 规则1：AI策略必须配置AI提示词
    if (config.EvaluationStrategy.Contains("AI") && string.IsNullOrWhiteSpace(config.AIPrompt))
    {
        errors.Add("选择AI策略时，AI提示词不能为空");
    }
    
    // 规则2：纯业务逻辑策略不需要AI提示词
    if (config.EvaluationStrategy == "BusinessOnly" && !string.IsNullOrWhiteSpace(config.AIPrompt))
    {
        warnings.Add("选择纯业务逻辑策略时，AI提示词配置将被忽略");
    }
    
    // 规则3：AI策略建议配置分析延迟
    if (config.EvaluationStrategy.Contains("AI") && !config.AIAnalysisDelaySec.HasValue)
    {
        warnings.Add("AI策略建议配置AI分析延迟时间以优化性能");
    }
    
    // 规则4：AI提示词长度检查
    if (!string.IsNullOrWhiteSpace(config.AIPrompt) && config.AIPrompt.Length < 10)
    {
        warnings.Add("AI提示词过短，建议提供详细的分析指导");
    }
    
    if (!string.IsNullOrWhiteSpace(config.AIPrompt) && config.AIPrompt.Length > 1000)
    {
        errors.Add("AI提示词过长，最大支持1000个字符");
    }
}
```

**时间窗口验证**：
```csharp
private void ValidateTimeWindow(EventConfiguration config, List<string> errors, List<string> warnings)
{
    // 规则1：自定义时间窗口必须指定大小
    if (config.CorrelationTimeWindow == "custom" && !config.CustomTimeWindowMinutes.HasValue)
    {
        errors.Add("选择自定义时间窗口时，必须设置自定义时间窗口大小");
    }
    
    // 规则2：非自定义时间窗口不需要自定义大小
    if (config.CorrelationTimeWindow != "custom" && config.CustomTimeWindowMinutes.HasValue)
    {
        warnings.Add($"选择{config.CorrelationTimeWindow}时间窗口时，自定义时间窗口大小配置将被忽略");
    }
}
```

#### **2.3 设备信号配置验证** 

```csharp
private void ValidateDeviceSignal(DeviceSignalConfiguration? deviceSignal, List<string> errors, List<string> warnings)
{
    if (deviceSignal == null)
    {
        warnings.Add("未配置设备信号，事件将无法自动触发");
        return;
    }
    
    // 规则1：主题配置验证
    if (deviceSignal.Topics == null || !deviceSignal.Topics.Any())
    {
        errors.Add("设备信号主题不能为空");
    }
    else
    {
        foreach (var topic in deviceSignal.Topics)
        {
            if (string.IsNullOrWhiteSpace(topic))
            {
                errors.Add("设备信号主题不能包含空值");
                continue;
            }
            
            // MQTT主题格式验证
            if (topic.Contains("#") && !topic.EndsWith("#"))
            {
                errors.Add($"MQTT通配符#只能用于主题末尾: {topic}");
            }
            
            if (topic.Contains("//"))
            {
                errors.Add($"MQTT主题不能包含连续的斜杠: {topic}");
            }
            
            if (topic.StartsWith("/") || topic.EndsWith("/"))
            {
                warnings.Add($"MQTT主题建议不要以斜杠开头或结尾: {topic}");
            }
        }
    }
    
    // 规则2：触发字段验证
    if (string.IsNullOrWhiteSpace(deviceSignal.TriggerField))
    {
        errors.Add("设备信号触发字段不能为空");
    }
    else
    {
        // 字段名格式检查
        if (!System.Text.RegularExpressions.Regex.IsMatch(deviceSignal.TriggerField, @"^[a-zA-Z_][a-zA-Z0-9_]*(\.[a-zA-Z_][a-zA-Z0-9_]*)*$"))
        {
            errors.Add($"触发字段名格式无效: {deviceSignal.TriggerField}");
        }
    }
    
    // 规则3：触发值映射验证
    if (deviceSignal.TriggerValues == null || !deviceSignal.TriggerValues.Any())
    {
        errors.Add("设备信号触发值映射不能为空");
    }
    else
    {
        // 检查映射完整性
        var hasTrue = deviceSignal.TriggerValues.ContainsKey("true");
        var hasFalse = deviceSignal.TriggerValues.ContainsKey("false");
        
        if (!hasTrue || !hasFalse)
        {
            warnings.Add("建议为true和false状态都配置触发值映射");
        }
        
        // 检查映射值的有效性
        foreach (var kvp in deviceSignal.TriggerValues)
        {
            if (string.IsNullOrWhiteSpace(kvp.Value))
            {
                errors.Add($"触发值映射的值不能为空: {kvp.Key}");
            }
        }
    }
    
    // 规则4：保持超时时间验证
    if (deviceSignal.HoldingTimeoutSec <= 0)
    {
        errors.Add("设备信号保持超时时间必须大于0");
    }
}
```

#### **2.4 规则配置验证** ❌ **需新增**

```csharp
private void ValidateRuleConfiguration(RuleConfiguration ruleConfig, List<string> errors, List<string> warnings)
{
    if (ruleConfig == null)
    {
        errors.Add("规则配置不能为空");
        return;
    }
    
    // 验证排除规则
    ValidateExclusionRules(ruleConfig.ExclusionRules, errors, warnings);
    
    // 验证业务规则
    ValidateBusinessRules(ruleConfig.BusinessRules, errors, warnings);
    
    // 验证AI结果规则
    ValidateAIResultRules(ruleConfig.AIResultRules, errors, warnings);
    
    // 验证告警配置
    ValidateAlarmConfiguration(ruleConfig.AlarmConfiguration, errors, warnings);
}

private void ValidateExclusionRules(List<RuleGroup>? exclusionRules, List<string> errors, List<string> warnings)
{
    if (exclusionRules == null || !exclusionRules.Any())
    {
        // 排除规则可选，但建议配置
        warnings.Add("未配置排除规则，建议配置以过滤正常业务操作");
        return;
    }
    
    for (int i = 0; i < exclusionRules.Count; i++)
    {
        var rule = exclusionRules[i];
        ValidateRuleGroup(rule, $"排除规则组{i + 1}", errors, warnings);
    }
}

private void ValidateBusinessRules(List<RuleGroup>? businessRules, List<string> errors, List<string> warnings)
{
    if (businessRules == null || !businessRules.Any())
    {
        errors.Add("业务规则不能为空，至少需要一个规则组");
        return;
    }
    
    for (int i = 0; i < businessRules.Count; i++)
    {
        var rule = businessRules[i];
        ValidateRuleGroup(rule, $"业务规则组{i + 1}", errors, warnings);
    }
}

private void ValidateRuleGroup(RuleGroup ruleGroup, string context, List<string> errors, List<string> warnings)
{
    if (ruleGroup == null)
    {
        errors.Add($"{context}不能为null");
        return;
    }
    
    // 验证逻辑操作符
    if (string.IsNullOrWhiteSpace(ruleGroup.LogicalOperator))
    {
        errors.Add($"{context}的逻辑操作符不能为空");
    }
    else if (!new[] { "AND", "OR" }.Contains(ruleGroup.LogicalOperator.ToUpper()))
    {
        errors.Add($"{context}的逻辑操作符必须是AND或OR");
    }
    
    // 验证条件列表
    if (ruleGroup.Conditions == null || !ruleGroup.Conditions.Any())
    {
        errors.Add($"{context}必须包含至少一个条件");
        return;
    }
    
    for (int i = 0; i < ruleGroup.Conditions.Count; i++)
    {
        var condition = ruleGroup.Conditions[i];
        ValidateCondition(condition, $"{context}-条件{i + 1}", errors, warnings);
    }
}

private void ValidateCondition(Condition condition, string context, List<string> errors, List<string> warnings)
{
    if (condition == null)
    {
        errors.Add($"{context}不能为null");
        return;
    }
    
    // 验证字段名
    if (string.IsNullOrWhiteSpace(condition.FieldName))
    {
        errors.Add($"{context}的字段名不能为空");
    }
    else
    {
        // 字段名格式检查（支持嵌套字段，如 user.name）
        if (!System.Text.RegularExpressions.Regex.IsMatch(condition.FieldName, @"^[a-zA-Z_][a-zA-Z0-9_]*(\.[a-zA-Z_][a-zA-Z0-9_]*)*$"))
        {
            errors.Add($"{context}的字段名格式无效: {condition.FieldName}");
        }
    }
    
    // 验证数据类型
    if (string.IsNullOrWhiteSpace(condition.DataType))
    {
        errors.Add($"{context}的数据类型不能为空");
    }
    else if (!new[] { "string", "number", "datetime", "boolean" }.Contains(condition.DataType.ToLower()))
    {
        errors.Add($"{context}的数据类型必须是string、number、datetime或boolean");
    }
    
    // 验证操作符
    if (string.IsNullOrWhiteSpace(condition.Operator))
    {
        errors.Add($"{context}的操作符不能为空");
    }
    else
    {
        ValidateOperator(condition.Operator, condition.DataType, context, errors, warnings);
    }
    
    // 验证比较值（某些操作符如IsNull不需要值）
    if (!new[] { "IsNull", "IsNotNull", "IsEmpty", "IsNotEmpty" }.Contains(condition.Operator) && 
        condition.Value == null)
    {
        errors.Add($"{context}的比较值不能为空");
    }
    
    // 验证值与数据类型的兼容性
    ValidateValueCompatibility(condition.Value, condition.DataType, context, errors, warnings);
}

private void ValidateOperator(string operatorName, string dataType, string context, List<string> errors, List<string> warnings)
{
    var validOperators = new Dictionary<string, string[]>
    {
        ["string"] = new[] { "Equals", "NotEquals", "Contains", "StartsWith", "EndsWith", "IsNull", "IsNotNull", "IsEmpty", "IsNotEmpty", "Regex" },
        ["number"] = new[] { "Equals", "NotEquals", "GreaterThan", "LessThan", "GreaterThanOrEqual", "LessThanOrEqual", "IsNull", "IsNotNull" },
        ["datetime"] = new[] { "Equals", "NotEquals", "GreaterThan", "LessThan", "GreaterThanOrEqual", "LessThanOrEqual", "IsNull", "IsNotNull" },
        ["boolean"] = new[] { "Equals", "NotEquals", "IsNull", "IsNotNull" }
    };
    
    if (!string.IsNullOrWhiteSpace(dataType) && validOperators.ContainsKey(dataType.ToLower()))
    {
        if (!validOperators[dataType.ToLower()].Contains(operatorName))
        {
            errors.Add($"{context}的操作符'{operatorName}'不适用于数据类型'{dataType}'");
        }
    }
}

private void ValidateValueCompatibility(object? value, string dataType, string context, List<string> errors, List<string> warnings)
{
    if (value == null) return;
    
    var valueStr = value.ToString();
    if (string.IsNullOrEmpty(valueStr)) return;
    
    switch (dataType?.ToLower())
    {
        case "number":
            if (!double.TryParse(valueStr, out _))
            {
                errors.Add($"{context}的比较值'{valueStr}'不是有效的数字");
            }
            break;
            
        case "datetime":
            if (!DateTime.TryParse(valueStr, out _) && 
                !System.Text.RegularExpressions.Regex.IsMatch(valueStr, @"^now([+-]\d+\s*(second|minute|hour|day|week|month|year)s?)?$", System.Text.RegularExpressions.RegexOptions.IgnoreCase))
            {
                errors.Add($"{context}的比较值'{valueStr}'不是有效的日期时间格式");
            }
            break;
            
        case "boolean":
            if (!bool.TryParse(valueStr, out _) && 
                !new[] { "0", "1", "true", "false", "yes", "no" }.Contains(valueStr.ToLower()))
            {
                errors.Add($"{context}的比较值'{valueStr}'不是有效的布尔值");
            }
            break;
    }
}
```

---

### **阶段三：实时验证与用户体验** 🎨 **用户体验**

#### **3.1 实时验证反馈** ❌ **需新增**

**输入验证**：
```csharp
// 在 EventViewModel 中添加属性验证
public string EventId
{
    get => _eventId;
    set
    {
        if (SetProperty(ref _eventId, value))
        {
            // 实时验证事件ID格式
            ValidateEventIdFormat(value);
        }
    }
}

private void ValidateEventIdFormat(string eventId)
{
    ClearValidationError(nameof(EventId));
    
    if (string.IsNullOrWhiteSpace(eventId))
    {
        AddValidationError(nameof(EventId), "事件ID不能为空");
        return;
    }
    
    if (!System.Text.RegularExpressions.Regex.IsMatch(eventId, @"^EV\d{6}$"))
    {
        AddValidationError(nameof(EventId), "事件ID格式必须为EV开头的6位数字，如EV001001");
    }
}
```

**视觉反馈**：
```xml
<!-- 在 XAML 中添加验证状态显示 -->
<TextBox Text="{Binding EventId, UpdateSourceTrigger=PropertyChanged, ValidatesOnDataErrors=True}"
         Style="{StaticResource ValidatedTextBoxStyle}">
    <TextBox.ToolTip>
        <ToolTip>
            <StackPanel>
                <TextBlock Text="{Binding EventIdToolTip}" FontWeight="Bold"/>
                <TextBlock Text="{Binding EventIdValidationError}" 
                           Foreground="Red" 
                           Visibility="{Binding HasEventIdError, Converter={StaticResource BooleanToVisibilityConverter}}"/>
            </StackPanel>
        </ToolTip>
    </TextBox.ToolTip>
</TextBox>
```

#### **3.2 验证结果增强显示** ❌ **需新增**

**详细错误信息**：
```csharp
private void ShowEnhancedValidationResult(YamlValidationResult result)
{
    var dialog = new ValidationResultDialog
    {
        Owner = Application.Current.MainWindow,
        ValidationResult = result
    };
    
    dialog.ShowDialog();
}

// ValidationResultDialog.xaml 包含：
// - 错误分类显示（基础验证、业务逻辑、一致性检查）
// - 每个错误的详细说明和修复建议
// - 警告信息的重要性级别
// - 一键修复功能（对于可自动修复的问题）
```

## 📁 **文件修改清单**

### **核心修改文件**
```
EPConfigTool/ViewModels/MainViewModel.cs
├── ValidateCurrentConfigurationAsync() - 修改主验证方法
├── ValidateInMemoryConfigurationAsync() - 新增内存验证方法
├── ValidateWithDataAnnotations() - 新增基础验证方法
├── ValidateBusinessLogicAsync() - 新增业务逻辑验证
├── ValidateConfigurationConsistency() - 新增一致性验证
└── ValidateComplexStructures() - 新增复杂结构验证

EPConfigTool/ViewModels/EventViewModel.cs
├── 添加实时验证属性
├── ValidateEventIdFormat() - 事件ID实时验证
├── ValidateEvaluationStrategy() - 策略实时验证
└── INotifyDataErrorInfo 接口实现

EPConfigTool/Services/ValidationService.cs (新建)
├── IValidationService 接口定义
├── 设备信号验证方法
├── 规则配置验证方法
├── 条件验证方法
└── 操作符兼容性检查
```

### **UI 增强文件**
```
EPConfigTool/Views/ValidationResultDialog.xaml (新建)
├── 验证结果详细显示
├── 错误分类和修复建议
└── 一键修复按钮

EPConfigTool/Styles/ValidationStyles.xaml (新建)
├── ValidatedTextBoxStyle - 带验证状态的文本框样式
├── ValidationErrorTemplate - 验证错误显示模板
└── ValidationTooltipStyle - 验证提示样式

EPConfigTool/Views/EventConfigurationView.xaml
├── 添加实时验证绑定
├── 增强ToolTip显示
└── 验证状态视觉反馈
```

---

## 🧪 **测试计划**

### **单元测试**


### **集成测试**

## 🎯 **成功标准**

### **功能完整性**
- [x]  文件验证与内存验证使用相同的验证逻辑
- [ ]  所有配置项都有对应的验证规则
- [ ]  业务逻辑一致性检查覆盖所有策略组合
- [ ]  实时验证提供即时反馈

### **用户体验**
- [ ]  验证错误信息清晰明了，包含修复建议
- [ ]  验证警告帮助用户优化配置
- [ ]  验证界面友好，支持快速定位问题
- [ ]  帮助文档与验证规则保持一致

### **代码质量**
- [ ]  验证逻辑模块化，易于维护和扩展
- [ ]  单元测试覆盖率 > 90%
- [ ]  集成测试覆盖主要用户场景
- [ ]  代码符合现有架构规范

---

## 📖 **相关文档**

1. **现有帮助文档**：`EPConfigTool/HELP_SYSTEM_GUIDE.md`
2. **验证规则参考**：`EventProcessor.Core/Models/EventConfiguration.cs`
3. **YAML配置服务**：`EPConfigTool/Services/YamlConfigurationService.cs`
4. **数据注解文档**：Microsoft Data Annotations 官方文档
