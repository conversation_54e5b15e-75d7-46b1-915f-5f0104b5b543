## EPConfigTool 重构

### 🎯 目标
基于实际使用场景（打开→修改→另存为），将EPConfigTool重构为YAML配置编辑器。

### 📋 技术规格

#### 目标架构
```
EPConfigTool.Simple/
├── Program.cs (入口点)
├── MainForm.cs (主窗口)
├── ConfigurationService.cs (配置服务)
├── ValidationHelper.cs (验证辅助)
└── Models/ (从EventProcessor.Core引用)
```

#### 核心功能规格
1. **文件操作**：打开YAML文件、另存为YAML文件
2. **表单编辑**：基于UnifiedConfiguration模型自动生成编辑表单
3. **实时验证**：基于DataAnnotations的字段验证
4. **错误提示**：友好的验证错误显示

#### 技术选型
- **UI框架**：WinForms（轻量级）
- **YAML处理**：YamlDotNet
- **验证机制**：System.ComponentModel.DataAnnotations
- **目标框架**：.NET 8.0

### 🔧 实施检查清单

#### 阶段1：项目结构创建
1. 在EPConfigTool目录下创建新项目EPConfigTool.Simple
2. 创建EPConfigTool.Simple.csproj文件，配置项目属性和依赖
3. 添加对EventProcessor.Core项目的引用
4. 添加YamlDotNet NuGet包引用
5. 创建基础目录结构（Models、Services、Forms）

#### 阶段2：核心服务实现
6. 实现ConfigurationService.cs，包含LoadConfiguration和SaveConfiguration方法
7. 实现ValidationHelper.cs，基于DataAnnotations进行配置验证
8. 创建简化的错误处理机制
9. 实现YAML序列化/反序列化逻辑

#### 阶段3：主窗口实现
10. 创建MainForm.cs主窗口，使用WinForms设计器
11. 实现文件菜单（打开、另存为、退出）
12. 创建配置编辑区域，使用TabControl组织不同配置节
13. 实现基于UnifiedConfiguration的动态表单生成
14. 添加验证状态显示区域

#### 阶段4：表单控件绑定
15. 实现EventProcessor配置节的表单控件
16. 实现MQTT配置节的表单控件
17. 实现ErrorHandling配置节的表单控件
18. 实现Logging和Serilog配置节的表单控件
19. 配置数据绑定和双向同步

#### 阶段5：验证和错误处理
20. 集成DataAnnotations验证到表单控件
21. 实现实时验证反馈机制
22. 创建友好的错误消息显示
23. 实现保存前的完整性检查
24. 添加验证失败时的用户引导

#### 阶段6：文件操作完善
25. 实现文件打开对话框，过滤.yaml和.yml文件
26. 实现文件保存对话框，默认.yaml扩展名
27. 添加文件变更检测和未保存提醒
28. 实现最近文件列表功能
29. 添加文件路径显示和标题栏更新

#### 阶段7：用户体验优化
30. 实现窗口状态记忆（位置、大小）
31. 添加键盘快捷键支持（Ctrl+O, Ctrl+S等）
32. 实现配置字段的智能默认值
33. 添加必填字段的视觉标识
34. 优化Tab键导航顺序

#### 阶段8：测试和调试
35. 创建基础单元测试项目
36. 测试YAML文件的加载和保存功能
37. 测试各种验证场景
38. 测试异常情况的处理
39. 进行用户界面的可用性测试

#### 阶段9：部署准备
40. 配置项目的发布配置
41. 创建独立部署的发布脚本
42. 测试在目标环境的运行情况
43. 创建简化的用户文档
44. 准备版本发布说明

#### 阶段10：清理和文档
45. 清理不需要的代码和注释
46. 更新项目README文档
47. 创建简化版本的使用说明
48. 备份原有复杂版本的EPConfigTool
49. 更新部署脚本指向新版本

### 🎯 最终动作
50. **部署简化版EPConfigTool.Simple到生产环境，替换原有的复杂版本**

### 📊 预期成果
- **代码量减少**：从~50个文件减少到~10个文件
- **启动速度**：提升80%以上
- **维护成本**：降低90%
- **用户体验**：符合实际使用场景的简化界面
- **功能完整性**：保留所有核心YAML编辑功能
        