# ================================================================
# EP_V3 统一配置示例 - 单文件配置（合并AI提示词）
# Event Processor V3 架构统一配置格式
# ================================================================

# ================================================================
# 核心配置区域
# ================================================================

# 基础配置
COMM_ID=101013
POSITION_ID=P001LfyBmIn
EVENT_IDS=["EV001008", "EV001009", "EV001003", "EV001005"]

# 位置信息
EP_POSITION_NAME=来福园北门车辆入口

# 日志配置
LOG_LEVEL=INFO

# ================================================================
# 外部服务配置
# ================================================================

# FTP配置
FTP_HOST=api.bangdouni.com
FTP_PORT=21
FTP_USERNAME=ep-download
FTP_PASSWORD=Ep@2024
FTP_REMOTE_DIR=/httpdocs/down/ep-download
FTP_IS_ENABLED=true
FTP_URL_PREFIX=https://api.bangdouni.com/down/ep-download

# MQTT服务器配置
MQTT_BROKER_HOST=mq.bangdouni.com
MQTT_BROKER_PORT=1883
MQTT_USERNAME=bdn_ai_process
MQTT_PASSWORD=Bdn@2024

# ================================================================
# V3架构配置参数
# ================================================================

# V3 AI配置（MQTT外部服务）
EP_AI_ANALYSIS_DELAY_SEC=6
EP_AI_RESULT_TIMEOUT=60
EP_AI_DETECT_TIME_BEFORE_ALARM=15

# V3 统一图片配置（移除per-event重复配置）
EP_PV_BIND_IPC_IMAGE_DIR=E:\FTP-IPC\LFY-IN01
EP_PV_IMAGE_CROP_COORDINATES=[751, 652, 2533, 1343]

# V3 通用配置
EP_PV_AI_RESULT_WAIT_TIMEOUT=60
EP_PV_DETAIL_INFO_SOURCE_TYPE=MQTT
EP_PV_DETAIL_INFO_SOURCE=
EP_PV_DETAIL_INFO_FIELDS=[]

# ================================================================
# 事件配置区域
# ================================================================

# ================================================================
# EV001008 - 三轮车快递车滞留检测（🤖 AI模式）
# ================================================================

# 基础配置
EVENT_ID_EV001008_EP_START_DEVICE_EVENT_TOPIC=["device/BDN861290073715232/event"]
EVENT_ID_EV001008_EP_START_FIELD_FROM_DEVICE_EVENT=I1
EVENT_ID_EV001008_EP_POSITION_START_VALUE_FROM_DEVICE_EVENT={"true": "0", "false": "1"}
EVENT_ID_EV001008_EP_PV_HOLDING_TIMEOUT=11

# 告警配置
EVENT_ID_EV001008_EP_PV_ALARM_TOPIC=hq/101013/P001LfyBmIn/event
EVENT_ID_EV001008_EP_PV_DETAIL_INFO_TOPIC=ajb/101013/in/P001LfyBmIn/time_log
EVENT_ID_EV001008_EP_PV_DETAIL_INFO_FIELDS=[{"field_name": "设备", "field_keyword": "", "field_default": "帮豆你智能门岗监测"}]

# AI提示词配置（V3统一格式）
EVENT_ID_EV001008_EP_AI_PROMPT=图片是否存在以下类型的车辆[三轮车]或[快递车], 请用JSON格式返回是或否. 返回格式: {"detected": true/false, "vehicle_types": ["检测到的车辆类型"], "confidence": 0.95}

# V3简化排除匹配配置
EVENT_ID_EV001008_EP_MQTT_EXCLUDE_INFO_SOURCE=ajb/101013/in/P001LfyBmIn/time_log
EVENT_ID_EV001008_EP_MQTT_EXCLUDE_INFO_FIELD=log_car_no
EVENT_ID_EV001008_EP_MQTT_EXCLUDE_LOGIC=exists

# ================================================================
# EV001009 - 特种车超时滞留检测（🤖 AI模式）
# ================================================================

# 基础配置
EVENT_ID_EV001009_EP_START_DEVICE_EVENT_TOPIC=["device/BDN861290073715232/event"]
EVENT_ID_EV001009_EP_START_FIELD_FROM_DEVICE_EVENT=I1
EVENT_ID_EV001009_EP_POSITION_START_VALUE_FROM_DEVICE_EVENT={"true": "0", "false": "1"}
EVENT_ID_EV001009_EP_PV_HOLDING_TIMEOUT=15

# 告警配置
EVENT_ID_EV001009_EP_PV_ALARM_TOPIC=hq/101013/P001LfyBmIn/event
EVENT_ID_EV001009_EP_PV_DETAIL_INFO_TOPIC=ajb/101013/in/P001LfyBmIn/time_log
EVENT_ID_EV001009_EP_PV_DETAIL_INFO_FIELDS=[{"field_name": "设备", "field_keyword": "", "field_default": "帮豆你智能门岗监测"}]

# AI提示词配置（V3统一格式）
EVENT_ID_EV001009_EP_AI_PROMPT=图片是否存在以下类型的车辆:[消防车、救护车、垃圾清运车、警车、工程车], 请用JSON格式返回检测结果. 返回格式: {"detected": true/false, "vehicle_types": ["检测到的特种车辆类型"], "emergency_level": "high/medium/low"}

# V3简化排除匹配配置
EVENT_ID_EV001009_EP_MQTT_EXCLUDE_INFO_SOURCE=ajb/101013/in/P001LfyBmIn/time_log
EVENT_ID_EV001009_EP_MQTT_EXCLUDE_INFO_FIELD=log_car_no
EVENT_ID_EV001009_EP_MQTT_EXCLUDE_LOGIC=exists

# ================================================================
# EV001003 - 月租车未过期超时滞留（📊 业务逻辑模式）
# ================================================================

# 基础配置
EVENT_ID_EV001003_EP_START_DEVICE_EVENT_TOPIC=["device/BDN861290073715232/event"]
EVENT_ID_EV001003_EP_START_FIELD_FROM_DEVICE_EVENT=I1
EVENT_ID_EV001003_EP_POSITION_START_VALUE_FROM_DEVICE_EVENT={"true": "0", "false": "1"}
EVENT_ID_EV001003_EP_PV_HOLDING_TIMEOUT=15

# 告警配置
EVENT_ID_EV001003_EP_PV_ALARM_TOPIC=hq/101013/P001LfyBmIn/event
EVENT_ID_EV001003_EP_PV_DETAIL_INFO_TOPIC=ajb/101013/in/P001LfyBmIn/time_log
EVENT_ID_EV001003_EP_PV_DETAIL_INFO_FIELDS=[{"field_name": "详情", "field_keyword": "CardType,log_car_no", "field_format": "[{CardType}][{log_car_no}]"}]

# 业务逻辑配置（V3格式）
EVENT_ID_EV001003_EP_BusinessLogic_JUDGE_SOURCE=ajb/101013/in/P001LfyBmIn/time_log
EVENT_ID_EV001003_EP_BusinessLogic_JUDGE_FIELD=CardType
EVENT_ID_EV001003_EP_BusinessLogic_JUDGE_LOGIC=contains
EVENT_ID_EV001003_EP_BusinessLogic_JUDGE_VALUE=月租卡|万全卡|贵宾卡|储值卡

# AI提示词配置（业务逻辑事件设为None）
EVENT_ID_EV001003_EP_AI_PROMPT=None

# V3简化排除匹配配置
EVENT_ID_EV001003_EP_MQTT_EXCLUDE_INFO_SOURCE=ajb/101013/in/P001LfyBmIn/time_log
EVENT_ID_EV001003_EP_MQTT_EXCLUDE_INFO_FIELD=log_car_no
EVENT_ID_EV001003_EP_MQTT_EXCLUDE_LOGIC=exists

# ================================================================
# EV001005 - 临时车超时滞留（📊 业务逻辑模式）
# ================================================================

# 基础配置
EVENT_ID_EV001005_EP_START_DEVICE_EVENT_TOPIC=["device/BDN861290073715232/event"]
EVENT_ID_EV001005_EP_START_FIELD_FROM_DEVICE_EVENT=I1
EVENT_ID_EV001005_EP_POSITION_START_VALUE_FROM_DEVICE_EVENT={"true": "0", "false": "1"}
EVENT_ID_EV001005_EP_PV_HOLDING_TIMEOUT=5

# 告警配置
EVENT_ID_EV001005_EP_PV_ALARM_TOPIC=hq/101013/P001LfyBmIn/event
EVENT_ID_EV001005_EP_PV_DETAIL_INFO_TOPIC=ajb/101013/in/P001LfyBmIn/time_log
EVENT_ID_EV001005_EP_PV_DETAIL_INFO_FIELDS=[{"field_name": "详情", "field_keyword": "CardType,log_car_no", "field_format": "[{CardType}][{log_car_no}]"}]

# 业务逻辑配置（V3格式）
EVENT_ID_EV001005_EP_BusinessLogic_JUDGE_SOURCE=ajb/101013/in/P001LfyBmIn/time_log
EVENT_ID_EV001005_EP_BusinessLogic_JUDGE_FIELD=CardType
EVENT_ID_EV001005_EP_BusinessLogic_JUDGE_LOGIC=contains
EVENT_ID_EV001005_EP_BusinessLogic_JUDGE_VALUE=临保卡

# AI提示词配置（业务逻辑事件设为None）
EVENT_ID_EV001005_EP_AI_PROMPT=None

# V3简化排除匹配配置
EVENT_ID_EV001005_EP_MQTT_EXCLUDE_INFO_SOURCE=ajb/101013/in/P001LfyBmIn/time_log
EVENT_ID_EV001005_EP_MQTT_EXCLUDE_INFO_FIELD=log_car_no
EVENT_ID_EV001005_EP_MQTT_EXCLUDE_LOGIC=exists

# ================================================================
# 配置说明和使用指南
# ================================================================

# ================================================================
# V3统一配置架构说明
# ================================================================
# 
# EP_V3架构统一了配置管理，将AI提示词从独立的events.ini文件
# 合并到.env文件中，实现真正的单文件配置。
#
# 核心改进：
# ✅ 单文件配置 - 不再需要维护events.ini文件
# ✅ 统一参数格式 - AI提示词使用ENV格式存储
# ✅ 简化启动命令 - 移除--events-ini参数
# ✅ 更好的版本控制 - 单文件更容易管理

# ================================================================
# AI提示词配置说明
# ================================================================
#
# EVENT_ID_{event_id}_EP_AI_PROMPT 参数说明：
# 🤖 AI事件：具体的AI分析提示词内容
# 📊 业务逻辑事件：设置为 None（不进行AI分析）
# ⚙️ 混合模式：既有AI分析又有业务逻辑判断
#
# 提示词编写建议：
# - 明确指定返回格式为JSON
# - 包含检测置信度要求
# - 详细描述检测目标特征
# - 支持多行内容使用\\n换行符

# ================================================================
# V3事件模式说明
# ================================================================
#
# 🤖 纯AI模式（如EV001008, EV001009）：
# - 有AI提示词配置
# - 使用图像分析检测
# - 简化的排除匹配机制
#
# 📊 纯业务逻辑模式（如EV001003, EV001005）：
# - AI提示词设为None
# - 基于MQTT数据进行逻辑判断
# - 支持contains/equals/regex等多种匹配
#
# ⚙️ 混合模式（预留扩展）：
# - 同时配置AI提示词和业务逻辑
# - 双重验证机制
# - 更高的准确性

# ================================================================
# 排除匹配简化配置
# ================================================================
#
# V3简化排除匹配配置格式：
# EVENT_ID_{event_id}_EP_MQTT_EXCLUDE_INFO_SOURCE - MQTT主题
# EVENT_ID_{event_id}_EP_MQTT_EXCLUDE_INFO_FIELD - 检查字段
# EVENT_ID_{event_id}_EP_MQTT_EXCLUDE_LOGIC - 匹配逻辑
#
# 支持的匹配逻辑：
# - exists: 字段存在即排除
# - equals: 字段值精确匹配
# - contains: 字段值包含指定内容
# - regex: 正则表达式匹配
#
# 多值匹配支持：
# 使用 | 分隔多个匹配值，如："月租卡|万全卡|贵宾卡"

# ================================================================
# 使用方式和命令示例
# ================================================================
#
# V3统一配置启动方式：
# python3 main.py --env .env.unified.example
# python3 main.py --env .env.unified.example --debug
#
# 配置验证模式：
# python3 main.py --env .env.unified.example -t
# python3 main.py --env .env.unified.example -t --debug
#
# 设备控制系统：
# python3 device_control_main.py --config device_control.ini
#
# 运行测试：
# python3 -m pytest tests/test_v3_*.py -v

# ================================================================
# 配置迁移指南
# ================================================================
#
# 从V2双文件配置迁移到V3统一配置：
#
# 1. 备份现有配置：
#    cp .env .env.v2.backup
#    cp events.ini events.ini.v2.backup
#
# 2. 合并AI提示词：
#    将events.ini中[prompts]段的内容迁移到.env文件
#    格式：EVENT_ID_{event_id}_EP_AI_PROMPT=提示词内容
#
# 3. 更新启动脚本：
#    旧版：python3 main.py --env .env --events-ini events.ini
#    新版：python3 main.py --env .env
#
# 4. 验证配置：
#    python3 main.py --env .env -t
#
# 详细迁移指南请参考：CONFIG_MIGRATION_V3.md

# ================================================================
# 技术支持
# ================================================================
#
# 如遇到配置问题：
# 1. 运行配置测试模式检查错误详情
# 2. 对比本示例文件确认参数格式
# 3. 检查CLAUDE.md中的配置规范
# 4. 确保所有必需参数都已正确配置