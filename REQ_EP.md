# Event Processor V3 智能事件处理系统 需求规格说明书

## 项目概述

### 目的
Event Processor V3 是一个基于MQTT的智能事件处理服务，采用独立AI模块+MQTT小区架构，支持AI分析、业务逻辑判断、统一排除匹配等功能，适用于智慧社区、工业监控等场景。

### 核心架构特性
- **分离式架构**：采用独立AI服务+MQTT小区机制，替代集成式AI分析
- **统一事件处理**：AI事件与业务逻辑事件使用相同的处理管道
- **简化排除匹配**：统一使用MQTT简化格式，移除复杂JSON配置
- **配置驱动**：通过配置参数灵活控制功能组合

### 范围
本系统支持单点位多事件并发处理，具备灵活的配置能力和高性能的事件处理能力。

---

## 功能需求

### 设备事件监听
系统应实时接收并处理来自多个设备的MQTT事件消息，支持多事件并发监听。

### 外部AI服务集成（V3新架构）
系统采用独立AI服务架构，通过MQTT消息进行AI控制和结果接收。

#### 核心特性
- **AI控制消息**：通过MQTT发送AI分析请求到外部AI服务
- **结果订阅**：订阅AI服务返回的分析结果消息
- **延迟触发**：使用`EP_AI_ANALYSIS_DELAY_SEC`参数控制AI分析启动时机
- **无内部集成**：移除内部AI客户端，完全依赖外部AI服务

#### MQTT AI控制协议
**控制消息主题格式**：
```
ai/{COMM_ID}/{POSITION_ID}/control
```

**控制消息格式**：
```json
{
  "request_id": "EV001008_1691234567890",
  "event_id": "EV001008", 
  "image_path": "/path/to/image.jpg",
  "prompt": "AI分析提示词",
  "timestamp": "2024-07-24T15:30:45"
}
```

**结果消息主题格式**：
```
ai/{COMM_ID}/{POSITION_ID}/result  
```

**结果消息格式**：
```json
{
  "request_id": "EV001008_1691234567890",
  "success": true,
  "result": {
    "detected_events": ["EV001008"],
    "confidence": 0.85,
    "analysis_details": {"EV001008": "检测到三轮车"}
  }
}
```

### 统一排除匹配机制（V3简化版）
系统采用统一的MQTT简化排除匹配格式，移除复杂的JSON配置，所有排除匹配都封装为MQTT形式。

#### 简化配置格式
```ini
# 排除信息源MQTT主题（必需）
EVENT_ID_{event_id}_EP_MQTT_EXCLUDE_INFO_SOURCE=ajb/101013/in/P001LfyBmIn/time_log

# 排除检查字段名（必需）  
EVENT_ID_{event_id}_EP_MQTT_EXCLUDE_INFO_FIELD=log_car_no

# 排除逻辑（可选，默认exists）
EVENT_ID_{event_id}_EP_MQTT_EXCLUDE_LOGIC=exists
```

#### 支持的排除逻辑
- **exists**：字段存在性检查，只要字段存在就排除事件
- **扩展预留**：可扩展contains、equals等逻辑（当前版本暂不支持）

#### 配置对比
**旧格式（已废弃）**：
```ini
EP_PV_EXCLUDE_INFO_SOURCE_TYPE=MQTT
EP_PV_EXCLUDE_INFO_SOURCE=topic
EP_PV_EXCLUDE_INFO_FIELDS=[{"field_keyword": "field", "exclude_condition": "exists"}]
EP_PV_EXCLUDE_LOGIC=ANY
```

**新格式（V3标准）**：
```ini
EP_MQTT_EXCLUDE_INFO_SOURCE=topic
EP_MQTT_EXCLUDE_INFO_FIELD=field
EP_MQTT_EXCLUDE_LOGIC=exists
```

### 统一事件处理架构
系统采用统一的事件处理架构，支持AI分析、业务逻辑判断、排除匹配等功能的任意组合。

#### 统一处理流程
```
事件触发 → 图片处理 → 排除匹配检查 → 条件判断 → 告警发送
                                    ↓
                        AI分析 ∩ 业务逻辑判断
                       (根据配置执行任意组合)
```

#### 支持的功能组合模式

**模式1: 纯AI事件**
```ini
# events.ini - 配置AI提示词
[prompts]
EV001008=图片是否存在以下类型的车辆[三轮车]或[快递车]

# .env - 可选配置排除匹配
EVENT_ID_EV001008_EP_MQTT_EXCLUDE_INFO_SOURCE=ajb/101013/in/P001LfyBmIn/time_log
EVENT_ID_EV001008_EP_MQTT_EXCLUDE_INFO_FIELD=log_car_no
EVENT_ID_EV001008_EP_MQTT_EXCLUDE_LOGIC=exists
```

**模式2: 纯业务逻辑事件**  
```ini
# events.ini - 空AI提示词
[prompts]
EV001003 = 

# .env - 配置业务逻辑判断
EVENT_ID_EV001003_EP_BusinessLogic_JUDGE_SOURCE=ajb/101013/in/P001LfyBmIn/time_log
EVENT_ID_EV001003_EP_BusinessLogic_JUDGE_FIELD=CardType
EVENT_ID_EV001003_EP_BusinessLogic_JUDGE_LOGIC=contains
EVENT_ID_EV001003_EP_BusinessLogic_JUDGE_VALUE=月租卡|万全卡|贵宾卡|储值卡
```

**模式3: AI + 业务逻辑混合事件**
```ini
# events.ini - 同时配置AI提示词
[prompts]
EV001010=图片中是否有人员站立或聚集

# .env - 同时配置业务逻辑和排除匹配
EVENT_ID_EV001010_EP_BusinessLogic_JUDGE_FIELD=PermissionLevel
EVENT_ID_EV001010_EP_BusinessLogic_JUDGE_LOGIC=exists
EVENT_ID_EV001010_EP_MQTT_EXCLUDE_INFO_SOURCE=face/recognition/topic
EVENT_ID_EV001010_EP_MQTT_EXCLUDE_INFO_FIELD=person_id
```

### 业务逻辑事件处理
系统支持纯业务逻辑事件，无需AI分析即可基于业务规则进行告警判断。

#### 业务逻辑配置参数
- `EP_BusinessLogic_JUDGE_SOURCE`：业务信息来源MQTT主题
- `EP_BusinessLogic_JUDGE_FIELD`：业务判断字段名
- `EP_BusinessLogic_JUDGE_LOGIC`：判断逻辑（contains/equals/exists）
- `EP_BusinessLogic_JUDGE_VALUE`：判断值，支持多值匹配（|分隔）

#### 多值匹配支持
```ini
# 支持多种卡类型匹配
EVENT_ID_EV001003_EP_BusinessLogic_JUDGE_VALUE=月租卡|万全卡|贵宾卡|储值卡
```

### 图片处理与上传
系统为所有事件类型（AI事件和业务逻辑事件）提供统一的图片处理能力。

- **图片查找**：基于时间戳匹配查找对应图片
- **图片裁剪**：使用`EP_PV_IMAGE_CROP_COORDINATES`参数进行裁剪
- **FTP上传**：支持FTP上传和CDN分发
- **URL生成**：生成标准CDN图片URL供告警消息使用

### 告警通知系统
系统支持基于MQTT的告警通知，根据AI分析结果或业务逻辑判断结果发送告警。

#### 告警主题灵活配置（V3新特性）
- **移除全局前缀**：不再使用`EP_COMPANY_NAME`全局参数
- **事件特定主题**：每个事件可在`EP_PV_ALARM_TOPIC`中自由定义完整主题路径
- **多租户支持**：支持不同事件发送到不同公司的告警主题

**配置示例**：
```ini
# V3架构：直接指定完整主题路径
EVENT_ID_EV001008_EP_PV_ALARM_TOPIC=hq/101013/P001LfyBmIn/event
EVENT_ID_EV001009_EP_PV_ALARM_TOPIC=company_a/101013/P001LfyBmIn/event
```

#### 告警消息格式
```json
{
  "eventid": "事件ID",
  "comm_id": "小区编号", 
  "positionid": "点位编号",
  "data": "详情数据字符串",
  "urls": ["图片URL列表"]
}
```

### 事件状态管理（V3简化版）
系统使用简化的事件状态管理，移除复杂的阶段管理。

#### 简化状态枚举
```python
class EventState(Enum):
    IDLE = "idle"                    # 空闲等待
    COLLECTING = "collecting"        # 信息收集中  
    WAITING_AI_RESULT = "waiting_ai_result"  # 等待AI结果
    ALARM_READY = "alarm_ready"      # 告警就绪
    EXCLUDED = "excluded"            # 已被排除
```

#### 状态转换流程
```
IDLE → COLLECTING → [排除检查] → WAITING_AI_RESULT → ALARM_READY → IDLE
       ↓                            ↓
   EXCLUDED ←───────────────────── AI分析完成
```

### 配置热更新
系统支持运行时配置热更新，无需重启即可生效。
- **AI提示词更新**：events.ini文件的AI提示词支持热更新
- **其他配置变更**：需要重启系统才能生效

### 日志记录与追踪
系统详细记录所有关键操作、状态变更、AI小区、告警发送等日志。

#### 日志级别
- **INFO**：关键状态变更、告警发送成功/失败
- **DEBUG**：详细的配置信息、MQTT消息、AI交互
- **WARNING**：配置问题、处理异常
- **ERROR**：系统错误、小区失败

---

## 接口需求

### 设备事件MQTT订阅
- **主题格式**：可配置，支持多主题订阅
- **数据格式**：JSON，包含事件触发/解除信号及时间戳

### 外部AI服务MQTT接口

#### AI控制消息发布
- **主题**：`ai/{COMM_ID}/{POSITION_ID}/control`
- **QoS**：1（确保消息送达）
- **消息格式**：JSON，包含request_id、event_id、image_path、prompt等

#### AI结果消息订阅  
- **主题**：`ai/{COMM_ID}/{POSITION_ID}/result`
- **QoS**：1（确保消息送达）
- **消息格式**：JSON，包含request_id、success、result等

### 排除信息MQTT订阅
- **主题格式**：通过`EP_MQTT_EXCLUDE_INFO_SOURCE`配置
- **数据格式**：JSON，包含排除检查所需字段

### 业务逻辑信息MQTT订阅
- **主题格式**：通过`EP_BusinessLogic_JUDGE_SOURCE`配置  
- **数据格式**：JSON，包含业务逻辑判断所需字段

### 告警MQTT发布
- **主题格式**：通过`EP_PV_ALARM_TOPIC`配置
- **数据格式**：JSON，包含事件ID、点位、详情、图片URL等

### 详情信息MQTT订阅
- **主题格式**：通过`EP_PV_DETAIL_INFO_TOPIC`配置
- **数据格式**：JSON，用于告警消息详情字段拼接

---

## 配置参数定义

### 全局参数表

| 参数 | 类型 | 必需 | 默认值 | 说明 |
|------|------|------|--------|------|
| COMM_ID | string | 是 | - | 小区编号 |
| POSITION_ID | string | 是 | - | 点位编号 |
| EVENT_IDS | string[] | 是 | - | 事件ID列表 |
| MQTT_BROKER_HOST | string | 是 | - | MQTT服务器地址 |
| MQTT_BROKER_PORT | int | 否 | 1883 | MQTT服务器端口 |
| MQTT_USERNAME | string | 否 | "" | MQTT用户名 |
| MQTT_PASSWORD | string | 否 | "" | MQTT密码 |
| EP_AI_ANALYSIS_DELAY_SEC | int | 否 | 6 | AI分析延迟启动时间(秒) |
| EP_PV_BIND_IPC_IMAGE_DIR | string | 是 | - | 图片目录路径 |
| EP_PV_IMAGE_CROP_COORDINATES | array | 否 | - | 图片裁剪坐标 |
| FTP_HOST | string | 否 | - | FTP服务器地址 |
| FTP_PORT | int | 否 | 21 | FTP服务器端口 |
| FTP_USERNAME | string | 否 | - | FTP用户名 |
| FTP_PASSWORD | string | 否 | - | FTP密码 |
| FTP_REMOTE_DIR | string | 否 | - | FTP远程目录 |
| FTP_URL_PREFIX | string | 否 | - | FTP文件URL前缀 |
| LOG_LEVEL | string | 否 | INFO | 日志级别 |

### 事件特定参数表

#### 基础事件参数
| 参数 | 类型 | 必需 | 说明 |
|------|------|------|------|
| EVENT_ID_{event_id}_EP_START_DEVICE_EVENT_TOPIC | array | 是 | 触发设备MQTT主题 |
| EVENT_ID_{event_id}_EP_START_FIELD_FROM_DEVICE_EVENT | string | 是 | 触发信号字段名 |
| EVENT_ID_{event_id}_EP_POSITION_START_VALUE_FROM_DEVICE_EVENT | object | 是 | 触发信号值映射 |
| EVENT_ID_{event_id}_EP_PV_HOLDING_TIMEOUT | int | 是 | 滞留超时时间(秒) |

#### MQTT排除匹配参数（V3新格式）
| 参数 | 类型 | 必需 | 默认值 | 说明 |
|------|------|------|--------|------|
| EVENT_ID_{event_id}_EP_MQTT_EXCLUDE_INFO_SOURCE | string | 否 | - | 排除信息MQTT主题 |
| EVENT_ID_{event_id}_EP_MQTT_EXCLUDE_INFO_FIELD | string | 否 | - | 排除检查字段名 |
| EVENT_ID_{event_id}_EP_MQTT_EXCLUDE_LOGIC | string | 否 | exists | 排除逻辑 |

#### 业务逻辑参数（V3重命名）
| 参数 | 类型 | 必需 | 默认值 | 说明 |
|------|------|------|--------|------|
| EVENT_ID_{event_id}_EP_BusinessLogic_JUDGE_SOURCE | string | 否 | - | 业务信息MQTT主题 |
| EVENT_ID_{event_id}_EP_BusinessLogic_JUDGE_FIELD | string | 否 | - | 业务判断字段名 |
| EVENT_ID_{event_id}_EP_BusinessLogic_JUDGE_LOGIC | string | 否 | contains | 判断逻辑 |
| EVENT_ID_{event_id}_EP_BusinessLogic_JUDGE_VALUE | string | 否 | - | 判断值，支持多值(\|分隔) |

#### 告警参数
| 参数 | 类型 | 必需 | 说明 |
|------|------|------|------|
| EVENT_ID_{event_id}_EP_PV_ALARM_TOPIC | string | 是 | 告警MQTT主题 |
| EVENT_ID_{event_id}_EP_PV_DETAIL_INFO_TOPIC | string | 否 | 详情信息MQTT主题 |
| EVENT_ID_{event_id}_EP_PV_DETAIL_INFO_FIELDS | array | 否 | 详情字段配置 |

### 配置示例

#### 纯AI事件配置
```ini
# events.ini
[prompts]
EV001008=图片是否存在以下类型的车辆[三轮车]或[快递车], 请用JSON格式返回是或否

# .env
EVENT_IDS=["EV001008"]
EVENT_ID_EV001008_EP_START_DEVICE_EVENT_TOPIC=["device/BDN861290073715232/event"]
EVENT_ID_EV001008_EP_START_FIELD_FROM_DEVICE_EVENT=I1
EVENT_ID_EV001008_EP_POSITION_START_VALUE_FROM_DEVICE_EVENT={"true": "0", "false": "1"}
EVENT_ID_EV001008_EP_PV_HOLDING_TIMEOUT=11

# 排除匹配配置（V3简化格式）
EVENT_ID_EV001008_EP_MQTT_EXCLUDE_INFO_SOURCE=ajb/101013/in/P001LfyBmIn/time_log
EVENT_ID_EV001008_EP_MQTT_EXCLUDE_INFO_FIELD=log_car_no
EVENT_ID_EV001008_EP_MQTT_EXCLUDE_LOGIC=exists

# 告警配置
EVENT_ID_EV001008_EP_PV_ALARM_TOPIC=hq/101013/P001LfyBmIn/event
EVENT_ID_EV001008_EP_PV_DETAIL_INFO_TOPIC=ajb/101013/in/P001LfyBmIn/time_log
EVENT_ID_EV001008_EP_PV_DETAIL_INFO_FIELDS=[{"field_name": "设备", "field_keyword": "", "field_default":"帮豆你智能门岗监测"}, {"field_name": "名称", "field_keyword": "", "field_default":"三轮车快递车超时滞留入口"}, {"field_name": "等级", "field_keyword": "", "field_default":"通知"}]
```

#### 纯业务逻辑事件配置
```ini
# events.ini
[prompts]
EV001003 = 

# .env
EVENT_IDS=["EV001003"]
EVENT_ID_EV001003_EP_START_DEVICE_EVENT_TOPIC=["device/BDN861290073715232/event"]
EVENT_ID_EV001003_EP_START_FIELD_FROM_DEVICE_EVENT=I1
EVENT_ID_EV001003_EP_POSITION_START_VALUE_FROM_DEVICE_EVENT={"true": "0", "false": "1"}
EVENT_ID_EV001003_EP_PV_HOLDING_TIMEOUT=20

# 业务逻辑配置（V3格式）
EVENT_ID_EV001003_EP_BusinessLogic_JUDGE_SOURCE=ajb/101013/in/P001LfyBmIn/time_log
EVENT_ID_EV001003_EP_BusinessLogic_JUDGE_FIELD=CardType
EVENT_ID_EV001003_EP_BusinessLogic_JUDGE_LOGIC=contains
EVENT_ID_EV001003_EP_BusinessLogic_JUDGE_VALUE=月租卡|万全卡|贵宾卡|储值卡

# 排除匹配配置（V3简化格式）
EVENT_ID_EV001003_EP_MQTT_EXCLUDE_INFO_SOURCE=ajb/101013/in/P001LfyBmIn/time_log
EVENT_ID_EV001003_EP_MQTT_EXCLUDE_INFO_FIELD=CardType
EVENT_ID_EV001003_EP_MQTT_EXCLUDE_LOGIC=exists

# 告警配置
EVENT_ID_EV001003_EP_PV_ALARM_TOPIC=hq/101013/P001LfyBmIn/event
EVENT_ID_EV001003_EP_PV_DETAIL_INFO_TOPIC=ajb/101013/in/P001LfyBmIn/time_log
EVENT_ID_EV001003_EP_PV_DETAIL_INFO_FIELDS=[{"field_name": "详情", "field_keyword": "CardType,log_car_no,log_user_name,log_end_time", "field_format": "[{CardType}][{log_car_no}][{log_user_name}][{log_end_time}]"}, {"field_name": "事件", "field_keyword": "duration", "field_format": "停留时间{duration}秒"}, {"field_name": "设备", "field_keyword": "", "field_default":"帮豆你门岗智能监测"}, {"field_name": "等级", "field_keyword": "", "field_default":"通知"}]
```

---

## V3架构变更说明

### 主要架构变更

#### 从集成式到分离式
- **移除组件**：删除内部AI客户端、渐进式收集管理器、事件聚合协调器
- **新增机制**：MQTT AI控制消息、外部AI服务集成
- **简化流程**：移除复杂的阶段管理，使用简单的延迟触发机制

#### 配置格式统一
- **排除匹配**：从复杂JSON格式简化为3个简单参数
- **业务逻辑**：参数重命名（`EP_BL_*` → `EP_BusinessLogic_*`）
- **时间参数**：重命名（`EP_STAGE1_DURATION` → `EP_AI_ANALYSIS_DELAY_SEC`）

#### 告警主题灵活化
- **移除全局前缀**：不再使用`EP_COMPANY_NAME`
- **事件特定主题**：每个事件可自由定义告警主题
- **多租户支持**：支持不同事件发送到不同系统

### 兼容性说明

#### 不兼容变更
- **排除匹配格式**：旧的`EP_PV_EXCLUDE_INFO_FIELDS`JSON格式不再支持
- **业务逻辑参数**：`EP_BL_*`参数需重命名为`EP_BusinessLogic_*`
- **AI集成方式**：需要部署独立的AI服务，不再支持内部AI客户端

#### 迁移指南
1. **更新排除匹配配置**：将JSON格式转换为简化的3参数格式
2. **重命名业务逻辑参数**：批量替换`EP_BL_`为`EP_BusinessLogic_`
3. **部署外部AI服务**：配置MQTT AI控制和结果主题
4. **更新告警主题**：移除`EP_COMPANY_NAME`，直接配置完整主题路径

---

## 技术实现要求

### 外部AI服务要求
- **MQTT客户端**：支持QoS 1消息订阅和发布
- **图片处理**：支持base64编码图片接收和处理
- **结果格式**：严格按照协议返回JSON格式结果
- **错误处理**：网络异常时提供合理的错误响应

### 系统性能要求
- **消息处理延迟**：MQTT消息处理延迟不超过100ms
- **AI控制响应**：AI控制消息发送后60秒内需收到结果
- **图片处理时间**：单张图片处理时间不超过5秒
- **并发事件支持**：支持至少10个事件并发处理

### 可靠性要求
- **MQTT重连**：自动检测连接断开并重新连接
- **消息重发**：QoS=1确保关键消息送达
- **异常处理**：AI服务异常时提供降级处理
- **日志记录**：完整记录系统状态和错误信息

---

## 测试验证要求

### 配置验证
- **参数完整性检查**：验证所有必需参数已正确配置
- **格式正确性检查**：验证JSON、数组等格式参数语法正确
- **一致性检查**：验证EVENT_IDS与实际事件配置的一致性

### 功能验证
- **AI控制消息**：验证AI控制消息正确发送和结果接收
- **排除匹配**：验证简化排除匹配格式正确工作
- **业务逻辑**：验证业务逻辑判断和多值匹配功能
- **告警发送**：验证告警消息格式和发送成功率

### 集成验证
- **端到端流程**：验证从事件触发到告警发送的完整流程
- **多事件并发**：验证多个事件同时处理的正确性
- **异常场景**：验证AI服务异常、MQTT断开等场景的处理

---

此文档基于Event Processor V3的最新架构，反映了从集成式AI分析到独立AI服务+MQTT小区的重大架构变更，以及统一排除匹配格式等重要改进。