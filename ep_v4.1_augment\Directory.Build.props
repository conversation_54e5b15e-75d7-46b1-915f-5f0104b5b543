<Project>

  <PropertyGroup>
    <Product>Event Processor V4.1</Product>
    <Company>Augment Code</Company>
    <Copyright>Copyright © 2025 Augment Code. All rights reserved.</Copyright>
    <Authors><PERSON> AI Assistant</Authors>
    <Description>EP_V4.1增强规则配置系统 - 支持嵌套条件、消息乱序容错和告警撤销机制</Description>
    <PackageProjectUrl>https://github.com/augment-code/event-processor-v4.1</PackageProjectUrl>
    <RepositoryUrl>https://github.com/augment-code/event-processor-v4.1</RepositoryUrl>
    <RepositoryType>git</RepositoryType>
    <PackageLicenseExpression>MIT</PackageLicenseExpression>
    <PackageRequireLicenseAcceptance>false</PackageRequireLicenseAcceptance>
    <PackageReleaseNotes>EP_V4.1 增强版本 - 解决消息乱序和告警撤销问题</PackageReleaseNotes>
    <PackageTags>event-processor;mqtt;rules-engine;alarm-system;iot</PackageTags>
  </PropertyGroup>

  <PropertyGroup>
    <LangVersion>latest</LangVersion>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <TreatWarningsAsErrors>false</TreatWarningsAsErrors>
    <WarningsAsErrors />
    <WarningsNotAsErrors />
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)' == 'Debug'">
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <DebugType>full</DebugType>
    <DebugSymbols>true</DebugSymbols>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)' == 'Release'">
    <DefineConstants>TRACE</DefineConstants>
    <DebugType>pdbonly</DebugType>
    <DebugSymbols>true</DebugSymbols>
    <Optimize>true</Optimize>
  </PropertyGroup>

</Project>
