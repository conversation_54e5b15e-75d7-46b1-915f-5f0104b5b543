# Event Processor V4.1

## 项目概述

Event Processor V4.1 是一个基于 .NET 8 的智能事件处理系统，专为复杂的实时事件分析和告警生成而设计。系统支持多种评估策略（纯业务逻辑、AI分析、混合模式），具备强大的规则引擎、状态管理和错误处理能力。

## 核心特性

### 🚀 增强的处理能力
- **多策略支持**: BusinessOnly、AI、AIAndBusiness 三种评估策略
- **智能状态机**: 7种状态的完整生命周期管理
- **告警静默期**: 防止误报的智能告警机制
- **告警撤销**: 支持迟到排除条件的告警撤销

### 🔧 强大的规则引擎
- **22种操作符**: 支持string、number、datetime三种数据类型
- **嵌套条件组**: 复杂逻辑表达式支持
- **三种规则类型**: 排除规则、业务规则、AI结果规则
- **动态配置**: 支持配置热重载

### 📊 智能数据处理
- **消息乱序处理**: 任意消息类型可触发事件创建
- **数据累积策略**: 不同数据源的智能累积和覆盖
- **时间窗口关联**: 灵活的事件关联策略
- **多数据源融合**: 设备信号、业务数据、AI结果的统一处理

### 🛡️ 企业级可靠性
- **错误处理**: 完整的异常处理和降级策略
- **重试机制**: 指数退避的智能重试
- **性能监控**: 实时的系统健康检查
- **日志审计**: 结构化日志和事件追踪

## 快速开始

### 环境要求
- **操作系统**: Windows 10/11 或 Windows Server 2019/2022
- **.NET SDK**: .NET 8.0 SDK 或更高版本
- **开发工具**: Visual Studio 2022 (17.8+) 或 VS Code
- **运行时**: .NET 8.0 Runtime（生产环境）

### 一键编译和部署
```bash
# 1. 克隆项目
git clone <repository-url>
cd ep_v4.1_augment

# 2. 恢复依赖
dotnet restore

# 3. 编译项目（Release配置）
dotnet build --configuration Release

# 4. 发布应用
dotnet publish src/EventProcessor.Host --configuration Release --output deploy/

# 5. 启动应用
cd deploy && start.bat
```

### 快速部署到服务器
```powershell
# 使用自动化部署脚本
.\deploy_bdnserver.ps1

# 或部署到自定义路径
.\deploy_bdnserver.ps1 -TargetPath "\\myserver\share\EP_V4.1"

# 使用批处理启动器（推荐）
.\deploy.bat
```

### 配置和运行
```bash
# 1. 选择配置模板
# - config-production-ready.yaml (生产环境推荐)
# - config-complete-sample.yaml (仅业务规则)
# - config-ai-sample.yaml (启用AI处理)

# 2. 复制配置文件
copy config-production-ready.yaml config.yaml

# 3. 编辑配置文件
# 修改 EventId、MQTT连接、规则定义等

# 4. 运行服务
EventProcessor.Host.exe

# 或作为 Windows 服务安装
scripts/install-service.ps1 -BinaryPath "C:\path\to\EventProcessor.Host.exe"
```

## 项目结构

```
ep_v4.1_augment/
├── src/
│   ├── EventProcessor.Host/         # 主程序 (可执行)
│   ├── EventProcessor.Core/         # 核心业务逻辑
│   └── EventProcessor.Core.Minimal/ # 最小化核心库
├── tests/
│   ├── EventProcessor.Tests/        # 单元测试
│   └── EventProcessor.IntegrationTests/ # 集成测试
├── config/                          # 事件配置文件
│   ├── EV001001-config.yaml        # 示例事件配置
│   └── delivery-vehicle-test-config.yaml
├── deploy/                          # 部署文件
│   ├── EventProcessor.Host.exe      # 主程序
│   ├── appsettings.yaml            # 应用配置
│   ├── config-*.yaml               # 配置模板
│   └── start*.ps1                  # 启动脚本
├── scripts/                         # 管理脚本
│   ├── install-service.ps1         # 服务安装
│   └── uninstall-service.ps1       # 服务卸载
├── docs/                           # 文档目录
└── deploy_bdnserver.ps1            # 自动化部署脚本
```

## 技术栈

- **.NET 8**: 现代化的跨平台运行时
- **MQTTnet**: 高性能MQTT客户端库
- **Serilog**: 结构化日志记录
- **YamlDotNet**: YAML配置支持
- **FluentAssertions**: 流畅的测试断言
- **xUnit**: 单元测试框架

## 文档索引

### 📚 **核心文档**
- **[部署指南](docs/DEPLOYMENT_GUIDE.md)** - 完整的部署、配置和运维指南
- **[操作符参考](OPERATORS_REFERENCE.md)** - 22种操作符的详细说明和示例
- **[系统设计](DES_EP_V4.1.md)** - 完整的架构设计和技术规范

### 🔧 **配置和示例**
- **[配置文件](config/)** - 事件配置示例
- **[部署模板](deploy/)** - 生产就绪的配置模板
- **[测试数据](test-data/)** - 测试用的示例消息

### 🚀 **部署工具**
- **[自动化部署](deploy_bdnserver.ps1)** - 完整的部署脚本
- **[部署启动器](deploy.bat)** - 用户友好的部署工具
- **[服务管理](scripts/)** - Windows服务安装/卸载脚本

## 项目状态

✅ **已完成的功能**
- 核心数据模型和配置系统
- 条件评估引擎（22种操作符）
- 规则引擎（支持嵌套条件组）
- 事件状态聚合器（7种状态）
- MQTT服务和消息处理
- 告警生成和撤销机制
- 配置热重载
- 错误处理和重试机制
- 完整的单元测试和集成测试
- Windows服务支持
- 部署脚本和文档

🚀 **系统特色**
- 支持消息乱序处理
- 智能告警静默期机制
- 多种评估策略（业务逻辑、AI、混合）
- 企业级错误处理和监控
- 配置文件热重载
- 完整的测试覆盖

---

**版本**: EP_V4.1  
**更新日期**: 2025-01-31  
**文档维护**: Claude Code AI Assistant