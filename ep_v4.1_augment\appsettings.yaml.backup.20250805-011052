# Event Processor V4.1 统一配置文件
# 默认配置文件 - 可以复制此文件为 EV001001.yaml, EV001008.yaml 等来创建特定事件的配置

Logging:
  LogLevel:
    Default: "Information"
    Microsoft: "Warning"
    Microsoft.Hosting.Lifetime: "Information"
    EventProcessor: "Debug"

Serilog:
  Using:
    - "Serilog.Sinks.Console"
    - "Serilog.Sinks.File"
  MinimumLevel:
    Default: "Information"
    Override:
      Microsoft: "Warning"
      System: "Warning"
      EventProcessor: "Debug"
  WriteTo:
    - Name: "Console"
      Args:
        outputTemplate: "[{Timestamp:HH:mm:ss.fff} {Level:u3}] {Message:lj}{NewLine}{Exception}"
    - Name: "File"
      Args:
        path: "logs/eventprocessor-.log"
        rollingInterval: "Day"
        retainedFileCountLimit: 30
        outputTemplate: "[{Timestamp:HH:mm:ss.fff} {Level:u3}] {Message:lj}{NewLine}{Exception}"
  Enrich:
    - "FromLogContext"
    - "WithMachineName"
    - "WithThreadId"

# 事件配置 - 根据具体事件修改此部分
EventProcessor:
  EventId: "EV001001"
  EventName: "月租车未过期超时滞留出口"
  EvaluationStrategy: "BusinessOnly"
  Priority: "P3"
  CommId: "101013"
  PositionId: "P002LfyBmOut"
  AlarmGracePeriodSeconds: 3
  EnableAlarmCancellation: true
  CorrelationTimeWindow: "minute"
  
  # 设备信号配置
  DeviceSignal:
    Topics:
      - "device/BDN861290073715232/event"
    TriggerField: "I2"
    TriggerValues:
      "true": "0"
      "false": "1"
    HoldingTimeoutSec: 20
  
  # 规则配置
  RuleConfiguration:
    BusinessRules:
      - SourceTopic: "ajb/101013/out/P002LfyBmOut/time_log"
        LogicOperator: "AND"
        ConditionGroups:
          - LogicOperator: "OR"
            Conditions:
              - FieldName: "CardType"
                DataType: "string"
                Operator: "In"
                Value: "月租卡|万全卡|贵宾卡|储值卡"
                Description: "有效卡类型"
          - LogicOperator: "AND"
            Conditions:
              - FieldName: "log_remain_days"
                DataType: "number"
                Operator: "GreaterThan"
                Value: "0"
                Description: "卡未过期"
              - FieldName: "duration"
                DataType: "number"
                Operator: "GreaterThan"
                Value: "20"
                Description: "滞留时间超过20秒"
    
    ExclusionRules:
      - SourceType: "MQTT"
        SourceTopic: "ajb/101013/out/P002LfyBmOut/time_log"
        LogicOperator: "OR"
        Conditions:
          - FieldName: "log_remain_days"
            DataType: "string"
            Operator: "Contains"
            Value: "0"
            Description: "剩余天数为0，卡已过期"
    
    AlarmConfig:
      Fields:
        - AlarmFieldName: "详情"
          SourceRuleType: "BusinessRules"
          SourceFieldName: "CardType,log_car_no,log_user_name,log_end_time"
          FormatTemplate: "[{CardType}][{log_car_no}][{log_user_name}][{log_end_time}]"
        - AlarmFieldName: "事件"
          SourceRuleType: "BusinessRules"
          SourceFieldName: "duration"
          FormatTemplate: "停留时间{duration}秒"
        - AlarmFieldName: "设备"
          SourceRuleType: "DeviceSignal"
          SourceFieldName: ""
          DefaultValue: "帮豆你门岗智能监测"
        - AlarmFieldName: "名称"
          SourceRuleType: "DeviceSignal"
          SourceFieldName: ""
          DefaultValue: "月租车超时滞留出口（卡未过期）"
        - AlarmFieldName: "等级"
          SourceRuleType: "DeviceSignal"
          SourceFieldName: ""
          DefaultValue: "通知"

# MQTT 配置
Mqtt:
  BrokerHost: "mq.bangdouni.com"
  BrokerPort: 1883
  ClientId: "EP_V4.1_EV001001_TEST"  # 临时使用不同的客户端ID避免冲突
  Username: "bdn_ai_process"
  Password: "Bdn@2024"
  KeepAliveInterval: 60
  ReconnectDelay: 5
  QualityOfServiceLevel: 1

# 错误处理配置
ErrorHandling:
  ToleranceLevel: "Normal"
  RetryPolicy:
    MaxRetries: 3
    RetryDelay: 1000
    BackoffMultiplier: 2.0
    MaxRetryDelay: 30000
  FallbackStrategy:
    OnRuleFailure: "ContinueProcessing"
    OnAIFailure: "UseBusinessOnly"
    OnTimerFailure: "ImmediateAlarm"
    OnMqttFailure: "Retry"
  Logging:
    ErrorLogLevel: "Error"
    DetailedStackTrace: true
    IncludeMessagePayload: false
    EnablePerformanceMonitoring: true
    ErrorRateThreshold: 0.1
