# Event Processor V4.1 配置文件 - EV001002 月租车过期超时滞留出口
# 事件描述：检测月租车卡已过期但仍在出口超时滞留的情况

# 事件处理器配置
EventProcessor:
  # 基本配置
  EventId: "EV001002"
  EventName: "月租车过期超时滞留出口"
  CommId: "101013"
  PositionId: "P002LfyBmOut"
  CompanyName: "hq"
  
  # 评估策略：仅业务规则
  EvaluationStrategy: "BusinessOnly"
  
  # 优先级
  Priority: "P3"
  
  # 告警宽限期（秒）
  AlarmGracePeriodSec: 3
  
  # 启用告警取消
  EnableAlarmCancellation: true
  
  # 自定义时间窗口（分钟）
  CustomTimeWindowMinutes: 5

  # 设备信号配置 - 使用测试主题避免真实设备信号干扰
  DeviceSignal:
    Topics:
      - "device/BDN888/event"
    TriggerField: "I2"
    TriggerValues:
      "true": "0"
      "false": "1"
    HoldingTimeoutSec: 20

  # 规则配置
  RuleConfiguration:
    # 业务规则配置 - 检测过期月租车
    BusinessRules:
      - SourceTopic: "ajb/101013/out/P088LfyBmOut/time_log"
        LogicOperator: "AND"
        ConditionGroups:
          # 条件组1：事件类型为有效期剩余天数
          - LogicOperator: "OR"
            Conditions:
              - FieldName: "log_event_type"
                DataType: "string"
                Operator: "Equals"
                Value: "有效期剩余天数"
                Description: "有效期检查事件"
          # 条件组2：剩余天数为0（已过期）
          - LogicOperator: "OR"
            Conditions:
              - FieldName: "log_remaining_days"
                DataType: "string"
                Operator: "Equals"
                Value: "0"
                Description: "卡已过期 (剩余天数为0)"
    
    # 排除规则配置 - 排除未过期的卡
    ExclusionRules:
      - SourceType: "MQTT"
        SourceTopic: "ajb/101013/out/P088LfyBmOut/time_log"
        LogicOperator: "OR"
        Conditions:
          - FieldName: "log_remaining_days"
            DataType: "string"
            Operator: "NotEqual"
            Value: "0"
            Description: "剩余天数不为0，卡未过期"
    
    # 告警配置
    AlarmConfig:
      # 自定义告警主题 - 发送到真实的告警接收方
      CustomAlarmTopic: "hq/101013/P002LfyBmOut/event"
      CustomAlarmCancellationTopic: "hq/101013/P002LfyBmOut/cancellation"
      Fields:
        - AlarmFieldName: "详情"
          SourceRuleType: "BusinessRules"
          SourceFieldName: "log_remaining_days,log_reminder_text"
          FormatTemplate: "[有效期剩余{log_remaining_days}天][{log_reminder_text}]"
        - AlarmFieldName: "事件"
          SourceRuleType: "DeviceSignal"
          SourceFieldName: "duration"
          FormatTemplate: "停留时间{duration}秒"
        - AlarmFieldName: "设备"
          SourceRuleType: "DeviceSignal"
          SourceFieldName: ""
          DefaultValue: "帮豆你门岗智能监测"
        - AlarmFieldName: "名称"
          SourceRuleType: "DeviceSignal"
          SourceFieldName: ""
          DefaultValue: "月租车过期超时滞留出口"
        - AlarmFieldName: "等级"
          SourceRuleType: "DeviceSignal"
          SourceFieldName: ""
          DefaultValue: "通知"

# MQTT 配置
Mqtt:
  BrokerHost: "mq.bangdouni.com"
  BrokerPort: 1883
  ClientId: "EP_V4.1_EV001002_TEST"
  Username: "bdn_ai_process"
  Password: "Bdn@2024"
  KeepAliveInterval: 60
  ReconnectDelay: 5
  QualityOfServiceLevel: 1

# 错误处理配置
ErrorHandling:
  ToleranceLevel: "Normal"
  RetryPolicy:
    MaxRetries: 3
    RetryDelay: 1000
    BackoffMultiplier: 2.0
    MaxRetryDelay: 30000
  FallbackStrategy:
    OnRuleFailure: "ContinueProcessing"
    OnAIFailure: "UseBusinessOnly"
    OnTimerFailure: "ImmediateAlarm"
    OnMqttFailure: "Retry"
  Logging:
    ErrorLogLevel: "Error"
    DetailedStackTrace: true
    IncludeMessagePayload: false
    EnablePerformanceMonitoring: true
    ErrorRateThreshold: 0.1

# 日志配置
Logging:
  LogLevel:
    Default: "Information"
    Microsoft: "Warning"
    Microsoft.Hosting.Lifetime: "Information"
    EventProcessor: "Debug"

Serilog:
  Using:
    - "Serilog.Sinks.Console"
    - "Serilog.Sinks.File"
  MinimumLevel:
    Default: "Information"
    Override:
      Microsoft: "Warning"
      System: "Warning"
      EventProcessor: "Debug"
  WriteTo:
    - Name: "Console"
      Args:
        outputTemplate: "[{Timestamp:HH:mm:ss.fff} {Level:u3}] {Message:lj}{NewLine}{Exception}"
    - Name: "File"
      Args:
        path: "logs/log_EP_V4.1_EV001002_TEST_.log"
        rollingInterval: "Day"
        retainedFileCountLimit: 30
        outputTemplate: "[{Timestamp:HH:mm:ss.fff} {Level:u3}] {Message:lj}{NewLine}{Exception}"
  Enrich:
    - "FromLogContext"
    - "WithMachineName"
    - "WithThreadId"


