# ============================================================================
# Event Processor V4.1 Deploy Script (Pure PowerShell Version)
# Target Server: \\bdnserver\BDN\EP_V4.1
# Created: 2025-01-03
# Description: Automatically build and deploy EventProcessor.Host to remote server
# Version: 2.0 (Completely refactored to PowerShell)
# ============================================================================

[CmdletBinding()]
param(
    [Parameter(HelpMessage = "Target deployment path")]
    [ValidateNotNullOrEmpty()]
    [string]$TargetPath = "\\bdnserver\BDN\EP_V4.1",
    
    [Parameter(HelpMessage = "Build configuration")]
    [ValidateSet("Debug", "Release")]
    [string]$Configuration = "Release",
    
    [Parameter(HelpMessage = "Skip build step")]
    [switch]$SkipBuild = $false,
    
    [Parameter(HelpMessage = "Force execution, skip some checks")]
    [switch]$Force = $false,
    
    [Parameter(HelpMessage = "Show verbose output")]
    [switch]$VerboseOutput = $false,
    
    [Parameter(HelpMessage = "Show help information")]
    [switch]$Help = $false
)

# Show help information
if ($Help) {
    Write-Host @"
Event Processor V4.1 Deployment Script

Usage:
    .\deploy_bdnserver.ps1 [parameters]

Parameters:
    -TargetPath <path>      Target deployment path (default: \\bdnserver\BDN\EP_V4.1)
    -Configuration <config> Build configuration Debug|Release (default: Release)
    -SkipBuild             Skip build step
    -Force                 Force execution, skip some checks
    -VerboseOutput         Show verbose output
    -Help                  Show this help information

Examples:
    .\deploy_bdnserver.ps1
    .\deploy_bdnserver.ps1 -Configuration Debug -VerboseOutput
    .\deploy_bdnserver.ps1 -TargetPath "C:\MyDeploy" -SkipBuild
"@ -ForegroundColor Green
    exit 0
}

# Set error handling and preferences
$ErrorActionPreference = "Stop"
$ProgressPreference = "Continue"
$VerbosePreference = if ($VerboseOutput) { "Continue" } else { "SilentlyContinue" }

# Script variables
$ScriptPath = Split-Path -Parent $MyInvocation.MyCommand.Path
$ProjectPath = Join-Path $ScriptPath "src\EventProcessor.Host"
$ProjectFile = Join-Path $ProjectPath "EventProcessor.Host.csproj"
$ConfigPath = Join-Path $ScriptPath "config"
$DeployPath = Join-Path $ScriptPath "deploy"
$LogFile = Join-Path $ScriptPath "deploy_$(Get-Date -Format 'yyyyMMdd_HHmmss').log"

# Logging function - Improved PowerShell version
function Write-Log {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $true, Position = 0)]
        [string]$Message,
        
        [Parameter(Position = 1)]
        [ValidateSet("INFO", "WARN", "ERROR", "SUCCESS", "DEBUG")]
        [string]$Level = "INFO"
    )
    
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logMessage = "[$timestamp] [$Level] $Message"
    
    # Choose color and output stream based on level
    switch ($Level) {
        "ERROR" { 
            Write-Host $logMessage -ForegroundColor Red
            Write-Error $Message -ErrorAction Continue
        }
        "WARN" { 
            Write-Host $logMessage -ForegroundColor Yellow
            Write-Warning $Message
        }
        "SUCCESS" { 
            Write-Host $logMessage -ForegroundColor Green
        }
        "DEBUG" {
            Write-Host $logMessage -ForegroundColor Gray
            Write-Verbose $Message
        }
        default { 
            Write-Host $logMessage -ForegroundColor White
            Write-Information $Message -InformationAction Continue
        }
    }
    
    # Write to log file (with error handling)
    try {
        Add-Content -Path $LogFile -Value $logMessage -Encoding UTF8 -ErrorAction Stop
    }
    catch {
        Write-Warning "Cannot write to log file: $($_.Exception.Message)"
    }
}

# Check prerequisites
function Test-Prerequisites {
    Write-Log "Checking deployment prerequisites..."
    
    # Check .NET SDK
    try {
        $dotnetVersion = dotnet --version
        Write-Log ".NET SDK version: $dotnetVersion"
    }
    catch {
        Write-Log ".NET SDK not installed or not in PATH" "ERROR"
        throw "Please install .NET 8.0 SDK"
    }
    
    # Check project file
    if (-not (Test-Path $ProjectFile)) {
        Write-Log "Project file does not exist: $ProjectFile" "ERROR"
        throw "Cannot find EventProcessor.Host project file"
    }
    
    # Check target path accessibility
    $targetDir = Split-Path $TargetPath -Parent
    if (-not (Test-Path $targetDir)) {
        Write-Log "Target server path not accessible: $targetDir" "ERROR"
        throw "Cannot access target server path, please check network connection and permissions"
    }
    
    Write-Log "Prerequisites check passed" "SUCCESS"
}

# Stop remote process
function Stop-RemoteProcess {
    Write-Log "Attempting to stop remote process on bdnserver..."
    
    $processName = "EventProcessor.Host"
    $computerName = "bdnserver"

    try {
        $p = Get-Process -Name $processName -ComputerName $computerName -ErrorAction SilentlyContinue
        if ($p) {
            Write-Log "Found running process '$processName' on $computerName. Stopping it..."
            Stop-Process -InputObject $p -Force
            Write-Log "Process '$processName' stopped successfully." "SUCCESS"
        } else {
            Write-Log "No running process '$processName' found on $computerName. No action needed."
        }
    }
    catch {
        Write-Log "Failed to stop remote process: $($_.Exception.Message)" "WARN"
        Write-Log "Please manually stop the EventProcessor.Host process on bdnserver before deployment." "WARN"
        Write-Log "Continuing with deployment..." "INFO"
    }
}

# Backup existing deployment
function Backup-ExistingDeployment {
    if (Test-Path $TargetPath) {
        $backupName = "BAK_$(Get-Date -Format 'yyyyMMddHHmmss')"
        $backupPath = Join-Path (Split-Path $TargetPath -Parent) $backupName
        
        Write-Log "Found existing deployment, creating backup: $backupPath"
        try {
            Copy-Item -Path $TargetPath -Destination $backupPath -Recurse -Force
            Write-Log "Backup created successfully" "SUCCESS"
        }
        catch {
            Write-Log "Backup creation failed: $($_.Exception.Message)" "ERROR"
            if (-not $Force) {
                throw "Backup failed, use -Force parameter to skip backup"
            }
        }
    }
    else {
        Write-Log "Target path does not exist, no backup needed"
    }
}

# Build project - Improved PowerShell version
function Build-Project {
    [CmdletBinding()]
    param()
    
    if ($SkipBuild) {
        Write-Log "Skipping build step" "INFO"
        return $true
    }
    
    Write-Log "Starting project build..." "INFO"
    Write-Log "Project path: $ProjectPath" "DEBUG"
    Write-Log "Configuration: $Configuration" "DEBUG"
    
    try {
        # Restore NuGet packages
        Write-Log "Restoring NuGet packages..." "INFO"
        $restoreArgs = @(
            "restore"
            $ProjectFile
            "--verbosity"
            "minimal"
        )
        
        $restoreResult = Start-Process -FilePath "dotnet" -ArgumentList $restoreArgs -Wait -PassThru -NoNewWindow
        if ($restoreResult.ExitCode -ne 0) {
            throw "NuGet package restore failed, exit code: $($restoreResult.ExitCode)"
        }
        
        # Build project
        Write-Log "Building project..." "INFO"
        $buildArgs = @(
            "build"
            $ProjectFile
            "--configuration"
            $Configuration
            "--no-restore"
            "--verbosity"
            "minimal"
        )
        
        $buildResult = Start-Process -FilePath "dotnet" -ArgumentList $buildArgs -Wait -PassThru -NoNewWindow
        if ($buildResult.ExitCode -ne 0) {
            throw "Build failed, exit code: $($buildResult.ExitCode)"
        }
        
        Write-Log "Project build successful" "SUCCESS"
        return $true
    }
    catch {
        Write-Log "Build failed: $($_.Exception.Message)" "ERROR"
        throw
    }
}

# Publish project - Improved PowerShell version
function Publish-Project {
    [CmdletBinding()]
    param()
    
    Write-Log "Starting project publish..." "INFO"
    
    $publishPath = Join-Path $ScriptPath "publish"
    Write-Log "Publish path: $publishPath" "DEBUG"
    
    try {
       
        # Publish project
        Write-Log "Publishing project to: $publishPath" "INFO"
        $publishArgs = @(
            "publish"
            $ProjectFile
            "--configuration"
            $Configuration
            "--output"
            $publishPath
            "--runtime"
            "win-x64"
            "--self-contained"
            "false"
            "--verbosity"
            "minimal"
            "--no-build"
        )
        
        $publishResult = Start-Process -FilePath "dotnet" -ArgumentList $publishArgs -Wait -PassThru -NoNewWindow
        if ($publishResult.ExitCode -ne 0) {
            throw "Publish failed, exit code: $($publishResult.ExitCode)"
        }
        
        # Verify publish result
        $mainExe = Join-Path $publishPath "EventProcessor.Host.exe"
        if (-not (Test-Path $mainExe)) {
            throw "Publish completed but main program file not found: $mainExe"
        }
        
        $publishedFiles = Get-ChildItem -Path $publishPath -Recurse -File
        Write-Log "Publish completed, total $($publishedFiles.Count) files" "SUCCESS"
        
        return $publishPath
    }
    catch {
        Write-Log "Publish failed: $($_.Exception.Message)" "ERROR"
        throw
    }
}

# Prepare deployment files
function Prepare-DeploymentFiles {
    param([string]$PublishPath)

    Write-Log "Preparing deployment files..."

    $deployFiles = @()

    # 主程序文件 (依赖运行时模式)
    $mainFiles = @(
        "EventProcessor.Host.exe",
        "EventProcessor.Host.dll",
        "EventProcessor.Host.pdb",
        "EventProcessor.Host.deps.json",
        "EventProcessor.Host.runtimeconfig.json",
        "EventProcessor.Core.dll",
        "EventProcessor.Core.pdb"
    )

    foreach ($file in $mainFiles) {
        $filePath = Join-Path $PublishPath $file
        if (Test-Path $filePath) {
            $deployFiles += $filePath
        }
        else {
            Write-Log "Warning: File not found $file" "WARN"
        }
    }

    # 依赖库文件 (第三方NuGet包等)
    $libFiles = Get-ChildItem -Path $PublishPath -Filter "*.dll" -ErrorAction SilentlyContinue | Where-Object {
        $_.Name -notlike "EventProcessor.Host.dll" -and $_.Name -notlike "EventProcessor.Core.dll"
    }
    if ($libFiles) {
        $deployFiles += $libFiles.FullName
        Write-Log "Found $($libFiles.Count) dependency libraries"
    }

    # Configuration files
    $configFiles = @()

    # Application configuration
    $appSettingsPath = Join-Path $DeployPath "appsettings.yaml"
    if (Test-Path $appSettingsPath) {
        $configFiles += $appSettingsPath
    }

    # Event configuration files
    if (Test-Path $ConfigPath) {
        $eventConfigs = Get-ChildItem -Path $ConfigPath -Filter "*.yaml" -ErrorAction SilentlyContinue
        if ($eventConfigs) {
            $configFiles += $eventConfigs.FullName
        }
    }

    # Sample deployment configurations
    if (Test-Path $DeployPath) {
        $sampleConfigs = Get-ChildItem -Path $DeployPath -Filter "*-sample.yaml" -ErrorAction SilentlyContinue
        if ($sampleConfigs) {
            $configFiles += $sampleConfigs.FullName
        }
    }

    Write-Log "Found $($deployFiles.Count) program files"
    Write-Log "Found $($configFiles.Count) configuration files"

    return @{
        ProgramFiles = $deployFiles
        ConfigFiles = $configFiles
    }
}

# Execute deployment
function Deploy-Files {
    param($Files)

    Write-Log "Starting deployment to target server..."
    Write-Log "Target path: $TargetPath"

    try {
        # Create target directory
        if (-not (Test-Path $TargetPath)) {
            New-Item -Path $TargetPath -ItemType Directory -Force | Out-Null
            Write-Log "Created target directory: $TargetPath"
        }

        # Create config subdirectory
        $configTargetPath = Join-Path $TargetPath "config"
        if (-not (Test-Path $configTargetPath)) {
            New-Item -Path $configTargetPath -ItemType Directory -Force | Out-Null
        }

        # Deploy program files
        Write-Log "Deploying program files..."
        $copiedCount = 0
        foreach ($file in $Files.ProgramFiles) {
            if ($file -and (Test-Path $file)) {
                $fileName = Split-Path $file -Leaf
                $targetFile = Join-Path $TargetPath $fileName
                Copy-Item -Path $file -Destination $targetFile -Force
                $copiedCount++
                if ($VerboseOutput) {
                    Write-Log "  Copied: $fileName"
                }
            }
        }
        Write-Log "Copied $copiedCount program files" "SUCCESS"

        # Deploy configuration files
        Write-Log "Deploying configuration files..."
        $configCopiedCount = 0
        foreach ($file in $Files.ConfigFiles) {
            if ($file -and (Test-Path $file)) {
                $fileName = Split-Path $file -Leaf

                # Event configuration files go to config subdirectory
                if ($fileName -like "*-config.yaml") {
                    $targetFile = Join-Path $configTargetPath $fileName
                }
                else {
                    $targetFile = Join-Path $TargetPath $fileName
                }

                Copy-Item -Path $file -Destination $targetFile -Force
                $configCopiedCount++
                if ($VerboseOutput) {
                    Write-Log "  Copied: $fileName"
                }
            }
        }
        Write-Log "Copied $configCopiedCount configuration files" "SUCCESS"

        # Create startup scripts
        Create-StartupScripts

        Write-Log "Deployment completed!" "SUCCESS"
    }
    catch {
        Write-Log "Deployment failed: $($_.Exception.Message)" "ERROR"
        throw
    }
}

# Create startup scripts
function Create-StartupScripts {
    Write-Log "Creating startup scripts..."

    # Create basic startup script (PowerShell version)
    $startScript = @"
# Event Processor V4.1 Startup Script
# Auto-generated at: $(Get-Date -Format "yyyy-MM-dd HH:mm:ss")

Write-Host "========================================" -ForegroundColor Green
Write-Host "Event Processor V4.1 Startup" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
Write-Host "Target: `$PSScriptRoot" -ForegroundColor Cyan

# Change to script directory
Set-Location -Path `$PSScriptRoot

# Check if main program exists
`$mainExe = Join-Path `$PSScriptRoot "EventProcessor.Host.exe"
if (-not (Test-Path `$mainExe)) {
    Write-Host "Error: Main program file not found EventProcessor.Host.exe" -ForegroundColor Red
    Read-Host "Press any key to exit"
    exit 1
}

# Display version information
try {
    `$fileVersion = (Get-Item `$mainExe).VersionInfo
    `$version = `$fileVersion.ProductVersion
    if ([string]::IsNullOrEmpty(`$version)) {
        `$version = `$fileVersion.FileVersion
    }
    if (-not [string]::IsNullOrEmpty(`$version)) {
        Write-Host "Version: `$version" -ForegroundColor Cyan
    }
    Write-Host "========================================" -ForegroundColor Green
}
catch {
    Write-Host "Version: Unknown" -ForegroundColor Yellow
    Write-Host "========================================" -ForegroundColor Green
}

try {
    # Start main program
    Write-Host "Starting Event Processor..." -ForegroundColor Yellow
    & `$mainExe
}
catch {
    Write-Host "Startup failed: `$($_.Exception.Message)" -ForegroundColor Red
}
finally {
    Read-Host "Press any key to exit"
}
"@

    $startScriptPath = Join-Path $TargetPath "start.ps1"
    Set-Content -Path $startScriptPath -Value $startScript -Encoding UTF8

    # Create startup script with configuration selection (PowerShell version)
    $startWithConfigScript = @"
# Event Processor V4.1 Configuration Selection Startup Script
# Auto-generated at: $(Get-Date -Format "yyyy-MM-dd HH:mm:ss")

Write-Host "Event Processor V4.1 - Configuration Selector" -ForegroundColor Green
Write-Host "=============================================" -ForegroundColor Green

# Change to script directory
Set-Location -Path `$PSScriptRoot

# Check if main program exists
`$mainExe = Join-Path `$PSScriptRoot "EventProcessor.Host.exe"
if (-not (Test-Path `$mainExe)) {
    Write-Host "Error: Main program file not found EventProcessor.Host.exe" -ForegroundColor Red
    Read-Host "Press any key to exit"
    exit 1
}

# Display version information
try {
    `$fileVersion = (Get-Item `$mainExe).VersionInfo
    `$version = `$fileVersion.ProductVersion
    if ([string]::IsNullOrEmpty(`$version)) {
        `$version = `$fileVersion.FileVersion
    }
    if (-not [string]::IsNullOrEmpty(`$version)) {
        Write-Host "Version: `$version" -ForegroundColor Cyan
        Write-Host "=============================================" -ForegroundColor Green
    }
}
catch {
    Write-Host "Version: Unknown" -ForegroundColor Yellow
    Write-Host "=============================================" -ForegroundColor Green
}

# Check config directory
`$configDir = Join-Path `$PSScriptRoot "config"
if (-not (Test-Path `$configDir)) {
    Write-Host "Config directory not found, using default settings..." -ForegroundColor Yellow
    try {
        & `$mainExe
    }
    catch {
        Write-Host "Startup failed: `$($_.Exception.Message)" -ForegroundColor Red
    }
    Read-Host "Press any key to exit"
    exit 0
}

# Get available configuration files
`$configFiles = Get-ChildItem -Path `$configDir -Filter "*.yaml" -File
if (`$configFiles.Count -eq 0) {
    Write-Host "No configuration files found, using default settings..." -ForegroundColor Yellow
    try {
        & `$mainExe
    }
    catch {
        Write-Host "Startup failed: `$($_.Exception.Message)" -ForegroundColor Red
    }
    Read-Host "Press any key to exit"
    exit 0
}

# Display available configurations
Write-Host ""
Write-Host "Available configurations:" -ForegroundColor Cyan
Write-Host ""
for (`$i = 0; `$i -lt `$configFiles.Count; `$i++) {
    Write-Host "`$(`$i + 1). `$(`$configFiles[`$i].BaseName)" -ForegroundColor White
}

Write-Host ""
`$maxChoice = `$configFiles.Count
`$choice = Read-Host "Select configuration (1-`$maxChoice, or press Enter for default)"

`$selectedConfig = `$null
if ([string]::IsNullOrWhiteSpace(`$choice)) {
    Write-Host "Using default configuration..." -ForegroundColor Yellow
}
elseif (`$choice -match '^\d+$' -and [int]`$choice -ge 1 -and [int]`$choice -le `$maxChoice) {
    `$selectedConfig = `$configFiles[[int]`$choice - 1].FullName
    Write-Host "Using configuration: `$(`$configFiles[[int]`$choice - 1].Name)" -ForegroundColor Green
}
else {
    Write-Host "Invalid selection, using default configuration..." -ForegroundColor Yellow
}

try {
    if (`$selectedConfig) {
        Write-Host "Starting Event Processor (config: `$(`$selectedConfig))..." -ForegroundColor Yellow
        & `$mainExe --config `$selectedConfig
    }
    else {
        Write-Host "Starting Event Processor (default config)..." -ForegroundColor Yellow
        & `$mainExe
    }
}
catch {
    Write-Host "Startup failed: `$($_.Exception.Message)" -ForegroundColor Red
}
finally {
    Read-Host "Press any key to exit"
}
"@

    $startWithConfigScriptPath = Join-Path $TargetPath "start-with-config-selection.ps1"
    Set-Content -Path $startWithConfigScriptPath -Value $startWithConfigScript -Encoding UTF8

    Write-Log "Startup scripts created (PowerShell version)"
}



# Get application version information
function Get-ApplicationVersion {
    [CmdletBinding()]
    param([string]$ExePath)
    
    try {
        if (-not (Test-Path $ExePath)) {
            return "Unknown (exe not found)"
        }
        
        # Get version from file properties
        $fileVersion = (Get-Item $ExePath).VersionInfo
        $productVersion = $fileVersion.ProductVersion
        $fileVersionString = $fileVersion.FileVersion
        $informationalVersion = $fileVersion.ProductVersion
        
        # Try to get assembly version using .NET reflection
        try {
            $assembly = [Reflection.Assembly]::LoadFrom($ExePath)
            $assemblyVersion = $assembly.GetName().Version.ToString()
            $informationalVersionAttr = $assembly.GetCustomAttributes([System.Reflection.AssemblyInformationalVersionAttribute], $false)
            if ($informationalVersionAttr.Count -gt 0) {
                $informationalVersion = $informationalVersionAttr[0].InformationalVersion
            }
        }
        catch {
            # If assembly loading fails, use file version info
            Write-Log "Assembly version detection failed, using file version" "DEBUG"
        }
        
        return @{
            ProductVersion = if ($productVersion) { $productVersion } else { "Unknown" }
            FileVersion = if ($fileVersionString) { $fileVersionString } else { "Unknown" }
            InformationalVersion = if ($informationalVersion) { $informationalVersion } else { "Unknown" }
            AssemblyVersion = if ($assemblyVersion) { $assemblyVersion } else { "Unknown" }
        }
    }
    catch {
        Write-Log "Version detection failed: $($_.Exception.Message)" "WARN"
        return "Unknown (error reading version)"
    }
}

# Verify deployment
function Test-Deployment {
    Write-Log "Verifying deployment..."

    $mainExe = Join-Path $TargetPath "EventProcessor.Host.exe"
    if (-not (Test-Path $mainExe)) {
        Write-Log "Main program file does not exist: $mainExe" "ERROR"
        return $false
    }

    $configFile = Join-Path $TargetPath "appsettings.yaml"
    if (-not (Test-Path $configFile)) {
        Write-Log "Configuration file does not exist: $configFile" "WARN"
    }

    # Get and display version information
    Write-Log "Getting application version information..." "INFO"
    $versionInfo = Get-ApplicationVersion -ExePath $mainExe
    
    if ($versionInfo -is [hashtable]) {
        Write-Log "==========================================" "SUCCESS"
        Write-Log "Deployed Application Version Information:" "SUCCESS"
        Write-Log "==========================================" "SUCCESS"
        Write-Log "Product Version: $($versionInfo.ProductVersion)" "SUCCESS"
        Write-Log "File Version: $($versionInfo.FileVersion)" "SUCCESS"
        Write-Log "Informational Version: $($versionInfo.InformationalVersion)" "SUCCESS"
        if ($versionInfo.AssemblyVersion -ne "Unknown") {
            Write-Log "Assembly Version: $($versionInfo.AssemblyVersion)" "SUCCESS"
        }
        Write-Log "==========================================" "SUCCESS"
    }
    else {
        Write-Log "Version: $versionInfo" "INFO"
    }

    Write-Log "Deployment verification passed" "SUCCESS"
    return $true
}

# Main function - Improved PowerShell version
function Invoke-Main {
    [CmdletBinding()]
    param()

    $deploymentStartTime = Get-Date
    $publishPath = $null

    try {
        Write-Log "==========================================" "INFO"
        Write-Log "Event Processor V4.1 Deployment Script Started (PowerShell Version)" "INFO"
        Write-Log "Start time: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')" "INFO"
        Write-Log "Target path: $TargetPath" "INFO"
        Write-Log "Configuration: $Configuration" "INFO"
        Write-Log "Skip build: $SkipBuild" "DEBUG"
        Write-Log "Force mode: $Force" "DEBUG"
        Write-Log "Verbose mode: $VerboseOutput" "DEBUG"
        Write-Log "==========================================" "INFO"

        # Check prerequisites
        Write-Log "Step 1/7: Checking prerequisites" "INFO"
        Test-Prerequisites

        # Stop remote process
        Write-Log "Step 2/7: Stopping remote process" "INFO"
        Stop-RemoteProcess

        # Backup existing deployment
        Write-Log "Step 3/7: Backing up existing deployment" "INFO"
        Backup-ExistingDeployment

        # Build project
        Write-Log "Step 4/7: Building project" "INFO"
        $buildSuccess = Build-Project
        if (-not $buildSuccess) {
            throw "Build step failed"
        }

        # Publish project
        Write-Log "Step 5/7: Publishing project" "INFO"
        $publishPath = Publish-Project

        # Prepare deployment files
        Write-Log "Step 6/7: Preparing deployment files" "INFO"
        $files = Prepare-DeploymentFiles -PublishPath $publishPath

        # Execute deployment
        Write-Log "Step 7/7: Executing deployment" "INFO"
        Deploy-Files -Files $files

        # Verify deployment
        Write-Log "Verifying deployment result..." "INFO"
        $deploymentValid = Test-Deployment
        if (-not $deploymentValid) {
            throw "Deployment verification failed"
        }

        # Calculate deployment time
        $deploymentEndTime = Get-Date
        $deploymentDuration = $deploymentEndTime - $deploymentStartTime

        # Get final version information for summary
        $mainExe = Join-Path $TargetPath "EventProcessor.Host.exe"
        $finalVersionInfo = Get-ApplicationVersion -ExePath $mainExe
        $deployedVersion = "Unknown"
        if ($finalVersionInfo -is [hashtable]) {
            $deployedVersion = $finalVersionInfo.InformationalVersion
            if ($deployedVersion -eq "Unknown" -or [string]::IsNullOrEmpty($deployedVersion)) {
                $deployedVersion = $finalVersionInfo.ProductVersion
            }
        }
        elseif ($finalVersionInfo -is [string]) {
            $deployedVersion = $finalVersionInfo
        }

        Write-Log "==========================================" "SUCCESS"
        Write-Log "🎉 DEPLOYMENT COMPLETED SUCCESSFULLY! 🎉" "SUCCESS"
        Write-Log "==========================================" "SUCCESS"
        Write-Log "📍 Target Path: $TargetPath" "SUCCESS"
        Write-Log "📦 Deployed Version: $deployedVersion" "SUCCESS"
        Write-Log "⏱️  Deployment Time: $($deploymentDuration.ToString('mm\:ss'))" "SUCCESS"
        Write-Log "📄 Log File: $LogFile" "INFO"
        Write-Log "" "SUCCESS"
        Write-Log "✅ The application is ready to run on the target server." "SUCCESS"
        Write-Log "✅ You can verify the version by running the executable." "SUCCESS"
        Write-Log "==========================================" "SUCCESS"

        return 0
    }
    catch {
        $deploymentEndTime = Get-Date
        $deploymentDuration = $deploymentEndTime - $deploymentStartTime

        Write-Log "==========================================" "ERROR"
        Write-Log "Deployment failed: $($_.Exception.Message)" "ERROR"
        Write-Log "Failure time: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')" "ERROR"
        Write-Log "Runtime: $($deploymentDuration.ToString('mm\:ss'))" "ERROR"
        Write-Log "Log file: $LogFile" "ERROR"
        Write-Log "==========================================" "ERROR"

        return 1
    }
    finally {
        Write-Log "Script execution completed" "INFO"
    }
}

# Script entry point
try {
    $exitCode = Invoke-Main
    exit $exitCode
}
catch {
    Write-Host "Script execution encountered unhandled error: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}
