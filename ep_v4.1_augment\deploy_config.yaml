# Event Processor V4.1 部署配置文件
# 用于自定义部署参数和设置

deployment:
  # 目标服务器配置
  target:
    server: "\\\\bdnserver\\BDN\\EP_V4.1"
    backup_retention_days: 7
    max_backup_count: 10
  
  # 构建配置
  build:
    configuration: "Release"
    target_framework: "net8.0"
    runtime_identifier: "win-x64"
    self_contained: false
    single_file: true
  
  # 部署选项
  options:
    create_backup: true
    verify_deployment: true
    create_startup_scripts: true
    copy_logs: false
    preserve_config: true
  
  # 文件包含/排除规则
  files:
    include_patterns:
      - "*.exe"
      - "*.dll"
      - "*.pdb"
      - "*.json"
      - "*.yaml"
      - "*.yml"
    
    exclude_patterns:
      - "*.log"
      - "*.tmp"
      - "obj/**"
      - "bin/Debug/**"
    
    required_files:
      - "EventProcessor.Host.exe"
      - "EventProcessor.Host.dll"
      - "EventProcessor.Host.deps.json"
      - "EventProcessor.Host.runtimeconfig.json"
      - "appsettings.yaml"
  
  # 配置文件映射
  config_mapping:
    source_configs:
      - path: "deploy/appsettings.yaml"
        target: "appsettings.yaml"
        required: true
              
  # 服务配置
  service:
    name: "EventProcessorV41"
    display_name: "Event Processor V4.1"
    description: "EP_V4.1增强规则配置系统服务"
    start_type: "Automatic"
    account: "LocalSystem"
  
  # 健康检查
  health_check:
    enabled: true
    endpoint: "http://localhost:5000/health"
    timeout_seconds: 30
    retry_count: 3
  
  # 日志配置
  logging:
    level: "Information"
    retain_days: 30
    max_file_size_mb: 100
    log_path: "logs"

# 环境特定配置
environments:
  production:
    target:
      server: "\\\\bdnserver\\BDN\\EP_V4.1"
    build:
      configuration: "Release"
    logging:
      level: "Warning"
  
  staging:
    target:
      server: "\\\\bdnserver\\BDN\\EP_V4.1_Staging"
    build:
      configuration: "Release"
    logging:
      level: "Information"
  
  development:
    target:
      server: "\\\\bdnserver\\BDN\\EP_V4.1_Dev"
    build:
      configuration: "Debug"
    logging:
      level: "Debug"

# 通知配置
notifications:
  email:
    enabled: false
    smtp_server: ""
    from_address: ""
    to_addresses: []
  
  webhook:
    enabled: false
    url: ""
    timeout_seconds: 10
