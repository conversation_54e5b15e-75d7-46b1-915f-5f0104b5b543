# 📋 设计文档: 事件复现工具集 (V2.0 - 专用脚本工作流)

本文档详细描述了为 Event Processor 设计的、基于专用脚本的三步事件复现工作流。该设计以**精确**和**简洁**为核心原则。

---

## 1. 工作流概述

本工具集包含三个独立的脚本，共同完成事件的捕获、转换和复现：

1.  **`EventCapture.py` (通用抓取工具)**:
    -   **职责**: 从数据库中抓取一个事件窗口内的**原始、未加工**的数据。
    -   **输入**: EP配置文件 (`--config`) 和精确的触发时间 (`--trigger-time`)。
    -   **输出**: 一个包含 `raw_events.csv` 的场景目录。

2.  **`EV001001YAMLGenerator.py` (专用场景生成器)**:
    -   **职责**: 读取 `raw_events.csv`，并根据 `EV001001` 事件的特定逻辑，生成最终的 `scene.yaml` 文件。
    -   **输入**: 场景ID (`--scene`)。
    -   **输出**: 在场景目录中创建 `scene.yaml`。

3.  **`EV001001Replayer.py` (专用复现工具)**:
    -   **职责**: 读取 `scene.yaml` 并精确地按时间间隔复现事件。
    -   **输入**: 场景ID (`--scene`)。
    -   **输出**: 将消息发布到MQTT代理。

---

## 2. 工具详解

### 2.1 `EventCapture.py`

-   **设计哲学**: 通用、原始、不带任何业务偏见。它只负责忠实地记录在某个时间窗口内发生了什么。
-   **调用示例**:
    ```bash
    python utils/EventCapture.py --config ep_v4.1_augment/appsettings.yaml --trigger-time "2025-08-05 07:52:58.707"
    ```
-   **输出**:
    ```
    utils/scenes/EV001001-20250805075258707/
    └── raw_events.csv
    ```

### 2.2 `EV001001YAMLGenerator.py`

-   **设计哲学**: 专用、逻辑集中。所有与 `EV001001` 事件相关的特殊处理逻辑都封装在此脚本中。
-   **核心逻辑**:
    -   读取 `raw_events.csv`。
    -   计算每条消息相对于第一条消息的 `offset_ms`。
    -   **对于 `EV001001` 事件，当前版本不需要修改 `payload` 内的时间戳**。如果未来需要，所有修改逻辑都将在此文件中实现。
-   **调用示例**:
    ```bash
    python utils/EV001001YAMLGenerator.py --scene EV001001-20250805075258707
    ```
-   **输出**:
    ```
    utils/scenes/EV001001-20250805075258707/
    ├── raw_events.csv
    └── scene.yaml  <-- 新生成的文件
    ```

### 2.3 `EV001001Replayer.py`

-   **设计哲学**: 专用、简单、可靠的执行引擎。它严格按照 `scene.yaml` 的“剧本”进行表演。
-   **核心逻辑**:
    -   连接到MQTT。
    -   等待用户确认。
    -   严格按照 `scene.yaml` 中定义的 `offset_ms` 时间间隔，依次发布 `payload`。
-   **调用示例**:
    ```bash
    python utils/EV001001Replayer.py --scene EV001001-20250805075258707
    ```

---

## 3. 优势

-   **关注点分离**: 抓取、转换、复现三个阶段完全解耦。
-   **高保真与灵活性的平衡**: 原始数据 (`raw_events.csv`) 被完整保留，允许我们随时调整生成逻辑 (`YAMLGenerator`)，而无需重新抓取数据。
-   **简洁性**: 每个脚本的职责都非常单一，易于理解和维护。
-   **可扩展性**: 当需要支持新事件（如 `EV001008`）时，只需创建新的 `EV001008YAMLGenerator.py` 和 `EV001008Replayer.py`，而无需改动通用的 `EventCapture.py`。