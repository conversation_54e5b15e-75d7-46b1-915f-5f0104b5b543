## 📋 **EV001001 事件深度解析 (V1.1)**

### 1. **事件定义**

**事件名称**：`月租车未过期超时滞留出口`  
**事件ID**：`EV001001`  
**优先级**：`P3`（通知级别）

**业务含义**：
- 检测持有有效月租卡/储值卡等的车辆在出口位置异常滞留的情况
- 目的是及时发现可能的设备故障、车辆故障或其他异常情况，避免出口堵塞
- 属于预防性监控，帮助管理人员及时介入处理

### 2. **触发条件分析 (V1.2)**

根据配置文件中的 `BusinessRules`，触发条件为：

```yaml
条件组合逻辑：
(CardType IN ["月租卡","万全卡","贵宾卡","储值卡"]) 
AND 
(log_remain_days != 0 OR Expire == "0") 
AND 
(holding_duration > 20)
```

**各条件的业务逻辑**：

1. **CardType 条件**：
   - **逻辑**：卡类型必须是有效的付费卡类型。
   - **目的**：将分析范围限定在月租、储值等长期客户车辆。

2. **车辆未过期条件 (组合逻辑)**：
   - **逻辑**：`log_remain_days` 字段不等于0 **或者** `Expire` 字段等于 "0"。
   - **目的**：建立一个更健壮的“未过期”判断。此规则兼容两种AJB系统返回的数据格式：
     - **`log_remain_days`**: 直接判断剩余天数。
     - **`Expire`**: 通过AJB系统的内部标志判断。
   - **优势**：解决了旧版规则强依赖 `log_remain_days` 字段而导致的漏报问题。

3. **duration 条件**：
   - **逻辑**：滞留时间超过20秒。
   - **目的**：识别出车辆在出口地感的异常停留行为。

### 3. **数据来源分析**

````yaml path=appsettings.yaml mode=EXCERPT
DeviceSignal:
  Topics:
    - "device/BDN861290073715232/event"
  TriggerField: "I2"
  HoldingTimeoutSec: 20

BusinessRules:
  - SourceTopic: "ajb/101013/out/P002LfyBmOut/time_log"
````

**数据来源映射**：

| 字段 | 数据来源 | 获取方式 | 业务含义 |
|------|----------|----------|----------|
| `CardType` | AJB系统 | `ajb/*/time_log` 主题 | 从 `response_payload.data.CarInfo.CardType` 提取 |
| `log_remain_days` | AJB系统 | `ajb/*/time_log` 主题 | 从 `response_payload.data.CarInfo.RemainDays` 提取 |
| `duration` | **EventProcessor** | 设备信号计算 | 基于 `I2` 信号和 `HoldingTimeoutSec=20` 计算 |

#### **正确的业务流程应该是**：

```mermaid
sequenceDiagram
    participant Car as 车辆
    participant Device as 地感设备
    participant EP as EventProcessor
    participant AJB as AJB系统
    participant Alarm as 告警系统

    Note over Car,Alarm: 场景：月租车正常出场但设备故障导致滞留

    Car->>Device: 1. 车辆到达出口，触发地感
    Device->>EP: 2. 发送 I2=true 信号 (一次性)
    Note over EP: 收到信号，状态变为 true<br/>开始计时 holding_duration=0

    Car->>AJB: 3. 车辆刷卡/扫码出场
    AJB->>EP: 4. 发送出场查询结果
    Note over EP: 提取并缓存：<br/>CardType="月租卡"<br/>log_remain_days=15

    Note over Car,Device: 5. 设备故障，闸机未抬起
    Note over Car: 车辆被迫停留在出口

    Note over EP: 6. EP 内部持续计时<br/>(未收到 I2=false 信号)
    
    alt 计时未超过 20 秒
        Note over EP: holding_duration 持续增加<br/>5s, 10s, 15s, 19s...
    else 计时超过 20 秒
        Note over EP: 7. HoldingTimeoutSec 触发<br/>holding_duration > 20
        EP->>EP: 8. 评估业务规则：<br/>✓ CardType="月租卡" (已缓存)<br/>✓ log_remain_days=15 > 0 (已缓存)<br/>✓ holding_duration > 20 (内部计时)
        EP->>Alarm: 9. 触发告警：月租车超时滞留出口
        Note over Alarm: 告警内容：<br/>[月租卡][粤B12345][张三][2025-08-05]<br/>停留时间25秒
    end
```