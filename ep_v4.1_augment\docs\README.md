# Event Processor V4.1 文档索引

本目录包含 Event Processor V4.1 项目的所有技术文档。

## 📚 文档结构

### 主要文档
- **[../README.md](../README.md)** - 项目主文档
  - 项目概述和核心特性
  - 快速开始指南
  - 项目结构说明
  - 文档索引

- **[DEPLOYMENT_GUIDE.md](DEPLOYMENT_GUIDE.md)** - 部署指南
  - 系统要求和环境准备
  - 自动化部署脚本使用
  - 手动部署步骤
  - 配置文件详细说明
  - 服务管理和运维

### 技术参考
- **[../OPERATORS_REFERENCE.md](../OPERATORS_REFERENCE.md)** - 操作符参考手册
  - 22种操作符的详细说明
  - 数据类型支持（string、number、datetime）
  - 配置示例和最佳实践

- **[../DES_EP_V4.1.md](../DES_EP_V4.1.md)** - 系统设计文档
  - 完整的架构设计
  - 技术规范和实现细节
  - 数据模型和接口定义

## 🗂️ 配置和示例

### 配置文件
- **[../config/](../config/)** - 事件配置示例
  - `EV001001-config.yaml` - 示例事件配置
  - `delivery-vehicle-test-config.yaml` - 测试配置

### 部署模板
- **[../deploy/](../deploy/)** - 部署文件和配置模板
  - `appsettings.yaml` - 应用程序配置
  - `config-production-ready.yaml` - 生产就绪配置（推荐）
  - `config-complete-sample.yaml` - 完整配置示例
  - `config-ai-sample.yaml` - AI配置示例

## 🛠️ 工具和脚本

### 部署工具
- **[../deploy_bdnserver.ps1](../deploy_bdnserver.ps1)** - 完整的自动化部署脚本
- **[../deploy.bat](../deploy.bat)** - 用户友好的部署启动器

### 服务管理
- **[../scripts/](../scripts/)** - Windows服务管理脚本
  - `install-service.ps1` - 服务安装脚本
  - `uninstall-service.ps1` - 服务卸载脚本

## 📖 阅读顺序建议

### 新用户
1. [项目主文档](../README.md) - 了解项目概述和特性
2. [部署指南](DEPLOYMENT_GUIDE.md) - 学习如何部署和配置
3. [操作符参考](../OPERATORS_REFERENCE.md) - 掌握规则配置语法

### 开发者
1. [系统设计文档](../DES_EP_V4.1.md) - 理解架构和设计
2. [项目主文档](../README.md) - 了解项目结构和开发环境
3. [操作符参考](../OPERATORS_REFERENCE.md) - 深入了解规则引擎

### 运维人员
1. [部署指南](DEPLOYMENT_GUIDE.md) - 掌握部署和运维流程
2. [项目主文档](../README.md) - 了解项目基本信息
3. 配置文件示例 - 学习配置管理

## 🔄 文档维护

### 更新记录
- **2025-01-03**: 统一文档结构，移除重复文档
- **2025-05-31**: 完成JSON到YAML的配置迁移

### 维护原则
- 保持文档的单一来源原则
- 避免内容重复和冗余
- 及时更新配置示例和最佳实践
- 确保文档与代码同步更新

---

**注意**: 如果发现文档中的错误或需要补充内容，请及时更新相应文档并保持索引的准确性。
