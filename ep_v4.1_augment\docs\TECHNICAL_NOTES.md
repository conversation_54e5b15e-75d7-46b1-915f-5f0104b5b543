# EventProcessor 技术说明文档

**版本**: 1.0  
**日期**: 2025-08-04  
**基于**: PLAN.md v1.1 的修正

---

## 🔧 **EventProcessor 工作机制详解**

### 1. 设备信号处理机制

#### 1.1 信号触发逻辑
EventProcessor 采用"**一次触发，持续计时**"的工作模式：

```yaml
DeviceSignal:
  TriggerField: "I2"
  TriggerValues:
    "true": "0"    # I2=true 表示有车辆
    "false": "1"   # I2=false 表示无车辆
  HoldingTimeoutSec: 20
```

**工作流程**：
1. **触发阶段**：收到 `I2=true` 信号
2. **计时阶段**：EventProcessor 内部开始计时 `holding_duration = 0`
3. **监控阶段**：等待 `I2=false` 信号或超时
4. **超时判断**：如果 `holding_duration > HoldingTimeoutSec`，触发超时事件

#### 1.2 关键技术点

**❌ 错误理解**：需要持续接收 `I2=true` 信号
```
Device -> EP: I2=true (t=0s)
Device -> EP: I2=true (t=1s)  
Device -> EP: I2=true (t=2s)
...持续发送...
Device -> EP: I2=true (t=20s) -> 触发超时
```

**✅ 正确理解**：一次触发，内部计时
```
Device -> EP: I2=true (t=0s)    -> 开始计时
EP 内部: holding_duration++     -> 1s, 2s, 3s...
EP 内部: holding_duration=20s   -> 触发超时
```

### 2. 事件关联机制 (单例实例模型)

#### 2.1 核心思想

对于配置为处理单一物理点位（如单个地感）的EP实例，在任何时刻只可能存在一个活跃事件。因此，系统采用**单例实例模型**。

- **关联ID (`CorrelationId`)**: `CorrelationId` 是一个基于配置（`CommId`, `PositionId`, `EventId`）生成的**恒定标识符**。它不再包含时间窗口信息。
- **事件聚合**: 所有收到的消息都会被路由到这个唯一的、由`CorrelationId`标识的事件实例中，直到该事件完成（例如，收到接触信号）。

这种设计确保了属于同一个物理事件的所有消息（无论它们之间的时间差有多大）都会被正确地聚合处理，彻底避免了“实例分裂”的问题。

#### 2.2 数据流转过程

```mermaid
graph TD
    A[设备信号 I2=true] --> B{EventManager}
    C[AJB业务消息] --> B

    subgraph EventManager
        D[获取或创建唯一的事件实例]
    end

    B --> D
    D --> E[EventStateAggregator]
    E --> F[处理消息 / 计时]
    F --> G{评估业务规则}
    G --> H[触发告警]
```

### 3. Duration 字段的三种概念

#### 3.1 概念区分

| 概念 | 来源 | 含义 | 用途 |
|------|------|------|------|
| **EventProcessor.duration** | 设备信号计算 | 实时滞留时长 | 超时检测 |
| **AJB.ParkTime** | AJB系统 | 历史停车总时长 | 业务记录 |
| **AJB.duration** | 代码转换 | ParkTime的分钟数 | ❌ 错误用法 |

#### 3.2 正确的使用方式

**✅ 正确配置**：
```yaml
# 设备层面处理滞留检测
DeviceSignal:
  HoldingTimeoutSec: 20

# 业务层面验证车辆资格（不包含duration）
BusinessRules:
  Conditions:
    - FieldName: "CardType"
    - FieldName: "log_remain_days"
    # ❌ 不应包含 duration 条件

# 告警显示使用设备计算的duration
AlarmConfig:
  Fields:
    - AlarmFieldName: "事件"
      SourceRuleType: "DeviceSignal"  # ✅ 正确数据源
      SourceFieldName: "duration"
```

### 4. 配置修复的技术原理

#### 4.1 问题根源分析

**原始错误配置**：
```yaml
BusinessRules:
  - SourceTopic: "ajb/101013/out/P002LfyBmOut/time_log"
    Conditions:
      - FieldName: "duration"  # ❌ 试图从AJB获取duration
        Operator: "GreaterThan"
        Value: "20"
```

**问题分析**：
1. **数据源错配**：AJB消息不包含EventProcessor需要的实时duration
2. **概念混淆**：将历史停车时长与实时滞留时长混淆
3. **逻辑错误**：业务规则层面不应处理时间检测

#### 4.2 修复方案的技术逻辑

**修复后的逻辑**：
```yaml
# 1. 设备层面：处理实时滞留检测
DeviceSignal:
  HoldingTimeoutSec: 20  # 自动触发超时

# 2. 业务层面：验证车辆资格
BusinessRules:
  Conditions:
    - FieldName: "CardType"      # 从AJB获取
    - FieldName: "log_remain_days"  # 从AJB获取
    # 移除duration条件

# 3. 组合逻辑：设备超时 AND 业务验证通过 = 触发告警
```

### 5. 实现细节

#### 5.1 AJB消息适配器

我们实现的AJB适配器解决了嵌套JSON解析问题：

```csharp
// ✅ 正确提取业务字段
if (TryGetNestedElement(root, "response_payload.data.CarInfo", out var carInfo))
{
    result["CardType"] = carInfo.GetProperty("CardType").GetString();
    result["log_remain_days"] = carInfo.GetProperty("RemainDays").GetInt64();
}

// ❌ 不再需要的duration计算（已移除）
// var durationMinutes = ParseDurationFromParkTime(parkTimeText);
// result["duration"] = durationMinutes;
```

#### 5.2 事件触发条件

**修复后的完整触发逻辑**：
```
触发条件 = DeviceSignal.超时 AND BusinessRules.验证通过

其中：
- DeviceSignal.超时 = (收到I2=true后20秒内未收到I2=false)
- BusinessRules.验证通过 = (CardType有效 AND log_remain_days>0)
```

### 6. 验证和测试

#### 6.1 单元测试要点

```csharp
[Test]
public void DeviceSignal_HoldingTimeout_ShouldTriggerAfter20Seconds()
{
    // 模拟I2=true信号
    // 验证20秒后触发超时
    // 确认duration=20
}

[Test]
public void BusinessRules_AjbMessage_ShouldExtractCorrectFields()
{
    // 模拟AJB消息
    // 验证CardType和log_remain_days提取正确
    // 确认不依赖duration字段
}
```

#### 6.2 集成测试场景

1. **正常滞留场景**：I2=true + 有效月租卡 + 未过期 → 触发告警
2. **快速通过场景**：I2=true → I2=false (< 20s) → 不触发告警
3. **无效卡场景**：I2=true + 临时卡 → 不触发告警
4. **过期卡场景**：I2=true + 过期卡 → 不触发告警

### 7. 性能和可靠性

#### 7.1 性能优化

- **数据缓存**：AJB业务数据在收到后缓存，避免重复解析
- **计时器优化**：使用高精度计时器确保准确的超时检测
- **内存管理**：及时清理过期的状态数据

#### 7.2 可靠性保障

- **异常处理**：JSON解析失败不影响设备信号处理
- **状态恢复**：服务重启后能正确恢复设备状态
- **日志记录**：完整记录事件触发过程便于排查

---

## 📚 **参考资料**

- `PLAN.md` - 综合修改方案
- `QUICK_FIX_GUIDE.md` - 快速修复指南
- `REV-20250804-03.0.md` - 技术审查报告
- `appsettings.yaml` - 配置文件

---

**注意**：本文档基于对EventProcessor v4.1的技术分析，如系统版本更新，请相应调整理解。
