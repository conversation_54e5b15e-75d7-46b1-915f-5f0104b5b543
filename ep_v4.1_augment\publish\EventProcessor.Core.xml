<?xml version="1.0"?>
<doc>
    <assembly>
        <name>EventProcessor.Core</name>
    </assembly>
    <members>
        <member name="T:EventProcessor.Core.Engine.ConditionEvaluator">
            <summary>
            条件评估引擎 - 支持string、number、datetime三种数据类型和22种操作符
            </summary>
        </member>
        <member name="M:EventProcessor.Core.Engine.ConditionEvaluator.#ctor(Microsoft.Extensions.Logging.ILogger{EventProcessor.Core.Engine.ConditionEvaluator})">
            <summary>
            初始化条件评估引擎
            </summary>
            <param name="logger">日志记录器</param>
        </member>
        <member name="M:EventProcessor.Core.Engine.ConditionEvaluator.EvaluateCondition(EventProcessor.Core.Models.Condition,System.Collections.Generic.Dictionary{System.String,System.Object})">
            <summary>
            评估单个条件
            </summary>
            <param name="condition">条件定义</param>
            <param name="data">数据字典</param>
            <returns>评估结果</returns>
        </member>
        <member name="M:EventProcessor.Core.Engine.ConditionEvaluator.EvaluateConditionGroup(EventProcessor.Core.Models.ConditionGroup,System.Collections.Generic.Dictionary{System.String,System.Object})">
            <summary>
            评估条件组
            </summary>
            <param name="group">条件组</param>
            <param name="data">数据字典</param>
            <returns>评估结果</returns>
        </member>
        <member name="M:EventProcessor.Core.Engine.ConditionEvaluator.EvaluateStringCondition(EventProcessor.Core.Models.Condition,System.String)">
            <summary>
            评估字符串条件
            </summary>
        </member>
        <member name="M:EventProcessor.Core.Engine.ConditionEvaluator.EvaluateNumberCondition(EventProcessor.Core.Models.Condition,System.Object)">
            <summary>
            评估数字条件
            </summary>
        </member>
        <member name="M:EventProcessor.Core.Engine.ConditionEvaluator.EvaluateNumberRange(System.Double,System.String)">
            <summary>
            评估数字范围
            </summary>
        </member>
        <member name="M:EventProcessor.Core.Engine.ConditionEvaluator.EvaluateDateTimeCondition(EventProcessor.Core.Models.Condition,System.Object)">
            <summary>
            评估日期时间条件
            </summary>
        </member>
        <member name="M:EventProcessor.Core.Engine.ConditionEvaluator.NormalizeDateTimeString(System.String)">
            <summary>
            标准化日期时间字符串为 YYYYMMDDHHMMSS 格式
            </summary>
        </member>
        <member name="M:EventProcessor.Core.Engine.ConditionEvaluator.EvaluateDateTimeRange(System.String,System.String)">
            <summary>
            评估日期时间范围
            </summary>
        </member>
        <member name="M:EventProcessor.Core.Engine.ConditionEvaluator.EvaluateWithinTimespan(System.String,System.String,System.String)">
            <summary>
            评估时间跨度内条件
            </summary>
        </member>
        <member name="M:EventProcessor.Core.Engine.ConditionEvaluator.ApplyLogicOperator(System.String,System.Collections.Generic.List{System.Boolean})">
            <summary>
            应用逻辑操作符
            </summary>
        </member>
        <member name="T:EventProcessor.Core.Engine.ExclusionDataAccumulator">
            <summary>
            排除数据累积器 - 累积所有排除消息的数据
            </summary>
        </member>
        <member name="M:EventProcessor.Core.Engine.ExclusionDataAccumulator.#ctor(Microsoft.Extensions.Logging.ILogger{EventProcessor.Core.Engine.ExclusionDataAccumulator})">
            <summary>
            初始化排除数据累加器
            </summary>
            <param name="logger">日志记录器</param>
        </member>
        <member name="M:EventProcessor.Core.Engine.ExclusionDataAccumulator.AddMessage(EventProcessor.Core.Models.EventMessage)">
            <summary>
            添加排除消息数据
            </summary>
            <param name="message">事件消息</param>
        </member>
        <member name="M:EventProcessor.Core.Engine.ExclusionDataAccumulator.GetCombinedData">
            <summary>
            获取合并后的所有数据
            </summary>
            <returns>合并的数据字典</returns>
        </member>
        <member name="M:EventProcessor.Core.Engine.ExclusionDataAccumulator.GetTopicData(System.String)">
            <summary>
            获取指定主题的数据
            </summary>
            <param name="topic">主题名称</param>
            <returns>主题数据</returns>
        </member>
        <member name="M:EventProcessor.Core.Engine.ExclusionDataAccumulator.Clear">
            <summary>
            清空所有数据
            </summary>
        </member>
        <member name="M:EventProcessor.Core.Engine.ExclusionDataAccumulator.GetStatistics">
            <summary>
            获取统计信息
            </summary>
        </member>
        <member name="T:EventProcessor.Core.Engine.BusinessDataAccumulator">
            <summary>
            业务数据累积器 - 使用最新值覆盖策略
            </summary>
        </member>
        <member name="M:EventProcessor.Core.Engine.BusinessDataAccumulator.#ctor(Microsoft.Extensions.Logging.ILogger{EventProcessor.Core.Engine.BusinessDataAccumulator})">
            <summary>
            初始化业务数据累加器
            </summary>
            <param name="logger">日志记录器</param>
        </member>
        <member name="M:EventProcessor.Core.Engine.BusinessDataAccumulator.UpdateMessage(EventProcessor.Core.Models.EventMessage)">
            <summary>
            更新业务消息数据 - 智能处理ajb消息格式
            </summary>
            <param name="message">事件消息</param>
        </member>
        <member name="M:EventProcessor.Core.Engine.BusinessDataAccumulator.LogAjbKeyFields(System.Collections.Generic.Dictionary{System.String,System.Object})">
            <summary>
            记录AJB消息的关键字段值（用于调试）
            </summary>
        </member>
        <member name="M:EventProcessor.Core.Engine.BusinessDataAccumulator.GetPayloadPreview(System.String)">
            <summary>
            获取负载预览（用于日志）
            </summary>
        </member>
        <member name="M:EventProcessor.Core.Engine.BusinessDataAccumulator.IsAjbMessage(System.String)">
            <summary>
            检查消息是否来自ajb系统
            </summary>
        </member>
        <member name="M:EventProcessor.Core.Engine.BusinessDataAccumulator.AdaptAjbMessage(System.String)">
            <summary>
            适配ajb消息格式，解决报告中的嵌套JSON和duration计算问题
            </summary>
        </member>
        <member name="M:EventProcessor.Core.Engine.BusinessDataAccumulator.GetLatestData">
            <summary>
            获取最新的业务数据
            </summary>
            <returns>最新数据字典</returns>
        </member>
        <member name="M:EventProcessor.Core.Engine.BusinessDataAccumulator.GetFieldValue(System.String)">
            <summary>
            获取指定字段的值
            </summary>
            <param name="fieldName">字段名称</param>
            <returns>字段值</returns>
        </member>
        <member name="M:EventProcessor.Core.Engine.BusinessDataAccumulator.ContainsField(System.String)">
            <summary>
            检查是否包含指定字段
            </summary>
            <param name="fieldName">字段名称</param>
            <returns>是否包含</returns>
        </member>
        <member name="M:EventProcessor.Core.Engine.BusinessDataAccumulator.Clear">
            <summary>
            清空所有数据
            </summary>
        </member>
        <member name="M:EventProcessor.Core.Engine.BusinessDataAccumulator.GetStatistics">
            <summary>
            获取统计信息
            </summary>
        </member>
        <member name="M:EventProcessor.Core.Engine.BusinessDataAccumulator.TryGetNestedElement(System.Text.Json.JsonElement,System.String,System.Text.Json.JsonElement@)">
            <summary>
            尝试获取嵌套JSON元素
            </summary>
        </member>
        <member name="M:EventProcessor.Core.Engine.BusinessDataAccumulator.ExtractJsonValue(System.Text.Json.JsonElement)">
            <summary>
            提取JSON值并转换为合适的.NET类型
            </summary>
        </member>
        <member name="M:EventProcessor.Core.Engine.BusinessDataAccumulator.ParseDurationFromParkTime(System.String)">
            <summary>
            从ParkTime文本解析停车时长（分钟）
            </summary>
        </member>
        <member name="M:EventProcessor.Core.Engine.BusinessDataAccumulator.ExtractCarInfoFields(System.Text.Json.JsonElement,System.Collections.Generic.Dictionary{System.String,System.Object})">
            <summary>
            提取CarInfo字段
            </summary>
        </member>
        <member name="M:EventProcessor.Core.Engine.BusinessDataAccumulator.ExtractChargeFields(System.Text.Json.JsonElement,System.Collections.Generic.Dictionary{System.String,System.Object})">
            <summary>
            提取Charge字段并计算duration
            </summary>
        </member>
        <member name="M:EventProcessor.Core.Engine.BusinessDataAccumulator.ExtractTopLevelFields(System.Text.Json.JsonElement,System.Collections.Generic.Dictionary{System.String,System.Object})">
            <summary>
            提取顶级字段
            </summary>
        </member>
        <member name="T:EventProcessor.Core.Engine.AIResultDataAccumulator">
            <summary>
            AI结果数据累积器
            </summary>
        </member>
        <member name="M:EventProcessor.Core.Engine.AIResultDataAccumulator.#ctor(Microsoft.Extensions.Logging.ILogger{EventProcessor.Core.Engine.AIResultDataAccumulator})">
            <summary>
            初始化AI结果数据累加器
            </summary>
            <param name="logger">日志记录器</param>
        </member>
        <member name="M:EventProcessor.Core.Engine.AIResultDataAccumulator.SetResult(System.Collections.Generic.Dictionary{System.String,System.Object})">
            <summary>
            设置AI结果数据
            </summary>
            <param name="resultData">AI结果数据</param>
        </member>
        <member name="M:EventProcessor.Core.Engine.AIResultDataAccumulator.GetResultData">
            <summary>
            获取AI结果数据
            </summary>
            <returns>AI结果数据</returns>
        </member>
        <member name="P:EventProcessor.Core.Engine.AIResultDataAccumulator.HasResult">
            <summary>
            是否有AI结果
            </summary>
        </member>
        <member name="M:EventProcessor.Core.Engine.AIResultDataAccumulator.Clear">
            <summary>
            清空AI结果
            </summary>
        </member>
        <member name="M:EventProcessor.Core.Engine.AIResultDataAccumulator.GetStatistics">
            <summary>
            获取统计信息
            </summary>
        </member>
        <member name="T:EventProcessor.Core.Engine.DataAccumulatorStatistics">
            <summary>
            数据累加器统计信息
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Engine.DataAccumulatorStatistics.TopicCount">
            <summary>
            主题数量
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Engine.DataAccumulatorStatistics.TotalFieldCount">
            <summary>
            总字段数量
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Engine.DataAccumulatorStatistics.LastUpdateTime">
            <summary>
            最后更新时间
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Engine.DataAccumulatorStatistics.Topics">
            <summary>
            主题列表
            </summary>
        </member>
        <member name="T:EventProcessor.Core.Engine.EventManager">
            <summary>
            事件管理器 - 管理所有EventStateAggregator实例的生命周期
            解决消息乱序问题，支持任意消息类型触发事件创建
            </summary>
        </member>
        <member name="M:EventProcessor.Core.Engine.EventManager.#ctor(EventProcessor.Core.Models.EventConfiguration,System.IServiceProvider,Microsoft.Extensions.Logging.ILogger{EventProcessor.Core.Engine.EventManager})">
            <summary>
            初始化事件管理器
            </summary>
            <param name="config">事件配置</param>
            <param name="serviceProvider">服务提供器</param>
            <param name="logger">日志记录器</param>
        </member>
        <member name="M:EventProcessor.Core.Engine.EventManager.GetOrCreateAggregator(EventProcessor.Core.Models.EventMessage)">
            <summary>
            获取或创建事件聚合器 - 任何消息类型都可以触发创建
            </summary>
            <param name="message">事件消息</param>
            <returns>事件状态聚合器</returns>
        </member>
        <member name="M:EventProcessor.Core.Engine.EventManager.GenerateCorrelationId(EventProcessor.Core.Models.EventMessage)">
            <summary>
            生成事件关联ID - 支持可配置的时间窗口策略
            </summary>
            <param name="message">事件消息</param>
            <returns>关联ID</returns>
        </member>
        <member name="M:EventProcessor.Core.Engine.EventManager.GetTimeWindowStart(System.DateTime,System.TimeSpan)">
            <summary>
            计算时间窗口的起始时间，避免跨窗口边界问题
            </summary>
            <param name="currentTime">当前时间</param>
            <param name="windowSize">窗口大小</param>
            <returns>窗口起始时间</returns>
        </member>
        <member name="M:EventProcessor.Core.Engine.EventManager.RemoveAggregator(System.String)">
            <summary>
            移除事件聚合器
            </summary>
            <param name="correlationId">关联ID</param>
        </member>
        <member name="P:EventProcessor.Core.Engine.EventManager.ActiveEventCount">
            <summary>
            获取活跃事件数量
            </summary>
        </member>
        <member name="M:EventProcessor.Core.Engine.EventManager.GetActiveEventIds">
            <summary>
            获取所有活跃事件的关联ID
            </summary>
        </member>
        <member name="M:EventProcessor.Core.Engine.EventManager.GetStatistics">
            <summary>
            获取事件统计信息
            </summary>
        </member>
        <member name="M:EventProcessor.Core.Engine.EventManager.CleanupExpiredEvents(System.Object)">
            <summary>
            清理过期的事件实例
            </summary>
        </member>
        <member name="M:EventProcessor.Core.Engine.EventManager.ClearAllEvents">
            <summary>
            强制清理所有事件实例
            </summary>
        </member>
        <member name="M:EventProcessor.Core.Engine.EventManager.Dispose">
            <summary>
            释放事件管理器资源
            </summary>
        </member>
        <member name="M:EventProcessor.Core.Engine.EventManager.Dispose(System.Boolean)">
            <summary>
            释放资源的具体实现
            </summary>
            <param name="disposing">是否释放托管资源</param>
        </member>
        <member name="T:EventProcessor.Core.Engine.EventManagerStatistics">
            <summary>
            事件管理器统计信息
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Engine.EventManagerStatistics.ActiveEventCount">
            <summary>
            活跃事件数量
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Engine.EventManagerStatistics.TotalEventsProcessed">
            <summary>
            已处理事件总数
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Engine.EventManagerStatistics.EventsByState">
            <summary>
            按状态分组的事件数量
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Engine.EventManagerStatistics.AverageProcessingTime">
            <summary>
            平均处理时间（毫秒）
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Engine.EventManagerStatistics.LastCleanupTime">
            <summary>
            最后清理时间
            </summary>
        </member>
        <member name="T:EventProcessor.Core.Engine.EventStateAggregator">
            <summary>
            事件状态聚合器 - EP_V4.1增强版本
            包含增强的状态机、消息处理、告警静默期机制和告警撤销功能
            </summary>
        </member>
        <member name="E:EventProcessor.Core.Engine.EventStateAggregator.OnCompleted">
            <summary>
            事件完成时触发
            </summary>
        </member>
        <member name="E:EventProcessor.Core.Engine.EventStateAggregator.OnAlarmGenerated">
            <summary>
            告警生成时触发
            </summary>
        </member>
        <member name="E:EventProcessor.Core.Engine.EventStateAggregator.OnStateChanged">
            <summary>
            状态变更时触发
            </summary>
        </member>
        <member name="M:EventProcessor.Core.Engine.EventStateAggregator.#ctor(System.String,EventProcessor.Core.Models.EventConfiguration,System.IServiceProvider)">
            <summary>
            初始化事件状态聚合器
            </summary>
            <param name="correlationId">关联ID</param>
            <param name="config">事件配置</param>
            <param name="serviceProvider">服务提供器</param>
        </member>
        <member name="P:EventProcessor.Core.Engine.EventStateAggregator.CurrentState">
            <summary>
            当前状态
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Engine.EventStateAggregator.CorrelationId">
            <summary>
            关联ID
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Engine.EventStateAggregator.LastActivityTime">
            <summary>
            最后活动时间
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Engine.EventStateAggregator.ProcessingTime">
            <summary>
            处理时间
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Engine.EventStateAggregator.IsCompleted">
            <summary>
            是否已完成
            </summary>
        </member>
        <member name="M:EventProcessor.Core.Engine.EventStateAggregator.ProcessMessage(EventProcessor.Core.Models.EventMessage)">
            <summary>
            处理消息的主入口
            </summary>
            <param name="message">事件消息</param>
        </member>
        <member name="M:EventProcessor.Core.Engine.EventStateAggregator.ValidateMessageFormat(EventProcessor.Core.Models.EventMessage)">
            <summary>
            验证消息格式
            </summary>
        </member>
        <member name="M:EventProcessor.Core.Engine.EventStateAggregator.ValidateDeviceSignalMessage(EventProcessor.Core.Models.EventMessage)">
            <summary>
            验证设备信号消息
            </summary>
        </member>
        <member name="M:EventProcessor.Core.Engine.EventStateAggregator.ValidateDataMessage(EventProcessor.Core.Models.EventMessage,System.String)">
            <summary>
            验证数据消息
            </summary>
        </member>
        <member name="M:EventProcessor.Core.Engine.EventStateAggregator.ValidateAIResultMessage(EventProcessor.Core.Models.EventMessage)">
            <summary>
            验证AI结果消息
            </summary>
        </member>
        <member name="M:EventProcessor.Core.Engine.EventStateAggregator.GetPayloadPreview(System.String)">
            <summary>
            获取负载预览
            </summary>
        </member>
        <member name="M:EventProcessor.Core.Engine.EventStateAggregator.ShouldIgnoreMessage(EventProcessor.Core.Models.EventMessage)">
            <summary>
            检查是否应该忽略消息
            </summary>
        </member>
        <member name="M:EventProcessor.Core.Engine.EventStateAggregator.HandleDeviceSignal(EventProcessor.Core.Models.EventMessage)">
            <summary>
            处理设备信号消息
            </summary>
            <returns>是否需要状态转换</returns>
        </member>
        <member name="M:EventProcessor.Core.Engine.EventStateAggregator.HandleExclusionMessage(EventProcessor.Core.Models.EventMessage)">
            <summary>
            处理排除消息
            </summary>
        </member>
        <member name="M:EventProcessor.Core.Engine.EventStateAggregator.HandleBusinessMessage(EventProcessor.Core.Models.EventMessage)">
            <summary>
            处理业务消息
            </summary>
        </member>
        <member name="M:EventProcessor.Core.Engine.EventStateAggregator.HandleAIResultMessage(EventProcessor.Core.Models.EventMessage)">
            <summary>
            处理AI结果消息
            </summary>
        </member>
        <member name="M:EventProcessor.Core.Engine.EventStateAggregator.TransitionToCollecting">
            <summary>
            从Initializing状态转换到Collecting状态
            </summary>
        </member>
        <member name="M:EventProcessor.Core.Engine.EventStateAggregator.StartHoldingTimer">
            <summary>
            启动保持定时器
            </summary>
        </member>
        <member name="M:EventProcessor.Core.Engine.EventStateAggregator.StartAIAnalysisTimer">
            <summary>
            启动AI分析定时器
            </summary>
        </member>
        <member name="M:EventProcessor.Core.Engine.EventStateAggregator.HoldingTimerCallback(System.Object)">
            <summary>
            保持定时器回调 - V4.1修复：超时驱动告警的唯一入口
            </summary>
        </member>
        <member name="M:EventProcessor.Core.Engine.EventStateAggregator.AIAnalysisTimerCallback(System.Object)">
            <summary>
            AI分析定时器回调
            </summary>
        </member>
        <member name="M:EventProcessor.Core.Engine.EventStateAggregator.TriggerAIAnalysis">
            <summary>
            触发AI分析请求
            </summary>
        </member>
        <member name="M:EventProcessor.Core.Engine.EventStateAggregator.GetCurrentImagePath">
            <summary>
            获取当前图片路径
            </summary>
        </member>
        <member name="M:EventProcessor.Core.Engine.EventStateAggregator.ValidateAIResult(EventProcessor.Core.Models.AIResultMessage)">
            <summary>
            验证AI结果
            </summary>
        </member>
        <member name="M:EventProcessor.Core.Engine.EventStateAggregator.ProcessSuccessfulAIResult(EventProcessor.Core.Models.AIResultMessage)">
            <summary>
            处理成功的AI结果
            </summary>
        </member>
        <member name="M:EventProcessor.Core.Engine.EventStateAggregator.ProcessFailedAIResult(EventProcessor.Core.Models.AIResultMessage)">
            <summary>
            处理失败的AI结果
            </summary>
        </member>
        <member name="M:EventProcessor.Core.Engine.EventStateAggregator.CreateErrorAIState(System.String)">
            <summary>
            创建错误的AI状态
            </summary>
        </member>
        <member name="M:EventProcessor.Core.Engine.EventStateAggregator.EvaluateAndDecide">
            <summary>
            核心决策评估逻辑 - 支持告警静默期机制
            </summary>
        </member>
        <member name="M:EventProcessor.Core.Engine.EventStateAggregator.EvaluateAndDecideOnTimeout">
            <summary>
            V4.1修复：专门的超时决策方法 - 只在HoldingTimerCallback中调用
            </summary>
        </member>
        <member name="M:EventProcessor.Core.Engine.EventStateAggregator.TransitionToCompleted(EventProcessor.Core.Models.EventCompletionReason)">
            <summary>
            V4.1修复：事件完成状态转换
            </summary>
        </member>
        <member name="M:EventProcessor.Core.Engine.EventStateAggregator.StartGracePeriodTimer">
            <summary>
            启动告警静默期定时器
            </summary>
        </member>
        <member name="M:EventProcessor.Core.Engine.EventStateAggregator.GracePeriodCallback(System.Object)">
            <summary>
            静默期定时器回调
            </summary>
        </member>
        <member name="M:EventProcessor.Core.Engine.EventStateAggregator.ShouldBeExcluded">
            <summary>
            检查是否应该被排除 - 带熔断保护
            </summary>
        </member>
        <member name="M:EventProcessor.Core.Engine.EventStateAggregator.ShouldTriggerAlarm">
            <summary>
            检查是否应该触发告警
            </summary>
        </member>
        <member name="M:EventProcessor.Core.Engine.EventStateAggregator.ShouldAlarm_AI">
            <summary>
            AI模式告警判断 - 带熔断保护
            </summary>
        </member>
        <member name="M:EventProcessor.Core.Engine.EventStateAggregator.EvaluateAIRulesWithProtection">
            <summary>
            带保护的AI规则评估
            </summary>
        </member>
        <member name="M:EventProcessor.Core.Engine.EventStateAggregator.ShouldAlarm_BusinessOnly">
            <summary>
            业务逻辑模式告警判断 - 带熔断保护
            </summary>
        </member>
        <member name="M:EventProcessor.Core.Engine.EventStateAggregator.EvaluateBusinessRulesWithProtection">
            <summary>
            带保护的业务规则评估
            </summary>
        </member>
        <member name="M:EventProcessor.Core.Engine.EventStateAggregator.ShouldAlarm_AIAndBusiness">
            <summary>
            混合模式告警判断 - 带熔断保护
            </summary>
        </member>
        <member name="M:EventProcessor.Core.Engine.EventStateAggregator.RecordRuleEvaluationError(System.String,System.Exception)">
            <summary>
            记录规则评估错误
            </summary>
            <param name="ruleType">规则类型</param>
            <param name="exception">异常信息</param>
        </member>
        <member name="M:EventProcessor.Core.Engine.EventStateAggregator.GetCircuitBreakerStatistics">
            <summary>
            获取熔断器统计信息
            </summary>
            <returns>熔断器统计信息</returns>
        </member>
        <member name="M:EventProcessor.Core.Engine.EventStateAggregator.ResetCircuitBreaker(System.String)">
            <summary>
            重置熔断器
            </summary>
            <param name="ruleType">规则类型（可选，为空则重置所有）</param>
        </member>
        <member name="M:EventProcessor.Core.Engine.EventStateAggregator.SendAlarmMessage">
            <summary>
            发送告警消息
            </summary>
        </member>
        <member name="M:EventProcessor.Core.Engine.EventStateAggregator.SendAlarmCancellation(System.String)">
            <summary>
            发送告警撤销消息
            </summary>
        </member>
        <member name="M:EventProcessor.Core.Engine.EventStateAggregator.CreateEventContext">
            <summary>
            创建事件上下文
            </summary>
        </member>
        <member name="M:EventProcessor.Core.Engine.EventStateAggregator.GetExclusionReason(System.Collections.Generic.Dictionary{System.String,System.Object})">
            <summary>
            获取排除原因
            </summary>
        </member>
        <member name="M:EventProcessor.Core.Engine.EventStateAggregator.CancelAllTimers">
            <summary>
            取消所有定时器
            </summary>
        </member>
        <member name="M:EventProcessor.Core.Engine.EventStateAggregator.MarkAsCompleted">
            <summary>
            标记为完成
            </summary>
        </member>
        <member name="M:EventProcessor.Core.Engine.EventStateAggregator.GetTotalMessagesProcessed">
            <summary>
            获取处理的消息总数
            </summary>
        </member>
        <member name="M:EventProcessor.Core.Engine.EventStateAggregator.HandleEvaluationFailure(System.Exception)">
            <summary>
            处理评估失败
            </summary>
        </member>
        <member name="M:EventProcessor.Core.Engine.EventStateAggregator.ForceCleanup">
            <summary>
            强制清理
            </summary>
        </member>
        <member name="M:EventProcessor.Core.Engine.EventStateAggregator.Dispose">
            <summary>
            释放事件状态聚合器资源
            </summary>
        </member>
        <member name="M:EventProcessor.Core.Engine.EventStateAggregator.Dispose(System.Boolean)">
            <summary>
            释放资源的具体实现
            </summary>
            <param name="disposing">是否释放托管资源</param>
        </member>
        <member name="T:EventProcessor.Core.Engine.RuleEngine">
            <summary>
            规则引擎 - 支持嵌套条件组评估和三种规则类型
            </summary>
        </member>
        <member name="M:EventProcessor.Core.Engine.RuleEngine.#ctor(EventProcessor.Core.Engine.ConditionEvaluator,Microsoft.Extensions.Logging.ILogger{EventProcessor.Core.Engine.RuleEngine})">
            <summary>
            初始化规则引擎
            </summary>
            <param name="conditionEvaluator">条件评估器</param>
            <param name="logger">日志记录器</param>
        </member>
        <member name="M:EventProcessor.Core.Engine.RuleEngine.EvaluateExclusionRules(EventProcessor.Core.Models.ExclusionRuleGroup[],System.Collections.Generic.Dictionary{System.String,System.Object})">
            <summary>
            评估排除规则
            </summary>
            <param name="exclusionRules">排除规则列表</param>
            <param name="data">数据字典</param>
            <returns>是否应该排除</returns>
        </member>
        <member name="M:EventProcessor.Core.Engine.RuleEngine.EvaluateBusinessRules(EventProcessor.Core.Models.BusinessRuleGroup[],System.Collections.Generic.Dictionary{System.String,System.Object})">
            <summary>
            评估业务规则
            </summary>
            <param name="businessRules">业务规则列表</param>
            <param name="data">数据字典</param>
            <returns>是否满足业务条件</returns>
        </member>
        <member name="M:EventProcessor.Core.Engine.RuleEngine.EvaluateAIResultRules(EventProcessor.Core.Models.AIResultRuleGroup[],System.Collections.Generic.Dictionary{System.String,System.Object})">
            <summary>
            评估AI结果规则
            </summary>
            <param name="aiResultRules">AI结果规则列表</param>
            <param name="data">AI结果数据</param>
            <returns>是否满足AI条件</returns>
        </member>
        <member name="M:EventProcessor.Core.Engine.RuleEngine.EvaluateExclusionRuleGroup(EventProcessor.Core.Models.ExclusionRuleGroup,System.Collections.Generic.Dictionary{System.String,System.Object})">
            <summary>
            评估排除规则组
            </summary>
        </member>
        <member name="M:EventProcessor.Core.Engine.RuleEngine.EvaluateBusinessRuleGroup(EventProcessor.Core.Models.BusinessRuleGroup,System.Collections.Generic.Dictionary{System.String,System.Object})">
            <summary>
            评估业务规则组
            </summary>
        </member>
        <member name="M:EventProcessor.Core.Engine.RuleEngine.EvaluateAIResultRuleGroup(EventProcessor.Core.Models.AIResultRuleGroup,System.Collections.Generic.Dictionary{System.String,System.Object})">
            <summary>
            评估AI结果规则组
            </summary>
        </member>
        <member name="M:EventProcessor.Core.Engine.RuleEngine.ApplyLogicOperator(System.String,System.Collections.Generic.List{System.Boolean})">
            <summary>
            应用逻辑操作符
            </summary>
        </member>
        <member name="M:EventProcessor.Core.Engine.RuleEngine.GetMatchedConditions(System.Object,System.Collections.Generic.Dictionary{System.String,System.Object})">
            <summary>
            获取匹配的条件描述
            </summary>
            <param name="ruleGroup">规则组</param>
            <param name="data">数据字典</param>
            <returns>匹配的条件描述列表</returns>
        </member>
        <member name="M:EventProcessor.Core.Engine.RuleEngine.CollectMatchedConditions(EventProcessor.Core.Models.Condition[],EventProcessor.Core.Models.ConditionGroup[],System.Collections.Generic.Dictionary{System.String,System.Object},System.Collections.Generic.List{System.String})">
            <summary>
            收集匹配的条件描述
            </summary>
        </member>
        <member name="T:EventProcessor.Core.Engine.RuleEvaluationCircuitBreaker">
            <summary>
            规则评估熔断器 - 防止重复失败的规则导致系统级联故障
            </summary>
        </member>
        <member name="M:EventProcessor.Core.Engine.RuleEvaluationCircuitBreaker.#ctor(Microsoft.Extensions.Logging.ILogger{EventProcessor.Core.Engine.RuleEvaluationCircuitBreaker})">
            <summary>
            初始化规则评估熔断器
            </summary>
            <param name="logger">日志记录器</param>
        </member>
        <member name="M:EventProcessor.Core.Engine.RuleEvaluationCircuitBreaker.ExecuteAsync``1(System.String,System.Func{System.Threading.Tasks.Task{System.Boolean}},System.Boolean)">
            <summary>
            执行规则评估，带熔断保护
            </summary>
            <typeparam name="T">规则类型</typeparam>
            <param name="ruleKey">规则唯一标识</param>
            <param name="ruleEvaluationFunc">规则评估函数</param>
            <param name="fallbackResult">熔断时的回退结果</param>
            <returns>评估结果</returns>
        </member>
        <member name="M:EventProcessor.Core.Engine.RuleEvaluationCircuitBreaker.Execute``1(System.String,System.Func{System.Boolean},System.Boolean)">
            <summary>
            同步版本的规则评估执行
            </summary>
        </member>
        <member name="M:EventProcessor.Core.Engine.RuleEvaluationCircuitBreaker.GetStatistics">
            <summary>
            获取熔断器统计信息
            </summary>
            <returns>熔断器统计信息</returns>
        </member>
        <member name="M:EventProcessor.Core.Engine.RuleEvaluationCircuitBreaker.Reset(System.String)">
            <summary>
            重置指定规则的熔断器
            </summary>
            <param name="ruleKey">规则标识</param>
        </member>
        <member name="M:EventProcessor.Core.Engine.RuleEvaluationCircuitBreaker.ResetAll">
            <summary>
            重置所有熔断器
            </summary>
        </member>
        <member name="T:EventProcessor.Core.Engine.CircuitBreakerState">
            <summary>
            熔断器状态
            </summary>
        </member>
        <member name="T:EventProcessor.Core.Engine.CircuitBreakerStatistics">
            <summary>
            熔断器统计信息
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Engine.CircuitBreakerStatistics.RuleKey">
            <summary>
            规则标识键
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Engine.CircuitBreakerStatistics.IsOpen">
            <summary>
            是否处于打开状态
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Engine.CircuitBreakerStatistics.IsHalfOpen">
            <summary>
            是否处于半开状态
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Engine.CircuitBreakerStatistics.FailureCount">
            <summary>
            失败次数
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Engine.CircuitBreakerStatistics.SuccessCount">
            <summary>
            成功次数
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Engine.CircuitBreakerStatistics.LastFailureTime">
            <summary>
            最后失败时间
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Engine.CircuitBreakerStatistics.LastSuccessTime">
            <summary>
            最后成功时间
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Engine.CircuitBreakerStatistics.Status">
            <summary>
            熔断器状态描述
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Engine.CircuitBreakerStatistics.FailureRate">
            <summary>
            失败率
            </summary>
        </member>
        <member name="T:EventProcessor.Core.Models.FieldMapping">
            <summary>
            字段映射配置 - 支持动态字段引用
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Models.FieldMapping.AlarmFieldName">
            <summary>
            告警字段名称
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Models.FieldMapping.SourceRuleType">
            <summary>
            源规则类型：ExclusionRules、BusinessRules、AIResultRules、DeviceSignal
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Models.FieldMapping.SourceFieldName">
            <summary>
            源字段名称（支持多字段用逗号分隔）
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Models.FieldMapping.DefaultValue">
            <summary>
            默认值（字段不存在时使用）
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Models.FieldMapping.FormatTemplate">
            <summary>
            格式化模板（支持{fieldName}占位符）
            </summary>
        </member>
        <member name="T:EventProcessor.Core.Models.AlarmConfiguration">
            <summary>
            告警配置
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Models.AlarmConfiguration.Fields">
            <summary>
            字段映射列表
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Models.AlarmConfiguration.CustomTemplate">
            <summary>
            自定义告警模板
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Models.AlarmConfiguration.CustomAlarmTopic">
            <summary>
            自定义告警主题（可选）
            如果指定，则使用此主题发送告警消息
            如果未指定，则使用默认格式：{CompanyName}/{CommId}/{PositionId}/event
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Models.AlarmConfiguration.CustomAlarmCancellationTopic">
            <summary>
            自定义告警撤销主题（可选）
            如果指定，则使用此主题发送告警撤销消息
            如果未指定，则使用默认格式：{CompanyName}/{CommId}/{PositionId}/cancellation
            </summary>
        </member>
        <member name="T:EventProcessor.Core.Models.DeviceSignalConfiguration">
            <summary>
            设备信号配置
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Models.DeviceSignalConfiguration.Topics">
            <summary>
            MQTT主题列表
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Models.DeviceSignalConfiguration.TriggerField">
            <summary>
            触发字段名称
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Models.DeviceSignalConfiguration.TriggerValues">
            <summary>
            触发值映射（true/false对应的实际值）
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Models.DeviceSignalConfiguration.HoldingTimeoutSec">
            <summary>
            保持超时时间（秒）
            </summary>
        </member>
        <member name="T:EventProcessor.Core.Models.RuleConfiguration">
            <summary>
            规则配置
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Models.RuleConfiguration.ExclusionRules">
            <summary>
            排除规则列表
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Models.RuleConfiguration.BusinessRules">
            <summary>
            业务规则列表
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Models.RuleConfiguration.AIResultRules">
            <summary>
            AI结果规则列表
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Models.RuleConfiguration.AlarmConfig">
            <summary>
            告警配置
            </summary>
        </member>
        <member name="T:EventProcessor.Core.Models.Condition">
            <summary>
            条件定义 - 支持string、number、datetime三种数据类型
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Models.Condition.FieldName">
            <summary>
            字段名称
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Models.Condition.DataType">
            <summary>
            数据类型：string、number、datetime
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Models.Condition.Operator">
            <summary>
            操作符 - 根据数据类型确定可用操作符
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Models.Condition.Value">
            <summary>
            比较值
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Models.Condition.Description">
            <summary>
            条件描述（可选）
            </summary>
        </member>
        <member name="T:EventProcessor.Core.Models.ConditionGroup">
            <summary>
            条件组 - 支持嵌套条件逻辑
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Models.ConditionGroup.LogicOperator">
            <summary>
            逻辑操作符：AND、OR、NOT
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Models.ConditionGroup.Conditions">
            <summary>
            条件列表
            </summary>
        </member>
        <member name="T:EventProcessor.Core.Models.ExclusionRuleGroup">
            <summary>
            排除规则组
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Models.ExclusionRuleGroup.SourceType">
            <summary>
            数据源类型：MQTT、HTTP、Database
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Models.ExclusionRuleGroup.SourceTopic">
            <summary>
            数据源主题/地址
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Models.ExclusionRuleGroup.LogicOperator">
            <summary>
            逻辑操作符：AND、OR
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Models.ExclusionRuleGroup.ConditionGroups">
            <summary>
            嵌套条件组（最多2层嵌套）
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Models.ExclusionRuleGroup.Conditions">
            <summary>
            直接条件
            </summary>
        </member>
        <member name="T:EventProcessor.Core.Models.BusinessRuleGroup">
            <summary>
            业务规则组
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Models.BusinessRuleGroup.SourceTopic">
            <summary>
            数据源主题
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Models.BusinessRuleGroup.LogicOperator">
            <summary>
            逻辑操作符：AND、OR
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Models.BusinessRuleGroup.ConditionGroups">
            <summary>
            嵌套条件组（最多2层嵌套）
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Models.BusinessRuleGroup.Conditions">
            <summary>
            直接条件
            </summary>
        </member>
        <member name="T:EventProcessor.Core.Models.AIResultRuleGroup">
            <summary>
            AI结果规则组
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Models.AIResultRuleGroup.LogicOperator">
            <summary>
            逻辑操作符：AND、OR
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Models.AIResultRuleGroup.ConditionGroups">
            <summary>
            嵌套条件组（最多2层嵌套）
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Models.AIResultRuleGroup.Conditions">
            <summary>
            直接条件
            </summary>
        </member>
        <member name="T:EventProcessor.Core.Models.ConfigurationModel">
            <summary>
            统一配置模型 - EP_V4.1增强版本
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Models.ConfigurationModel.EventProcessor">
            <summary>
            事件处理器配置
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Models.ConfigurationModel.Mqtt">
            <summary>
            MQTT配置
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Models.ConfigurationModel.Logging">
            <summary>
            日志配置
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Models.ConfigurationModel.ErrorHandling">
            <summary>
            错误处理配置
            </summary>
        </member>
        <member name="T:EventProcessor.Core.Models.ErrorHandlingConfiguration">
            <summary>
            错误处理配置
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Models.ErrorHandlingConfiguration.ToleranceLevel">
            <summary>
            错误容忍级别：Strict、Normal、Lenient
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Models.ErrorHandlingConfiguration.RetryPolicy">
            <summary>
            重试策略
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Models.ErrorHandlingConfiguration.FallbackStrategy">
            <summary>
            降级策略
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Models.ErrorHandlingConfiguration.Logging">
            <summary>
            日志配置
            </summary>
        </member>
        <member name="T:EventProcessor.Core.Models.RetryPolicyConfiguration">
            <summary>
            重试策略配置
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Models.RetryPolicyConfiguration.MaxRetries">
            <summary>
            最大重试次数
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Models.RetryPolicyConfiguration.RetryDelay">
            <summary>
            重试延迟（毫秒）
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Models.RetryPolicyConfiguration.UseExponentialBackoff">
            <summary>
            是否使用指数退避
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Models.RetryPolicyConfiguration.MaxBackoffDelay">
            <summary>
            最大退避延迟（毫秒）
            </summary>
        </member>
        <member name="T:EventProcessor.Core.Models.FallbackStrategyConfiguration">
            <summary>
            降级策略配置
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Models.FallbackStrategyConfiguration.OnRuleFailure">
            <summary>
            规则失败时的策略：StopProcessing、ContinueProcessing
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Models.FallbackStrategyConfiguration.OnAIFailure">
            <summary>
            AI失败时的策略：SkipEvent、UseBusinessOnly、ForceAlarm
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Models.FallbackStrategyConfiguration.OnTimerFailure">
            <summary>
            定时器失败时的策略：ImmediateAlarm、SkipAlarm
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Models.FallbackStrategyConfiguration.OnMqttFailure">
            <summary>
            MQTT连接失败时的策略：Retry、Shutdown、ContinueOffline
            </summary>
        </member>
        <member name="T:EventProcessor.Core.Models.ErrorLoggingConfiguration">
            <summary>
            错误日志配置
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Models.ErrorLoggingConfiguration.ErrorLogLevel">
            <summary>
            错误日志级别：Debug、Information、Warning、Error、Critical
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Models.ErrorLoggingConfiguration.DetailedStackTrace">
            <summary>
            是否包含详细堆栈跟踪
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Models.ErrorLoggingConfiguration.IncludeMessagePayload">
            <summary>
            是否包含消息负载（生产环境建议false）
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Models.ErrorLoggingConfiguration.EnablePerformanceMonitoring">
            <summary>
            是否启用性能监控
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Models.ErrorLoggingConfiguration.ErrorRateThreshold">
            <summary>
            错误率阈值（超过此阈值时触发告警）
            </summary>
        </member>
        <member name="T:EventProcessor.Core.Models.PerformanceMonitoringConfiguration">
            <summary>
            性能监控配置
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Models.PerformanceMonitoringConfiguration.EnableSystemMonitoring">
            <summary>
            是否启用系统监控
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Models.PerformanceMonitoringConfiguration.MetricsRetentionMinutes">
            <summary>
            指标保留时间（分钟）
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Models.PerformanceMonitoringConfiguration.HealthCheckThresholds">
            <summary>
            健康检查阈值
            </summary>
        </member>
        <member name="T:EventProcessor.Core.Models.HealthCheckThresholds">
            <summary>
            健康检查阈值
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Models.HealthCheckThresholds.MaxRuleEvaluationMs">
            <summary>
            最大规则评估时间（毫秒）
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Models.HealthCheckThresholds.MaxAlarmGenerationMs">
            <summary>
            最大告警生成时间（毫秒）
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Models.HealthCheckThresholds.MaxConditionErrors">
            <summary>
            最大条件错误数
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Models.HealthCheckThresholds.MaxMemoryUsageMB">
            <summary>
            最大内存使用量（MB）
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Models.HealthCheckThresholds.MaxCpuUsagePercent">
            <summary>
            最大CPU使用率（百分比）
            </summary>
        </member>
        <member name="T:EventProcessor.Core.Models.EventConfiguration">
            <summary>
            事件配置 - EP_V4.1增强版本
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Models.EventConfiguration.EventId">
            <summary>
            事件ID
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Models.EventConfiguration.EventName">
            <summary>
            事件名称
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Models.EventConfiguration.EvaluationStrategy">
            <summary>
            评估策略：AI、BusinessOnly、AIAndBusiness
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Models.EventConfiguration.Priority">
            <summary>
            优先级：P1、P2、P3、P4、P5
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Models.EventConfiguration.CommId">
            <summary>
            小区ID
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Models.EventConfiguration.PositionId">
            <summary>
            位置ID
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Models.EventConfiguration.CompanyName">
            <summary>
            公司名称（用于告警主题）
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Models.EventConfiguration.AIPrompt">
            <summary>
            AI提示词（AI模式时必填）
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Models.EventConfiguration.AIAnalysisDelaySec">
            <summary>
            AI分析延迟时间（秒）
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Models.EventConfiguration.ImageCropCoordinates">
            <summary>
            图片裁剪坐标（格式：x1,y1,x2,y2）
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Models.EventConfiguration.DeviceSignal">
            <summary>
            设备信号配置
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Models.EventConfiguration.RuleConfiguration">
            <summary>
            规则配置
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Models.EventConfiguration.AlarmGracePeriodSeconds">
            <summary>
            告警静默期时长（秒），默认3秒
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Models.EventConfiguration.EnableAlarmCancellation">
            <summary>
            是否启用告警撤销功能，默认启用
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Models.EventConfiguration.CustomAlarmTopic">
            <summary>
            自定义告警主题（可选）
            如果未指定，则使用默认格式：alarm/{CommId}/{PositionId}/event
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Models.EventConfiguration.CustomAlarmCancellationTopic">
            <summary>
            自定义告警撤销主题（可选）
            如果未指定，则使用默认格式：alarm/{CommId}/{PositionId}/cancellation
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Models.EventConfiguration.CorrelationTimeWindow">
            <summary>
            时间窗口关联策略：minute(分钟)、hour(小时)、custom(自定义)
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Models.EventConfiguration.CustomTimeWindowMinutes">
            <summary>
            自定义时间窗口大小（分钟），仅当CorrelationTimeWindow="custom"时有效
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Models.EventConfiguration.AlarmConfiguration">
            <summary>
            告警配置
            </summary>
        </member>
        <member name="T:EventProcessor.Core.Models.EventContext">
            <summary>
            事件上下文 - 包含事件处理过程中的所有数据
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Models.EventContext.EventId">
            <summary>
            事件ID
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Models.EventContext.InstanceId">
            <summary>
            实例ID（关联ID）
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Models.EventContext.ExclusionData">
            <summary>
            排除数据
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Models.EventContext.BusinessData">
            <summary>
            业务数据
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Models.EventContext.AIResultData">
            <summary>
            AI结果数据
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Models.EventContext.DeviceData">
            <summary>
            设备数据
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Models.EventContext.BusinessRuleState">
            <summary>
            业务规则状态
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Models.EventContext.AIRuleState">
            <summary>
            AI规则状态
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Models.EventContext.CreatedAt">
            <summary>
            创建时间
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Models.EventContext.LastUpdatedAt">
            <summary>
            最后更新时间
            </summary>
        </member>
        <member name="T:EventProcessor.Core.Models.EventCompletedEventArgs">
            <summary>
            事件完成事件参数
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Models.EventCompletedEventArgs.CorrelationId">
            <summary>
            关联ID
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Models.EventCompletedEventArgs.FinalState">
            <summary>
            最终状态
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Models.EventCompletedEventArgs.ProcessingTime">
            <summary>
            处理时间
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Models.EventCompletedEventArgs.TotalMessages">
            <summary>
            处理的消息总数
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Models.EventCompletedEventArgs.CompletedAt">
            <summary>
            完成时间
            </summary>
        </member>
        <member name="T:EventProcessor.Core.Models.AlarmGeneratedEventArgs">
            <summary>
            告警生成事件参数
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Models.AlarmGeneratedEventArgs.CorrelationId">
            <summary>
            关联ID
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Models.AlarmGeneratedEventArgs.AlarmMessage">
            <summary>
            告警消息
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Models.AlarmGeneratedEventArgs.AlarmType">
            <summary>
            告警类型：Alarm、Cancellation
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Models.AlarmGeneratedEventArgs.GeneratedAt">
            <summary>
            生成时间
            </summary>
        </member>
        <member name="T:EventProcessor.Core.Models.StateChangedEventArgs">
            <summary>
            状态变更事件参数
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Models.StateChangedEventArgs.CorrelationId">
            <summary>
            关联ID
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Models.StateChangedEventArgs.PreviousState">
            <summary>
            之前的状态
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Models.StateChangedEventArgs.NewState">
            <summary>
            新状态
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Models.StateChangedEventArgs.Reason">
            <summary>
            状态变更原因
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Models.StateChangedEventArgs.ChangedAt">
            <summary>
            变更时间
            </summary>
        </member>
        <member name="T:EventProcessor.Core.Models.EventMessage">
            <summary>
            事件消息基类
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Models.EventMessage.MessageType">
            <summary>
            消息类型：DeviceSignal、ExclusionData、BusinessData、AIResult
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Models.EventMessage.Topic">
            <summary>
            MQTT主题
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Models.EventMessage.Payload">
            <summary>
            消息负载（JSON格式）
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Models.EventMessage.EventId">
            <summary>
            事件ID
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Models.EventMessage.CommId">
            <summary>
            小区ID
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Models.EventMessage.PositionId">
            <summary>
            位置ID
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Models.EventMessage.Timestamp">
            <summary>
            消息时间戳
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Models.EventMessage.MessageId">
            <summary>
            消息ID（用于去重）
            </summary>
        </member>
        <member name="T:EventProcessor.Core.Models.DeviceSignalMessage">
            <summary>
            设备信号消息
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Models.DeviceSignalMessage.DeviceId">
            <summary>
            设备ID
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Models.DeviceSignalMessage.TriggerValue">
            <summary>
            触发字段值
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Models.DeviceSignalMessage.ImagePath">
            <summary>
            图片路径
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Models.DeviceSignalMessage.DeviceData">
            <summary>
            设备状态数据
            </summary>
        </member>
        <member name="T:EventProcessor.Core.Models.AIResultMessage">
            <summary>
            AI结果消息
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Models.AIResultMessage.EventId">
            <summary>
            事件ID
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Models.AIResultMessage.RequestId">
            <summary>
            请求ID（用于关联）
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Models.AIResultMessage.Result">
            <summary>
            AI分析结果
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Models.AIResultMessage.Timestamp">
            <summary>
            结果时间戳
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Models.AIResultMessage.ProcessingTime">
            <summary>
            处理时间（秒）
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Models.AIResultMessage.Success">
            <summary>
            处理成功标志
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Models.AIResultMessage.ErrorMessage">
            <summary>
            错误信息（仅在Success=false时提供）
            </summary>
        </member>
        <member name="T:EventProcessor.Core.Models.StandardAlarmMessage">
            <summary>
            标准告警消息（符合接收方规范）
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Models.StandardAlarmMessage.EventId">
            <summary>
            事件ID
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Models.StandardAlarmMessage.CommId">
            <summary>
            小区编号
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Models.StandardAlarmMessage.PositionId">
            <summary>
            点位编号
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Models.StandardAlarmMessage.Data">
            <summary>
            详情数据字符串（最多6行，使用\r\n分隔）
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Models.StandardAlarmMessage.Urls">
            <summary>
            图片URL列表
            </summary>
        </member>
        <member name="T:EventProcessor.Core.Models.AlarmMessage">
            <summary>
            告警消息（内部使用，用于兼容现有代码）
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Models.AlarmMessage.EventId">
            <summary>
            事件ID
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Models.AlarmMessage.InstanceId">
            <summary>
            实例ID（关联ID）
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Models.AlarmMessage.Timestamp">
            <summary>
            告警时间戳
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Models.AlarmMessage.Fields">
            <summary>
            告警字段
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Models.AlarmMessage.Template">
            <summary>
            自定义模板
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Models.AlarmMessage.AlarmType">
            <summary>
            告警类型：Alarm、Cancellation
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Models.AlarmMessage.CancellationReason">
            <summary>
            撤销原因（仅在AlarmType=Cancellation时使用）
            </summary>
        </member>
        <member name="T:EventProcessor.Core.Models.EventProcessingState">
            <summary>
            事件处理状态
            </summary>
        </member>
        <member name="F:EventProcessor.Core.Models.EventProcessingState.Initializing">
            <summary>
            初始化状态 - 等待DeviceSignal到达
            </summary>
        </member>
        <member name="F:EventProcessor.Core.Models.EventProcessingState.Collecting">
            <summary>
            收集数据状态
            </summary>
        </member>
        <member name="F:EventProcessor.Core.Models.EventProcessingState.WaitingAIResult">
            <summary>
            等待AI分析结果
            </summary>
        </member>
        <member name="F:EventProcessor.Core.Models.EventProcessingState.PendingAlarm">
            <summary>
            告警静默期
            </summary>
        </member>
        <member name="F:EventProcessor.Core.Models.EventProcessingState.Alarmed">
            <summary>
            已告警
            </summary>
        </member>
        <member name="F:EventProcessor.Core.Models.EventProcessingState.Excluded">
            <summary>
            已排除
            </summary>
        </member>
        <member name="T:EventProcessor.Core.Models.EventCompletionReason">
            <summary>
            事件完成原因 - V4.1修复新增
            </summary>
        </member>
        <member name="F:EventProcessor.Core.Models.EventCompletionReason.ReleasedBeforeTimeout">
            <summary>超时前收到解除信号</summary>
        </member>
        <member name="F:EventProcessor.Core.Models.EventCompletionReason.Excluded">
            <summary>排除条件匹配</summary>
        </member>
        <member name="F:EventProcessor.Core.Models.EventCompletionReason.AlarmGenerated">
            <summary>告警已生成</summary>
        </member>
        <member name="F:EventProcessor.Core.Models.EventCompletionReason.NoMatchingRules">
            <summary>超时但无匹配规则</summary>
        </member>
        <member name="F:EventProcessor.Core.Models.EventCompletionReason.ProcessingError">
            <summary>处理异常</summary>
        </member>
        <member name="T:EventProcessor.Core.Models.RuleState">
            <summary>
            规则状态
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Models.RuleState.IsMatched">
            <summary>
            是否匹配
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Models.RuleState.LastUpdated">
            <summary>
            最后更新时间
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Models.RuleState.MatchedConditions">
            <summary>
            匹配的条件描述
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Models.RuleState.ErrorMessage">
            <summary>
            错误信息
            </summary>
        </member>
        <member name="T:EventProcessor.Core.Models.BusinessRuleState">
            <summary>
            业务规则状态
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Models.BusinessRuleState.BusinessData">
            <summary>
            业务数据
            </summary>
        </member>
        <member name="T:EventProcessor.Core.Models.AIRuleState">
            <summary>
            AI规则状态
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Models.AIRuleState.ProcessingTime">
            <summary>
            处理时间（秒）
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Models.AIRuleState.ResultData">
            <summary>
            AI结果数据
            </summary>
        </member>
        <member name="T:EventProcessor.Core.Models.LoggingConfiguration">
            <summary>
            日志配置
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Models.LoggingConfiguration.MinimumLevel">
            <summary>
            最小日志级别配置
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Models.LoggingConfiguration.WriteTo">
            <summary>
            写入器配置列表
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Models.LoggingConfiguration.OutputTemplate">
            <summary>
            日志输出模板
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Models.LoggingConfiguration.EnableStructuredLogging">
            <summary>
            是否启用结构化日志
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Models.LoggingConfiguration.MaxFileSizeMB">
            <summary>
            日志文件最大大小（MB）
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Models.LoggingConfiguration.RetainedFileCountLimit">
            <summary>
            日志文件保留天数
            </summary>
        </member>
        <member name="T:EventProcessor.Core.Models.SerilogMinimumLevel">
            <summary>
            Serilog最小日志级别配置
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Models.SerilogMinimumLevel.Default">
            <summary>
            默认日志级别
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Models.SerilogMinimumLevel.Override">
            <summary>
            特定命名空间的日志级别覆盖
            </summary>
        </member>
        <member name="T:EventProcessor.Core.Models.SerilogWriteTo">
            <summary>
            Serilog写入器配置
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Models.SerilogWriteTo.Name">
            <summary>
            写入器名称
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Models.SerilogWriteTo.Args">
            <summary>
            写入器参数
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Models.SerilogWriteTo.RestrictedToMinimumLevel">
            <summary>
            写入器的最小日志级别
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Models.SerilogWriteTo.OutputTemplate">
            <summary>
            输出模板
            </summary>
        </member>
        <member name="T:EventProcessor.Core.Models.MqttConfiguration">
            <summary>
            MQTT配置
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Models.MqttConfiguration.BrokerHost">
            <summary>
            MQTT服务器主机地址
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Models.MqttConfiguration.BrokerPort">
            <summary>
            MQTT服务器端口
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Models.MqttConfiguration.ClientId">
            <summary>
            客户端ID
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Models.MqttConfiguration.Username">
            <summary>
            用户名（可选）
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Models.MqttConfiguration.Password">
            <summary>
            密码（可选）
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Models.MqttConfiguration.ConnectionTimeout">
            <summary>
            连接超时时间（秒）
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Models.MqttConfiguration.KeepAliveInterval">
            <summary>
            保持连接间隔（秒）
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Models.MqttConfiguration.UseTls">
            <summary>
            是否启用TLS/SSL
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Models.MqttConfiguration.AutoReconnect">
            <summary>
            是否启用自动重连
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Models.MqttConfiguration.ReconnectInterval">
            <summary>
            重连间隔（秒）
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Models.MqttConfiguration.MaxReconnectAttempts">
            <summary>
            最大重连次数（0表示无限重连）
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Models.MqttConfiguration.QualityOfServiceLevel">
            <summary>
            消息质量等级（QoS）
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Models.MqttConfiguration.RetainMessages">
            <summary>
            是否保留消息
            </summary>
        </member>
        <member name="T:EventProcessor.Core.Services.AlarmGenerator">
            <summary>
            告警生成器实现 - 支持动态字段映射和多数据源组合
            </summary>
        </member>
        <member name="M:EventProcessor.Core.Services.AlarmGenerator.#ctor(Microsoft.Extensions.Logging.ILogger{EventProcessor.Core.Services.AlarmGenerator})">
            <summary>
            初始化告警生成器
            </summary>
            <param name="logger">日志记录器</param>
        </member>
        <member name="M:EventProcessor.Core.Services.AlarmGenerator.GenerateAlarmAsync(EventProcessor.Core.Models.EventContext,EventProcessor.Core.Models.AlarmConfiguration)">
            <summary>
            生成告警消息
            </summary>
            <param name="context">事件上下文</param>
            <param name="config">告警配置</param>
            <returns>告警消息</returns>
        </member>
        <member name="M:EventProcessor.Core.Services.AlarmGenerator.GenerateCancellationAsync(EventProcessor.Core.Models.EventContext,EventProcessor.Core.Models.AlarmConfiguration,System.String)">
            <summary>
            生成取消告警消息
            </summary>
            <param name="context">事件上下文</param>
            <param name="config">告警配置</param>
            <param name="reason">取消原因</param>
            <returns>取消告警消息</returns>
        </member>
        <member name="M:EventProcessor.Core.Services.AlarmGenerator.ValidateConfiguration(EventProcessor.Core.Models.AlarmConfiguration)">
            <summary>
            验证告警配置
            </summary>
            <param name="config">告警配置</param>
            <returns>验证结果</returns>
        </member>
        <member name="M:EventProcessor.Core.Services.AlarmGenerator.ExtractFieldValueAsync(EventProcessor.Core.Models.EventContext,EventProcessor.Core.Models.FieldMapping)">
            <summary>
            提取字段值
            </summary>
        </member>
        <member name="M:EventProcessor.Core.Services.AlarmGenerator.GetSourceData(EventProcessor.Core.Models.EventContext,System.String)">
            <summary>
            获取源数据
            </summary>
        </member>
        <member name="M:EventProcessor.Core.Services.AlarmGenerator.ProcessMultipleFields(System.Collections.Generic.Dictionary{System.String,System.Object},EventProcessor.Core.Models.FieldMapping)">
            <summary>
            处理多字段
            </summary>
        </member>
        <member name="M:EventProcessor.Core.Services.AlarmGenerator.ApplyFormatTemplate(System.Object,System.String)">
            <summary>
            应用格式化模板
            </summary>
        </member>
        <member name="M:EventProcessor.Core.Services.AlarmGenerator.ValidateFieldMapping(EventProcessor.Core.Models.FieldMapping,System.Collections.Generic.List{System.String},System.Collections.Generic.List{System.String})">
            <summary>
            验证字段映射
            </summary>
        </member>
        <member name="M:EventProcessor.Core.Services.AlarmGenerator.IsValidSourceRuleType(System.String)">
            <summary>
            验证源规则类型
            </summary>
        </member>
        <member name="M:EventProcessor.Core.Services.AlarmGenerator.ValidateFormatTemplate(System.String,System.String,System.Collections.Generic.List{System.String})">
            <summary>
            验证格式化模板
            </summary>
        </member>
        <member name="M:EventProcessor.Core.Services.AlarmGenerator.ValidateCustomTemplate(System.String,System.Collections.Generic.List{System.String},System.Collections.Generic.List{System.String})">
            <summary>
            验证自定义模板
            </summary>
        </member>
        <member name="T:EventProcessor.Core.Services.ConfigurationService">
            <summary>
            配置服务实现 - 支持JSON/YAML配置加载、验证和热重载
            </summary>
        </member>
        <member name="E:EventProcessor.Core.Services.ConfigurationService.ConfigurationChanged">
            <summary>
            配置变更事件
            </summary>
        </member>
        <member name="E:EventProcessor.Core.Services.ConfigurationService.ValidationFailed">
            <summary>
            配置验证失败事件
            </summary>
        </member>
        <member name="M:EventProcessor.Core.Services.ConfigurationService.#ctor(Microsoft.Extensions.Logging.ILogger{EventProcessor.Core.Services.ConfigurationService})">
            <summary>
            初始化配置服务
            </summary>
            <param name="logger">日志记录器</param>
        </member>
        <member name="P:EventProcessor.Core.Services.ConfigurationService.CurrentConfiguration">
            <summary>
            当前配置
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Services.ConfigurationService.IsValid">
            <summary>
            配置是否有效
            </summary>
        </member>
        <member name="M:EventProcessor.Core.Services.ConfigurationService.LoadConfigurationAsync(System.String,System.Threading.CancellationToken)">
            <summary>
            加载配置文件
            </summary>
            <param name="configurationPath">配置文件路径</param>
            <param name="cancellationToken">取消令牌</param>
        </member>
        <member name="M:EventProcessor.Core.Services.ConfigurationService.ReloadConfigurationAsync(System.Threading.CancellationToken)">
            <summary>
            重新加载配置
            </summary>
            <param name="cancellationToken">取消令牌</param>
        </member>
        <member name="M:EventProcessor.Core.Services.ConfigurationService.ValidateConfiguration(EventProcessor.Core.Models.EventConfiguration)">
            <summary>
            验证配置
            </summary>
            <param name="configuration">事件配置</param>
            <returns>验证结果</returns>
        </member>
        <member name="M:EventProcessor.Core.Services.ConfigurationService.ValidateStartupConfigurationAsync(System.String,System.Threading.CancellationToken)">
            <summary>
            启动时验证配置 - 如果有严重错误则快速失败
            </summary>
            <param name="configurationPath">配置文件路径</param>
            <param name="cancellationToken">取消令牌</param>
            <returns>验证任务</returns>
        </member>
        <member name="M:EventProcessor.Core.Services.ConfigurationService.IsCriticalError(System.String)">
            <summary>
            判断是否为严重错误（会阻止系统启动）
            </summary>
            <param name="error">错误信息</param>
            <returns>是否为严重错误</returns>
        </member>
        <member name="M:EventProcessor.Core.Services.ConfigurationService.StartMonitoringAsync(System.Threading.CancellationToken)">
            <summary>
            开始监控配置文件变化
            </summary>
            <param name="cancellationToken">取消令牌</param>
        </member>
        <member name="M:EventProcessor.Core.Services.ConfigurationService.StopMonitoringAsync(System.Threading.CancellationToken)">
            <summary>
            停止监控配置文件变化
            </summary>
            <param name="cancellationToken">取消令牌</param>
        </member>
        <member name="M:EventProcessor.Core.Services.ConfigurationService.GetStatistics">
            <summary>
            获取配置服务统计信息
            </summary>
            <returns>配置统计信息</returns>
        </member>
        <member name="M:EventProcessor.Core.Services.ConfigurationService.ParseConfigurationAsync(System.String,System.String)">
            <summary>
            解析配置文件
            </summary>
        </member>
        <member name="M:EventProcessor.Core.Services.ConfigurationService.ParseJsonConfiguration(System.String)">
            <summary>
            解析JSON配置
            </summary>
        </member>
        <member name="M:EventProcessor.Core.Services.ConfigurationService.ParseYamlConfiguration(System.String)">
            <summary>
            解析YAML配置
            </summary>
        </member>
        <member name="M:EventProcessor.Core.Services.ConfigurationService.DetermineConfigurationFormat(System.String)">
            <summary>
            确定配置文件格式
            </summary>
        </member>
        <member name="M:EventProcessor.Core.Services.ConfigurationService.ValidateBusinessLogic(EventProcessor.Core.Models.EventConfiguration,System.Collections.Generic.List{System.String},System.Collections.Generic.List{System.String})">
            <summary>
            验证业务逻辑
            </summary>
        </member>
        <member name="M:EventProcessor.Core.Services.ConfigurationService.OnConfigurationFileChanged(System.Object,System.IO.FileSystemEventArgs)">
            <summary>
            配置文件变更事件处理
            </summary>
        </member>
        <member name="M:EventProcessor.Core.Services.ConfigurationService.DelayedReload(System.Object)">
            <summary>
            延迟重新加载
            </summary>
        </member>
        <member name="M:EventProcessor.Core.Services.ConfigurationService.Dispose">
            <summary>
            释放配置服务资源
            </summary>
        </member>
        <member name="M:EventProcessor.Core.Services.ConfigurationService.Dispose(System.Boolean)">
            <summary>
            释放资源的具体实现
            </summary>
            <param name="disposing">是否释放托管资源</param>
        </member>
        <member name="T:EventProcessor.Core.Services.ErrorHandlingService">
            <summary>
            错误处理服务实现
            </summary>
        </member>
        <member name="E:EventProcessor.Core.Services.ErrorHandlingService.ErrorOccurred">
            <summary>
            错误发生事件
            </summary>
        </member>
        <member name="E:EventProcessor.Core.Services.ErrorHandlingService.ErrorRecovered">
            <summary>
            错误恢复事件
            </summary>
        </member>
        <member name="M:EventProcessor.Core.Services.ErrorHandlingService.#ctor(Microsoft.Extensions.Options.IOptions{EventProcessor.Core.Models.ErrorHandlingConfiguration},Microsoft.Extensions.Logging.ILogger{EventProcessor.Core.Services.ErrorHandlingService})">
            <summary>
            初始化错误处理服务
            </summary>
            <param name="config">错误处理配置</param>
            <param name="logger">日志记录器</param>
        </member>
        <member name="P:EventProcessor.Core.Services.ErrorHandlingService.Configuration">
            <summary>
            错误处理配置
            </summary>
        </member>
        <member name="M:EventProcessor.Core.Services.ErrorHandlingService.HandleExceptionAsync(System.Exception,EventProcessor.Core.Services.ErrorContext)">
            <summary>
            处理异常
            </summary>
            <param name="exception">异常</param>
            <param name="context">错误上下文</param>
            <returns>错误处理结果</returns>
        </member>
        <member name="M:EventProcessor.Core.Services.ErrorHandlingService.ExecuteWithRetryAsync``1(System.Func{System.Threading.Tasks.Task{``0}},EventProcessor.Core.Services.ErrorContext)">
            <summary>
            带重试的执行操作（泛型版本）
            </summary>
            <typeparam name="T">返回类型</typeparam>
            <param name="operation">操作</param>
            <param name="context">错误上下文</param>
            <returns>操作结果</returns>
        </member>
        <member name="M:EventProcessor.Core.Services.ErrorHandlingService.ExecuteWithRetryAsync(System.Func{System.Threading.Tasks.Task},EventProcessor.Core.Services.ErrorContext)">
            <summary>
            带重试的执行操作（无返回值版本）
            </summary>
            <param name="operation">操作</param>
            <param name="context">错误上下文</param>
        </member>
        <member name="M:EventProcessor.Core.Services.ErrorHandlingService.ShouldDegrade(System.String,System.Int32)">
            <summary>
            判断是否应该降级
            </summary>
            <param name="errorType">错误类型</param>
            <param name="errorCount">错误数量</param>
            <returns>是否应该降级</returns>
        </member>
        <member name="M:EventProcessor.Core.Services.ErrorHandlingService.GetErrorStatistics">
            <summary>
            获取错误统计信息
            </summary>
            <returns>错误统计信息</returns>
        </member>
        <member name="M:EventProcessor.Core.Services.ErrorHandlingService.ResetErrorStatistics">
            <summary>
            重置错误统计
            </summary>
        </member>
        <member name="M:EventProcessor.Core.Services.ErrorHandlingService.UpdateErrorStatistics(System.Exception,EventProcessor.Core.Services.ErrorContext)">
            <summary>
            更新错误统计信息
            </summary>
        </member>
        <member name="M:EventProcessor.Core.Services.ErrorHandlingService.GetErrorType(System.Exception)">
            <summary>
            获取错误类型
            </summary>
        </member>
        <member name="M:EventProcessor.Core.Services.ErrorHandlingService.GetErrorCount(System.String)">
            <summary>
            获取错误计数
            </summary>
        </member>
        <member name="M:EventProcessor.Core.Services.ErrorHandlingService.ShouldRetry(System.Exception,EventProcessor.Core.Services.ErrorContext)">
            <summary>
            检查是否应该重试
            </summary>
        </member>
        <member name="M:EventProcessor.Core.Services.ErrorHandlingService.CalculateRetryDelay(EventProcessor.Core.Services.ErrorContext,System.Int32)">
            <summary>
            计算重试延迟
            </summary>
        </member>
        <member name="M:EventProcessor.Core.Services.ErrorHandlingService.GetDegradationStrategy(System.String)">
            <summary>
            获取降级策略
            </summary>
        </member>
        <member name="M:EventProcessor.Core.Services.ErrorHandlingService.GetErrorMessage(System.Exception,EventProcessor.Core.Services.ErrorContext)">
            <summary>
            获取错误消息
            </summary>
        </member>
        <member name="M:EventProcessor.Core.Services.ErrorHandlingService.LogError(System.Exception,EventProcessor.Core.Services.ErrorContext,EventProcessor.Core.Services.ErrorHandlingResult)">
            <summary>
            记录错误日志
            </summary>
        </member>
        <member name="M:EventProcessor.Core.Services.ErrorHandlingService.GetLogLevel(System.Exception)">
            <summary>
            获取日志级别
            </summary>
        </member>
        <member name="M:EventProcessor.Core.Services.ErrorHandlingService.TriggerRecoveryEvent(EventProcessor.Core.Services.ErrorContext,System.Exception)">
            <summary>
            触发恢复事件
            </summary>
        </member>
        <member name="M:EventProcessor.Core.Services.ErrorHandlingService.CleanupRecentErrors">
            <summary>
            清理过期的错误记录
            </summary>
        </member>
        <member name="M:EventProcessor.Core.Services.ErrorHandlingService.CalculateErrorRate">
            <summary>
            计算错误率
            </summary>
        </member>
        <member name="T:EventProcessor.Core.Services.IAlarmGenerator">
            <summary>
            告警生成器接口
            </summary>
        </member>
        <member name="M:EventProcessor.Core.Services.IAlarmGenerator.GenerateAlarmAsync(EventProcessor.Core.Models.EventContext,EventProcessor.Core.Models.AlarmConfiguration)">
            <summary>
            生成告警消息
            </summary>
            <param name="context">事件上下文</param>
            <param name="config">告警配置</param>
            <returns>告警消息</returns>
        </member>
        <member name="M:EventProcessor.Core.Services.IAlarmGenerator.GenerateCancellationAsync(EventProcessor.Core.Models.EventContext,EventProcessor.Core.Models.AlarmConfiguration,System.String)">
            <summary>
            生成告警撤销消息
            </summary>
            <param name="context">事件上下文</param>
            <param name="config">告警配置</param>
            <param name="reason">撤销原因</param>
            <returns>告警撤销消息</returns>
        </member>
        <member name="M:EventProcessor.Core.Services.IAlarmGenerator.ValidateConfiguration(EventProcessor.Core.Models.AlarmConfiguration)">
            <summary>
            验证告警配置
            </summary>
            <param name="config">告警配置</param>
            <returns>验证结果</returns>
        </member>
        <member name="T:EventProcessor.Core.Services.ValidationResult">
            <summary>
            验证结果
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Services.ValidationResult.IsValid">
            <summary>
            是否有效
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Services.ValidationResult.Errors">
            <summary>
            错误消息列表
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Services.ValidationResult.Warnings">
            <summary>
            警告消息列表
            </summary>
        </member>
        <member name="T:EventProcessor.Core.Services.IConfigurationService">
            <summary>
            配置服务接口
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Services.IConfigurationService.CurrentConfiguration">
            <summary>
            当前事件配置
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Services.IConfigurationService.IsValid">
            <summary>
            配置是否有效
            </summary>
        </member>
        <member name="E:EventProcessor.Core.Services.IConfigurationService.ConfigurationChanged">
            <summary>
            配置变更事件
            </summary>
        </member>
        <member name="E:EventProcessor.Core.Services.IConfigurationService.ValidationFailed">
            <summary>
            配置验证失败事件
            </summary>
        </member>
        <member name="M:EventProcessor.Core.Services.IConfigurationService.LoadConfigurationAsync(System.String,System.Threading.CancellationToken)">
            <summary>
            加载配置
            </summary>
            <param name="configurationPath">配置文件路径</param>
            <param name="cancellationToken">取消令牌</param>
            <returns>加载任务</returns>
        </member>
        <member name="M:EventProcessor.Core.Services.IConfigurationService.ReloadConfigurationAsync(System.Threading.CancellationToken)">
            <summary>
            重新加载配置
            </summary>
            <param name="cancellationToken">取消令牌</param>
            <returns>重新加载任务</returns>
        </member>
        <member name="M:EventProcessor.Core.Services.IConfigurationService.ValidateConfiguration(EventProcessor.Core.Models.EventConfiguration)">
            <summary>
            验证配置
            </summary>
            <param name="configuration">事件配置</param>
            <returns>验证结果</returns>
        </member>
        <member name="M:EventProcessor.Core.Services.IConfigurationService.ValidateStartupConfigurationAsync(System.String,System.Threading.CancellationToken)">
            <summary>
            启动时验证配置 - 如果有严重错误则快速失败
            </summary>
            <param name="configurationPath">配置文件路径</param>
            <param name="cancellationToken">取消令牌</param>
            <returns>验证任务</returns>
        </member>
        <member name="M:EventProcessor.Core.Services.IConfigurationService.StartMonitoringAsync(System.Threading.CancellationToken)">
            <summary>
            启动配置监控
            </summary>
            <param name="cancellationToken">取消令牌</param>
            <returns>启动任务</returns>
        </member>
        <member name="M:EventProcessor.Core.Services.IConfigurationService.StopMonitoringAsync(System.Threading.CancellationToken)">
            <summary>
            停止配置监控
            </summary>
            <param name="cancellationToken">取消令牌</param>
            <returns>停止任务</returns>
        </member>
        <member name="M:EventProcessor.Core.Services.IConfigurationService.GetStatistics">
            <summary>
            获取配置统计信息
            </summary>
            <returns>配置统计信息</returns>
        </member>
        <member name="T:EventProcessor.Core.Services.ConfigurationChangedEventArgs">
            <summary>
            配置变更事件参数
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Services.ConfigurationChangedEventArgs.OldConfiguration">
            <summary>
            旧配置
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Services.ConfigurationChangedEventArgs.NewConfiguration">
            <summary>
            新配置
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Services.ConfigurationChangedEventArgs.ChangeReason">
            <summary>
            变更原因
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Services.ConfigurationChangedEventArgs.ChangedAt">
            <summary>
            变更时间
            </summary>
        </member>
        <member name="T:EventProcessor.Core.Services.ConfigurationValidationFailedEventArgs">
            <summary>
            配置验证失败事件参数
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Services.ConfigurationValidationFailedEventArgs.ConfigurationPath">
            <summary>
            配置文件路径
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Services.ConfigurationValidationFailedEventArgs.Errors">
            <summary>
            错误信息
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Services.ConfigurationValidationFailedEventArgs.Exception">
            <summary>
            异常信息
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Services.ConfigurationValidationFailedEventArgs.FailedAt">
            <summary>
            失败时间
            </summary>
        </member>
        <member name="T:EventProcessor.Core.Services.ConfigurationStatistics">
            <summary>
            配置统计信息
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Services.ConfigurationStatistics.ConfigurationPath">
            <summary>
            配置文件路径
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Services.ConfigurationStatistics.LoadedAt">
            <summary>
            配置加载时间
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Services.ConfigurationStatistics.LastModifiedAt">
            <summary>
            最后修改时间
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Services.ConfigurationStatistics.FileSize">
            <summary>
            配置文件大小（字节）
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Services.ConfigurationStatistics.ReloadCount">
            <summary>
            重新加载次数
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Services.ConfigurationStatistics.ValidationFailureCount">
            <summary>
            验证失败次数
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Services.ConfigurationStatistics.IsMonitoring">
            <summary>
            是否正在监控
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Services.ConfigurationStatistics.ConfigurationFormat">
            <summary>
            配置格式：JSON、YAML
            </summary>
        </member>
        <member name="T:EventProcessor.Core.Services.IErrorHandlingService">
            <summary>
            错误处理服务接口
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Services.IErrorHandlingService.Configuration">
            <summary>
            错误处理配置
            </summary>
        </member>
        <member name="E:EventProcessor.Core.Services.IErrorHandlingService.ErrorOccurred">
            <summary>
            错误发生事件
            </summary>
        </member>
        <member name="E:EventProcessor.Core.Services.IErrorHandlingService.ErrorRecovered">
            <summary>
            错误恢复事件
            </summary>
        </member>
        <member name="M:EventProcessor.Core.Services.IErrorHandlingService.HandleExceptionAsync(System.Exception,EventProcessor.Core.Services.ErrorContext)">
            <summary>
            处理异常
            </summary>
            <param name="exception">异常</param>
            <param name="context">错误上下文</param>
            <returns>处理结果</returns>
        </member>
        <member name="M:EventProcessor.Core.Services.IErrorHandlingService.ExecuteWithRetryAsync``1(System.Func{System.Threading.Tasks.Task{``0}},EventProcessor.Core.Services.ErrorContext)">
            <summary>
            执行带重试的操作
            </summary>
            <typeparam name="T">返回类型</typeparam>
            <param name="operation">操作</param>
            <param name="context">错误上下文</param>
            <returns>操作结果</returns>
        </member>
        <member name="M:EventProcessor.Core.Services.IErrorHandlingService.ExecuteWithRetryAsync(System.Func{System.Threading.Tasks.Task},EventProcessor.Core.Services.ErrorContext)">
            <summary>
            执行带重试的操作（无返回值）
            </summary>
            <param name="operation">操作</param>
            <param name="context">错误上下文</param>
            <returns>执行任务</returns>
        </member>
        <member name="M:EventProcessor.Core.Services.IErrorHandlingService.ShouldDegrade(System.String,System.Int32)">
            <summary>
            检查是否应该降级
            </summary>
            <param name="errorType">错误类型</param>
            <param name="errorCount">错误次数</param>
            <returns>是否应该降级</returns>
        </member>
        <member name="M:EventProcessor.Core.Services.IErrorHandlingService.GetErrorStatistics">
            <summary>
            获取错误统计信息
            </summary>
            <returns>错误统计信息</returns>
        </member>
        <member name="M:EventProcessor.Core.Services.IErrorHandlingService.ResetErrorStatistics">
            <summary>
            重置错误统计
            </summary>
        </member>
        <member name="T:EventProcessor.Core.Services.ErrorContext">
            <summary>
            错误上下文
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Services.ErrorContext.OperationName">
            <summary>
            操作名称
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Services.ErrorContext.ComponentName">
            <summary>
            组件名称
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Services.ErrorContext.EventId">
            <summary>
            事件ID（可选）
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Services.ErrorContext.CorrelationId">
            <summary>
            关联ID（可选）
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Services.ErrorContext.AdditionalData">
            <summary>
            附加数据
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Services.ErrorContext.OccurredAt">
            <summary>
            错误发生时间
            </summary>
        </member>
        <member name="T:EventProcessor.Core.Services.ErrorHandlingResult">
            <summary>
            错误处理结果
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Services.ErrorHandlingResult.IsHandled">
            <summary>
            是否成功处理
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Services.ErrorHandlingResult.ShouldRetry">
            <summary>
            是否应该重试
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Services.ErrorHandlingResult.ShouldDegrade">
            <summary>
            是否应该降级
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Services.ErrorHandlingResult.DegradationStrategy">
            <summary>
            降级策略
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Services.ErrorHandlingResult.Message">
            <summary>
            处理消息
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Services.ErrorHandlingResult.RetryDelayMs">
            <summary>
            重试延迟（毫秒）
            </summary>
        </member>
        <member name="T:EventProcessor.Core.Services.ErrorOccurredEventArgs">
            <summary>
            错误发生事件参数
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Services.ErrorOccurredEventArgs.Exception">
            <summary>
            异常
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Services.ErrorOccurredEventArgs.Context">
            <summary>
            错误上下文
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Services.ErrorOccurredEventArgs.Result">
            <summary>
            处理结果
            </summary>
        </member>
        <member name="T:EventProcessor.Core.Services.ErrorRecoveredEventArgs">
            <summary>
            错误恢复事件参数
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Services.ErrorRecoveredEventArgs.Context">
            <summary>
            错误上下文
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Services.ErrorRecoveredEventArgs.RecoveredAt">
            <summary>
            恢复时间
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Services.ErrorRecoveredEventArgs.ErrorDuration">
            <summary>
            错误持续时间
            </summary>
        </member>
        <member name="T:EventProcessor.Core.Services.ErrorStatistics">
            <summary>
            错误统计信息
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Services.ErrorStatistics.TotalErrors">
            <summary>
            总错误数
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Services.ErrorStatistics.ErrorsByType">
            <summary>
            按类型分组的错误数
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Services.ErrorStatistics.ErrorsByComponent">
            <summary>
            按组件分组的错误数
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Services.ErrorStatistics.TotalRetries">
            <summary>
            重试次数
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Services.ErrorStatistics.TotalDegradations">
            <summary>
            降级次数
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Services.ErrorStatistics.LastErrorAt">
            <summary>
            最后错误时间
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Services.ErrorStatistics.ErrorRatePerMinute">
            <summary>
            错误率（每分钟）
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Services.ErrorStatistics.StatisticsWindow">
            <summary>
            统计时间窗口
            </summary>
        </member>
        <member name="T:EventProcessor.Core.Services.IMqttService">
            <summary>
            MQTT服务接口
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Services.IMqttService.IsConnected">
            <summary>
            连接状态
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Services.IMqttService.ClientId">
            <summary>
            客户端ID
            </summary>
        </member>
        <member name="E:EventProcessor.Core.Services.IMqttService.Connected">
            <summary>
            连接事件
            </summary>
        </member>
        <member name="E:EventProcessor.Core.Services.IMqttService.Disconnected">
            <summary>
            断开连接事件
            </summary>
        </member>
        <member name="E:EventProcessor.Core.Services.IMqttService.MessageReceived">
            <summary>
            消息接收事件
            </summary>
        </member>
        <member name="M:EventProcessor.Core.Services.IMqttService.StartAsync(System.Threading.CancellationToken)">
            <summary>
            启动MQTT服务
            </summary>
            <param name="cancellationToken">取消令牌</param>
            <returns>启动任务</returns>
        </member>
        <member name="M:EventProcessor.Core.Services.IMqttService.StopAsync(System.Threading.CancellationToken)">
            <summary>
            停止MQTT服务
            </summary>
            <param name="cancellationToken">取消令牌</param>
            <returns>停止任务</returns>
        </member>
        <member name="M:EventProcessor.Core.Services.IMqttService.SubscribeAsync(System.String,System.Int32)">
            <summary>
            订阅主题
            </summary>
            <param name="topic">主题</param>
            <param name="qos">服务质量等级</param>
            <returns>订阅任务</returns>
        </member>
        <member name="M:EventProcessor.Core.Services.IMqttService.SubscribeAsync(System.String[],System.Int32)">
            <summary>
            订阅多个主题
            </summary>
            <param name="topics">主题列表</param>
            <param name="qos">服务质量等级</param>
            <returns>订阅任务</returns>
        </member>
        <member name="M:EventProcessor.Core.Services.IMqttService.UnsubscribeAsync(System.String)">
            <summary>
            取消订阅主题
            </summary>
            <param name="topic">主题</param>
            <returns>取消订阅任务</returns>
        </member>
        <member name="M:EventProcessor.Core.Services.IMqttService.PublishAsync(System.String,System.String,System.Int32,System.Boolean)">
            <summary>
            发布消息
            </summary>
            <param name="topic">主题</param>
            <param name="payload">消息负载</param>
            <param name="qos">服务质量等级</param>
            <param name="retain">是否保留消息</param>
            <returns>发布任务</returns>
        </member>
        <member name="M:EventProcessor.Core.Services.IMqttService.PublishAsync(System.String,System.Byte[],System.Int32,System.Boolean)">
            <summary>
            发布消息（字节数组）
            </summary>
            <param name="topic">主题</param>
            <param name="payload">消息负载</param>
            <param name="qos">服务质量等级</param>
            <param name="retain">是否保留消息</param>
            <returns>发布任务</returns>
        </member>
        <member name="M:EventProcessor.Core.Services.IMqttService.GetConnectionStatistics">
            <summary>
            获取连接统计信息
            </summary>
            <returns>连接统计信息</returns>
        </member>
        <member name="T:EventProcessor.Core.Services.MqttConnectedEventArgs">
            <summary>
            MQTT连接事件参数
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Services.MqttConnectedEventArgs.ClientId">
            <summary>
            客户端ID
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Services.MqttConnectedEventArgs.ServerAddress">
            <summary>
            服务器地址
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Services.MqttConnectedEventArgs.ConnectedAt">
            <summary>
            连接时间
            </summary>
        </member>
        <member name="T:EventProcessor.Core.Services.MqttDisconnectedEventArgs">
            <summary>
            MQTT断开连接事件参数
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Services.MqttDisconnectedEventArgs.ClientId">
            <summary>
            客户端ID
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Services.MqttDisconnectedEventArgs.Reason">
            <summary>
            断开原因
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Services.MqttDisconnectedEventArgs.IsException">
            <summary>
            是否为异常断开
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Services.MqttDisconnectedEventArgs.DisconnectedAt">
            <summary>
            断开时间
            </summary>
        </member>
        <member name="T:EventProcessor.Core.Services.MqttMessageReceivedEventArgs">
            <summary>
            MQTT消息接收事件参数
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Services.MqttMessageReceivedEventArgs.Topic">
            <summary>
            主题
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Services.MqttMessageReceivedEventArgs.Payload">
            <summary>
            消息负载
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Services.MqttMessageReceivedEventArgs.QualityOfServiceLevel">
            <summary>
            服务质量等级
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Services.MqttMessageReceivedEventArgs.Retain">
            <summary>
            是否保留消息
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Services.MqttMessageReceivedEventArgs.ReceivedAt">
            <summary>
            接收时间
            </summary>
        </member>
        <member name="T:EventProcessor.Core.Services.MqttConnectionStatistics">
            <summary>
            MQTT连接统计信息
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Services.MqttConnectionStatistics.IsConnected">
            <summary>
            是否已连接
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Services.MqttConnectionStatistics.ConnectedAt">
            <summary>
            连接时间
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Services.MqttConnectionStatistics.ReconnectCount">
            <summary>
            重连次数
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Services.MqttConnectionStatistics.MessagesSent">
            <summary>
            发送的消息数
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Services.MqttConnectionStatistics.MessagesReceived">
            <summary>
            接收的消息数
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Services.MqttConnectionStatistics.SubscribedTopicCount">
            <summary>
            订阅的主题数
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Services.MqttConnectionStatistics.LastActivityAt">
            <summary>
            最后活动时间
            </summary>
        </member>
        <member name="T:EventProcessor.Core.Services.IPerformanceMonitoringService">
            <summary>
            性能监控服务接口
            </summary>
        </member>
        <member name="E:EventProcessor.Core.Services.IPerformanceMonitoringService.MetricsUpdated">
            <summary>
            性能指标更新事件
            </summary>
        </member>
        <member name="E:EventProcessor.Core.Services.IPerformanceMonitoringService.ThresholdExceeded">
            <summary>
            性能阈值超出事件
            </summary>
        </member>
        <member name="M:EventProcessor.Core.Services.IPerformanceMonitoringService.StartOperation(System.String,System.String)">
            <summary>
            开始操作计时
            </summary>
            <param name="operationName">操作名称</param>
            <param name="componentName">组件名称</param>
            <returns>操作上下文</returns>
        </member>
        <member name="M:EventProcessor.Core.Services.IPerformanceMonitoringService.CompleteOperation(EventProcessor.Core.Services.IOperationContext)">
            <summary>
            记录操作完成
            </summary>
            <param name="context">操作上下文</param>
        </member>
        <member name="M:EventProcessor.Core.Services.IPerformanceMonitoringService.FailOperation(EventProcessor.Core.Services.IOperationContext,System.Exception)">
            <summary>
            记录操作失败
            </summary>
            <param name="context">操作上下文</param>
            <param name="exception">异常</param>
        </member>
        <member name="M:EventProcessor.Core.Services.IPerformanceMonitoringService.RecordMetric(System.String,System.Double,System.Collections.Generic.Dictionary{System.String,System.String})">
            <summary>
            记录自定义指标
            </summary>
            <param name="metricName">指标名称</param>
            <param name="value">指标值</param>
            <param name="tags">标签</param>
        </member>
        <member name="M:EventProcessor.Core.Services.IPerformanceMonitoringService.IncrementCounter(System.String,System.Int64,System.Collections.Generic.Dictionary{System.String,System.String})">
            <summary>
            增加计数器
            </summary>
            <param name="counterName">计数器名称</param>
            <param name="increment">增量</param>
            <param name="tags">标签</param>
        </member>
        <member name="M:EventProcessor.Core.Services.IPerformanceMonitoringService.GetMetrics">
            <summary>
            获取性能指标
            </summary>
            <returns>性能指标</returns>
        </member>
        <member name="M:EventProcessor.Core.Services.IPerformanceMonitoringService.GetHealthStatus">
            <summary>
            获取系统健康状态
            </summary>
            <returns>健康状态</returns>
        </member>
        <member name="M:EventProcessor.Core.Services.IPerformanceMonitoringService.ResetMetrics">
            <summary>
            重置指标
            </summary>
        </member>
        <member name="M:EventProcessor.Core.Services.IPerformanceMonitoringService.StartAsync(System.Threading.CancellationToken)">
            <summary>
            启动监控
            </summary>
            <param name="cancellationToken">取消令牌</param>
            <returns>启动任务</returns>
        </member>
        <member name="M:EventProcessor.Core.Services.IPerformanceMonitoringService.StopAsync(System.Threading.CancellationToken)">
            <summary>
            停止监控
            </summary>
            <param name="cancellationToken">取消令牌</param>
            <returns>停止任务</returns>
        </member>
        <member name="T:EventProcessor.Core.Services.IOperationContext">
            <summary>
            操作上下文接口
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Services.IOperationContext.OperationName">
            <summary>
            操作名称
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Services.IOperationContext.ComponentName">
            <summary>
            组件名称
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Services.IOperationContext.StartTime">
            <summary>
            开始时间
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Services.IOperationContext.OperationId">
            <summary>
            操作ID
            </summary>
        </member>
        <member name="M:EventProcessor.Core.Services.IOperationContext.AddTag(System.String,System.String)">
            <summary>
            添加标签
            </summary>
            <param name="key">键</param>
            <param name="value">值</param>
        </member>
        <member name="M:EventProcessor.Core.Services.IOperationContext.SetResult(System.Boolean,System.Object)">
            <summary>
            设置结果
            </summary>
            <param name="success">是否成功</param>
            <param name="resultData">结果数据</param>
        </member>
        <member name="T:EventProcessor.Core.Services.PerformanceMetrics">
            <summary>
            性能指标
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Services.PerformanceMetrics.CpuUsagePercent">
            <summary>
            CPU使用率（百分比）
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Services.PerformanceMetrics.MemoryUsageMB">
            <summary>
            内存使用量（MB）
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Services.PerformanceMetrics.MemoryUsagePercent">
            <summary>
            内存使用率（百分比）
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Services.PerformanceMetrics.ThreadCount">
            <summary>
            线程数
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Services.PerformanceMetrics.GCCollectionCount">
            <summary>
            GC回收次数
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Services.PerformanceMetrics.OperationStats">
            <summary>
            操作统计
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Services.PerformanceMetrics.CustomMetrics">
            <summary>
            自定义指标
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Services.PerformanceMetrics.Counters">
            <summary>
            计数器
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Services.PerformanceMetrics.CollectedAt">
            <summary>
            采集时间
            </summary>
        </member>
        <member name="T:EventProcessor.Core.Services.OperationStatistics">
            <summary>
            操作统计
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Services.OperationStatistics.TotalCalls">
            <summary>
            总调用次数
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Services.OperationStatistics.SuccessfulCalls">
            <summary>
            成功次数
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Services.OperationStatistics.FailedCalls">
            <summary>
            失败次数
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Services.OperationStatistics.AverageExecutionTimeMs">
            <summary>
            平均执行时间（毫秒）
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Services.OperationStatistics.MinExecutionTimeMs">
            <summary>
            最小执行时间（毫秒）
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Services.OperationStatistics.MaxExecutionTimeMs">
            <summary>
            最大执行时间（毫秒）
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Services.OperationStatistics.P95ExecutionTimeMs">
            <summary>
            95百分位执行时间（毫秒）
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Services.OperationStatistics.CallsPerSecond">
            <summary>
            每秒调用次数
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Services.OperationStatistics.SuccessRatePercent">
            <summary>
            成功率（百分比）
            </summary>
        </member>
        <member name="T:EventProcessor.Core.Services.HealthStatus">
            <summary>
            健康状态
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Services.HealthStatus.OverallHealth">
            <summary>
            整体健康状态
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Services.HealthStatus.ComponentHealths">
            <summary>
            组件健康状态
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Services.HealthStatus.CheckedAt">
            <summary>
            健康检查时间
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Services.HealthStatus.Message">
            <summary>
            健康消息
            </summary>
        </member>
        <member name="T:EventProcessor.Core.Services.ComponentHealth">
            <summary>
            组件健康状态
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Services.ComponentHealth.State">
            <summary>
            健康状态
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Services.ComponentHealth.Message">
            <summary>
            健康消息
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Services.ComponentHealth.CheckedAt">
            <summary>
            检查时间
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Services.ComponentHealth.ResponseTimeMs">
            <summary>
            响应时间（毫秒）
            </summary>
        </member>
        <member name="T:EventProcessor.Core.Services.HealthState">
            <summary>
            健康状态枚举
            </summary>
        </member>
        <member name="F:EventProcessor.Core.Services.HealthState.Healthy">
            <summary>
            健康
            </summary>
        </member>
        <member name="F:EventProcessor.Core.Services.HealthState.Warning">
            <summary>
            警告
            </summary>
        </member>
        <member name="F:EventProcessor.Core.Services.HealthState.Unhealthy">
            <summary>
            不健康
            </summary>
        </member>
        <member name="F:EventProcessor.Core.Services.HealthState.Unknown">
            <summary>
            未知
            </summary>
        </member>
        <member name="T:EventProcessor.Core.Services.PerformanceMetricsUpdatedEventArgs">
            <summary>
            性能指标更新事件参数
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Services.PerformanceMetricsUpdatedEventArgs.Metrics">
            <summary>
            性能指标
            </summary>
        </member>
        <member name="T:EventProcessor.Core.Services.PerformanceThresholdExceededEventArgs">
            <summary>
            性能阈值超出事件参数
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Services.PerformanceThresholdExceededEventArgs.MetricName">
            <summary>
            指标名称
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Services.PerformanceThresholdExceededEventArgs.CurrentValue">
            <summary>
            当前值
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Services.PerformanceThresholdExceededEventArgs.Threshold">
            <summary>
            阈值
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Services.PerformanceThresholdExceededEventArgs.ComponentName">
            <summary>
            组件名称
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Services.PerformanceThresholdExceededEventArgs.ExceededAt">
            <summary>
            超出时间
            </summary>
        </member>
        <member name="T:EventProcessor.Core.Services.IStandardAlarmGenerator">
            <summary>
            标准告警生成器接口
            </summary>
        </member>
        <member name="M:EventProcessor.Core.Services.IStandardAlarmGenerator.GenerateStandardAlarmAsync(EventProcessor.Core.Models.EventContext,EventProcessor.Core.Models.AlarmConfiguration,EventProcessor.Core.Models.EventConfiguration)">
            <summary>
            生成标准告警消息
            </summary>
            <param name="context">事件上下文</param>
            <param name="config">告警配置</param>
            <param name="eventConfig">事件配置</param>
            <returns>标准告警消息</returns>
        </member>
        <member name="M:EventProcessor.Core.Services.IStandardAlarmGenerator.GenerateStandardCancellationAsync(EventProcessor.Core.Models.EventContext,EventProcessor.Core.Models.AlarmConfiguration,EventProcessor.Core.Models.EventConfiguration,System.String)">
            <summary>
            生成标准告警撤销消息
            </summary>
            <param name="context">事件上下文</param>
            <param name="config">告警配置</param>
            <param name="eventConfig">事件配置</param>
            <param name="reason">撤销原因</param>
            <returns>标准告警撤销消息</returns>
        </member>
        <member name="T:EventProcessor.Core.Services.MqttService">
            <summary>
            MQTT服务实现
            </summary>
        </member>
        <member name="E:EventProcessor.Core.Services.MqttService.Connected">
            <summary>
            MQTT连接成功事件
            </summary>
        </member>
        <member name="E:EventProcessor.Core.Services.MqttService.Disconnected">
            <summary>
            MQTT断开连接事件
            </summary>
        </member>
        <member name="E:EventProcessor.Core.Services.MqttService.MessageReceived">
            <summary>
            MQTT消息接收事件
            </summary>
        </member>
        <member name="M:EventProcessor.Core.Services.MqttService.#ctor(Microsoft.Extensions.Options.IOptions{EventProcessor.Core.Models.MqttConfiguration},Microsoft.Extensions.Logging.ILogger{EventProcessor.Core.Services.MqttService})">
            <summary>
            初始化MQTT服务
            </summary>
            <param name="config">MQTT配置</param>
            <param name="logger">日志记录器</param>
        </member>
        <member name="P:EventProcessor.Core.Services.MqttService.IsConnected">
            <summary>
            获取MQTT客户端连接状态
            </summary>
        </member>
        <member name="P:EventProcessor.Core.Services.MqttService.ClientId">
            <summary>
            获取MQTT客户端ID
            </summary>
        </member>
        <member name="M:EventProcessor.Core.Services.MqttService.StartAsync(System.Threading.CancellationToken)">
            <summary>
            启动MQTT服务
            </summary>
            <param name="cancellationToken">取消令牌</param>
            <returns>异步任务</returns>
        </member>
        <member name="M:EventProcessor.Core.Services.MqttService.StopAsync(System.Threading.CancellationToken)">
            <summary>
            停止MQTT服务
            </summary>
            <param name="cancellationToken">取消令牌</param>
            <returns>异步任务</returns>
        </member>
        <member name="M:EventProcessor.Core.Services.MqttService.SubscribeAsync(System.String,System.Int32)">
            <summary>
            订阅单个MQTT主题
            </summary>
            <param name="topic">主题名称</param>
            <param name="qos">服务质量等级</param>
            <returns>异步任务</returns>
        </member>
        <member name="M:EventProcessor.Core.Services.MqttService.SubscribeAsync(System.String[],System.Int32)">
            <summary>
            订阅多个MQTT主题
            </summary>
            <param name="topics">主题名称数组</param>
            <param name="qos">服务质量等级</param>
            <returns>异步任务</returns>
        </member>
        <member name="M:EventProcessor.Core.Services.MqttService.UnsubscribeAsync(System.String)">
            <summary>
            取消订阅MQTT主题
            </summary>
            <param name="topic">主题名称</param>
            <returns>异步任务</returns>
        </member>
        <member name="M:EventProcessor.Core.Services.MqttService.PublishAsync(System.String,System.String,System.Int32,System.Boolean)">
            <summary>
            发布MQTT消息
            </summary>
            <param name="topic">主题名称</param>
            <param name="payload">消息负载</param>
            <param name="qos">服务质量等级</param>
            <param name="retain">是否保留消息</param>
            <returns>异步任务</returns>
        </member>
        <member name="M:EventProcessor.Core.Services.MqttService.PublishAsync(System.String,System.Byte[],System.Int32,System.Boolean)">
            <summary>
            发布MQTT消息（字节数组格式）
            </summary>
            <param name="topic">主题名称</param>
            <param name="payload">消息负载（字节数组）</param>
            <param name="qos">服务质量等级</param>
            <param name="retain">是否保留消息</param>
            <returns>异步任务</returns>
        </member>
        <member name="M:EventProcessor.Core.Services.MqttService.GetConnectionStatistics">
            <summary>
            获取MQTT连接统计信息
            </summary>
            <returns>连接统计信息</returns>
        </member>
        <member name="M:EventProcessor.Core.Services.MqttService.Dispose">
            <summary>
            释放MQTT服务资源
            </summary>
        </member>
        <member name="M:EventProcessor.Core.Services.MqttService.Dispose(System.Boolean)">
            <summary>
            释放资源的具体实现
            </summary>
            <param name="disposing">是否释放托管资源</param>
        </member>
        <member name="T:EventProcessor.Core.Services.StandardAlarmGenerator">
            <summary>
            标准告警生成器 - 生成符合接收方规范的告警消息
            </summary>
        </member>
        <member name="M:EventProcessor.Core.Services.StandardAlarmGenerator.#ctor(Microsoft.Extensions.Logging.ILogger{EventProcessor.Core.Services.StandardAlarmGenerator})">
            <summary>
            构造函数
            </summary>
            <param name="logger">日志记录器</param>
        </member>
        <member name="M:EventProcessor.Core.Services.StandardAlarmGenerator.GenerateStandardAlarmAsync(EventProcessor.Core.Models.EventContext,EventProcessor.Core.Models.AlarmConfiguration,EventProcessor.Core.Models.EventConfiguration)">
            <summary>
            生成标准告警消息
            </summary>
            <param name="context">事件上下文</param>
            <param name="config">告警配置</param>
            <param name="eventConfig">事件配置</param>
            <returns>标准告警消息</returns>
        </member>
        <member name="M:EventProcessor.Core.Services.StandardAlarmGenerator.GenerateStandardCancellationAsync(EventProcessor.Core.Models.EventContext,EventProcessor.Core.Models.AlarmConfiguration,EventProcessor.Core.Models.EventConfiguration,System.String)">
            <summary>
            生成标准告警撤销消息
            </summary>
            <param name="context">事件上下文</param>
            <param name="config">告警配置</param>
            <param name="eventConfig">事件配置</param>
            <param name="reason">撤销原因</param>
            <returns>标准告警撤销消息</returns>
        </member>
        <member name="M:EventProcessor.Core.Services.StandardAlarmGenerator.BuildDataContentAsync(EventProcessor.Core.Models.EventContext,EventProcessor.Core.Models.AlarmConfiguration)">
            <summary>
            构建详情数据内容
            </summary>
            <param name="context">事件上下文</param>
            <param name="config">告警配置</param>
            <returns>格式化的详情数据字符串</returns>
        </member>
        <member name="M:EventProcessor.Core.Services.StandardAlarmGenerator.BuildCancellationDataContent(EventProcessor.Core.Models.EventContext,System.String)">
            <summary>
            构建撤销消息的详情数据内容
            </summary>
            <param name="context">事件上下文</param>
            <param name="reason">撤销原因</param>
            <returns>格式化的撤销详情数据字符串</returns>
        </member>
        <member name="M:EventProcessor.Core.Services.StandardAlarmGenerator.BuildImageUrls(EventProcessor.Core.Models.EventContext)">
            <summary>
            构建图片URL列表
            </summary>
            <param name="context">事件上下文</param>
            <returns>图片URL数组</returns>
        </member>
        <member name="M:EventProcessor.Core.Services.StandardAlarmGenerator.ExtractFieldValue(EventProcessor.Core.Models.EventContext,EventProcessor.Core.Models.FieldMapping)">
            <summary>
            提取字段值
            </summary>
            <param name="context">事件上下文</param>
            <param name="fieldMapping">字段映射配置</param>
            <returns>字段值</returns>
        </member>
        <member name="M:EventProcessor.Core.Services.StandardAlarmGenerator.GetSourceData(EventProcessor.Core.Models.EventContext,System.String)">
            <summary>
            获取源数据
            </summary>
            <param name="context">事件上下文</param>
            <param name="sourceRuleType">源规则类型</param>
            <returns>源数据字典</returns>
        </member>
        <member name="M:EventProcessor.Core.Services.StandardAlarmGenerator.ProcessMultipleFields(System.Collections.Generic.Dictionary{System.String,System.Object},EventProcessor.Core.Models.FieldMapping)">
            <summary>
            处理多字段
            </summary>
            <param name="sourceData">源数据</param>
            <param name="mapping">字段映射</param>
            <returns>处理后的字段值</returns>
        </member>
        <member name="M:EventProcessor.Core.Services.StandardAlarmGenerator.ApplyFormatTemplate(System.Object,System.String)">
            <summary>
            应用格式化模板
            </summary>
            <param name="value">字段值</param>
            <param name="formatTemplate">格式化模板</param>
            <returns>格式化后的值</returns>
        </member>
        <member name="M:EventProcessor.Core.Services.StandardAlarmGenerator.ExtractFromBusinessData(EventProcessor.Core.Models.EventContext,System.String)">
            <summary>
            从业务数据中提取字段值
            </summary>
        </member>
        <member name="M:EventProcessor.Core.Services.StandardAlarmGenerator.ExtractFromDeviceSignal(EventProcessor.Core.Models.EventContext,System.String)">
            <summary>
            从设备数据中提取字段值
            </summary>
        </member>
        <member name="M:EventProcessor.Core.Services.StandardAlarmGenerator.ExtractFromExclusionData(EventProcessor.Core.Models.EventContext,System.String)">
            <summary>
            从排除数据中提取字段值
            </summary>
        </member>
        <member name="M:EventProcessor.Core.Services.StandardAlarmGenerator.ExtractFromAIResult(EventProcessor.Core.Models.EventContext,System.String)">
            <summary>
            从AI结果中提取字段值
            </summary>
        </member>
        <member name="T:EventProcessor.Core.Validation.ConfigurationValidator">
            <summary>
            配置验证器 - 提供全面的EventConfiguration验证
            </summary>
        </member>
        <member name="M:EventProcessor.Core.Validation.ConfigurationValidator.#ctor(Microsoft.Extensions.Logging.ILogger{EventProcessor.Core.Validation.ConfigurationValidator})">
            <summary>
            初始化配置验证器
            </summary>
            <param name="logger">日志记录器</param>
        </member>
        <member name="M:EventProcessor.Core.Validation.ConfigurationValidator.ValidateConfiguration(EventProcessor.Core.Models.EventConfiguration)">
            <summary>
            验证事件配置
            </summary>
            <param name="configuration">事件配置</param>
            <returns>验证结果</returns>
        </member>
        <member name="M:EventProcessor.Core.Validation.ConfigurationValidator.ValidateBasicConfiguration(EventProcessor.Core.Models.EventConfiguration,System.Collections.Generic.List{System.String},System.Collections.Generic.List{System.String})">
            <summary>
            验证基础配置
            </summary>
        </member>
        <member name="M:EventProcessor.Core.Validation.ConfigurationValidator.ValidateDeviceSignalConfiguration(EventProcessor.Core.Models.DeviceSignalConfiguration,System.Collections.Generic.List{System.String},System.Collections.Generic.List{System.String})">
            <summary>
            验证设备信号配置
            </summary>
        </member>
        <member name="M:EventProcessor.Core.Validation.ConfigurationValidator.ValidateRuleConfiguration(EventProcessor.Core.Models.RuleConfiguration,System.Collections.Generic.List{System.String},System.Collections.Generic.List{System.String})">
            <summary>
            验证规则配置
            </summary>
        </member>
        <member name="M:EventProcessor.Core.Validation.ConfigurationValidator.ValidateExclusionRuleGroup(EventProcessor.Core.Models.ExclusionRuleGroup,System.Int32,System.Collections.Generic.List{System.String},System.Collections.Generic.List{System.String})">
            <summary>
            验证排除规则组
            </summary>
        </member>
        <member name="M:EventProcessor.Core.Validation.ConfigurationValidator.ValidateBusinessRuleGroup(EventProcessor.Core.Models.BusinessRuleGroup,System.Int32,System.Collections.Generic.List{System.String},System.Collections.Generic.List{System.String})">
            <summary>
            验证业务规则组
            </summary>
        </member>
        <member name="M:EventProcessor.Core.Validation.ConfigurationValidator.ValidateAIResultRuleGroup(EventProcessor.Core.Models.AIResultRuleGroup,System.Int32,System.Collections.Generic.List{System.String},System.Collections.Generic.List{System.String})">
            <summary>
            验证AI结果规则组
            </summary>
        </member>
        <member name="M:EventProcessor.Core.Validation.ConfigurationValidator.ValidateConditionGroup(EventProcessor.Core.Models.ConditionGroup,System.String,System.Collections.Generic.List{System.String},System.Collections.Generic.List{System.String})">
            <summary>
            验证条件组
            </summary>
        </member>
        <member name="M:EventProcessor.Core.Validation.ConfigurationValidator.ValidateCondition(EventProcessor.Core.Models.Condition,System.String,System.Collections.Generic.List{System.String},System.Collections.Generic.List{System.String})">
            <summary>
            验证单个条件
            </summary>
        </member>
        <member name="M:EventProcessor.Core.Validation.ConfigurationValidator.ValidateEvaluationStrategyConsistency(EventProcessor.Core.Models.EventConfiguration,System.Collections.Generic.List{System.String},System.Collections.Generic.List{System.String})">
            <summary>
            验证评估策略一致性
            </summary>
        </member>
        <member name="M:EventProcessor.Core.Validation.ConfigurationValidator.ValidateAlarmConfiguration(EventProcessor.Core.Models.AlarmConfiguration,System.Collections.Generic.List{System.String},System.Collections.Generic.List{System.String})">
            <summary>
            验证告警配置
            </summary>
        </member>
        <member name="M:EventProcessor.Core.Validation.ConfigurationValidator.ValidateFieldMapping(EventProcessor.Core.Models.FieldMapping,System.Int32,System.Collections.Generic.List{System.String},System.Collections.Generic.List{System.String})">
            <summary>
            验证字段映射
            </summary>
        </member>
        <member name="M:EventProcessor.Core.Validation.ConfigurationValidator.ValidateFormatTemplate(System.String,System.String,System.Collections.Generic.List{System.String})">
            <summary>
            验证格式化模板
            </summary>
        </member>
        <member name="M:EventProcessor.Core.Validation.ConfigurationValidator.IsValidMqttTopic(System.String)">
            <summary>
            验证MQTT主题格式
            </summary>
        </member>
        <member name="T:EventProcessor.Core.Validation.ValidationConstants">
            <summary>
            验证常量定义 - 包含所有支持的操作符和数据类型
            </summary>
        </member>
        <member name="F:EventProcessor.Core.Validation.ValidationConstants.SupportedDataTypes">
            <summary>
            支持的数据类型
            </summary>
        </member>
        <member name="F:EventProcessor.Core.Validation.ValidationConstants.SupportedLogicOperators">
            <summary>
            支持的逻辑操作符
            </summary>
        </member>
        <member name="F:EventProcessor.Core.Validation.ValidationConstants.SupportedEvaluationStrategies">
            <summary>
            支持的评估策略
            </summary>
        </member>
        <member name="F:EventProcessor.Core.Validation.ValidationConstants.SupportedPriorities">
            <summary>
            支持的优先级
            </summary>
        </member>
        <member name="F:EventProcessor.Core.Validation.ValidationConstants.SupportedTimeWindows">
            <summary>
            支持的时间窗口策略
            </summary>
        </member>
        <member name="F:EventProcessor.Core.Validation.ValidationConstants.SupportedSourceTypes">
            <summary>
            支持的数据源类型
            </summary>
        </member>
        <member name="F:EventProcessor.Core.Validation.ValidationConstants.SupportedSourceRuleTypes">
            <summary>
            支持的告警字段源规则类型
            </summary>
        </member>
        <member name="F:EventProcessor.Core.Validation.ValidationConstants.StringOperators">
            <summary>
            字符串类型支持的操作符
            </summary>
        </member>
        <member name="F:EventProcessor.Core.Validation.ValidationConstants.NumberOperators">
            <summary>
            数字类型支持的操作符
            </summary>
        </member>
        <member name="F:EventProcessor.Core.Validation.ValidationConstants.DateTimeOperators">
            <summary>
            日期时间类型支持的操作符
            </summary>
        </member>
        <member name="F:EventProcessor.Core.Validation.ValidationConstants.AllSupportedOperators">
            <summary>
            所有支持的操作符
            </summary>
        </member>
        <member name="M:EventProcessor.Core.Validation.ValidationConstants.GetSupportedOperators(System.String)">
            <summary>
            获取指定数据类型支持的操作符
            </summary>
            <param name="dataType">数据类型</param>
            <returns>支持的操作符数组</returns>
        </member>
        <member name="M:EventProcessor.Core.Validation.ValidationConstants.IsOperatorSupportedForDataType(System.String,System.String)">
            <summary>
            验证操作符是否支持指定数据类型
            </summary>
            <param name="operatorName">操作符名称</param>
            <param name="dataType">数据类型</param>
            <returns>是否支持</returns>
        </member>
        <member name="F:EventProcessor.Core.Validation.ValidationConstants.RangeOperators">
            <summary>
            需要范围值的操作符（格式：value1,value2）
            </summary>
        </member>
        <member name="F:EventProcessor.Core.Validation.ValidationConstants.DelimitedOperators">
            <summary>
            需要分隔符值的操作符（格式：value1|value2|value3）
            </summary>
        </member>
        <member name="F:EventProcessor.Core.Validation.ValidationConstants.RegexOperators">
            <summary>
            需要正则表达式的操作符
            </summary>
        </member>
        <member name="F:EventProcessor.Core.Validation.ValidationConstants.NoValueOperators">
            <summary>
            不需要值的操作符
            </summary>
        </member>
        <member name="M:EventProcessor.Core.Validation.ValidationConstants.ValidateOperatorValue(System.String,System.String,System.String)">
            <summary>
            验证操作符值格式
            </summary>
            <param name="operatorName">操作符名称</param>
            <param name="value">操作符值</param>
            <param name="dataType">数据类型</param>
            <returns>验证结果</returns>
        </member>
    </members>
</doc>
