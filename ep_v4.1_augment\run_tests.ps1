# EP_V4.1 测试执行脚本
# 统一管理所有测试脚本的执行

param(
    [Parameter(HelpMessage = "测试类型")]
    [ValidateSet("quick", "scenarios", "yaml", "expire", "all")]
    [string]$TestType = "",
    
    [Parameter(HelpMessage = "显示帮助信息")]
    [switch]$Help = $false
)

# 显示帮助信息
if ($Help -or $TestType -eq "") {
    Write-Host @"
EP_V4.1 测试执行脚本

用法:
    .\run_tests.ps1 -TestType <类型>

测试类型:
    quick      - 快速测试 (test_quick_ev001001.py)
    scenarios  - 完整场景测试 (test_ev001001_scenarios.py)
    yaml       - YAML复现测试 (test_yaml_replay.py)
    expire     - 过期车测试 (test_expire_ev001002.py)
    all        - 显示所有可用测试

示例:
    .\run_tests.ps1 -TestType quick
    .\run_tests.ps1 -TestType scenarios
    .\run_tests.ps1 -TestType yaml
    .\run_tests.ps1 -TestType expire

注意:
    - 确保已安装Python和paho-mqtt库
    - 确保EP_V4.1服务正在运行
    - 确保网络连接正常
"@ -ForegroundColor Green
    exit 0
}

# 检查Python环境
function Test-PythonEnvironment {
    Write-Host "🔍 检查Python环境..." -ForegroundColor Yellow
    
    try {
        $pythonVersion = python --version 2>&1
        Write-Host "✅ Python版本: $pythonVersion" -ForegroundColor Green
    }
    catch {
        Write-Host "❌ Python未安装或不在PATH中" -ForegroundColor Red
        Write-Host "请安装Python 3.7+并确保在PATH中" -ForegroundColor Yellow
        return $false
    }
    
    # 检查paho-mqtt库
    try {
        $result = python -c "import paho.mqtt.client; print('paho-mqtt已安装')" 2>&1
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ paho-mqtt库已安装" -ForegroundColor Green
        } else {
            Write-Host "❌ paho-mqtt库未安装" -ForegroundColor Red
            Write-Host "请运行: pip install paho-mqtt" -ForegroundColor Yellow
            return $false
        }
    }
    catch {
        Write-Host "❌ 无法检查paho-mqtt库" -ForegroundColor Red
        return $false
    }
    
    return $true
}

# 检查测试文件
function Test-TestFiles {
    Write-Host "🔍 检查测试文件..." -ForegroundColor Yellow
    
    $testFiles = @(
        "test_quick_ev001001.py",
        "test_ev001001_scenarios.py", 
        "test_yaml_replay.py",
        "test_expire_ev001002.py"
    )
    
    $allExist = $true
    foreach ($file in $testFiles) {
        if (Test-Path $file) {
            Write-Host "✅ $file" -ForegroundColor Green
        } else {
            Write-Host "❌ $file 不存在" -ForegroundColor Red
            $allExist = $false
        }
    }
    
    return $allExist
}

# 运行测试
function Invoke-Test {
    param([string]$TestScript, [string]$Description)
    
    Write-Host "`n" + "="*60 -ForegroundColor Cyan
    Write-Host "🚀 执行测试: $Description" -ForegroundColor Cyan
    Write-Host "📄 脚本: $TestScript" -ForegroundColor Cyan
    Write-Host "⏰ 开始时间: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')" -ForegroundColor Cyan
    Write-Host "="*60 -ForegroundColor Cyan
    
    if (-not (Test-Path $TestScript)) {
        Write-Host "❌ 测试脚本不存在: $TestScript" -ForegroundColor Red
        return $false
    }
    
    try {
        # 运行Python脚本
        python $TestScript
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "`n✅ 测试执行完成" -ForegroundColor Green
            return $true
        } else {
            Write-Host "`n❌ 测试执行失败，退出码: $LASTEXITCODE" -ForegroundColor Red
            return $false
        }
    }
    catch {
        Write-Host "`n❌ 测试执行异常: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# 显示所有测试
function Show-AllTests {
    Write-Host "`n📋 可用的测试脚本:" -ForegroundColor Cyan
    Write-Host "-"*60 -ForegroundColor Gray
    
    Write-Host "1. 快速测试 (quick)" -ForegroundColor White
    Write-Host "   文件: test_quick_ev001001.py" -ForegroundColor Gray
    Write-Host "   描述: 简化版EV001001测试，快速验证基本功能" -ForegroundColor Gray
    Write-Host "   场景: 正常告警、解除信号" -ForegroundColor Gray
    Write-Host ""
    
    Write-Host "2. 完整场景测试 (scenarios)" -ForegroundColor White
    Write-Host "   文件: test_ev001001_scenarios.py" -ForegroundColor Gray
    Write-Host "   描述: 完整的EV001001测试场景，包含告警监控" -ForegroundColor Gray
    Write-Host "   场景: 正常告警、解除信号、排除规则" -ForegroundColor Gray
    Write-Host ""
    
    Write-Host "3. YAML复现测试 (yaml)" -ForegroundColor White
    Write-Host "   文件: test_yaml_replay.py" -ForegroundColor Gray
    Write-Host "   描述: 基于真实事件YAML文件的复现测试" -ForegroundColor Gray
    Write-Host "   场景: 复现历史事件序列" -ForegroundColor Gray
    Write-Host ""
    
    Write-Host "4. 过期车测试 (expire)" -ForegroundColor White
    Write-Host "   文件: test_expire_ev001002.py" -ForegroundColor Gray
    Write-Host "   描述: EV001002过期月租车测试" -ForegroundColor Gray
    Write-Host "   场景: 过期卡滞留检测" -ForegroundColor Gray
    Write-Host ""
    
    Write-Host "💡 使用方法:" -ForegroundColor Yellow
    Write-Host "   .\run_tests.ps1 -TestType <类型名>" -ForegroundColor Gray
    Write-Host ""
}

# 主执行逻辑
Write-Host "🎯 EP_V4.1 测试执行器" -ForegroundColor Green
Write-Host "时间: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')" -ForegroundColor Gray

# 显示所有测试
if ($TestType -eq "all") {
    Show-AllTests
    exit 0
}

# 检查环境
if (-not (Test-PythonEnvironment)) {
    Write-Host "`n❌ Python环境检查失败，无法继续" -ForegroundColor Red
    exit 1
}

if (-not (Test-TestFiles)) {
    Write-Host "`n❌ 测试文件检查失败，请确保所有测试脚本存在" -ForegroundColor Red
    exit 1
}

# 执行指定测试
$success = $false

switch ($TestType) {
    "quick" {
        $success = Invoke-Test "test_quick_ev001001.py" "EV001001快速测试"
    }
    "scenarios" {
        $success = Invoke-Test "test_ev001001_scenarios.py" "EV001001完整场景测试"
    }
    "yaml" {
        $success = Invoke-Test "test_yaml_replay.py" "YAML事件复现测试"
    }
    "expire" {
        $success = Invoke-Test "test_expire_ev001002.py" "EV001002过期车测试"
    }
    default {
        Write-Host "❌ 未知的测试类型: $TestType" -ForegroundColor Red
        Write-Host "使用 -Help 查看可用选项" -ForegroundColor Yellow
        exit 1
    }
}

# 输出最终结果
Write-Host "`n" + "="*60 -ForegroundColor Cyan
if ($success) {
    Write-Host "🎉 测试执行成功完成！" -ForegroundColor Green
} else {
    Write-Host "⚠️  测试执行遇到问题，请检查输出信息" -ForegroundColor Yellow
}
Write-Host "⏰ 结束时间: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')" -ForegroundColor Gray
Write-Host "="*60 -ForegroundColor Cyan
