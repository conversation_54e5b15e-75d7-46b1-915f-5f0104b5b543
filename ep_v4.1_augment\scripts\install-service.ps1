# Event Processor V4.1 Windows服务安装脚本
# 使用管理员权限运行此脚本

param(
    [Parameter(Mandatory=$false)]
    [string]$ServiceName = "EventProcessor.V4.1",
    
    [Parameter(Mandatory=$false)]
    [string]$DisplayName = "Event Processor V4.1",
    
    [Parameter(Mandatory=$false)]
    [string]$Description = "Event Processor V4.1 - 智能事件处理服务",
    
    [Parameter(Mandatory=$false)]
    [string]$BinaryPath = "",
    
    [Parameter(Mandatory=$false)]
    [string]$ConfigPath = "",
    
    [Parameter(Mandatory=$false)]
    [string]$StartupType = "Automatic",
    
    [Parameter(Mandatory=$false)]
    [string]$ServiceAccount = "LocalSystem",
    
    [Parameter(Mandatory=$false)]
    [switch]$Force
)

# 检查管理员权限
function Test-Administrator {
    $currentUser = [Security.Principal.WindowsIdentity]::GetCurrent()
    $principal = New-Object Security.Principal.WindowsPrincipal($currentUser)
    return $principal.IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)
}

# 停止并删除现有服务
function Remove-ExistingService {
    param([string]$Name)
    
    $service = Get-Service -Name $Name -ErrorAction SilentlyContinue
    if ($service) {
        Write-Host "发现现有服务: $Name" -ForegroundColor Yellow
        
        if ($service.Status -eq 'Running') {
            Write-Host "停止服务..." -ForegroundColor Yellow
            Stop-Service -Name $Name -Force
            Start-Sleep -Seconds 5
        }
        
        Write-Host "删除服务..." -ForegroundColor Yellow
        sc.exe delete $Name
        Start-Sleep -Seconds 2
    }
}

# 创建服务目录结构
function New-ServiceDirectories {
    param([string]$BasePath)
    
    $directories = @(
        "$BasePath\logs",
        "$BasePath\config",
        "$BasePath\data",
        "$BasePath\temp"
    )
    
    foreach ($dir in $directories) {
        if (!(Test-Path $dir)) {
            New-Item -ItemType Directory -Path $dir -Force | Out-Null
            Write-Host "创建目录: $dir" -ForegroundColor Green
        }
    }
}

# 设置目录权限
function Set-DirectoryPermissions {
    param([string]$BasePath)
    
    try {
        # 给予服务账户对服务目录的完全控制权限
        $acl = Get-Acl $BasePath
        $accessRule = New-Object System.Security.AccessControl.FileSystemAccessRule(
            "NT AUTHORITY\LOCAL SERVICE", "FullControl", "ContainerInherit,ObjectInherit", "None", "Allow"
        )
        $acl.SetAccessRule($accessRule)
        Set-Acl -Path $BasePath -AclObject $acl
        
        Write-Host "设置目录权限完成" -ForegroundColor Green
    }
    catch {
        Write-Warning "设置目录权限失败: $($_.Exception.Message)"
    }
}

# 复制配置文件
function Copy-ConfigurationFiles {
    param([string]$SourcePath, [string]$TargetPath)
    
    if (Test-Path "$SourcePath\config") {
        Copy-Item -Path "$SourcePath\config\*" -Destination "$TargetPath\config" -Recurse -Force
        Write-Host "配置文件已复制" -ForegroundColor Green
    }
}

# 创建服务
function New-WindowsService {
    param(
        [string]$Name,
        [string]$DisplayName,
        [string]$Description,
        [string]$BinaryPath,
        [string]$StartupType,
        [string]$ServiceAccount
    )
    
    try {
        $params = @{
            Name = $Name
            DisplayName = $DisplayName
            Description = $Description
            BinaryPathName = $BinaryPath
            StartupType = $StartupType
        }
        
        if ($ServiceAccount -ne "LocalSystem") {
            $params.Credential = Get-Credential -Message "请输入服务账户凭据"
        }
        
        New-Service @params
        Write-Host "服务创建成功: $Name" -ForegroundColor Green
        return $true
    }
    catch {
        Write-Error "服务创建失败: $($_.Exception.Message)"
        return $false
    }
}

# 主程序
try {
    Write-Host "Event Processor V4.1 服务安装程序" -ForegroundColor Cyan
    Write-Host "=================================" -ForegroundColor Cyan
    
    # 检查管理员权限
    if (!(Test-Administrator)) {
        Write-Error "请以管理员身份运行此脚本"
        exit 1
    }
    
    # 确定二进制文件路径
    if ([string]::IsNullOrEmpty($BinaryPath)) {
        $scriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path
        $BinaryPath = Join-Path (Split-Path -Parent $scriptDir) "src\EventProcessor.Host\bin\Release\net8.0\EventProcessor.Host.exe"
    }
    
    if (!(Test-Path $BinaryPath)) {
        Write-Error "找不到可执行文件: $BinaryPath"
        Write-Host "请先编译项目或指定正确的二进制文件路径" -ForegroundColor Yellow
        exit 1
    }
    
    $serviceDir = Split-Path -Parent $BinaryPath
    
    # 确定配置文件路径
    if ([string]::IsNullOrEmpty($ConfigPath)) {
        $ConfigPath = Join-Path $serviceDir "config\event-config.yaml"
    }
    
    Write-Host "服务名称: $ServiceName" -ForegroundColor White
    Write-Host "显示名称: $DisplayName" -ForegroundColor White
    Write-Host "二进制路径: $BinaryPath" -ForegroundColor White
    Write-Host "配置路径: $ConfigPath" -ForegroundColor White
    Write-Host "启动类型: $StartupType" -ForegroundColor White
    Write-Host "服务账户: $ServiceAccount" -ForegroundColor White
    Write-Host ""
    
    # 确认安装
    if (!$Force) {
        $confirm = Read-Host "是否继续安装? (Y/N)"
        if ($confirm -ne 'Y' -and $confirm -ne 'y') {
            Write-Host "安装已取消" -ForegroundColor Yellow
            exit 0
        }
    }
    
    # 删除现有服务
    Remove-ExistingService -Name $ServiceName
    
    # 创建目录结构
    New-ServiceDirectories -BasePath $serviceDir
    
    # 设置权限
    Set-DirectoryPermissions -BasePath $serviceDir
    
    # 复制配置文件
    $sourceDir = Split-Path -Parent (Split-Path -Parent $serviceDir)
    Copy-ConfigurationFiles -SourcePath $sourceDir -TargetPath $serviceDir
    
    # 构建服务命令行
    $serviceCommand = "`"$BinaryPath`""
    if (Test-Path $ConfigPath) {
        $serviceCommand += " --config `"$ConfigPath`""
    }
    
    # 创建服务
    $success = New-WindowsService -Name $ServiceName -DisplayName $DisplayName -Description $Description -BinaryPath $serviceCommand -StartupType $StartupType -ServiceAccount $ServiceAccount
    
    if ($success) {
        Write-Host ""
        Write-Host "服务安装完成!" -ForegroundColor Green
        Write-Host "服务名称: $ServiceName" -ForegroundColor Green
        Write-Host ""
        Write-Host "管理命令:" -ForegroundColor Cyan
        Write-Host "  启动服务: Start-Service -Name '$ServiceName'" -ForegroundColor White
        Write-Host "  停止服务: Stop-Service -Name '$ServiceName'" -ForegroundColor White
        Write-Host "  重启服务: Restart-Service -Name '$ServiceName'" -ForegroundColor White
        Write-Host "  查看状态: Get-Service -Name '$ServiceName'" -ForegroundColor White
        Write-Host "  查看日志: Get-EventLog -LogName Application -Source '$ServiceName' -Newest 10" -ForegroundColor White
        Write-Host ""
        
        # 询问是否立即启动服务
        if (!$Force) {
            $startNow = Read-Host "是否立即启动服务? (Y/N)"
            if ($startNow -eq 'Y' -or $startNow -eq 'y') {
                Start-Service -Name $ServiceName
                Write-Host "服务已启动" -ForegroundColor Green
            }
        }
    }
    else {
        Write-Error "服务安装失败"
        exit 1
    }
}
catch {
    Write-Error "安装过程中发生错误: $($_.Exception.Message)"
    Write-Host "错误详情: $($_.Exception.StackTrace)" -ForegroundColor Red
    exit 1
}

Write-Host ""
Write-Host "安装完成!" -ForegroundColor Green
