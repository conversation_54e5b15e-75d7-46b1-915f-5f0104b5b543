# Event Processor V4.1 Windows服务卸载脚本
# 使用管理员权限运行此脚本

param(
    [Parameter(Mandatory=$false)]
    [string]$ServiceName = "EventProcessor.V4.1",
    
    [Parameter(Mandatory=$false)]
    [switch]$RemoveData,
    
    [Parameter(Mandatory=$false)]
    [switch]$RemoveLogs,
    
    [Parameter(Mandatory=$false)]
    [switch]$Force
)

# 检查管理员权限
function Test-Administrator {
    $currentUser = [Security.Principal.WindowsIdentity]::GetCurrent()
    $principal = New-Object Security.Principal.WindowsPrincipal($currentUser)
    return $principal.IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)
}

# 停止服务
function Stop-EventProcessorService {
    param([string]$Name)
    
    try {
        $service = Get-Service -Name $Name -ErrorAction SilentlyContinue
        if ($service) {
            if ($service.Status -eq 'Running') {
                Write-Host "正在停止服务: $Name" -ForegroundColor Yellow
                Stop-Service -Name $Name -Force -NoWait
                
                # 等待服务停止，最多等待30秒
                $timeout = 30
                $elapsed = 0
                while ($service.Status -eq 'Running' -and $elapsed -lt $timeout) {
                    Start-Sleep -Seconds 1
                    $elapsed++
                    $service.Refresh()
                    Write-Host "." -NoNewline -ForegroundColor Yellow
                }
                
                if ($service.Status -eq 'Running') {
                    Write-Warning "服务未能在30秒内停止，将强制终止相关进程"
                    Get-Process -Name "EventProcessor.Host" -ErrorAction SilentlyContinue | Stop-Process -Force
                }
                else {
                    Write-Host ""
                    Write-Host "服务已停止" -ForegroundColor Green
                }
            }
            else {
                Write-Host "服务已经停止" -ForegroundColor Green
            }
            return $true
        }
        else {
            Write-Host "服务不存在: $Name" -ForegroundColor Yellow
            return $false
        }
    }
    catch {
        Write-Error "停止服务时发生错误: $($_.Exception.Message)"
        return $false
    }
}

# 删除服务
function Remove-EventProcessorService {
    param([string]$Name)
    
    try {
        Write-Host "正在删除服务: $Name" -ForegroundColor Yellow
        sc.exe delete $Name
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "服务删除成功" -ForegroundColor Green
            return $true
        }
        else {
            Write-Error "服务删除失败，错误代码: $LASTEXITCODE"
            return $false
        }
    }
    catch {
        Write-Error "删除服务时发生错误: $($_.Exception.Message)"
        return $false
    }
}

# 清理文件和目录
function Remove-ServiceFiles {
    param(
        [string]$ServicePath,
        [bool]$RemoveData,
        [bool]$RemoveLogs
    )
    
    if (![string]::IsNullOrEmpty($ServicePath) -and (Test-Path $ServicePath)) {
        $serviceDir = Split-Path -Parent $ServicePath
        
        Write-Host "服务目录: $serviceDir" -ForegroundColor White
        
        # 删除数据文件
        if ($RemoveData) {
            $dataDir = Join-Path $serviceDir "data"
            if (Test-Path $dataDir) {
                Write-Host "删除数据目录: $dataDir" -ForegroundColor Yellow
                Remove-Item -Path $dataDir -Recurse -Force -ErrorAction SilentlyContinue
            }
            
            $tempDir = Join-Path $serviceDir "temp"
            if (Test-Path $tempDir) {
                Write-Host "删除临时目录: $tempDir" -ForegroundColor Yellow
                Remove-Item -Path $tempDir -Recurse -Force -ErrorAction SilentlyContinue
            }
        }
        
        # 删除日志文件
        if ($RemoveLogs) {
            $logsDir = Join-Path $serviceDir "logs"
            if (Test-Path $logsDir) {
                Write-Host "删除日志目录: $logsDir" -ForegroundColor Yellow
                Remove-Item -Path $logsDir -Recurse -Force -ErrorAction SilentlyContinue
            }
        }
        
        Write-Host "文件清理完成" -ForegroundColor Green
    }
}

# 清理注册表项
function Remove-ServiceRegistryEntries {
    param([string]$ServiceName)
    
    try {
        $servicePath = "HKLM:\SYSTEM\CurrentControlSet\Services\$ServiceName"
        if (Test-Path $servicePath) {
            Write-Host "清理注册表项: $servicePath" -ForegroundColor Yellow
            Remove-Item -Path $servicePath -Recurse -Force -ErrorAction SilentlyContinue
        }
        
        # 清理事件日志源
        $eventLogSources = @(
            "HKLM:\SYSTEM\CurrentControlSet\Services\EventLog\Application\$ServiceName",
            "HKLM:\SYSTEM\CurrentControlSet\Services\EventLog\System\$ServiceName"
        )
        
        foreach ($source in $eventLogSources) {
            if (Test-Path $source) {
                Write-Host "清理事件日志源: $source" -ForegroundColor Yellow
                Remove-Item -Path $source -Recurse -Force -ErrorAction SilentlyContinue
            }
        }
        
        Write-Host "注册表清理完成" -ForegroundColor Green
    }
    catch {
        Write-Warning "清理注册表时发生错误: $($_.Exception.Message)"
    }
}

# 主程序
try {
    Write-Host "Event Processor V4.1 服务卸载程序" -ForegroundColor Cyan
    Write-Host "===================================" -ForegroundColor Cyan
    
    # 检查管理员权限
    if (!(Test-Administrator)) {
        Write-Error "请以管理员身份运行此脚本"
        exit 1
    }
    
    # 获取服务信息
    $service = Get-Service -Name $ServiceName -ErrorAction SilentlyContinue
    if (!$service) {
        Write-Host "服务不存在: $ServiceName" -ForegroundColor Yellow
        Write-Host "可能已经被卸载" -ForegroundColor Yellow
        exit 0
    }
    
    # 获取服务路径
    $servicePath = ""
    try {
        $serviceWmi = Get-WmiObject -Class Win32_Service -Filter "Name='$ServiceName'"
        if ($serviceWmi) {
            $servicePath = $serviceWmi.PathName -replace '"', ''
            # 移除命令行参数
            if ($servicePath -match '^(.+\.exe)') {
                $servicePath = $matches[1]
            }
        }
    }
    catch {
        Write-Warning "无法获取服务路径: $($_.Exception.Message)"
    }
    
    Write-Host "服务名称: $ServiceName" -ForegroundColor White
    Write-Host "服务状态: $($service.Status)" -ForegroundColor White
    if (![string]::IsNullOrEmpty($servicePath)) {
        Write-Host "服务路径: $servicePath" -ForegroundColor White
    }
    Write-Host "删除数据: $RemoveData" -ForegroundColor White
    Write-Host "删除日志: $RemoveLogs" -ForegroundColor White
    Write-Host ""
    
    # 确认卸载
    if (!$Force) {
        Write-Warning "此操作将完全卸载 Event Processor V4.1 服务"
        if ($RemoveData) {
            Write-Warning "数据文件也将被删除"
        }
        if ($RemoveLogs) {
            Write-Warning "日志文件也将被删除"
        }
        
        $confirm = Read-Host "是否继续卸载? (Y/N)"
        if ($confirm -ne 'Y' -and $confirm -ne 'y') {
            Write-Host "卸载已取消" -ForegroundColor Yellow
            exit 0
        }
    }
    
    Write-Host "开始卸载..." -ForegroundColor Yellow
    Write-Host ""
    
    # 停止服务
    $stopped = Stop-EventProcessorService -Name $ServiceName
    
    # 等待一段时间确保服务完全停止
    Start-Sleep -Seconds 3
    
    # 删除服务
    $removed = Remove-EventProcessorService -Name $ServiceName
    
    if ($removed) {
        # 清理文件
        if (![string]::IsNullOrEmpty($servicePath)) {
            Remove-ServiceFiles -ServicePath $servicePath -RemoveData $RemoveData -RemoveLogs $RemoveLogs
        }
        
        # 清理注册表
        Remove-ServiceRegistryEntries -ServiceName $ServiceName
        
        Write-Host ""
        Write-Host "服务卸载完成!" -ForegroundColor Green
        Write-Host ""
        Write-Host "注意事项:" -ForegroundColor Cyan
        Write-Host "- 配置文件已保留，如需删除请手动清理" -ForegroundColor White
        if (!$RemoveLogs) {
            Write-Host "- 日志文件已保留，如需删除请使用 -RemoveLogs 参数" -ForegroundColor White
        }
        if (!$RemoveData) {
            Write-Host "- 数据文件已保留，如需删除请使用 -RemoveData 参数" -ForegroundColor White
        }
        Write-Host "- 重启计算机以完全清理所有相关资源" -ForegroundColor White
    }
    else {
        Write-Error "服务卸载失败"
        exit 1
    }
}
catch {
    Write-Error "卸载过程中发生错误: $($_.Exception.Message)"
    Write-Host "错误详情: $($_.Exception.StackTrace)" -ForegroundColor Red
    exit 1
}

Write-Host ""
Write-Host "卸载完成!" -ForegroundColor Green
