using System.Globalization;
using System.Text.RegularExpressions;
using EventProcessor.Core.Models;
using Microsoft.Extensions.Logging;

namespace EventProcessor.Core.Engine;

/// <summary>
/// 条件评估引擎 - 支持string、number、datetime三种数据类型和22种操作符
/// </summary>
public class ConditionEvaluator
{
    private readonly ILogger<ConditionEvaluator> _logger;

    /// <summary>
    /// 初始化条件评估引擎
    /// </summary>
    /// <param name="logger">日志记录器</param>
    public ConditionEvaluator(ILogger<ConditionEvaluator> logger)
    {
        _logger = logger;
    }

    /// <summary>
    /// 评估单个条件
    /// </summary>
    /// <param name="condition">条件定义</param>
    /// <param name="data">数据字典</param>
    /// <returns>评估结果</returns>
    public bool EvaluateCondition(Condition condition, Dictionary<string, object> data)
    {
        try
        {
            if (!data.TryGetValue(condition.FieldName, out var fieldValue))
            {
                _logger.LogWarning("字段不存在: {FieldName}, 条件ID: {ConditionId}", 
                                 condition.FieldName, condition.GetHashCode());
                return false;
            }

            return condition.DataType switch
            {
                "string" => EvaluateStringCondition(condition, fieldValue?.ToString() ?? ""),
                "number" => EvaluateNumberCondition(condition, fieldValue),
                "datetime" => EvaluateDateTimeCondition(condition, fieldValue),
                _ => throw new InvalidOperationException($"不支持的数据类型: {condition.DataType}")
            };
        }
        catch (FormatException ex)
        {
            _logger.LogError(ex, "数据格式错误 - 字段: {FieldName}, 值: {Value}, 期望类型: {DataType}", 
                           condition.FieldName, data.GetValueOrDefault(condition.FieldName), condition.DataType);
            return false;
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogError(ex, "操作符错误 - 操作符: {Operator}, 数据类型: {DataType}", 
                           condition.Operator, condition.DataType);
            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "条件评估异常 - 字段: {FieldName}, 操作符: {Operator}", 
                           condition.FieldName, condition.Operator);
            return false;
        }
    }

    /// <summary>
    /// 评估条件组
    /// </summary>
    /// <param name="group">条件组</param>
    /// <param name="data">数据字典</param>
    /// <returns>评估结果</returns>
    public bool EvaluateConditionGroup(ConditionGroup group, Dictionary<string, object> data)
    {
        try
        {
            var results = new List<bool>();

            if (group.Conditions != null)
            {
                foreach (var condition in group.Conditions)
                {
                    results.Add(EvaluateCondition(condition, data));
                }
            }

            return ApplyLogicOperator(group.LogicOperator, results);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "条件组评估异常: {GroupId}", group.GetHashCode());
            return false;
        }
    }

    /// <summary>
    /// 评估字符串条件
    /// </summary>
    private bool EvaluateStringCondition(Condition condition, string value)
    {
        return condition.Operator switch
        {
            "Equals" => value == condition.Value,
            "Equal" => value == condition.Value,  // 🔧 修复：支持Equal操作符（向后兼容）
            "Contains" => value.Contains(condition.Value),
            "StartsWith" => value.StartsWith(condition.Value),
            "EndsWith" => value.EndsWith(condition.Value),
            "Regex" => Regex.IsMatch(value, condition.Value),
            "In" => condition.Value.Split('|').Contains(value),
            "NotEquals" => value != condition.Value,
            "NotEqual" => value != condition.Value,  // 🔧 修复：支持NotEqual操作符（向后兼容）
            "IsEmpty" => string.IsNullOrEmpty(value),
            "IsNotEmpty" => !string.IsNullOrEmpty(value),
            _ => throw new InvalidOperationException($"字符串类型不支持操作符: {condition.Operator}")
        };
    }

    /// <summary>
    /// 评估数字条件
    /// </summary>
    private bool EvaluateNumberCondition(Condition condition, object? value)
    {
        if (!double.TryParse(value?.ToString(), out var numValue) ||
            !double.TryParse(condition.Value, out var conditionValue))
        {
            throw new FormatException($"无法将值转换为数字: {value} 或 {condition.Value}");
        }

        return condition.Operator switch
        {
            "Equals" => Math.Abs(numValue - conditionValue) < double.Epsilon,
            "NotEquals" => Math.Abs(numValue - conditionValue) >= double.Epsilon,
            "GreaterThan" => numValue > conditionValue,
            "LessThan" => numValue < conditionValue,
            "GreaterThanOrEqual" => numValue >= conditionValue,
            "LessThanOrEqual" => numValue <= conditionValue,
            "Between" => EvaluateNumberRange(numValue, condition.Value),
            _ => throw new InvalidOperationException($"数字类型不支持操作符: {condition.Operator}")
        };
    }

    /// <summary>
    /// 评估数字范围
    /// </summary>
    private bool EvaluateNumberRange(double value, string rangeValue)
    {
        var parts = rangeValue.Split(',');
        if (parts.Length != 2)
        {
            throw new FormatException($"数字范围格式错误，应为 'min,max': {rangeValue}");
        }

        if (!double.TryParse(parts[0], out var min) || !double.TryParse(parts[1], out var max))
        {
            throw new FormatException($"数字范围值无效: {rangeValue}");
        }

        return value >= min && value <= max;
    }

    /// <summary>
    /// 评估日期时间条件
    /// </summary>
    private bool EvaluateDateTimeCondition(Condition condition, object? value)
    {
        var dateValue = NormalizeDateTimeString(value?.ToString());
        var conditionDate = NormalizeDateTimeString(condition.Value);

        if (string.IsNullOrEmpty(dateValue) || string.IsNullOrEmpty(conditionDate))
        {
            throw new FormatException($"日期时间格式错误: {value} 或 {condition.Value}");
        }

        return condition.Operator switch
        {
            "Equals" => dateValue == conditionDate,
            "Before" => string.Compare(dateValue, conditionDate, StringComparison.Ordinal) < 0,
            "After" => string.Compare(dateValue, conditionDate, StringComparison.Ordinal) > 0,
            "Between" => EvaluateDateTimeRange(dateValue, condition.Value),
            "WithinMinutes" => EvaluateWithinTimespan(dateValue, condition.Value, "minutes"),
            "WithinHours" => EvaluateWithinTimespan(dateValue, condition.Value, "hours"),
            "WithinDays" => EvaluateWithinTimespan(dateValue, condition.Value, "days"),
            _ => throw new InvalidOperationException($"日期时间类型不支持操作符: {condition.Operator}")
        };
    }

    /// <summary>
    /// 标准化日期时间字符串为 YYYYMMDDHHMMSS 格式
    /// </summary>
    private string NormalizeDateTimeString(string? dateTimeStr)
    {
        if (string.IsNullOrEmpty(dateTimeStr)) return "";

        // 如果已经是标准格式，直接返回
        if (dateTimeStr.Length == 14 && dateTimeStr.All(char.IsDigit))
            return dateTimeStr;

        // 尝试解析各种格式并统一为 YYYYMMDDHHMMSS
        if (DateTime.TryParse(dateTimeStr, out var dt))
            return dt.ToString("yyyyMMddHHmmss");

        // 尝试解析时间戳（毫秒）
        if (long.TryParse(dateTimeStr, out var timestamp))
        {
            var dateTimeOffset = DateTimeOffset.FromUnixTimeMilliseconds(timestamp);
            return dateTimeOffset.ToString("yyyyMMddHHmmss");
        }

        return "";
    }

    /// <summary>
    /// 评估日期时间范围
    /// </summary>
    private bool EvaluateDateTimeRange(string dateValue, string rangeValue)
    {
        var parts = rangeValue.Split(',');
        if (parts.Length != 2)
        {
            throw new FormatException($"日期时间范围格式错误，应为 'start,end': {rangeValue}");
        }

        var startDate = NormalizeDateTimeString(parts[0]);
        var endDate = NormalizeDateTimeString(parts[1]);

        if (string.IsNullOrEmpty(startDate) || string.IsNullOrEmpty(endDate))
        {
            throw new FormatException($"日期时间范围值无效: {rangeValue}");
        }

        return string.Compare(dateValue, startDate, StringComparison.Ordinal) >= 0 &&
               string.Compare(dateValue, endDate, StringComparison.Ordinal) <= 0;
    }

    /// <summary>
    /// 评估时间跨度内条件
    /// </summary>
    private bool EvaluateWithinTimespan(string dateValue, string spanValue, string unit)
    {
        if (!int.TryParse(spanValue, out var span))
        {
            throw new FormatException($"时间跨度值无效: {spanValue}");
        }

        if (!DateTime.TryParseExact(dateValue, "yyyyMMddHHmmss", CultureInfo.InvariantCulture, 
                                   DateTimeStyles.None, out var targetDate))
        {
            throw new FormatException($"日期时间格式错误: {dateValue}");
        }

        var now = DateTime.UtcNow;
        var timeDiff = now - targetDate;

        return unit switch
        {
            "minutes" => Math.Abs(timeDiff.TotalMinutes) <= span,
            "hours" => Math.Abs(timeDiff.TotalHours) <= span,
            "days" => Math.Abs(timeDiff.TotalDays) <= span,
            _ => throw new InvalidOperationException($"不支持的时间单位: {unit}")
        };
    }

    /// <summary>
    /// 应用逻辑操作符
    /// </summary>
    private bool ApplyLogicOperator(string logicOperator, List<bool> results)
    {
        if (!results.Any()) return false;

        return logicOperator switch
        {
            "AND" => results.All(r => r),
            "OR" => results.Any(r => r),
            "NOT" => !results.First(),
            _ => throw new InvalidOperationException($"不支持的逻辑操作符: {logicOperator}")
        };
    }
}
