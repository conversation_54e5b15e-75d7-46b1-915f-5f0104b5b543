using System.Collections.Concurrent;
using System.Text.Json;
using EventProcessor.Core.Models;
using Microsoft.Extensions.Logging;

namespace EventProcessor.Core.Engine;

/// <summary>
/// 排除数据累积器 - 累积所有排除消息的数据
/// </summary>
public class ExclusionDataAccumulator
{
    private readonly ConcurrentDictionary<string, Dictionary<string, object>> _topicData = new();
    private readonly object _lock = new();
    private readonly ILogger<ExclusionDataAccumulator> _logger;

    /// <summary>
    /// 初始化排除数据累加器
    /// </summary>
    /// <param name="logger">日志记录器</param>
    public ExclusionDataAccumulator(ILogger<ExclusionDataAccumulator> logger)
    {
        _logger = logger;
    }

    /// <summary>
    /// 添加排除消息数据
    /// </summary>
    /// <param name="message">事件消息</param>
    public void AddMessage(EventMessage message)
    {
        try
        {
            lock (_lock)
            {
                var topic = message.Topic;
                var data = JsonSerializer.Deserialize<Dictionary<string, object>>(message.Payload);

                if (data == null)
                {
                    _logger.LogWarning("排除消息负载为空或无效: {Topic}", topic);
                    return;
                }

                if (!_topicData.ContainsKey(topic))
                    _topicData[topic] = new Dictionary<string, object>();

                // 合并字段数据（新字段覆盖旧字段）
                foreach (var kvp in data)
                {
                    _topicData[topic][kvp.Key] = kvp.Value;
                }

                _logger.LogDebug("排除数据已添加: {Topic}, 字段数: {FieldCount}", topic, data.Count);
            }
        }
        catch (JsonException ex)
        {
            _logger.LogError(ex, "排除消息JSON解析失败: {Topic}, {Payload}", message.Topic, message.Payload);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "添加排除消息数据时发生异常: {Topic}", message.Topic);
        }
    }

    /// <summary>
    /// 获取合并后的所有数据
    /// </summary>
    /// <returns>合并的数据字典</returns>
    public Dictionary<string, object> GetCombinedData()
    {
        lock (_lock)
        {
            var combined = new Dictionary<string, object>();

            // 合并所有主题的数据
            foreach (var topicData in _topicData.Values)
            {
                foreach (var kvp in topicData)
                {
                    combined[kvp.Key] = kvp.Value;
                }
            }

            _logger.LogDebug("排除数据合并完成，总字段数: {FieldCount}", combined.Count);
            return combined;
        }
    }

    /// <summary>
    /// 获取指定主题的数据
    /// </summary>
    /// <param name="topic">主题名称</param>
    /// <returns>主题数据</returns>
    public Dictionary<string, object> GetTopicData(string topic)
    {
        lock (_lock)
        {
            return _topicData.TryGetValue(topic, out var data) 
                ? new Dictionary<string, object>(data) 
                : new Dictionary<string, object>();
        }
    }

    /// <summary>
    /// 清空所有数据
    /// </summary>
    public void Clear()
    {
        lock (_lock)
        {
            _topicData.Clear();
            _logger.LogDebug("排除数据累积器已清空");
        }
    }

    /// <summary>
    /// 获取统计信息
    /// </summary>
    public DataAccumulatorStatistics GetStatistics()
    {
        lock (_lock)
        {
            return new DataAccumulatorStatistics
            {
                TopicCount = _topicData.Count,
                TotalFieldCount = _topicData.Values.Sum(d => d.Count),
                Topics = _topicData.Keys.ToArray()
            };
        }
    }
}

/// <summary>
/// 业务数据累积器 - 使用最新值覆盖策略
/// </summary>
public class BusinessDataAccumulator
{
    private readonly Dictionary<string, object> _latestData = new();
    private readonly object _lock = new();
    private readonly ILogger<BusinessDataAccumulator> _logger;
    private DateTime _lastUpdateTime = DateTime.UtcNow;

    /// <summary>
    /// 初始化业务数据累加器
    /// </summary>
    /// <param name="logger">日志记录器</param>
    public BusinessDataAccumulator(ILogger<BusinessDataAccumulator> logger)
    {
        _logger = logger;
    }

    /// <summary>
    /// 更新业务消息数据 - 智能处理ajb消息格式
    /// </summary>
    /// <param name="message">事件消息</param>
    public void UpdateMessage(EventMessage message)
    {
        try
        {
            lock (_lock)
            {
                Dictionary<string, object> data;

                // 智能识别ajb消息并使用专门的适配器处理
                if (IsAjbMessage(message.Topic))
                {
                    _logger.LogDebug("检测到AJB消息，使用专门适配器处理: {Topic}", message.Topic);
                    data = AdaptAjbMessage(message.Payload);
                }
                else
                {
                    // 对于非ajb消息，使用原有的JSON反序列化逻辑
                    data = JsonSerializer.Deserialize<Dictionary<string, object>>(message.Payload) ?? new Dictionary<string, object>();
                }

                if (data == null || data.Count == 0)
                {
                    _logger.LogWarning("业务消息处理后无有效数据: {Topic}", message.Topic);
                    return;
                }

                // 业务数据使用最新值覆盖
                foreach (var kvp in data)
                {
                    _latestData[kvp.Key] = kvp.Value;
                }

                _lastUpdateTime = DateTime.UtcNow;
                _logger.LogDebug("业务数据已更新: {Topic}, 字段数: {FieldCount}, 包含duration: {HasDuration}",
                    message.Topic, data.Count, data.ContainsKey("duration"));

                // 记录关键字段的值（用于调试ajb消息处理）
                if (IsAjbMessage(message.Topic))
                {
                    LogAjbKeyFields(data);
                }
            }
        }
        catch (JsonException ex)
        {
            _logger.LogError(ex, "业务消息JSON解析失败: {Topic}, {Payload}", message.Topic, GetPayloadPreview(message.Payload));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "更新业务消息数据时发生异常: {Topic}", message.Topic);
        }
    }

    /// <summary>
    /// 记录AJB消息的关键字段值（用于调试）
    /// </summary>
    private void LogAjbKeyFields(Dictionary<string, object> data)
    {
        var keyFields = new[] { "CardType", "log_car_no", "log_remain_days", "Expire", "duration" };
        var fieldValues = new List<string>();

        foreach (var field in keyFields)
        {
            if (data.TryGetValue(field, out var value))
            {
                fieldValues.Add($"{field}={value}");
            }
            else
            {
                fieldValues.Add($"{field}=MISSING");
            }
        }

        _logger.LogDebug("AJB关键字段: {KeyFields}", string.Join(", ", fieldValues));
    }

    /// <summary>
    /// 获取负载预览（用于日志）
    /// </summary>
    private string GetPayloadPreview(string payload)
    {
        return payload.Length > 200 ? payload.Substring(0, 200) + "..." : payload;
    }

    /// <summary>
    /// 检查消息是否来自ajb系统
    /// </summary>
    private static bool IsAjbMessage(string topic)
    {
        return topic.StartsWith("ajb/", StringComparison.OrdinalIgnoreCase) &&
               topic.EndsWith("/time_log", StringComparison.OrdinalIgnoreCase);
    }

    /// <summary>
    /// 适配ajb消息格式，解决报告中的嵌套JSON和duration计算问题
    /// </summary>
    private Dictionary<string, object> AdaptAjbMessage(string jsonPayload)
    {
        var result = new Dictionary<string, object>();

        try
        {
            using var document = JsonDocument.Parse(jsonPayload);
            var root = document.RootElement;

            // 1. 提取CarInfo字段（解决嵌套路径问题）
            if (TryGetNestedElement(root, "response_payload.data.CarInfo", out var carInfo))
            {
                ExtractCarInfoFields(carInfo, result);
            }

            // 2. 提取Charge信息并计算duration（解决duration计算问题）
            if (TryGetNestedElement(root, "response_payload.data.Charge", out var charge))
            {
                ExtractChargeFields(charge, result);
            }

            // 3. 保留其他有用字段
            ExtractTopLevelFields(root, result);

            _logger.LogDebug("AJB消息适配完成: 提取字段数={FieldCount}", result.Count);
        }
        catch (JsonException ex)
        {
            _logger.LogError(ex, "AJB消息JSON解析失败");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "AJB消息适配异常");
        }

        return result;
    }

    /// <summary>
    /// 获取最新的业务数据
    /// </summary>
    /// <returns>最新数据字典</returns>
    public Dictionary<string, object> GetLatestData()
    {
        lock (_lock)
        {
            var result = new Dictionary<string, object>(_latestData);
            _logger.LogDebug("获取业务数据，字段数: {FieldCount}", result.Count);
            return result;
        }
    }

    /// <summary>
    /// 获取指定字段的值
    /// </summary>
    /// <param name="fieldName">字段名称</param>
    /// <returns>字段值</returns>
    public object? GetFieldValue(string fieldName)
    {
        lock (_lock)
        {
            return _latestData.TryGetValue(fieldName, out var value) ? value : null;
        }
    }

    /// <summary>
    /// 检查是否包含指定字段
    /// </summary>
    /// <param name="fieldName">字段名称</param>
    /// <returns>是否包含</returns>
    public bool ContainsField(string fieldName)
    {
        lock (_lock)
        {
            return _latestData.ContainsKey(fieldName);
        }
    }

    /// <summary>
    /// 清空所有数据
    /// </summary>
    public void Clear()
    {
        lock (_lock)
        {
            _latestData.Clear();
            _lastUpdateTime = DateTime.UtcNow;
            _logger.LogDebug("业务数据累积器已清空");
        }
    }

    /// <summary>
    /// 获取统计信息
    /// </summary>
    public DataAccumulatorStatistics GetStatistics()
    {
        lock (_lock)
        {
            return new DataAccumulatorStatistics
            {
                TopicCount = 1, // 业务数据只有一个逻辑主题
                TotalFieldCount = _latestData.Count,
                LastUpdateTime = _lastUpdateTime,
                Topics = new[] { "BusinessData" }
            };
        }
    }

    /// <summary>
    /// 尝试获取嵌套JSON元素
    /// </summary>
    private static bool TryGetNestedElement(JsonElement root, string path, out JsonElement element)
    {
        element = root;
        var parts = path.Split('.');

        foreach (var part in parts)
        {
            if (element.TryGetProperty(part, out var nextElement))
            {
                element = nextElement;
            }
            else
            {
                element = default;
                return false;
            }
        }

        return true;
    }

    /// <summary>
    /// 提取JSON值并转换为合适的.NET类型
    /// </summary>
    private static object? ExtractJsonValue(JsonElement element)
    {
        return element.ValueKind switch
        {
            JsonValueKind.String => element.GetString(),
            JsonValueKind.Number => element.TryGetInt64(out var longValue) ? longValue : element.GetDouble(),
            JsonValueKind.True => true,
            JsonValueKind.False => false,
            JsonValueKind.Null => null,
            _ => element.GetRawText()
        };
    }

    /// <summary>
    /// 从ParkTime文本解析停车时长（分钟）
    /// </summary>
    private static int ParseDurationFromParkTime(string parkTimeText)
    {
        if (string.IsNullOrWhiteSpace(parkTimeText))
            return 0;

        try
        {
            var totalMinutes = 0;

            // 匹配小时：支持 "2小时" 或 "2时"
            var hourMatch = System.Text.RegularExpressions.Regex.Match(parkTimeText, @"(\d+)[小时时]");
            if (hourMatch.Success)
            {
                totalMinutes += int.Parse(hourMatch.Groups[1].Value) * 60;
            }

            // 匹配分钟：支持 "30分钟" 或 "30分"
            var minuteMatch = System.Text.RegularExpressions.Regex.Match(parkTimeText, @"(\d+)分");
            if (minuteMatch.Success)
            {
                totalMinutes += int.Parse(minuteMatch.Groups[1].Value);
            }

            return totalMinutes;
        }
        catch (Exception)
        {
            return 0;
        }
    }

    /// <summary>
    /// 提取CarInfo字段
    /// </summary>
    private void ExtractCarInfoFields(JsonElement carInfo, Dictionary<string, object> result)
    {
        var fieldMappings = new Dictionary<string, string>
        {
            ["CardType"] = "CardType",
            ["CarNo"] = "log_car_no",
            ["Name"] = "log_user_name",
            ["UserName"] = "log_user_name",
            ["RemainDays"] = "log_remain_days",
            ["EndTime"] = "log_end_time",
            ["Intime"] = "log_in_time"
        };

        foreach (var mapping in fieldMappings)
        {
            if (carInfo.TryGetProperty(mapping.Key, out var property))
            {
                var value = ExtractJsonValue(property);
                if (value != null)
                {
                    result[mapping.Value] = value;
                }
            }
        }
    }

    /// <summary>
    /// 提取Charge字段并计算duration
    /// </summary>
    private void ExtractChargeFields(JsonElement charge, Dictionary<string, object> result)
    {
        if (charge.TryGetProperty("ParkTime", out var parkTimeProperty))
        {
            var parkTimeText = parkTimeProperty.GetString();
            if (!string.IsNullOrEmpty(parkTimeText))
            {
                var durationMinutes = ParseDurationFromParkTime(parkTimeText);
                result["duration"] = durationMinutes;
                result["ParkTime"] = parkTimeText;

                _logger.LogDebug("计算duration: ParkTime='{ParkTime}' -> duration={Duration}分钟",
                    parkTimeText, durationMinutes);
            }
        }

        if (charge.TryGetProperty("StartTime", out var startTime))
        {
            result["log_start_time"] = startTime.GetString() ?? "";
        }

        if (charge.TryGetProperty("EndTime", out var endTime))
        {
            result["log_charge_end_time"] = endTime.GetString() ?? "";
        }

        // 🔧 修复：提取Expire字段 - 解决EV001001业务规则评估失败问题
        if (charge.TryGetProperty("Expire", out var expire))
        {
            result["Expire"] = expire.GetString() ?? "";
            _logger.LogDebug("提取Expire字段: Expire='{Expire}'", result["Expire"]);
        }
    }

    /// <summary>
    /// 提取顶级字段
    /// </summary>
    private void ExtractTopLevelFields(JsonElement root, Dictionary<string, object> result)
    {
        // 🔧 修复：添加EV001002过期车消息的关键字段
        var topLevelFields = new[] {
            "log_original_timestamp",
            "log_event_type",
            "timestamp",
            "log_remaining_days",  // EV001002关键字段
            "log_reminder_text",   // EV001002关键字段
            "log_send_day_num"     // EV001002辅助字段
        };

        foreach (var fieldName in topLevelFields)
        {
            if (root.TryGetProperty(fieldName, out var property))
            {
                var value = ExtractJsonValue(property);
                if (value != null)
                {
                    result[fieldName] = value;
                    _logger.LogDebug("提取顶层字段: {FieldName}='{Value}'", fieldName, value);
                }
            }
        }
    }
}

/// <summary>
/// AI结果数据累积器
/// </summary>
public class AIResultDataAccumulator
{
    private Dictionary<string, object> _resultData = new();
    private readonly object _lock = new();
    private readonly ILogger<AIResultDataAccumulator> _logger;
    private DateTime _lastUpdateTime = DateTime.UtcNow;
    private bool _hasResult = false;

    /// <summary>
    /// 初始化AI结果数据累加器
    /// </summary>
    /// <param name="logger">日志记录器</param>
    public AIResultDataAccumulator(ILogger<AIResultDataAccumulator> logger)
    {
        _logger = logger;
    }

    /// <summary>
    /// 设置AI结果数据
    /// </summary>
    /// <param name="resultData">AI结果数据</param>
    public void SetResult(Dictionary<string, object> resultData)
    {
        try
        {
            lock (_lock)
            {
                _resultData = new Dictionary<string, object>(resultData);
                _lastUpdateTime = DateTime.UtcNow;
                _hasResult = true;

                _logger.LogDebug("AI结果数据已设置，字段数: {FieldCount}", resultData.Count);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "设置AI结果数据时发生异常");
        }
    }

    /// <summary>
    /// 获取AI结果数据
    /// </summary>
    /// <returns>AI结果数据</returns>
    public Dictionary<string, object> GetResultData()
    {
        lock (_lock)
        {
            return new Dictionary<string, object>(_resultData);
        }
    }

    /// <summary>
    /// 是否有AI结果
    /// </summary>
    public bool HasResult
    {
        get
        {
            lock (_lock)
            {
                return _hasResult;
            }
        }
    }

    /// <summary>
    /// 清空AI结果
    /// </summary>
    public void Clear()
    {
        lock (_lock)
        {
            _resultData.Clear();
            _hasResult = false;
            _lastUpdateTime = DateTime.UtcNow;
            _logger.LogDebug("AI结果数据累积器已清空");
        }
    }

    /// <summary>
    /// 获取统计信息
    /// </summary>
    public DataAccumulatorStatistics GetStatistics()
    {
        lock (_lock)
        {
            return new DataAccumulatorStatistics
            {
                TopicCount = _hasResult ? 1 : 0,
                TotalFieldCount = _resultData.Count,
                LastUpdateTime = _lastUpdateTime,
                Topics = _hasResult ? new[] { "AIResult" } : Array.Empty<string>()
            };
        }
    }
}

/// <summary>
/// 数据累加器统计信息
/// </summary>
public record DataAccumulatorStatistics
{
    /// <summary>
    /// 主题数量
    /// </summary>
    public int TopicCount { get; init; }
    
    /// <summary>
    /// 总字段数量
    /// </summary>
    public int TotalFieldCount { get; init; }
    
    /// <summary>
    /// 最后更新时间
    /// </summary>
    public DateTime LastUpdateTime { get; init; } = DateTime.UtcNow;
    
    /// <summary>
    /// 主题列表
    /// </summary>
    public string[] Topics { get; init; } = Array.Empty<string>();
}
