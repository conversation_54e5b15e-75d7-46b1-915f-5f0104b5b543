using System.Collections.Concurrent;
using EventProcessor.Core.Models;
using EventProcessor.Core.Services;
using Microsoft.Extensions.Logging;

namespace EventProcessor.Core.Engine;

/// <summary>
/// 事件管理器 - 管理所有EventStateAggregator实例的生命周期
/// 解决消息乱序问题，支持任意消息类型触发事件创建
/// </summary>
public class EventManager : IDisposable
{
    private readonly ConcurrentDictionary<string, EventStateAggregator> _activeEvents = new();
    private readonly EventConfiguration _config;
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger<EventManager> _logger;
    private readonly Timer _cleanupTimer;
    private readonly object _lockObject = new();
    private bool _disposed = false;

    /// <summary>
    /// 初始化事件管理器
    /// </summary>
    /// <param name="config">事件配置</param>
    /// <param name="serviceProvider">服务提供器</param>
    /// <param name="logger">日志记录器</param>
    public EventManager(
        EventConfiguration config,
        IServiceProvider serviceProvider,
        ILogger<EventManager> logger)
    {
        _config = config;
        _serviceProvider = serviceProvider;
        _logger = logger;

        // 启动清理定时器，每5分钟清理一次过期的事件实例
        _cleanupTimer = new Timer(CleanupExpiredEvents, null, TimeSpan.FromMinutes(5), TimeSpan.FromMinutes(5));

        _logger.LogInformation("EventManager已初始化，事件ID: {EventId}", _config.EventId);
    }

    /// <summary>
    /// 获取或创建事件聚合器 - 任何消息类型都可以触发创建
    /// </summary>
    /// <param name="message">事件消息</param>
    /// <returns>事件状态聚合器</returns>
    public EventStateAggregator GetOrCreateAggregator(EventMessage message)
    {
        var correlationId = GenerateCorrelationId(message);

        return _activeEvents.GetOrAdd(correlationId, _ =>
        {
            _logger.LogInformation("创建新的事件聚合器: {CorrelationId}, 触发消息类型: {MessageType}", 
                                 correlationId, message.MessageType);

            var aggregator = new EventStateAggregator(correlationId, _config, _serviceProvider);
            
            // 订阅聚合器完成事件，用于自动清理
            aggregator.OnCompleted += (sender, args) =>
            {
                RemoveAggregator(correlationId);
            };

            return aggregator;
        });
    }

    /// <summary>
    /// 生成事件关联ID - 支持可配置的时间窗口策略
    /// </summary>
    /// <param name="message">事件消息</param>
    /// <returns>关联ID</returns>
    private string GenerateCorrelationId(EventMessage message)
    {
        // 对于单点、单事件类型的EP，CorrelationId是恒定的，代表一个单例的处理实例。
        // 这样可以确保所有相关的消息都路由到同一个聚合器，无论它们之间的时间差是多少。
        return $"{message.CommId}_{message.PositionId}_{message.EventId}";
    }

    /// <summary>
    /// 计算时间窗口的起始时间，避免跨窗口边界问题
    /// </summary>
    /// <param name="currentTime">当前时间</param>
    /// <param name="windowSize">窗口大小</param>
    /// <returns>窗口起始时间</returns>
    private DateTime GetTimeWindowStart(DateTime currentTime, TimeSpan windowSize)
    {
        var totalMinutes = (long)windowSize.TotalMinutes;
        var currentMinute = currentTime.Hour * 60 + currentTime.Minute;
        var windowStartMinute = (currentMinute / totalMinutes) * totalMinutes;

        return new DateTime(currentTime.Year, currentTime.Month, currentTime.Day,
                           (int)(windowStartMinute / 60), (int)(windowStartMinute % 60), 0, DateTimeKind.Utc);
    }

    /// <summary>
    /// 移除事件聚合器
    /// </summary>
    /// <param name="correlationId">关联ID</param>
    public void RemoveAggregator(string correlationId)
    {
        if (_activeEvents.TryRemove(correlationId, out var aggregator))
        {
            try
            {
                aggregator.Dispose();
                _logger.LogInformation("事件聚合器已移除: {CorrelationId}", correlationId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "释放事件聚合器时发生异常: {CorrelationId}", correlationId);
            }
        }
    }

    /// <summary>
    /// 获取活跃事件数量
    /// </summary>
    public int ActiveEventCount => _activeEvents.Count;

    /// <summary>
    /// 获取所有活跃事件的关联ID
    /// </summary>
    public string[] GetActiveEventIds()
    {
        return _activeEvents.Keys.ToArray();
    }

    /// <summary>
    /// 获取事件统计信息
    /// </summary>
    public EventManagerStatistics GetStatistics()
    {
        var statistics = new EventManagerStatistics
        {
            ActiveEventCount = _activeEvents.Count,
            TotalEventsProcessed = GetTotalEventsProcessed(),
            EventsByState = GetEventsByState(),
            AverageProcessingTime = GetAverageProcessingTime(),
            LastCleanupTime = _lastCleanupTime
        };

        return statistics;
    }

    /// <summary>
    /// 清理过期的事件实例
    /// </summary>
    private void CleanupExpiredEvents(object? state)
    {
        if (_disposed) return;

        try
        {
            lock (_lockObject)
            {
                var expiredEvents = new List<string>();
                var cutoffTime = DateTime.UtcNow.AddMinutes(-30); // 30分钟超时

                foreach (var kvp in _activeEvents)
                {
                    if (kvp.Value.LastActivityTime < cutoffTime)
                    {
                        expiredEvents.Add(kvp.Key);
                    }
                }

                foreach (var correlationId in expiredEvents)
                {
                    RemoveAggregator(correlationId);
                    _logger.LogWarning("清理过期事件实例: {CorrelationId}", correlationId);
                }

                _lastCleanupTime = DateTime.UtcNow;

                if (expiredEvents.Count > 0)
                {
                    _logger.LogInformation("清理完成，移除 {Count} 个过期事件实例", expiredEvents.Count);
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "清理过期事件实例时发生异常");
        }
    }

    private DateTime _lastCleanupTime = DateTime.UtcNow;
    private long _totalEventsProcessed = 0;

    private long GetTotalEventsProcessed()
    {
        return Interlocked.Read(ref _totalEventsProcessed);
    }

    private Dictionary<string, int> GetEventsByState()
    {
        var stateCount = new Dictionary<string, int>();

        foreach (var aggregator in _activeEvents.Values)
        {
            var state = aggregator.CurrentState.ToString();
            stateCount[state] = stateCount.GetValueOrDefault(state, 0) + 1;
        }

        return stateCount;
    }

    private double GetAverageProcessingTime()
    {
        var processingTimes = _activeEvents.Values
            .Where(a => a.ProcessingTime.HasValue)
            .Select(a => a.ProcessingTime!.Value.TotalMilliseconds)
            .ToArray();

        return processingTimes.Length > 0 ? processingTimes.Average() : 0;
    }

    /// <summary>
    /// 强制清理所有事件实例
    /// </summary>
    public void ClearAllEvents()
    {
        lock (_lockObject)
        {
            var allEvents = _activeEvents.Keys.ToArray();
            foreach (var correlationId in allEvents)
            {
                RemoveAggregator(correlationId);
            }

            _logger.LogWarning("强制清理所有事件实例，共 {Count} 个", allEvents.Length);
        }
    }

    /// <summary>
    /// 释放事件管理器资源
    /// </summary>
    public void Dispose()
    {
        Dispose(true);
        GC.SuppressFinalize(this);
    }

    /// <summary>
    /// 释放资源的具体实现
    /// </summary>
    /// <param name="disposing">是否释放托管资源</param>
    protected virtual void Dispose(bool disposing)
    {
        if (!_disposed && disposing)
        {
            try
            {
                _cleanupTimer?.Dispose();
                ClearAllEvents();
                _logger.LogInformation("EventManager已释放");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "释放EventManager时发生异常");
            }
            finally
            {
                _disposed = true;
            }
        }
    }
}

/// <summary>
/// 事件管理器统计信息
/// </summary>
public record EventManagerStatistics
{
    /// <summary>
    /// 活跃事件数量
    /// </summary>
    public int ActiveEventCount { get; init; }
    
    /// <summary>
    /// 已处理事件总数
    /// </summary>
    public long TotalEventsProcessed { get; init; }
    
    /// <summary>
    /// 按状态分组的事件数量
    /// </summary>
    public Dictionary<string, int> EventsByState { get; init; } = new();
    
    /// <summary>
    /// 平均处理时间（毫秒）
    /// </summary>
    public double AverageProcessingTime { get; init; }
    
    /// <summary>
    /// 最后清理时间
    /// </summary>
    public DateTime LastCleanupTime { get; init; }
}
