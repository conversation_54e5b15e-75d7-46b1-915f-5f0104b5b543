using System.Text.Json;
using EventProcessor.Core.Models;
using EventProcessor.Core.Services;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;

namespace EventProcessor.Core.Engine;

/// <summary>
/// 事件状态聚合器 - EP_V4.1增强版本
/// 包含增强的状态机、消息处理、告警静默期机制和告警撤销功能
/// </summary>
public class EventStateAggregator : IDisposable
{
    private readonly string _correlationId;
    private readonly EventConfiguration _config;
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger<EventStateAggregator> _logger;
    private readonly RuleEngine _ruleEngine;
    private readonly IMqttService _mqttService;
    private readonly IAlarmGenerator _alarmGenerator;
    private readonly IStandardAlarmGenerator _standardAlarmGenerator;
    private readonly RuleEvaluationCircuitBreaker _circuitBreaker;

    // 状态管理
    private EventProcessingState _currentState = EventProcessingState.Initializing;
    private readonly object _lockObject = new();
    private bool _disposed = false;

    // 数据累积器
    private readonly ExclusionDataAccumulator _exclusionDataAccumulator;
    private readonly BusinessDataAccumulator _businessDataAccumulator;
    private readonly AIResultDataAccumulator _aiResultDataAccumulator;

    // 规则状态
    private BusinessRuleState? _businessRuleState;
    private AIRuleState? _aiRuleState;

    // 定时器
    private Timer? _holdingTimer;
    private Timer? _aiAnalysisTimer;
    private Timer? _gracePeriodTimer;

    // 设备信号状态
    private bool _deviceSignalReceived = false;
    private Dictionary<string, object> _deviceData = new();

    // AI处理状态
    private bool _ignoreAIResult = false;
    private CancellationTokenSource? _aiRequestCancellation;

    // 时间跟踪
    private DateTime _creationTime = DateTime.UtcNow;
    private DateTime _lastActivityTime = DateTime.UtcNow;
    private TimeSpan? _processingTime;

    // 事件
    /// <summary>
    /// 事件完成时触发
    /// </summary>
    public event EventHandler<EventCompletedEventArgs>? OnCompleted;
    
    /// <summary>
    /// 告警生成时触发
    /// </summary>
    public event EventHandler<AlarmGeneratedEventArgs>? OnAlarmGenerated;
    
    /// <summary>
    /// 状态变更时触发
    /// </summary>
    public event EventHandler<StateChangedEventArgs>? OnStateChanged;

    /// <summary>
    /// 初始化事件状态聚合器
    /// </summary>
    /// <param name="correlationId">关联ID</param>
    /// <param name="config">事件配置</param>
    /// <param name="serviceProvider">服务提供器</param>
    public EventStateAggregator(
        string correlationId,
        EventConfiguration config,
        IServiceProvider serviceProvider)
    {
        _correlationId = correlationId;
        _config = config;
        _serviceProvider = serviceProvider;
        _logger = serviceProvider.GetRequiredService<ILogger<EventStateAggregator>>();
        _ruleEngine = serviceProvider.GetRequiredService<RuleEngine>();
        _mqttService = serviceProvider.GetRequiredService<IMqttService>();
        _alarmGenerator = serviceProvider.GetRequiredService<IAlarmGenerator>();
        _standardAlarmGenerator = serviceProvider.GetRequiredService<IStandardAlarmGenerator>();
        _circuitBreaker = new RuleEvaluationCircuitBreaker(
            serviceProvider.GetRequiredService<ILogger<RuleEvaluationCircuitBreaker>>());

        // 初始化数据累积器
        _exclusionDataAccumulator = new ExclusionDataAccumulator(
            serviceProvider.GetRequiredService<ILogger<ExclusionDataAccumulator>>());
        _businessDataAccumulator = new BusinessDataAccumulator(
            serviceProvider.GetRequiredService<ILogger<BusinessDataAccumulator>>());
        _aiResultDataAccumulator = new AIResultDataAccumulator(
            serviceProvider.GetRequiredService<ILogger<AIResultDataAccumulator>>());

        _logger.LogInformation("EventStateAggregator已创建: {CorrelationId}, 事件ID: {EventId}", 
                             _correlationId, _config.EventId);
    }

    /// <summary>
    /// 当前状态
    /// </summary>
    public EventProcessingState CurrentState
    {
        get
        {
            lock (_lockObject)
            {
                return _currentState;
            }
        }
    }

    /// <summary>
    /// 关联ID
    /// </summary>
    public string CorrelationId => _correlationId;

    /// <summary>
    /// 最后活动时间
    /// </summary>
    public DateTime LastActivityTime
    {
        get
        {
            lock (_lockObject)
            {
                return _lastActivityTime;
            }
        }
    }

    /// <summary>
    /// 处理时间
    /// </summary>
    public TimeSpan? ProcessingTime
    {
        get
        {
            lock (_lockObject)
            {
                return _processingTime;
            }
        }
    }

    /// <summary>
    /// 是否已完成
    /// </summary>
    public bool IsCompleted
    {
        get
        {
            lock (_lockObject)
            {
                return _currentState == EventProcessingState.Alarmed || 
                       _currentState == EventProcessingState.Excluded;
            }
        }
    }

    /// <summary>
    /// 处理消息的主入口
    /// </summary>
    /// <param name="message">事件消息</param>
    public async Task ProcessMessage(EventMessage message)
    {
        try
        {
            // 消息格式验证
            if (!ValidateMessageFormat(message))
            {
                _logger.LogWarning("消息格式无效，忽略处理: {MessageType}, {PayloadPreview}", 
                                 message.MessageType, GetPayloadPreview(message.Payload));
                return;
            }

            bool needsTransition = false;

            lock (_lockObject)
            {
                // 更新活动时间
                _lastActivityTime = DateTime.UtcNow;

                // 检查是否应该忽略消息
                if (ShouldIgnoreMessage(message))
                {
                    return;
                }

                // 根据消息类型分别处理
                switch (message.MessageType)
                {
                    case "DeviceSignal":
                        needsTransition = HandleDeviceSignal(message);
                        break;
                    case "ExclusionData":
                        HandleExclusionMessage(message);
                        break;
                    case "BusinessData":
                        HandleBusinessMessage(message);
                        break;
                    case "AIResult":
                        HandleAIResultMessage(message);
                        break;
                    default:
                        _logger.LogWarning("未知的消息类型: {MessageType}", message.MessageType);
                        return;
                }

                // V4.1修复：移除实时决策评估，改为仅在超时时决策
                // 数据收集和决策分离，只有HoldingTimerCallback可以触发告警决策
            }

            // 在 lock 外处理异步操作
            if (needsTransition)
            {
                await TransitionToCollecting();
            }
        }
        catch (JsonException ex)
        {
            _logger.LogError(ex, "JSON解析错误 - Topic: {Topic}, Payload: {Payload}", 
                           message.Topic, message.Payload);
        }
        catch (ArgumentException ex)
        {
            _logger.LogError(ex, "消息参数错误 - MessageType: {MessageType}", message.MessageType);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "消息处理异常 - CorrelationId: {CorrelationId}, MessageType: {MessageType}", 
                           _correlationId, message.MessageType);
        }
    }

    /// <summary>
    /// 验证消息格式
    /// </summary>
    private bool ValidateMessageFormat(EventMessage message)
    {
        if (string.IsNullOrEmpty(message.MessageType))
        {
            _logger.LogWarning("MessageType为空");
            return false;
        }

        if (string.IsNullOrEmpty(message.Payload))
        {
            _logger.LogWarning("消息负载为空: {MessageType}", message.MessageType);
            return false;
        }

        // 根据消息类型进行特定验证
        return message.MessageType switch
        {
            "DeviceSignal" => ValidateDeviceSignalMessage(message),
            "ExclusionData" => ValidateDataMessage(message, "ExclusionData"),
            "BusinessData" => ValidateDataMessage(message, "BusinessData"),
            "AIResult" => ValidateAIResultMessage(message),
            _ => false
        };
    }

    /// <summary>
    /// 验证设备信号消息
    /// </summary>
    private bool ValidateDeviceSignalMessage(EventMessage message)
    {
        try
        {
            var data = JsonSerializer.Deserialize<Dictionary<string, object>>(message.Payload);
            return data != null && data.Count > 0;
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    /// 验证数据消息
    /// </summary>
    private bool ValidateDataMessage(EventMessage message, string messageType)
    {
        try
        {
            var data = JsonSerializer.Deserialize<Dictionary<string, object>>(message.Payload);
            return data != null;
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    /// 验证AI结果消息
    /// </summary>
    private bool ValidateAIResultMessage(EventMessage message)
    {
        try
        {
            var aiResult = JsonSerializer.Deserialize<AIResultMessage>(message.Payload);
            return aiResult != null && !string.IsNullOrEmpty(aiResult.RequestId);
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    /// 获取负载预览
    /// </summary>
    private string GetPayloadPreview(string payload)
    {
        return payload?.Length > 100 ? payload.Substring(0, 100) + "..." : payload ?? "null";
    }

    /// <summary>
    /// 检查是否应该忽略消息
    /// </summary>
    private bool ShouldIgnoreMessage(EventMessage message)
    {
        // 已排除的事件完全忽略所有消息
        if (_currentState == EventProcessingState.Excluded)
        {
            _logger.LogDebug("事件已被排除，忽略消息: {MessageType}", message.MessageType);
            return true;
        }

        // 已告警的事件只接受排除消息用于告警撤销
        if (_currentState == EventProcessingState.Alarmed && message.MessageType != "ExclusionData")
        {
            _logger.LogDebug("事件已告警，只接受排除消息，忽略: {MessageType}", message.MessageType);
            return true;
        }

        return false;
    }

    /// <summary>
    /// 处理设备信号消息
    /// </summary>
    /// <returns>是否需要状态转换</returns>
    private bool HandleDeviceSignal(EventMessage message)
    {
        try
        {
            var deviceData = JsonSerializer.Deserialize<Dictionary<string, object>>(message.Payload);
            if (deviceData == null)
            {
                _logger.LogWarning("设备信号数据为空: {Topic}", message.Topic);
                return false;
            }

            // 保存设备数据
            _deviceData = deviceData;

            // V4.1修复：添加触发和解除信号检测
            var triggerField = _config.DeviceSignal?.TriggerField;
            if (!string.IsNullOrEmpty(triggerField) && deviceData.TryGetValue(triggerField, out var fieldValue))
            {
                var triggerValues = _config.DeviceSignal?.TriggerValues;
                var stringValue = fieldValue?.ToString();
                
                // 检查是否为触发值
                bool isTrigger = triggerValues?.ContainsKey("true") == true && 
                                triggerValues["true"] == stringValue;
                
                // 检查是否为解除值  
                bool isRelease = triggerValues?.ContainsKey("false") == true && 
                                triggerValues["false"] == stringValue;
                
                if (isTrigger && _currentState == EventProcessingState.Initializing)
                {
                    // 开始事件，启动定时器
                    _deviceSignalReceived = true;
                    _logger.LogInformation("收到触发信号，开始事件: {CorrelationId}, {TriggerField}={Value}", 
                        _correlationId, triggerField, stringValue);
                    return true; // 需要状态转换到Collecting
                }
                else if (isRelease && _currentState == EventProcessingState.Collecting)
                {
                    // V4.1修复关键：解除信号立即取消事件
                    _logger.LogInformation("收到解除信号，事件在超时前被取消: {CorrelationId}, {TriggerField}={Value}", 
                        _correlationId, triggerField, stringValue);
                    
                    // 取消所有定时器
                    CancelAllTimers();
                    
                    // 转换到完成状态
                    TransitionToCompleted(EventCompletionReason.ReleasedBeforeTimeout);
                    return false; // 不需要额外的状态转换
                }
                else
                {
                    _logger.LogDebug("设备信号状态不匹配: {CorrelationId}, 当前状态={CurrentState}, 信号值={Value}", 
                        _correlationId, _currentState, stringValue);
                }
            }

            bool needsTransition = false;
            if (!_deviceSignalReceived)
            {
                _deviceSignalReceived = true;
                _logger.LogInformation("设备信号已接收: {CorrelationId}", _correlationId);
                needsTransition = true;
            }

            _logger.LogDebug("设备信号处理完成: {Topic}", message.Topic);
            return needsTransition;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "处理设备信号消息时发生异常: {Topic}", message.Topic);
            return false;
        }
    }

    /// <summary>
    /// 处理排除消息
    /// </summary>
    private void HandleExclusionMessage(EventMessage message)
    {
        try
        {
            // 累积排除消息数据
            _exclusionDataAccumulator.AddMessage(message);

            // 重新评估排除规则（使用累积的所有数据）- 使用带保护的评估
            var isExcluded = ShouldBeExcluded();

            if (isExcluded)
            {
                var previousState = _currentState;
                _currentState = EventProcessingState.Excluded;

                _logger.LogInformation("事件被排除: {CorrelationId}", _correlationId);

                // 如果之前已经告警，发送告警撤销消息
                if (previousState == EventProcessingState.Alarmed && _config.EnableAlarmCancellation)
                {
                    _ = Task.Run(async () => await SendAlarmCancellation("late_exclusion_condition_met"));
                }

                // 取消所有定时器
                CancelAllTimers();

                // 触发状态变更事件
                OnStateChanged?.Invoke(this, new StateChangedEventArgs
                {
                    CorrelationId = _correlationId,
                    PreviousState = previousState,
                    NewState = _currentState,
                    Reason = "ExclusionRuleMatched"
                });

                // 标记为完成
                MarkAsCompleted();
                return;
            }

            _logger.LogDebug("排除规则检查通过，继续处理: {CorrelationId}", _correlationId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "处理排除消息时发生异常: {Topic}", message.Topic);
        }
    }

    /// <summary>
    /// 处理业务消息
    /// </summary>
    private void HandleBusinessMessage(EventMessage message)
    {
        try
        {
            // 累积业务消息数据（最新覆盖旧的）
            _businessDataAccumulator.UpdateMessage(message);

            // 重新评估业务规则 - 使用带保护的评估
            var businessResult = EvaluateBusinessRulesWithProtection();

            // 如果规则满足，则记录潜在的告警消息快照
            if (businessResult)
            {
                try
                {
                    var potentialAlarm = _alarmGenerator.GenerateAlarmAsync(CreateEventContext(), _config.RuleConfiguration.AlarmConfig).Result;
                    _logger.LogDebug("业务规则满足，潜在告警内容更新: {AlarmContent}", JsonSerializer.Serialize(potentialAlarm));
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "生成潜在告警日志时发生异常");
                }
            }

            // 更新业务状态
            var businessData = _businessDataAccumulator.GetLatestData();
            _businessRuleState = new BusinessRuleState
            {
                IsMatched = businessResult,
                LastUpdated = DateTime.UtcNow,
                MatchedConditions = businessResult && _config.RuleConfiguration.BusinessRules?.FirstOrDefault() != null ?
                    _ruleEngine.GetMatchedConditions(_config.RuleConfiguration.BusinessRules.FirstOrDefault()!, businessData) :
                    Array.Empty<string>(),
                BusinessData = businessData
            };

            _logger.LogDebug("业务规则评估完成: {Result}, CorrelationId: {CorrelationId}",
                           businessResult, _correlationId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "处理业务消息时发生异常: {Topic}", message.Topic);
        }
    }

    /// <summary>
    /// 处理AI结果消息
    /// </summary>
    private void HandleAIResultMessage(EventMessage message)
    {
        try
        {
            var aiResult = JsonSerializer.Deserialize<AIResultMessage>(message.Payload);

            if (aiResult == null)
            {
                _logger.LogError("AI结果反序列化失败: Payload为null");
                return;
            }

            if (!ValidateAIResult(aiResult))
            {
                _logger.LogWarning("AI结果验证失败: {RequestId}", aiResult.RequestId);
                return;
            }

            // 检查是否应该忽略AI结果
            if (_ignoreAIResult)
            {
                _logger.LogInformation("忽略AI结果（事件已被排除）: {RequestId}", aiResult.RequestId);
                return;
            }

            // AI结果已接收

            if (aiResult.Success)
            {
                ProcessSuccessfulAIResult(aiResult);
            }
            else
            {
                ProcessFailedAIResult(aiResult);
            }
        }
        catch (JsonException ex)
        {
            _logger.LogError(ex, "AI结果JSON解析错误: {Payload}", message.Payload);
            CreateErrorAIState("JSON解析错误");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "AI结果处理异常: {CorrelationId}", _correlationId);
            CreateErrorAIState(ex.Message);
        }
    }

    /// <summary>
    /// 从Initializing状态转换到Collecting状态
    /// </summary>
    private Task TransitionToCollecting()
    {
        if (_currentState == EventProcessingState.Initializing)
        {
            var previousState = _currentState;
            _currentState = EventProcessingState.Collecting;

            // 启动保持定时器
            StartHoldingTimer();

            // 如果配置了AI分析，启动AI分析定时器
            if (!string.IsNullOrEmpty(_config.AIPrompt) && _config.AIAnalysisDelaySec.HasValue)
            {
                StartAIAnalysisTimer();
            }

            _logger.LogInformation("事件状态转换为收集中: {CorrelationId}", _correlationId);

            // 触发状态变更事件
            OnStateChanged?.Invoke(this, new StateChangedEventArgs
            {
                CorrelationId = _correlationId,
                PreviousState = previousState,
                NewState = _currentState,
                Reason = "DeviceSignalReceived"
            });
        }
        return Task.CompletedTask;
    }

    /// <summary>
    /// 启动保持定时器
    /// </summary>
    private void StartHoldingTimer()
    {
        try
        {
            var holdingTimeout = TimeSpan.FromSeconds(_config.DeviceSignal?.HoldingTimeoutSec ?? 30);

            _holdingTimer = new Timer(HoldingTimerCallback, null, holdingTimeout, Timeout.InfiniteTimeSpan);

            _logger.LogDebug("保持定时器已启动: {Duration}秒", holdingTimeout.TotalSeconds);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "启动保持定时器失败: {CorrelationId}", _correlationId);
        }
    }

    /// <summary>
    /// 启动AI分析定时器
    /// </summary>
    private void StartAIAnalysisTimer()
    {
        try
        {
            var aiDelay = TimeSpan.FromSeconds(_config.AIAnalysisDelaySec ?? 5);

            _aiAnalysisTimer = new Timer(AIAnalysisTimerCallback, null, aiDelay, Timeout.InfiniteTimeSpan);

            _logger.LogDebug("AI分析定时器已启动: {Duration}秒", aiDelay.TotalSeconds);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "启动AI分析定时器失败: {CorrelationId}", _correlationId);
        }
    }

    /// <summary>
    /// 保持定时器回调 - V4.1修复：超时驱动告警的唯一入口
    /// </summary>
    private void HoldingTimerCallback(object? state)
    {
        try
        {
            lock (_lockObject)
            {
                if (_disposed || _currentState != EventProcessingState.Collecting)
                    return;

                _logger.LogWarning("保持定时器触发，达到滞留超时条件: {CorrelationId}, 超时时间: {Timeout}秒", 
                    _correlationId, _config.DeviceSignal?.HoldingTimeoutSec ?? 30);

                // V4.1修复关键：只有这里可以触发告警决策
                EvaluateAndDecideOnTimeout();
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "保持定时器回调异常: {CorrelationId}", _correlationId);
            // 异常情况下也要完成事件处理
            TransitionToCompleted(EventCompletionReason.ProcessingError);
        }
    }

    /// <summary>
    /// AI分析定时器回调
    /// </summary>
    private async void AIAnalysisTimerCallback(object? state)
    {
        try
        {
            lock (_lockObject)
            {
                if (_disposed || _currentState == EventProcessingState.Excluded)
                    return;

                _currentState = EventProcessingState.WaitingAIResult;
            }

            await TriggerAIAnalysis();

            _logger.LogInformation("AI分析请求已发送: {CorrelationId}", _correlationId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "AI分析定时器回调异常: {CorrelationId}", _correlationId);
        }
    }

    /// <summary>
    /// 触发AI分析请求
    /// </summary>
    private async Task TriggerAIAnalysis()
    {
        if (string.IsNullOrEmpty(_config.AIPrompt))
            return;

        try
        {
            // AI请求已发送
            _aiRequestCancellation = new CancellationTokenSource();

            var aiRequestMessage = new
            {
                event_id = _config.EventId,
                request_id = _correlationId,
                image_path = GetCurrentImagePath(),
                image_crop_coordinates = _config.ImageCropCoordinates ?? "0,0,0,0",
                prompt = _config.AIPrompt,
                timestamp = DateTime.UtcNow.ToString("yyyyMMddHHmmssfff")
            };

            var aiRequestTopic = $"ai/{_config.CommId}/{_config.PositionId}/control";
            await _mqttService.PublishAsync(aiRequestTopic, JsonSerializer.Serialize(aiRequestMessage));

            _logger.LogInformation("已发送AI分析请求: {CorrelationId}, Topic: {Topic}",
                                 _correlationId, aiRequestTopic);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "发送AI分析请求失败: {CorrelationId}", _correlationId);
            CreateErrorAIState("AI请求发送失败");
        }
    }

    /// <summary>
    /// 获取当前图片路径
    /// </summary>
    private string GetCurrentImagePath()
    {
        // 根据当前上下文获取图片路径的逻辑
        // 这里简化为示例，实际实现需要根据具体的图片获取逻辑
        var timestamp = DateTime.UtcNow.ToString("yyyyMMddHHmmssfff");
        return $"E:\\FTP-IPC\\{_config.PositionId}_{timestamp}.jpg";
    }

    /// <summary>
    /// 验证AI结果
    /// </summary>
    private bool ValidateAIResult(AIResultMessage aiResult)
    {
        if (string.IsNullOrEmpty(aiResult.RequestId))
        {
            _logger.LogWarning("AI结果缺少RequestId");
            return false;
        }

        if (aiResult.RequestId != _correlationId)
        {
            _logger.LogWarning("AI结果RequestId不匹配: 期望 {Expected}, 实际 {Actual}",
                             _correlationId, aiResult.RequestId);
            return false;
        }

        return true;
    }

    /// <summary>
    /// 处理成功的AI结果
    /// </summary>
    private void ProcessSuccessfulAIResult(AIResultMessage aiResult)
    {
        // 评估AI结果规则 - 使用带保护的评估
        var aiRuleResult = EvaluateAIRulesWithProtection();

        // 更新AI状态
        _aiRuleState = new AIRuleState
        {
            IsMatched = aiRuleResult,
            ProcessingTime = aiResult.ProcessingTime,
            LastUpdated = DateTime.UtcNow,
            ResultData = aiResult.Result,
            MatchedConditions = aiRuleResult && _config.RuleConfiguration.AIResultRules?.FirstOrDefault() != null ?
                _ruleEngine.GetMatchedConditions(_config.RuleConfiguration.AIResultRules.FirstOrDefault()!, aiResult.Result) :
                Array.Empty<string>()
        };

        // 设置AI结果数据
        _aiResultDataAccumulator.SetResult(aiResult.Result);

        _logger.LogInformation("AI分析完成: {Result}, 处理时间: {ProcessingTime}秒, CorrelationId: {CorrelationId}",
                             aiRuleResult, aiResult.ProcessingTime, _correlationId);
    }

    /// <summary>
    /// 处理失败的AI结果
    /// </summary>
    private void ProcessFailedAIResult(AIResultMessage aiResult)
    {
        _aiRuleState = new AIRuleState
        {
            IsMatched = false,
            ProcessingTime = aiResult.ProcessingTime,
            LastUpdated = DateTime.UtcNow,
            ErrorMessage = aiResult.ErrorMessage
        };

        _logger.LogWarning("AI分析失败: {Error}, 处理时间: {ProcessingTime}秒, CorrelationId: {CorrelationId}",
                         aiResult.ErrorMessage, aiResult.ProcessingTime, _correlationId);
    }

    /// <summary>
    /// 创建错误的AI状态
    /// </summary>
    private void CreateErrorAIState(string errorMessage)
    {
        _aiRuleState = new AIRuleState
        {
            IsMatched = false,
            ProcessingTime = 0,
            LastUpdated = DateTime.UtcNow,
            ErrorMessage = errorMessage
        };
    }

    /// <summary>
    /// 核心决策评估逻辑 - 支持告警静默期机制
    /// </summary>
    private void EvaluateAndDecide()
    {
        try
        {
            // 1. 检查排除条件（优先级最高）
            if (ShouldBeExcluded())
            {
                var previousState = _currentState;
                _currentState = EventProcessingState.Excluded;

                // 如果之前已经告警，发送告警撤销消息
                if (previousState == EventProcessingState.Alarmed && _config.EnableAlarmCancellation)
                {
                    _ = Task.Run(async () => await SendAlarmCancellation("late_exclusion_condition_met"));
                }

                CancelAllTimers();
                MarkAsCompleted();
                return;
            }

            // 2. 检查是否已处理完毕
            if (_currentState == EventProcessingState.Excluded ||
                _currentState == EventProcessingState.Alarmed)
                return;

            // 3. 检查告警条件
            if (ShouldTriggerAlarm())
            {
                // 关键改进：不立即告警，而是进入静默期
                if (_currentState != EventProcessingState.PendingAlarm)
                {
                    var previousState = _currentState;
                    _currentState = EventProcessingState.PendingAlarm;
                    StartGracePeriodTimer(); // 启动静默期定时器

                    _logger.LogInformation("进入告警静默期: {CorrelationId}, 静默期: {GracePeriod}秒",
                                         _correlationId, _config.AlarmGracePeriodSeconds);

                    // 触发状态变更事件
                    OnStateChanged?.Invoke(this, new StateChangedEventArgs
                    {
                        CorrelationId = _correlationId,
                        PreviousState = previousState,
                        NewState = _currentState,
                        Reason = "AlarmConditionMet"
                    });
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "决策评估异常，执行降级策略: {CorrelationId}, 当前状态: {CurrentState}",
                _correlationId, _currentState);

            // 记录详细的错误信息
            RecordRuleEvaluationError("EvaluateAndDecide", ex);

            // 执行降级策略
            HandleEvaluationFailure(ex);
        }
    }

    /// <summary>
    /// V4.1修复：专门的超时决策方法 - 只在HoldingTimerCallback中调用
    /// </summary>
    private void EvaluateAndDecideOnTimeout()
    {
        try
        {
            _logger.LogWarning("开始超时决策评估: {CorrelationId}, 持续时间: {Duration}秒", 
                _correlationId, _config.DeviceSignal?.HoldingTimeoutSec ?? 30);

            // 1. 检查排除条件（优先级最高）
            if (ShouldBeExcluded())
            {
                _logger.LogInformation("事件因排除条件被取消: {CorrelationId}", _correlationId);
                TransitionToCompleted(EventCompletionReason.Excluded);
                return;
            }

            // 2. 评估业务规则（使用已收集的数据）
            var businessResult = EvaluateBusinessRulesWithProtection();
            if (businessResult)
            {
                _logger.LogWarning("业务规则匹配，触发告警: {CorrelationId}", _correlationId);
                _ = Task.Run(async () => await SendAlarmMessage());
                TransitionToCompleted(EventCompletionReason.AlarmGenerated);
                return;
            }

            // 3. 如果有AI规则，评估AI结果
            if (!string.IsNullOrEmpty(_config.AIPrompt))
            {
                var aiResult = EvaluateAIRulesWithProtection();
                if (aiResult)
                {
                    _logger.LogWarning("AI规则匹配，触发告警: {CorrelationId}", _correlationId);
                    _ = Task.Run(async () => await SendAlarmMessage());
                    TransitionToCompleted(EventCompletionReason.AlarmGenerated);
                    return;
                }
            }

            // 4. 所有条件都不满足
            _logger.LogInformation("超时但未满足告警条件: {CorrelationId}", _correlationId);
            TransitionToCompleted(EventCompletionReason.NoMatchingRules);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "超时决策评估异常: {CorrelationId}", _correlationId);
            TransitionToCompleted(EventCompletionReason.ProcessingError);
        }
    }

    /// <summary>
    /// V4.1修复：事件完成状态转换
    /// </summary>
    private void TransitionToCompleted(EventCompletionReason reason)
    {
        try
        {
            // 取消所有定时器
            CancelAllTimers();
            
            // 根据完成原因设置合适的状态
            var previousState = _currentState;
            _currentState = reason switch
            {
                EventCompletionReason.Excluded => EventProcessingState.Excluded,
                EventCompletionReason.AlarmGenerated => EventProcessingState.Alarmed,
                _ => EventProcessingState.Excluded // 其他情况都视为已完成
            };

            _logger.LogInformation("事件完成: {CorrelationId}, 原因: {Reason}, 状态: {PreviousState} -> {NewState}", 
                _correlationId, reason, previousState, _currentState);

            // 标记为完成
            MarkAsCompleted();

            // 触发完成事件
            OnCompleted?.Invoke(this, new EventCompletedEventArgs
            {
                CorrelationId = _correlationId,
                FinalState = _currentState,
                ProcessingTime = _processingTime ?? TimeSpan.Zero,
                TotalMessages = 0 // V4.1修复：暂时设为0，后续可优化
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "事件完成转换异常: {CorrelationId}, 原因: {Reason}", _correlationId, reason);
        }
    }

    /// <summary>
    /// 启动告警静默期定时器
    /// </summary>
    private void StartGracePeriodTimer()
    {
        try
        {
            var gracePeriod = TimeSpan.FromSeconds(_config.AlarmGracePeriodSeconds);

            _gracePeriodTimer = new Timer(GracePeriodCallback, null, gracePeriod, Timeout.InfiniteTimeSpan);

            _logger.LogDebug("告警静默期定时器启动: {Duration}秒", gracePeriod.TotalSeconds);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "启动静默期定时器失败: {CorrelationId}", _correlationId);

            // 定时器启动失败时，立即触发告警
            Task.Run(async () =>
            {
                await Task.Delay(100); // 短暂延迟确保状态一致

                lock (_lockObject)
                {
                    if (_currentState == EventProcessingState.PendingAlarm)
                    {
                        _currentState = EventProcessingState.Alarmed;
                        _ = Task.Run(async () => await SendAlarmMessage());
                        _logger.LogWarning("定时器失败，立即确认告警: {CorrelationId}", _correlationId);
                    }
                }
            });
        }
    }

    /// <summary>
    /// 静默期定时器回调
    /// </summary>
    private void GracePeriodCallback(object? state)
    {
        try
        {
            lock (_lockObject)
            {
                if (_disposed || _currentState != EventProcessingState.PendingAlarm)
                    return;

                var previousState = _currentState;
                _currentState = EventProcessingState.Alarmed;
                _ = Task.Run(async () => await SendAlarmMessage());

                _logger.LogInformation("静默期结束，确认告警: {CorrelationId}", _correlationId);

                // 触发状态变更事件
                OnStateChanged?.Invoke(this, new StateChangedEventArgs
                {
                    CorrelationId = _correlationId,
                    PreviousState = previousState,
                    NewState = _currentState,
                    Reason = "GracePeriodExpired"
                });

                MarkAsCompleted();
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "静默期回调异常: {CorrelationId}", _correlationId);

            // 即使回调异常，也要尝试发送告警
            try
            {
                _ = Task.Run(async () => await SendAlarmMessage());
            }
            catch (Exception sendEx)
            {
                _logger.LogError(sendEx, "告警发送失败: {CorrelationId}", _correlationId);
            }
        }
    }

    /// <summary>
    /// 检查是否应该被排除 - 带熔断保护
    /// </summary>
    private bool ShouldBeExcluded()
    {
        var ruleKey = $"exclusion_{_config.EventId}";

        return _circuitBreaker.Execute<bool>(ruleKey, () =>
        {
            try
            {
                var exclusionData = _exclusionDataAccumulator.GetCombinedData();
                var result = _ruleEngine.EvaluateExclusionRules(_config.RuleConfiguration.ExclusionRules, exclusionData);

                _logger.LogDebug("排除规则评估完成: {CorrelationId}, 结果: {Result}, 数据项数: {DataCount}",
                    _correlationId, result, exclusionData.Count);

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "排除规则评估异常: {CorrelationId}, 规则数: {RuleCount}",
                    _correlationId, _config.RuleConfiguration.ExclusionRules?.Length ?? 0);

                // 记录错误到错误处理服务
                RecordRuleEvaluationError("ExclusionRules", ex);

                // 重新抛出异常让熔断器处理
                throw;
            }
        }, fallbackResult: false); // 排除规则失败时默认不排除，允许继续处理
    }

    /// <summary>
    /// 检查是否应该触发告警
    /// </summary>
    private bool ShouldTriggerAlarm()
    {
        return _config.EvaluationStrategy switch
        {
            "AI" => ShouldAlarm_AI(),
            "BusinessOnly" => ShouldAlarm_BusinessOnly(),
            "AIAndBusiness" => ShouldAlarm_AIAndBusiness(),
            _ => false
        };
    }

    /// <summary>
    /// AI模式告警判断 - 带熔断保护
    /// </summary>
    private bool ShouldAlarm_AI()
    {
        // AI模式：需要AI分析完成且匹配
        return EvaluateAIRulesWithProtection();
    }

    /// <summary>
    /// 带保护的AI规则评估
    /// </summary>
    private bool EvaluateAIRulesWithProtection()
    {
        var ruleKey = $"ai_{_config.EventId}";

        return _circuitBreaker.Execute<bool>(ruleKey, () =>
        {
            try
            {
                // 检查是否有AI结果数据
                var aiData = _aiResultDataAccumulator.GetResultData();
                if (aiData.Count == 0)
                {
                    _logger.LogDebug("AI规则评估跳过: {CorrelationId}, 无AI结果数据", _correlationId);
                    return false;
                }

                var result = _ruleEngine.EvaluateAIResultRules(_config.RuleConfiguration.AIResultRules, aiData);

                _logger.LogDebug("AI规则评估完成: {CorrelationId}, 结果: {Result}, 数据项数: {DataCount}",
                    _correlationId, result, aiData.Count);

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "AI规则评估异常: {CorrelationId}, 规则数: {RuleCount}",
                    _correlationId, _config.RuleConfiguration.AIResultRules?.Length ?? 0);

                // 记录错误到错误处理服务
                RecordRuleEvaluationError("AIResultRules", ex);

                // 重新抛出异常让熔断器处理
                throw;
            }
        }, fallbackResult: false); // AI规则失败时默认不匹配，不触发告警
    }

    /// <summary>
    /// 业务逻辑模式告警判断 - 带熔断保护
    /// </summary>
    private bool ShouldAlarm_BusinessOnly()
    {
        // 业务逻辑模式：只需业务规则匹配
        return EvaluateBusinessRulesWithProtection();
    }

    /// <summary>
    /// 带保护的业务规则评估
    /// </summary>
    private bool EvaluateBusinessRulesWithProtection()
    {
        var ruleKey = $"business_{_config.EventId}";

        return _circuitBreaker.Execute<bool>(ruleKey, () =>
        {
            try
            {
                var businessData = _businessDataAccumulator.GetLatestData();
                var result = _ruleEngine.EvaluateBusinessRules(_config.RuleConfiguration.BusinessRules, businessData);

                _logger.LogDebug("业务规则评估完成: {CorrelationId}, 结果: {Result}, 数据项数: {DataCount}",
                    _correlationId, result, businessData.Count);

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "业务规则评估异常: {CorrelationId}, 规则数: {RuleCount}",
                    _correlationId, _config.RuleConfiguration.BusinessRules?.Length ?? 0);

                // 记录错误到错误处理服务
                RecordRuleEvaluationError("BusinessRules", ex);

                // 重新抛出异常让熔断器处理
                throw;
            }
        }, fallbackResult: false); // 业务规则失败时默认不匹配，不触发告警
    }

    /// <summary>
    /// 混合模式告警判断 - 带熔断保护
    /// </summary>
    private bool ShouldAlarm_AIAndBusiness()
    {
        // 混合模式：AI和业务规则都必须匹配
        var aiReady = EvaluateAIRulesWithProtection();
        var businessReady = EvaluateBusinessRulesWithProtection();

        var result = aiReady && businessReady;

        _logger.LogDebug("混合模式评估完成: {CorrelationId}, AI: {AIResult}, 业务: {BusinessResult}, 最终: {FinalResult}",
            _correlationId, aiReady, businessReady, result);

        return result;
    }

    /// <summary>
    /// 记录规则评估错误
    /// </summary>
    /// <param name="ruleType">规则类型</param>
    /// <param name="exception">异常信息</param>
    private void RecordRuleEvaluationError(string ruleType, Exception exception)
    {
        try
        {
            // 创建错误上下文
            var errorContext = new Dictionary<string, object>
            {
                ["CorrelationId"] = _correlationId,
                ["EventId"] = _config.EventId,
                ["RuleType"] = ruleType,
                ["CurrentState"] = _currentState.ToString(),
                ["ExceptionType"] = exception.GetType().Name,
                ["ExceptionMessage"] = exception.Message,
                ["StackTrace"] = exception.StackTrace ?? string.Empty
            };

            // 记录到日志
            _logger.LogError(exception, "规则评估失败: {RuleType}, 事件: {EventId}, 关联ID: {CorrelationId}",
                ruleType, _config.EventId, _correlationId);

            // 如果有错误处理服务，记录错误统计
            var errorHandlingService = _serviceProvider.GetService<IErrorHandlingService>();
            if (errorHandlingService != null)
            {
                _ = Task.Run(async () =>
                {
                    try
                    {
                        var errorContextObj = new ErrorContext
                        {
                            OperationName = "RuleEvaluation",
                            ComponentName = "EventStateAggregator",
                            EventId = _config.EventId,
                            CorrelationId = _correlationId,
                            AdditionalData = errorContext
                        };
                        await errorHandlingService.HandleExceptionAsync(exception, errorContextObj);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "记录规则评估错误失败: {CorrelationId}", _correlationId);
                    }
                });
            }
        }
        catch (Exception ex)
        {
            // 防止错误记录本身出错
            _logger.LogError(ex, "记录规则评估错误时发生异常: {CorrelationId}", _correlationId);
        }
    }

    /// <summary>
    /// 获取熔断器统计信息
    /// </summary>
    /// <returns>熔断器统计信息</returns>
    public Dictionary<string, CircuitBreakerStatistics> GetCircuitBreakerStatistics()
    {
        return _circuitBreaker.GetStatistics();
    }

    /// <summary>
    /// 重置熔断器
    /// </summary>
    /// <param name="ruleType">规则类型（可选，为空则重置所有）</param>
    public void ResetCircuitBreaker(string? ruleType = null)
    {
        if (string.IsNullOrEmpty(ruleType))
        {
            _circuitBreaker.ResetAll();
            _logger.LogInformation("重置所有熔断器: {CorrelationId}", _correlationId);
        }
        else
        {
            var ruleKey = $"{ruleType}_{_config.EventId}";
            _circuitBreaker.Reset(ruleKey);
            _logger.LogInformation("重置熔断器: {RuleKey}, {CorrelationId}", ruleKey, _correlationId);
        }
    }

    /// <summary>
    /// 发送告警消息
    /// </summary>
    private async Task SendAlarmMessage()
    {
        try
        {
            // 🔧 修复：使用标准告警生成器生成符合规范的告警消息
            var standardAlarmMessage = await _standardAlarmGenerator.GenerateStandardAlarmAsync(
                CreateEventContext(),
                _config.RuleConfiguration.AlarmConfig,
                _config);

            // 使用自定义告警主题或默认动态主题
            var alarmTopic = !string.IsNullOrEmpty(_config.RuleConfiguration.AlarmConfig.CustomAlarmTopic)
                ? _config.RuleConfiguration.AlarmConfig.CustomAlarmTopic
                : $"{_config.CompanyName}/{_config.CommId}/{_config.PositionId}/event";

            // 序列化标准告警消息
            var jsonMessage = JsonSerializer.Serialize(standardAlarmMessage, new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                WriteIndented = false,
                Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping // 支持中文显示
            });

            await _mqttService.PublishAsync(alarmTopic, jsonMessage);

            _logger.LogWarning("告警已发送: {Topic}, {CorrelationId}", alarmTopic, _correlationId);
            _logger.LogDebug("告警消息内容: {Message}", jsonMessage);

            // 为了兼容现有代码，同时生成内部告警消息用于事件触发
            var internalAlarmMessage = await _alarmGenerator.GenerateAlarmAsync(CreateEventContext(), _config.RuleConfiguration.AlarmConfig);

            // 触发告警生成事件
            OnAlarmGenerated?.Invoke(this, new AlarmGeneratedEventArgs
            {
                CorrelationId = _correlationId,
                AlarmMessage = internalAlarmMessage,
                AlarmType = "Alarm"
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "发送告警消息失败: {CorrelationId}", _correlationId);
        }
    }

    /// <summary>
    /// 发送告警撤销消息
    /// </summary>
    private async Task SendAlarmCancellation(string reason)
    {
        try
        {
            // 🔧 修复：使用标准告警生成器生成符合规范的撤销消息
            var standardCancellationMessage = await _standardAlarmGenerator.GenerateStandardCancellationAsync(
                CreateEventContext(),
                _config.RuleConfiguration.AlarmConfig,
                _config,
                reason);

            // 使用自定义告警撤销主题或默认动态主题
            var topic = !string.IsNullOrEmpty(_config.RuleConfiguration.AlarmConfig.CustomAlarmCancellationTopic)
                ? _config.RuleConfiguration.AlarmConfig.CustomAlarmCancellationTopic
                : $"{_config.CompanyName}/{_config.CommId}/{_config.PositionId}/cancellation";

            // 序列化标准撤销消息
            var jsonMessage = JsonSerializer.Serialize(standardCancellationMessage, new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                WriteIndented = false,
                Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping // 支持中文显示
            });

            await _mqttService.PublishAsync(topic, jsonMessage);

            _logger.LogWarning("已发送告警撤销: {Topic}, {CorrelationId}, 原因: {Reason}",
                             topic, _correlationId, reason);
            _logger.LogDebug("撤销消息内容: {Message}", jsonMessage);

            // 为了兼容现有代码，同时生成内部撤销消息用于事件触发
            var internalCancellationMessage = new AlarmMessage
            {
                EventId = _config.EventId,
                InstanceId = _correlationId,
                AlarmType = "Cancellation",
                CancellationReason = reason,
                Fields = new Dictionary<string, object>
                {
                    ["action"] = "cancel_alarm",
                    ["reason"] = reason,
                    ["timestamp"] = DateTime.UtcNow.ToString("yyyyMMddHHmmssfff")
                }
            };

            // 触发告警生成事件
            OnAlarmGenerated?.Invoke(this, new AlarmGeneratedEventArgs
            {
                CorrelationId = _correlationId,
                AlarmMessage = internalCancellationMessage,
                AlarmType = "Cancellation"
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "发送告警撤销消息失败: {CorrelationId}", _correlationId);
        }
    }

    /// <summary>
    /// 创建事件上下文
    /// </summary>
    private EventContext CreateEventContext()
    {
        return new EventContext
        {
            EventId = _config.EventId,
            InstanceId = _correlationId,
            ExclusionData = _exclusionDataAccumulator.GetCombinedData(),
            BusinessData = _businessDataAccumulator.GetLatestData(),
            AIResultData = _aiResultDataAccumulator.GetResultData(),
            DeviceData = _deviceData,
            BusinessRuleState = _businessRuleState,
            AIRuleState = _aiRuleState
        };
    }

    /// <summary>
    /// 获取排除原因
    /// </summary>
    private string GetExclusionReason(Dictionary<string, object> exclusionData)
    {
        var firstRule = _config.RuleConfiguration.ExclusionRules?.FirstOrDefault();
        var matchedConditions = firstRule != null ?
            _ruleEngine.GetMatchedConditions(firstRule, exclusionData) :
            Array.Empty<string>();

        return matchedConditions.Length > 0 ? string.Join(", ", matchedConditions) : "排除条件匹配";
    }

    /// <summary>
    /// 取消所有定时器
    /// </summary>
    private void CancelAllTimers()
    {
        try
        {
            _holdingTimer?.Dispose();
            _aiAnalysisTimer?.Dispose();
            _gracePeriodTimer?.Dispose();

            _aiRequestCancellation?.Cancel();
            _aiRequestCancellation?.Dispose();

            _logger.LogDebug("所有定时器已取消: {CorrelationId}", _correlationId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "取消定时器时发生异常: {CorrelationId}", _correlationId);
        }
    }

    /// <summary>
    /// 标记为完成
    /// </summary>
    private void MarkAsCompleted()
    {
        _processingTime = DateTime.UtcNow - _creationTime;

        // 触发完成事件
        OnCompleted?.Invoke(this, new EventCompletedEventArgs
        {
            CorrelationId = _correlationId,
            FinalState = _currentState,
            ProcessingTime = _processingTime.Value,
            TotalMessages = GetTotalMessagesProcessed()
        });

        _logger.LogInformation("事件处理完成: {CorrelationId}, 最终状态: {FinalState}, 处理时间: {ProcessingTime}",
                             _correlationId, _currentState, _processingTime);
    }

    /// <summary>
    /// 获取处理的消息总数
    /// </summary>
    private int GetTotalMessagesProcessed()
    {
        var exclusionStats = _exclusionDataAccumulator.GetStatistics();
        var businessStats = _businessDataAccumulator.GetStatistics();
        var aiStats = _aiResultDataAccumulator.GetStatistics();

        return exclusionStats.TopicCount + businessStats.TopicCount + aiStats.TopicCount +
               (_deviceSignalReceived ? 1 : 0);
    }

    /// <summary>
    /// 处理评估失败
    /// </summary>
    private void HandleEvaluationFailure(Exception ex)
    {
        // 根据异常类型采取不同的降级策略
        switch (ex)
        {
            case JsonException:
                _logger.LogWarning("配置解析错误，使用默认策略");
                // 使用最保守的策略：不告警
                break;

            case TimeoutException:
                _logger.LogWarning("操作超时，延迟重试");
                // 延迟重试
                _ = Task.Delay(1000).ContinueWith(_ => EvaluateAndDecide());
                break;

            case OutOfMemoryException:
                _logger.LogCritical("内存不足，强制清理");
                // 强制清理和降级
                ForceCleanup();
                break;

            default:
                _logger.LogError("未知错误，继续监控");
                // 继续运行但标记为错误状态
                break;
        }
    }

    /// <summary>
    /// 强制清理
    /// </summary>
    private void ForceCleanup()
    {
        try
        {
            CancelAllTimers();
            _exclusionDataAccumulator.Clear();
            _businessDataAccumulator.Clear();
            _aiResultDataAccumulator.Clear();
            _deviceData.Clear();

            _logger.LogWarning("强制清理完成: {CorrelationId}", _correlationId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "强制清理时发生异常: {CorrelationId}", _correlationId);
        }
    }

    /// <summary>
    /// 释放事件状态聚合器资源
    /// </summary>
    public void Dispose()
    {
        Dispose(true);
        GC.SuppressFinalize(this);
    }

    /// <summary>
    /// 释放资源的具体实现
    /// </summary>
    /// <param name="disposing">是否释放托管资源</param>
    protected virtual void Dispose(bool disposing)
    {
        if (!_disposed && disposing)
        {
            try
            {
                CancelAllTimers();
                _exclusionDataAccumulator.Clear();
                _businessDataAccumulator.Clear();
                _aiResultDataAccumulator.Clear();

                _logger.LogDebug("EventStateAggregator资源已释放: {CorrelationId}", _correlationId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "资源释放异常: {CorrelationId}", _correlationId);
            }
            finally
            {
                _disposed = true;
            }
        }
    }
}
