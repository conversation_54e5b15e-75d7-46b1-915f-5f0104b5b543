using EventProcessor.Core.Models;
using Microsoft.Extensions.Logging;

namespace EventProcessor.Core.Engine;

/// <summary>
/// 规则引擎 - 支持嵌套条件组评估和三种规则类型
/// </summary>
public class RuleEngine
{
    private readonly ConditionEvaluator _conditionEvaluator;
    private readonly ILogger<RuleEngine> _logger;

    /// <summary>
    /// 初始化规则引擎
    /// </summary>
    /// <param name="conditionEvaluator">条件评估器</param>
    /// <param name="logger">日志记录器</param>
    public RuleEngine(ConditionEvaluator conditionEvaluator, ILogger<RuleEngine> logger)
    {
        _conditionEvaluator = conditionEvaluator;
        _logger = logger;
    }

    /// <summary>
    /// 评估排除规则
    /// </summary>
    /// <param name="exclusionRules">排除规则列表</param>
    /// <param name="data">数据字典</param>
    /// <returns>是否应该排除</returns>
    public bool EvaluateExclusionRules(ExclusionRuleGroup[]? exclusionRules, Dictionary<string, object> data)
    {
        if (exclusionRules == null || exclusionRules.Length == 0)
        {
            _logger.LogDebug("没有配置排除规则，继续处理");
            return false;
        }

        try
        {
            foreach (var ruleGroup in exclusionRules)
            {
                var result = EvaluateExclusionRuleGroup(ruleGroup, data);
                if (result)
                {
                    _logger.LogInformation("排除规则匹配，事件被排除: {SourceTopic}", ruleGroup.SourceTopic);
                    return true;
                }
            }

            _logger.LogDebug("所有排除规则检查通过，继续处理");
            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "排除规则评估异常");
            return false; // 异常时不排除，继续处理
        }
    }

    /// <summary>
    /// 评估业务规则
    /// </summary>
    /// <param name="businessRules">业务规则列表</param>
    /// <param name="data">数据字典</param>
    /// <returns>是否满足业务条件</returns>
    public bool EvaluateBusinessRules(BusinessRuleGroup[]? businessRules, Dictionary<string, object> data)
    {
        if (businessRules == null || businessRules.Length == 0)
        {
            _logger.LogDebug("没有配置业务规则，默认不满足");
            return false;
        }

        try
        {
            foreach (var ruleGroup in businessRules)
            {
                var result = EvaluateBusinessRuleGroup(ruleGroup, data);
                if (result)
                {
                    _logger.LogInformation("业务规则匹配: {SourceTopic}", ruleGroup.SourceTopic);
                    return true;
                }
            }

            _logger.LogDebug("业务规则不满足");
            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "业务规则评估异常");
            return false;
        }
    }

    /// <summary>
    /// 评估AI结果规则
    /// </summary>
    /// <param name="aiResultRules">AI结果规则列表</param>
    /// <param name="data">AI结果数据</param>
    /// <returns>是否满足AI条件</returns>
    public bool EvaluateAIResultRules(AIResultRuleGroup[]? aiResultRules, Dictionary<string, object> data)
    {
        if (aiResultRules == null || aiResultRules.Length == 0)
        {
            _logger.LogDebug("没有配置AI结果规则，默认不满足");
            return false;
        }

        try
        {
            foreach (var ruleGroup in aiResultRules)
            {
                var result = EvaluateAIResultRuleGroup(ruleGroup, data);
                if (result)
                {
                    _logger.LogInformation("AI结果规则匹配");
                    return true;
                }
            }

            _logger.LogDebug("AI结果规则不满足");
            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "AI结果规则评估异常");
            return false;
        }
    }

    /// <summary>
    /// 评估排除规则组
    /// </summary>
    private bool EvaluateExclusionRuleGroup(ExclusionRuleGroup ruleGroup, Dictionary<string, object> data)
    {
        try
        {
            var results = new List<bool>();

            // 评估嵌套条件组
            if (ruleGroup.ConditionGroups != null)
            {
                foreach (var group in ruleGroup.ConditionGroups)
                {
                    try
                    {
                        results.Add(_conditionEvaluator.EvaluateConditionGroup(group, data));
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "排除规则条件组评估失败，跳过该组: {GroupId}", group.GetHashCode());
                        results.Add(false); // 评估失败时默认为false
                    }
                }
            }

            // 评估直接条件
            if (ruleGroup.Conditions != null)
            {
                foreach (var condition in ruleGroup.Conditions)
                {
                    results.Add(_conditionEvaluator.EvaluateCondition(condition, data));
                }
            }

            return ApplyLogicOperator(ruleGroup.LogicOperator, results);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "排除规则组评估异常: {SourceTopic}", ruleGroup.SourceTopic);
            return false;
        }
    }

    /// <summary>
    /// 评估业务规则组
    /// </summary>
    private bool EvaluateBusinessRuleGroup(BusinessRuleGroup ruleGroup, Dictionary<string, object> data)
    {
        try
        {
            var results = new List<bool>();

            // 评估嵌套条件组
            if (ruleGroup.ConditionGroups != null)
            {
                foreach (var group in ruleGroup.ConditionGroups)
                {
                    try
                    {
                        results.Add(_conditionEvaluator.EvaluateConditionGroup(group, data));
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "业务规则条件组评估失败，跳过该组: {GroupId}", group.GetHashCode());
                        results.Add(false);
                    }
                }
            }

            // 评估直接条件
            if (ruleGroup.Conditions != null)
            {
                foreach (var condition in ruleGroup.Conditions)
                {
                    results.Add(_conditionEvaluator.EvaluateCondition(condition, data));
                }
            }

            return ApplyLogicOperator(ruleGroup.LogicOperator, results);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "业务规则组评估异常: {SourceTopic}", ruleGroup.SourceTopic);
            return false;
        }
    }

    /// <summary>
    /// 评估AI结果规则组
    /// </summary>
    private bool EvaluateAIResultRuleGroup(AIResultRuleGroup ruleGroup, Dictionary<string, object> data)
    {
        try
        {
            var results = new List<bool>();

            // 评估嵌套条件组
            if (ruleGroup.ConditionGroups != null)
            {
                foreach (var group in ruleGroup.ConditionGroups)
                {
                    try
                    {
                        results.Add(_conditionEvaluator.EvaluateConditionGroup(group, data));
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "AI结果规则条件组评估失败，跳过该组: {GroupId}", group.GetHashCode());
                        results.Add(false);
                    }
                }
            }

            // 评估直接条件
            if (ruleGroup.Conditions != null)
            {
                foreach (var condition in ruleGroup.Conditions)
                {
                    results.Add(_conditionEvaluator.EvaluateCondition(condition, data));
                }
            }

            return ApplyLogicOperator(ruleGroup.LogicOperator, results);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "AI结果规则组评估异常");
            return false;
        }
    }

    /// <summary>
    /// 应用逻辑操作符
    /// </summary>
    private bool ApplyLogicOperator(string logicOperator, List<bool> results)
    {
        if (!results.Any()) return false;

        return logicOperator switch
        {
            "AND" => results.All(r => r),
            "OR" => results.Any(r => r),
            "NOT" => !results.First(),
            _ => throw new InvalidOperationException($"不支持的逻辑操作符: {logicOperator}")
        };
    }

    /// <summary>
    /// 获取匹配的条件描述
    /// </summary>
    /// <param name="ruleGroup">规则组</param>
    /// <param name="data">数据字典</param>
    /// <returns>匹配的条件描述列表</returns>
    public string[] GetMatchedConditions(object ruleGroup, Dictionary<string, object> data)
    {
        var matchedConditions = new List<string>();

        try
        {
            switch (ruleGroup)
            {
                case ExclusionRuleGroup exclusionGroup:
                    CollectMatchedConditions(exclusionGroup.Conditions, exclusionGroup.ConditionGroups, data, matchedConditions);
                    break;
                case BusinessRuleGroup businessGroup:
                    CollectMatchedConditions(businessGroup.Conditions, businessGroup.ConditionGroups, data, matchedConditions);
                    break;
                case AIResultRuleGroup aiGroup:
                    CollectMatchedConditions(aiGroup.Conditions, aiGroup.ConditionGroups, data, matchedConditions);
                    break;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取匹配条件描述时发生异常");
        }

        return matchedConditions.ToArray();
    }

    /// <summary>
    /// 收集匹配的条件描述
    /// </summary>
    private void CollectMatchedConditions(Condition[]? conditions, ConditionGroup[]? conditionGroups, 
                                        Dictionary<string, object> data, List<string> matchedConditions)
    {
        // 检查直接条件
        if (conditions != null)
        {
            foreach (var condition in conditions)
            {
                if (_conditionEvaluator.EvaluateCondition(condition, data))
                {
                    matchedConditions.Add(condition.Description ?? $"{condition.FieldName} {condition.Operator} {condition.Value}");
                }
            }
        }

        // 检查条件组
        if (conditionGroups != null)
        {
            foreach (var group in conditionGroups)
            {
                if (_conditionEvaluator.EvaluateConditionGroup(group, data))
                {
                    CollectMatchedConditions(group.Conditions, null, data, matchedConditions);
                }
            }
        }
    }
}
