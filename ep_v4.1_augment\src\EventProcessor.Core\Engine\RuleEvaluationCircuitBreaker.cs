using Microsoft.Extensions.Logging;
using System.Collections.Concurrent;

namespace EventProcessor.Core.Engine;

/// <summary>
/// 规则评估熔断器 - 防止重复失败的规则导致系统级联故障
/// </summary>
public class RuleEvaluationCircuitBreaker
{
    private readonly ILogger<RuleEvaluationCircuitBreaker> _logger;
    private readonly ConcurrentDictionary<string, CircuitBreakerState> _circuitStates = new();
    private readonly TimeSpan _failureWindow = TimeSpan.FromMinutes(5);
    private readonly int _failureThreshold = 10;
    private readonly TimeSpan _recoveryTimeout = TimeSpan.FromMinutes(2);

    /// <summary>
    /// 初始化规则评估熔断器
    /// </summary>
    /// <param name="logger">日志记录器</param>
    public RuleEvaluationCircuitBreaker(ILogger<RuleEvaluationCircuitBreaker> logger)
    {
        _logger = logger;
    }

    /// <summary>
    /// 执行规则评估，带熔断保护
    /// </summary>
    /// <typeparam name="T">规则类型</typeparam>
    /// <param name="ruleKey">规则唯一标识</param>
    /// <param name="ruleEvaluationFunc">规则评估函数</param>
    /// <param name="fallbackResult">熔断时的回退结果</param>
    /// <returns>评估结果</returns>
    public async Task<bool> ExecuteAsync<T>(string ruleKey, Func<Task<bool>> ruleEvaluationFunc, bool fallbackResult = false)
    {
        var state = _circuitStates.GetOrAdd(ruleKey, _ => new CircuitBreakerState());

        // 检查熔断器状态
        if (state.IsOpen)
        {
            if (DateTime.UtcNow - state.LastFailureTime > _recoveryTimeout)
            {
                // 尝试半开状态
                state.IsHalfOpen = true;
                state.IsOpen = false;
                _logger.LogInformation("规则熔断器进入半开状态: {RuleKey}", ruleKey);
            }
            else
            {
                // 熔断器仍然开启，返回回退结果
                _logger.LogWarning("规则熔断器开启，使用回退结果: {RuleKey}, 回退值: {FallbackResult}", ruleKey, fallbackResult);
                return fallbackResult;
            }
        }

        try
        {
            var result = await ruleEvaluationFunc();
            
            // 成功执行，重置失败计数
            if (state.IsHalfOpen)
            {
                state.Reset();
                _logger.LogInformation("规则熔断器恢复正常: {RuleKey}", ruleKey);
            }
            else
            {
                state.RecordSuccess();
            }

            return result;
        }
        catch (Exception ex)
        {
            // 记录失败
            state.RecordFailure();
            
            _logger.LogError(ex, "规则评估失败: {RuleKey}, 失败次数: {FailureCount}", 
                ruleKey, state.FailureCount);

            // 检查是否需要开启熔断器
            if (state.FailureCount >= _failureThreshold)
            {
                state.IsOpen = true;
                state.IsHalfOpen = false;
                _logger.LogWarning("规则熔断器开启: {RuleKey}, 失败次数: {FailureCount}", 
                    ruleKey, state.FailureCount);
            }

            // 返回回退结果
            return fallbackResult;
        }
    }

    /// <summary>
    /// 同步版本的规则评估执行
    /// </summary>
    public bool Execute<T>(string ruleKey, Func<bool> ruleEvaluationFunc, bool fallbackResult = false)
    {
        var state = _circuitStates.GetOrAdd(ruleKey, _ => new CircuitBreakerState());

        // 检查熔断器状态
        if (state.IsOpen)
        {
            if (DateTime.UtcNow - state.LastFailureTime > _recoveryTimeout)
            {
                // 尝试半开状态
                state.IsHalfOpen = true;
                state.IsOpen = false;
                _logger.LogInformation("规则熔断器进入半开状态: {RuleKey}", ruleKey);
            }
            else
            {
                // 熔断器仍然开启，返回回退结果
                _logger.LogWarning("规则熔断器开启，使用回退结果: {RuleKey}, 回退值: {FallbackResult}", ruleKey, fallbackResult);
                return fallbackResult;
            }
        }

        try
        {
            var result = ruleEvaluationFunc();
            
            // 成功执行，重置失败计数
            if (state.IsHalfOpen)
            {
                state.Reset();
                _logger.LogInformation("规则熔断器恢复正常: {RuleKey}", ruleKey);
            }
            else
            {
                state.RecordSuccess();
            }

            return result;
        }
        catch (Exception ex)
        {
            // 记录失败
            state.RecordFailure();
            
            _logger.LogError(ex, "规则评估失败: {RuleKey}, 失败次数: {FailureCount}", 
                ruleKey, state.FailureCount);

            // 检查是否需要开启熔断器
            if (state.FailureCount >= _failureThreshold)
            {
                state.IsOpen = true;
                state.IsHalfOpen = false;
                _logger.LogWarning("规则熔断器开启: {RuleKey}, 失败次数: {FailureCount}", 
                    ruleKey, state.FailureCount);
            }

            // 返回回退结果
            return fallbackResult;
        }
    }

    /// <summary>
    /// 获取熔断器统计信息
    /// </summary>
    /// <returns>熔断器统计信息</returns>
    public Dictionary<string, CircuitBreakerStatistics> GetStatistics()
    {
        var statistics = new Dictionary<string, CircuitBreakerStatistics>();

        foreach (var kvp in _circuitStates)
        {
            var state = kvp.Value;
            statistics[kvp.Key] = new CircuitBreakerStatistics
            {
                RuleKey = kvp.Key,
                IsOpen = state.IsOpen,
                IsHalfOpen = state.IsHalfOpen,
                FailureCount = state.FailureCount,
                SuccessCount = state.SuccessCount,
                LastFailureTime = state.LastFailureTime,
                LastSuccessTime = state.LastSuccessTime
            };
        }

        return statistics;
    }

    /// <summary>
    /// 重置指定规则的熔断器
    /// </summary>
    /// <param name="ruleKey">规则标识</param>
    public void Reset(string ruleKey)
    {
        if (_circuitStates.TryGetValue(ruleKey, out var state))
        {
            state.Reset();
            _logger.LogInformation("手动重置规则熔断器: {RuleKey}", ruleKey);
        }
    }

    /// <summary>
    /// 重置所有熔断器
    /// </summary>
    public void ResetAll()
    {
        foreach (var kvp in _circuitStates)
        {
            kvp.Value.Reset();
        }
        _logger.LogInformation("重置所有规则熔断器");
    }
}

/// <summary>
/// 熔断器状态
/// </summary>
internal class CircuitBreakerState
{
    public bool IsOpen { get; set; }
    public bool IsHalfOpen { get; set; }
    public int FailureCount { get; private set; }
    public int SuccessCount { get; private set; }
    public DateTime LastFailureTime { get; private set; }
    public DateTime LastSuccessTime { get; private set; }

    public void RecordFailure()
    {
        FailureCount++;
        LastFailureTime = DateTime.UtcNow;
    }

    public void RecordSuccess()
    {
        SuccessCount++;
        LastSuccessTime = DateTime.UtcNow;
        
        // 成功时减少失败计数（逐渐恢复）
        if (FailureCount > 0)
        {
            FailureCount = Math.Max(0, FailureCount - 1);
        }
    }

    public void Reset()
    {
        IsOpen = false;
        IsHalfOpen = false;
        FailureCount = 0;
        SuccessCount = 0;
        LastFailureTime = default;
        LastSuccessTime = DateTime.UtcNow;
    }
}

/// <summary>
/// 熔断器统计信息
/// </summary>
public record CircuitBreakerStatistics
{
    /// <summary>
    /// 规则标识键
    /// </summary>
    public required string RuleKey { get; init; }
    
    /// <summary>
    /// 是否处于打开状态
    /// </summary>
    public bool IsOpen { get; init; }
    
    /// <summary>
    /// 是否处于半开状态
    /// </summary>
    public bool IsHalfOpen { get; init; }
    
    /// <summary>
    /// 失败次数
    /// </summary>
    public int FailureCount { get; init; }
    
    /// <summary>
    /// 成功次数
    /// </summary>
    public int SuccessCount { get; init; }
    
    /// <summary>
    /// 最后失败时间
    /// </summary>
    public DateTime LastFailureTime { get; init; }
    
    /// <summary>
    /// 最后成功时间
    /// </summary>
    public DateTime LastSuccessTime { get; init; }
    
    /// <summary>
    /// 熔断器状态描述
    /// </summary>
    public string Status => IsOpen ? "Open" : IsHalfOpen ? "Half-Open" : "Closed";
    
    /// <summary>
    /// 失败率
    /// </summary>
    public double FailureRate => SuccessCount + FailureCount == 0 ? 0 : (double)FailureCount / (SuccessCount + FailureCount);
}
