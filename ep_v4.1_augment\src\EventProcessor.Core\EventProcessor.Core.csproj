<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <LangVersion>latest</LangVersion>
    <GenerateDocumentationFile>true</GenerateDocumentationFile>
    <TreatWarningsAsErrors>true</TreatWarningsAsErrors>
    <WarningsNotAsErrors>CS1591</WarningsNotAsErrors>
  </PropertyGroup>

  <PropertyGroup>
    <AssemblyTitle>Event Processor V4.1 Core Library</AssemblyTitle>
    <AssemblyDescription>EP_V4.1增强规则配置系统核心库</AssemblyDescription>
    <AssemblyCompany>Augment Code</AssemblyCompany>
    <AssemblyProduct>Event Processor V4.1</AssemblyProduct>
    <AssemblyVersion>*******</AssemblyVersion>
    <FileVersion>*******</FileVersion>
    <InformationalVersion>4.1.0</InformationalVersion>

    <Authors>Joe</Authors>
    <Company>BDN</Company>
    <Copyright>Copyright © 2025 BDN All rights reserved.</Copyright>
    <RepositoryUrl />
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Extensions.Configuration" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="8.0.0" />

    <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Hosting" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Logging" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Options" Version="8.0.2" />
    <PackageReference Include="Microsoft.Extensions.Options.ConfigurationExtensions" Version="8.0.0" />
    <PackageReference Include="MQTTnet" Version="4.3.6.1152" />
    <PackageReference Include="MQTTnet.Extensions.ManagedClient" Version="4.3.6.1152" />
    <PackageReference Include="System.Text.Json" Version="8.0.4" />
    <PackageReference Include="YamlDotNet" Version="15.1.2" />
    <PackageReference Include="System.ComponentModel.Annotations" Version="5.0.0" />
  </ItemGroup>

</Project>
