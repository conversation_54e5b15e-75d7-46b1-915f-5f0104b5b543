using System.ComponentModel.DataAnnotations;

namespace EventProcessor.Core.Models;

/// <summary>
/// 字段映射配置 - 支持动态字段引用
/// </summary>
public record FieldMapping
{
    /// <summary>
    /// 告警字段名称
    /// </summary>
    [Required(ErrorMessage = "告警字段名称不能为空")]
    public required string AlarmFieldName { get; init; }

    /// <summary>
    /// 源规则类型：ExclusionRules、BusinessRules、AIResultRules、DeviceSignal
    /// </summary>
    [Required(ErrorMessage = "源规则类型不能为空")]
    [RegularExpression("^(ExclusionRules|BusinessRules|AIResultRules|DeviceSignal)$", 
        ErrorMessage = "源规则类型必须是ExclusionRules、BusinessRules、AIResultRules或DeviceSignal")]
    public required string SourceRuleType { get; init; }

    /// <summary>
    /// 源字段名称（支持多字段用逗号分隔）
    /// </summary>
    [Required(ErrorMessage = "源字段名称不能为空")]
    public required string SourceFieldName { get; init; }

    /// <summary>
    /// 默认值（字段不存在时使用）
    /// </summary>
    public string? DefaultValue { get; init; }

    /// <summary>
    /// 格式化模板（支持{fieldName}占位符）
    /// </summary>
    public string? FormatTemplate { get; init; }
}

/// <summary>
/// 告警配置
/// </summary>
public record AlarmConfiguration
{
    /// <summary>
    /// 字段映射列表
    /// </summary>
    [Required(ErrorMessage = "字段映射列表不能为空")]
    public required List<FieldMapping> Fields { get; init; }

    /// <summary>
    /// 自定义告警模板
    /// </summary>
    public string? CustomTemplate { get; init; }

    /// <summary>
    /// 自定义告警主题（可选）
    /// 如果指定，则使用此主题发送告警消息
    /// 如果未指定，则使用默认格式：{CompanyName}/{CommId}/{PositionId}/event
    /// </summary>
    public string? CustomAlarmTopic { get; init; }

    /// <summary>
    /// 自定义告警撤销主题（可选）
    /// 如果指定，则使用此主题发送告警撤销消息
    /// 如果未指定，则使用默认格式：{CompanyName}/{CommId}/{PositionId}/cancellation
    /// </summary>
    public string? CustomAlarmCancellationTopic { get; init; }
}

/// <summary>
/// 设备信号配置
/// </summary>
public record DeviceSignalConfiguration
{
    /// <summary>
    /// MQTT主题列表
    /// </summary>
    [Required(ErrorMessage = "MQTT主题列表不能为空")]
    [MinLength(1, ErrorMessage = "至少需要一个MQTT主题")]
    public required string[] Topics { get; init; }

    /// <summary>
    /// 触发字段名称
    /// </summary>
    [Required(ErrorMessage = "触发字段名称不能为空")]
    public required string TriggerField { get; init; }

    /// <summary>
    /// 触发值映射（true/false对应的实际值）
    /// </summary>
    [Required(ErrorMessage = "触发值映射不能为空")]
    public required Dictionary<string, string> TriggerValues { get; init; }

    /// <summary>
    /// 保持超时时间（秒）
    /// </summary>
    [Range(1, 3600, ErrorMessage = "保持超时时间必须在1-3600秒之间")]
    public int HoldingTimeoutSec { get; init; } = 30;
}

/// <summary>
/// 规则配置
/// </summary>
public record RuleConfiguration
{
    /// <summary>
    /// 排除规则列表
    /// </summary>
    public ExclusionRuleGroup[]? ExclusionRules { get; init; }

    /// <summary>
    /// 业务规则列表
    /// </summary>
    public BusinessRuleGroup[]? BusinessRules { get; init; }

    /// <summary>
    /// AI结果规则列表
    /// </summary>
    public AIResultRuleGroup[]? AIResultRules { get; init; }

    /// <summary>
    /// 告警配置
    /// </summary>
    public AlarmConfiguration AlarmConfig { get; init; } = new() { Fields = new List<FieldMapping>() };
}
