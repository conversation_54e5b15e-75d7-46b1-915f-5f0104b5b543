using System.ComponentModel.DataAnnotations;

namespace EventProcessor.Core.Models;

/// <summary>
/// 条件定义 - 支持string、number、datetime三种数据类型
/// </summary>
public record Condition
{
    /// <summary>
    /// 字段名称
    /// </summary>
    [Required(ErrorMessage = "字段名称不能为空")]
    public required string FieldName { get; init; }

    /// <summary>
    /// 数据类型：string、number、datetime
    /// </summary>
    [Required(ErrorMessage = "数据类型不能为空")]
    [RegularExpression("^(string|number|datetime)$", ErrorMessage = "数据类型必须是string、number或datetime")]
    public required string DataType { get; init; }

    /// <summary>
    /// 操作符 - 根据数据类型确定可用操作符
    /// </summary>
    [Required(ErrorMessage = "操作符不能为空")]
    public required string Operator { get; init; }

    /// <summary>
    /// 比较值
    /// </summary>
    [Required(ErrorMessage = "比较值不能为空")]
    public required string Value { get; init; }

    /// <summary>
    /// 条件描述（可选）
    /// </summary>
    public string? Description { get; init; }
}

/// <summary>
/// 条件组 - 支持嵌套条件逻辑
/// </summary>
public record ConditionGroup
{
    /// <summary>
    /// 逻辑操作符：AND、OR、NOT
    /// </summary>
    [Required(ErrorMessage = "逻辑操作符不能为空")]
    [RegularExpression("^(AND|OR|NOT)$", ErrorMessage = "逻辑操作符必须是AND、OR或NOT")]
    public required string LogicOperator { get; init; }

    /// <summary>
    /// 条件列表
    /// </summary>
    public Condition[]? Conditions { get; init; }
}

/// <summary>
/// 排除规则组
/// </summary>
public record ExclusionRuleGroup
{
    /// <summary>
    /// 数据源类型：MQTT、HTTP、Database
    /// </summary>
    [Required(ErrorMessage = "数据源类型不能为空")]
    [RegularExpression("^(MQTT|HTTP|Database)$", ErrorMessage = "数据源类型必须是MQTT、HTTP或Database")]
    public required string SourceType { get; init; }

    /// <summary>
    /// 数据源主题/地址
    /// </summary>
    [Required(ErrorMessage = "数据源主题不能为空")]
    public required string SourceTopic { get; init; }

    /// <summary>
    /// 逻辑操作符：AND、OR
    /// </summary>
    [Required(ErrorMessage = "逻辑操作符不能为空")]
    [RegularExpression("^(AND|OR)$", ErrorMessage = "逻辑操作符必须是AND或OR")]
    public required string LogicOperator { get; init; }

    /// <summary>
    /// 嵌套条件组（最多2层嵌套）
    /// </summary>
    public ConditionGroup[]? ConditionGroups { get; init; }

    /// <summary>
    /// 直接条件
    /// </summary>
    public Condition[]? Conditions { get; init; }
}

/// <summary>
/// 业务规则组
/// </summary>
public record BusinessRuleGroup
{
    /// <summary>
    /// 数据源主题
    /// </summary>
    [Required(ErrorMessage = "数据源主题不能为空")]
    public required string SourceTopic { get; init; }

    /// <summary>
    /// 逻辑操作符：AND、OR
    /// </summary>
    [Required(ErrorMessage = "逻辑操作符不能为空")]
    [RegularExpression("^(AND|OR)$", ErrorMessage = "逻辑操作符必须是AND或OR")]
    public required string LogicOperator { get; init; }

    /// <summary>
    /// 嵌套条件组（最多2层嵌套）
    /// </summary>
    public ConditionGroup[]? ConditionGroups { get; init; }

    /// <summary>
    /// 直接条件
    /// </summary>
    public Condition[]? Conditions { get; init; }
}

/// <summary>
/// AI结果规则组
/// </summary>
public record AIResultRuleGroup
{
    /// <summary>
    /// 逻辑操作符：AND、OR
    /// </summary>
    [Required(ErrorMessage = "逻辑操作符不能为空")]
    [RegularExpression("^(AND|OR)$", ErrorMessage = "逻辑操作符必须是AND或OR")]
    public required string LogicOperator { get; init; }

    /// <summary>
    /// 嵌套条件组（最多2层嵌套）
    /// </summary>
    public ConditionGroup[]? ConditionGroups { get; init; }

    /// <summary>
    /// 直接条件
    /// </summary>
    public Condition[]? Conditions { get; init; }
}
