using System.ComponentModel.DataAnnotations;

namespace EventProcessor.Core.Models;

/// <summary>
/// 统一配置模型 - EP_V4.1增强版本
/// </summary>
public record ConfigurationModel
{
    /// <summary>
    /// 事件处理器配置
    /// </summary>
    [Required(ErrorMessage = "事件处理器配置不能为空")]
    public required EventConfiguration EventProcessor { get; init; }
    
    /// <summary>
    /// MQTT配置
    /// </summary>
    [Required(ErrorMessage = "MQTT配置不能为空")]
    public required MqttConfiguration Mqtt { get; init; }
    
    /// <summary>
    /// 日志配置
    /// </summary>
    [Required(ErrorMessage = "日志配置不能为空")]
    public required LoggingConfiguration Logging { get; init; }
    
    /// <summary>
    /// 错误处理配置
    /// </summary>
    public ErrorHandlingConfiguration? ErrorHandling { get; init; }
}