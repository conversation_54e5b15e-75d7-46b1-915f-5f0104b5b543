using System.ComponentModel.DataAnnotations;

namespace EventProcessor.Core.Models;

/// <summary>
/// 错误处理配置
/// </summary>
public record ErrorHandlingConfiguration
{
    /// <summary>
    /// 错误容忍级别：Strict、Normal、Lenient
    /// </summary>
    [RegularExpression("^(Strict|Normal|Lenient)$", 
        ErrorMessage = "错误容忍级别必须是Strict、Normal或Lenient")]
    public string ToleranceLevel { get; init; } = "Normal";

    /// <summary>
    /// 重试策略
    /// </summary>
    public RetryPolicyConfiguration RetryPolicy { get; init; } = new();

    /// <summary>
    /// 降级策略
    /// </summary>
    public FallbackStrategyConfiguration FallbackStrategy { get; init; } = new();

    /// <summary>
    /// 日志配置
    /// </summary>
    public ErrorLoggingConfiguration Logging { get; init; } = new();
}

/// <summary>
/// 重试策略配置
/// </summary>
public record RetryPolicyConfiguration
{
    /// <summary>
    /// 最大重试次数
    /// </summary>
    [Range(0, 10, ErrorMessage = "最大重试次数必须在0-10之间")]
    public int MaxRetries { get; init; } = 3;

    /// <summary>
    /// 重试延迟（毫秒）
    /// </summary>
    [Range(100, 60000, ErrorMessage = "重试延迟必须在100-60000毫秒之间")]
    public int RetryDelay { get; init; } = 1000;

    /// <summary>
    /// 是否使用指数退避
    /// </summary>
    public bool UseExponentialBackoff { get; init; } = true;

    /// <summary>
    /// 最大退避延迟（毫秒）
    /// </summary>
    [Range(1000, 300000, ErrorMessage = "最大退避延迟必须在1000-300000毫秒之间")]
    public int MaxBackoffDelay { get; init; } = 30000;
}

/// <summary>
/// 降级策略配置
/// </summary>
public record FallbackStrategyConfiguration
{
    /// <summary>
    /// 规则失败时的策略：StopProcessing、ContinueProcessing
    /// </summary>
    [RegularExpression("^(StopProcessing|ContinueProcessing)$", 
        ErrorMessage = "规则失败策略必须是StopProcessing或ContinueProcessing")]
    public string OnRuleFailure { get; init; } = "ContinueProcessing";

    /// <summary>
    /// AI失败时的策略：SkipEvent、UseBusinessOnly、ForceAlarm
    /// </summary>
    [RegularExpression("^(SkipEvent|UseBusinessOnly|ForceAlarm)$", 
        ErrorMessage = "AI失败策略必须是SkipEvent、UseBusinessOnly或ForceAlarm")]
    public string OnAIFailure { get; init; } = "UseBusinessOnly";

    /// <summary>
    /// 定时器失败时的策略：ImmediateAlarm、SkipAlarm
    /// </summary>
    [RegularExpression("^(ImmediateAlarm|SkipAlarm)$", 
        ErrorMessage = "定时器失败策略必须是ImmediateAlarm或SkipAlarm")]
    public string OnTimerFailure { get; init; } = "ImmediateAlarm";

    /// <summary>
    /// MQTT连接失败时的策略：Retry、Shutdown、ContinueOffline
    /// </summary>
    [RegularExpression("^(Retry|Shutdown|ContinueOffline)$", 
        ErrorMessage = "MQTT连接失败策略必须是Retry、Shutdown或ContinueOffline")]
    public string OnMqttFailure { get; init; } = "Retry";
}

/// <summary>
/// 错误日志配置
/// </summary>
public record ErrorLoggingConfiguration
{
    /// <summary>
    /// 错误日志级别：Debug、Information、Warning、Error、Critical
    /// </summary>
    [RegularExpression("^(Debug|Information|Warning|Error|Critical)$", 
        ErrorMessage = "错误日志级别必须是Debug、Information、Warning、Error或Critical")]
    public string ErrorLogLevel { get; init; } = "Error";

    /// <summary>
    /// 是否包含详细堆栈跟踪
    /// </summary>
    public bool DetailedStackTrace { get; init; } = true;

    /// <summary>
    /// 是否包含消息负载（生产环境建议false）
    /// </summary>
    public bool IncludeMessagePayload { get; init; } = false;

    /// <summary>
    /// 是否启用性能监控
    /// </summary>
    public bool EnablePerformanceMonitoring { get; init; } = true;

    /// <summary>
    /// 错误率阈值（超过此阈值时触发告警）
    /// </summary>
    [Range(0.0, 1.0, ErrorMessage = "错误率阈值必须在0.0-1.0之间")]
    public double ErrorRateThreshold { get; init; } = 0.1;
}

/// <summary>
/// 性能监控配置
/// </summary>
public record PerformanceMonitoringConfiguration
{
    /// <summary>
    /// 是否启用系统监控
    /// </summary>
    public bool EnableSystemMonitoring { get; init; } = true;

    /// <summary>
    /// 指标保留时间（分钟）
    /// </summary>
    [Range(1, 1440, ErrorMessage = "指标保留时间必须在1-1440分钟之间")]
    public int MetricsRetentionMinutes { get; init; } = 60;

    /// <summary>
    /// 健康检查阈值
    /// </summary>
    public HealthCheckThresholds HealthCheckThresholds { get; init; } = new();
}

/// <summary>
/// 健康检查阈值
/// </summary>
public record HealthCheckThresholds
{
    /// <summary>
    /// 最大规则评估时间（毫秒）
    /// </summary>
    [Range(1, 10000, ErrorMessage = "最大规则评估时间必须在1-10000毫秒之间")]
    public int MaxRuleEvaluationMs { get; init; } = 100;

    /// <summary>
    /// 最大告警生成时间（毫秒）
    /// </summary>
    [Range(1, 5000, ErrorMessage = "最大告警生成时间必须在1-5000毫秒之间")]
    public int MaxAlarmGenerationMs { get; init; } = 50;

    /// <summary>
    /// 最大条件错误数
    /// </summary>
    [Range(1, 100, ErrorMessage = "最大条件错误数必须在1-100之间")]
    public int MaxConditionErrors { get; init; } = 5;

    /// <summary>
    /// 最大内存使用量（MB）
    /// </summary>
    [Range(100, 8192, ErrorMessage = "最大内存使用量必须在100-8192MB之间")]
    public int MaxMemoryUsageMB { get; init; } = 512;

    /// <summary>
    /// 最大CPU使用率（百分比）
    /// </summary>
    [Range(10, 100, ErrorMessage = "最大CPU使用率必须在10-100%之间")]
    public int MaxCpuUsagePercent { get; init; } = 80;
}
