using System.ComponentModel.DataAnnotations;

namespace EventProcessor.Core.Models;

/// <summary>
/// 事件配置 - EP_V4.1增强版本
/// </summary>
public record EventConfiguration
{
    /// <summary>
    /// 事件ID
    /// </summary>
    [Required(ErrorMessage = "事件ID不能为空")]
    [RegularExpression(@"^EV\d{6}$", ErrorMessage = "事件ID格式必须为EV开头的6位数字")]
    public required string EventId { get; init; }

    /// <summary>
    /// 事件名称
    /// </summary>
    [Required(ErrorMessage = "事件名称不能为空")]
    [StringLength(100, ErrorMessage = "事件名称长度不能超过100个字符")]
    public required string EventName { get; init; }

    /// <summary>
    /// 评估策略：AI、BusinessOnly、AIAndBusiness
    /// </summary>
    [Required(ErrorMessage = "评估策略不能为空")]
    [RegularExpression("^(AI|BusinessOnly|AIAndBusiness)$", 
        ErrorMessage = "评估策略必须是AI、BusinessOnly或AIAndBusiness")]
    public required string EvaluationStrategy { get; init; }

    /// <summary>
    /// 优先级：P1、P2、P3、P4、P5
    /// </summary>
    [Required(ErrorMessage = "优先级不能为空")]
    [RegularExpression("^P[1-5]$", ErrorMessage = "优先级必须是P1-P5")]
    public required string Priority { get; init; }

    /// <summary>
    /// 小区ID
    /// </summary>
    [Required(ErrorMessage = "小区ID不能为空")]
    public required string CommId { get; init; }

    /// <summary>
    /// 位置ID
    /// </summary>
    [Required(ErrorMessage = "位置ID不能为空")]
    public required string PositionId { get; init; }

    /// <summary>
    /// 公司名称（用于告警主题）
    /// </summary>
    [Required(ErrorMessage = "公司名称不能为空")]
    [StringLength(50, ErrorMessage = "公司名称长度不能超过50个字符")]
    public required string CompanyName { get; init; }

    /// <summary>
    /// AI提示词（AI模式时必填）
    /// </summary>
    public string? AIPrompt { get; init; }

    /// <summary>
    /// AI分析延迟时间（秒）
    /// </summary>
    [Range(1, 300, ErrorMessage = "AI分析延迟时间必须在1-300秒之间")]
    public int? AIAnalysisDelaySec { get; init; }

    /// <summary>
    /// 图片裁剪坐标（格式：x1,y1,x2,y2）
    /// </summary>
    [RegularExpression(@"^\d+,\d+,\d+,\d+$", ErrorMessage = "图片裁剪坐标格式必须为x1,y1,x2,y2")]
    public string? ImageCropCoordinates { get; init; }

    /// <summary>
    /// 设备信号配置
    /// </summary>
    public DeviceSignalConfiguration? DeviceSignal { get; init; }

    /// <summary>
    /// 规则配置
    /// </summary>
    [Required(ErrorMessage = "规则配置不能为空")]
    public required RuleConfiguration RuleConfiguration { get; init; }

    /// <summary>
    /// 告警静默期时长（秒），默认3秒
    /// </summary>
    [Range(0, 60, ErrorMessage = "告警静默期时长必须在0-60秒之间")]
    public int AlarmGracePeriodSeconds { get; init; } = 3;

    /// <summary>
    /// 是否启用告警撤销功能，默认启用
    /// </summary>
    public bool EnableAlarmCancellation { get; init; } = true;

    /// <summary>
    /// 自定义告警主题（可选）
    /// 如果未指定，则使用默认格式：alarm/{CommId}/{PositionId}/event
    /// </summary>
    public string? CustomAlarmTopic { get; init; }

    /// <summary>
    /// 自定义告警撤销主题（可选）
    /// 如果未指定，则使用默认格式：alarm/{CommId}/{PositionId}/cancellation
    /// </summary>
    public string? CustomAlarmCancellationTopic { get; init; }

    /// <summary>
    /// 时间窗口关联策略：minute(分钟)、hour(小时)、custom(自定义)
    /// </summary>
    [RegularExpression("^(minute|hour|custom)$", 
        ErrorMessage = "时间窗口关联策略必须是minute、hour或custom")]
    public string CorrelationTimeWindow { get; init; } = "minute";

    /// <summary>
    /// 自定义时间窗口大小（分钟），仅当CorrelationTimeWindow="custom"时有效
    /// </summary>
    [Range(1, 1440, ErrorMessage = "自定义时间窗口大小必须在1-1440分钟之间")]
    public int? CustomTimeWindowMinutes { get; init; }
    
    /// <summary>
    /// 告警配置
    /// </summary>
    [Required(ErrorMessage = "告警配置不能为空")]
    public required AlarmConfiguration AlarmConfiguration { get; init; }
}
