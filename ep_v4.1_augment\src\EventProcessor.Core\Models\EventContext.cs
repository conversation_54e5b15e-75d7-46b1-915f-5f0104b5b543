namespace EventProcessor.Core.Models;

/// <summary>
/// 事件上下文 - 包含事件处理过程中的所有数据
/// </summary>
public record EventContext
{
    /// <summary>
    /// 事件ID
    /// </summary>
    public required string EventId { get; init; }

    /// <summary>
    /// 实例ID（关联ID）
    /// </summary>
    public required string InstanceId { get; init; }

    /// <summary>
    /// 排除数据
    /// </summary>
    public Dictionary<string, object> ExclusionData { get; init; } = new();

    /// <summary>
    /// 业务数据
    /// </summary>
    public Dictionary<string, object> BusinessData { get; init; } = new();

    /// <summary>
    /// AI结果数据
    /// </summary>
    public Dictionary<string, object> AIResultData { get; init; } = new();

    /// <summary>
    /// 设备数据
    /// </summary>
    public Dictionary<string, object> DeviceData { get; init; } = new();

    /// <summary>
    /// 业务规则状态
    /// </summary>
    public BusinessRuleState? BusinessRuleState { get; init; }

    /// <summary>
    /// AI规则状态
    /// </summary>
    public AIRuleState? AIRuleState { get; init; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedAt { get; init; } = DateTime.UtcNow;

    /// <summary>
    /// 最后更新时间
    /// </summary>
    public DateTime LastUpdatedAt { get; init; } = DateTime.UtcNow;
}

/// <summary>
/// 事件完成事件参数
/// </summary>
public class EventCompletedEventArgs : EventArgs
{
    /// <summary>
    /// 关联ID
    /// </summary>
    public required string CorrelationId { get; init; }

    /// <summary>
    /// 最终状态
    /// </summary>
    public EventProcessingState FinalState { get; init; }

    /// <summary>
    /// 处理时间
    /// </summary>
    public TimeSpan ProcessingTime { get; init; }

    /// <summary>
    /// 处理的消息总数
    /// </summary>
    public int TotalMessages { get; init; }

    /// <summary>
    /// 完成时间
    /// </summary>
    public DateTime CompletedAt { get; init; } = DateTime.UtcNow;
}

/// <summary>
/// 告警生成事件参数
/// </summary>
public class AlarmGeneratedEventArgs : EventArgs
{
    /// <summary>
    /// 关联ID
    /// </summary>
    public required string CorrelationId { get; init; }

    /// <summary>
    /// 告警消息
    /// </summary>
    public required AlarmMessage AlarmMessage { get; init; }

    /// <summary>
    /// 告警类型：Alarm、Cancellation
    /// </summary>
    public required string AlarmType { get; init; }

    /// <summary>
    /// 生成时间
    /// </summary>
    public DateTime GeneratedAt { get; init; } = DateTime.UtcNow;
}

/// <summary>
/// 状态变更事件参数
/// </summary>
public class StateChangedEventArgs : EventArgs
{
    /// <summary>
    /// 关联ID
    /// </summary>
    public required string CorrelationId { get; init; }

    /// <summary>
    /// 之前的状态
    /// </summary>
    public EventProcessingState PreviousState { get; init; }

    /// <summary>
    /// 新状态
    /// </summary>
    public EventProcessingState NewState { get; init; }

    /// <summary>
    /// 状态变更原因
    /// </summary>
    public required string Reason { get; init; }

    /// <summary>
    /// 变更时间
    /// </summary>
    public DateTime ChangedAt { get; init; } = DateTime.UtcNow;
}
