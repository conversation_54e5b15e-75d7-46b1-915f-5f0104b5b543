using System.Text.Json;
using System.Text.Json.Serialization;

namespace EventProcessor.Core.Models;

/// <summary>
/// 事件消息基类
/// </summary>
public record EventMessage
{
    /// <summary>
    /// 消息类型：DeviceSignal、ExclusionData、BusinessData、AIResult
    /// </summary>
    public required string MessageType { get; init; }

    /// <summary>
    /// MQTT主题
    /// </summary>
    public required string Topic { get; init; }

    /// <summary>
    /// 消息负载（JSON格式）
    /// </summary>
    public required string Payload { get; init; }

    /// <summary>
    /// 事件ID
    /// </summary>
    public required string EventId { get; init; }

    /// <summary>
    /// 小区ID
    /// </summary>
    public required string CommId { get; init; }

    /// <summary>
    /// 位置ID
    /// </summary>
    public required string PositionId { get; init; }

    /// <summary>
    /// 消息时间戳
    /// </summary>
    public DateTime Timestamp { get; init; } = DateTime.UtcNow;

    /// <summary>
    /// 消息ID（用于去重）
    /// </summary>
    public string MessageId { get; init; } = Guid.NewGuid().ToString();
}

/// <summary>
/// 设备信号消息
/// </summary>
public record DeviceSignalMessage : EventMessage
{
    /// <summary>
    /// 设备ID
    /// </summary>
    public required string DeviceId { get; init; }

    /// <summary>
    /// 触发字段值
    /// </summary>
    public required string TriggerValue { get; init; }

    /// <summary>
    /// 图片路径
    /// </summary>
    public string? ImagePath { get; init; }

    /// <summary>
    /// 设备状态数据
    /// </summary>
    public Dictionary<string, object> DeviceData { get; init; } = new();
}

/// <summary>
/// AI结果消息
/// </summary>
public record AIResultMessage
{
    /// <summary>
    /// 事件ID
    /// </summary>
    public required string EventId { get; init; }

    /// <summary>
    /// 请求ID（用于关联）
    /// </summary>
    public required string RequestId { get; init; }

    /// <summary>
    /// AI分析结果
    /// </summary>
    public Dictionary<string, object> Result { get; init; } = new();

    /// <summary>
    /// 结果时间戳
    /// </summary>
    public required string Timestamp { get; init; }

    /// <summary>
    /// 处理时间（秒）
    /// </summary>
    public double ProcessingTime { get; init; }

    /// <summary>
    /// 处理成功标志
    /// </summary>
    public bool Success { get; init; }

    /// <summary>
    /// 错误信息（仅在Success=false时提供）
    /// </summary>
    public string? ErrorMessage { get; init; }
}

/// <summary>
/// 标准告警消息（符合接收方规范）
/// </summary>
public record StandardAlarmMessage
{
    /// <summary>
    /// 事件ID
    /// </summary>
    [JsonPropertyName("eventid")]
    public required string EventId { get; init; }

    /// <summary>
    /// 小区编号
    /// </summary>
    [JsonPropertyName("comm_id")]
    public required string CommId { get; init; }

    /// <summary>
    /// 点位编号
    /// </summary>
    [JsonPropertyName("positionid")]
    public required string PositionId { get; init; }

    /// <summary>
    /// 详情数据字符串（最多6行，使用\r\n分隔）
    /// </summary>
    [JsonPropertyName("data")]
    public required string Data { get; init; }

    /// <summary>
    /// 图片URL列表
    /// </summary>
    [JsonPropertyName("urls")]
    public string[] Urls { get; init; } = Array.Empty<string>();
}

/// <summary>
/// 告警消息（内部使用，用于兼容现有代码）
/// </summary>
public record AlarmMessage
{
    /// <summary>
    /// 事件ID
    /// </summary>
    public required string EventId { get; init; }

    /// <summary>
    /// 实例ID（关联ID）
    /// </summary>
    public required string InstanceId { get; init; }

    /// <summary>
    /// 告警时间戳
    /// </summary>
    public DateTime Timestamp { get; init; } = DateTime.UtcNow;

    /// <summary>
    /// 告警字段
    /// </summary>
    public Dictionary<string, object> Fields { get; init; } = new();

    /// <summary>
    /// 自定义模板
    /// </summary>
    public string? Template { get; init; }

    /// <summary>
    /// 告警类型：Alarm、Cancellation
    /// </summary>
    public string AlarmType { get; init; } = "Alarm";

    /// <summary>
    /// 撤销原因（仅在AlarmType=Cancellation时使用）
    /// </summary>
    public string? CancellationReason { get; init; }
}

/// <summary>
/// 事件处理状态
/// </summary>
public enum EventProcessingState
{
    /// <summary>
    /// 初始化状态 - 等待DeviceSignal到达
    /// </summary>
    Initializing,

    /// <summary>
    /// 收集数据状态
    /// </summary>
    Collecting,

    /// <summary>
    /// 等待AI分析结果
    /// </summary>
    WaitingAIResult,

    /// <summary>
    /// 告警静默期
    /// </summary>
    PendingAlarm,

    /// <summary>
    /// 已告警
    /// </summary>
    Alarmed,

    /// <summary>
    /// 已排除
    /// </summary>
    Excluded
}

/// <summary>
/// 事件完成原因 - V4.1修复新增
/// </summary>
public enum EventCompletionReason
{
    /// <summary>超时前收到解除信号</summary>
    ReleasedBeforeTimeout,
    
    /// <summary>排除条件匹配</summary>
    Excluded,
    
    /// <summary>告警已生成</summary>
    AlarmGenerated,
    
    /// <summary>超时但无匹配规则</summary>
    NoMatchingRules,
    
    /// <summary>处理异常</summary>
    ProcessingError
}

/// <summary>
/// 规则状态
/// </summary>
public record RuleState
{
    /// <summary>
    /// 是否匹配
    /// </summary>
    public bool IsMatched { get; init; }

    /// <summary>
    /// 最后更新时间
    /// </summary>
    public DateTime LastUpdated { get; init; } = DateTime.UtcNow;

    /// <summary>
    /// 匹配的条件描述
    /// </summary>
    public string[]? MatchedConditions { get; init; }

    /// <summary>
    /// 错误信息
    /// </summary>
    public string? ErrorMessage { get; init; }
}

/// <summary>
/// 业务规则状态
/// </summary>
public record BusinessRuleState : RuleState
{
    /// <summary>
    /// 业务数据
    /// </summary>
    public Dictionary<string, object> BusinessData { get; init; } = new();
}

/// <summary>
/// AI规则状态
/// </summary>
public record AIRuleState : RuleState
{
    /// <summary>
    /// 处理时间（秒）
    /// </summary>
    public double ProcessingTime { get; init; }

    /// <summary>
    /// AI结果数据
    /// </summary>
    public Dictionary<string, object> ResultData { get; init; } = new();
}
