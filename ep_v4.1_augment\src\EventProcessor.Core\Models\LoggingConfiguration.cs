using System.ComponentModel.DataAnnotations;

namespace EventProcessor.Core.Models;

/// <summary>
/// 日志配置
/// </summary>
public record LoggingConfiguration
{
    /// <summary>
    /// 最小日志级别配置
    /// </summary>
    public SerilogMinimumLevel? MinimumLevel { get; init; }
    
    /// <summary>
    /// 写入器配置列表
    /// </summary>
    [Required(ErrorMessage = "写入器配置不能为空")]
    public required List<SerilogWriteTo> WriteTo { get; init; }
    
    /// <summary>
    /// 日志输出模板
    /// </summary>
    public string? OutputTemplate { get; init; }
    
    /// <summary>
    /// 是否启用结构化日志
    /// </summary>
    public bool EnableStructuredLogging { get; init; } = true;
    
    /// <summary>
    /// 日志文件最大大小（MB）
    /// </summary>
    [Range(1, 1024, ErrorMessage = "日志文件最大大小必须在1-1024MB之间")]
    public int MaxFileSizeMB { get; init; } = 100;
    
    /// <summary>
    /// 日志文件保留天数
    /// </summary>
    [Range(1, 365, ErrorMessage = "日志文件保留天数必须在1-365天之间")]
    public int RetainedFileCountLimit { get; init; } = 30;
}

/// <summary>
/// Serilog最小日志级别配置
/// </summary>
public record SerilogMinimumLevel
{
    /// <summary>
    /// 默认日志级别
    /// </summary>
    [RegularExpression("^(Verbose|Debug|Information|Warning|Error|Fatal)$", 
        ErrorMessage = "日志级别必须是Verbose、Debug、Information、Warning、Error或Fatal")]
    public string Default { get; init; } = "Information";
    
    /// <summary>
    /// 特定命名空间的日志级别覆盖
    /// </summary>
    public Dictionary<string, string> Override { get; init; } = new();
}

/// <summary>
/// Serilog写入器配置
/// </summary>
public record SerilogWriteTo
{
    /// <summary>
    /// 写入器名称
    /// </summary>
    [Required(ErrorMessage = "写入器名称不能为空")]
    public required string Name { get; init; }
    
    /// <summary>
    /// 写入器参数
    /// </summary>
    public Dictionary<string, object>? Args { get; init; }
    
    /// <summary>
    /// 写入器的最小日志级别
    /// </summary>
    [RegularExpression("^(Verbose|Debug|Information|Warning|Error|Fatal)$", 
        ErrorMessage = "日志级别必须是Verbose、Debug、Information、Warning、Error或Fatal")]
    public string? RestrictedToMinimumLevel { get; init; }
    
    /// <summary>
    /// 输出模板
    /// </summary>
    public string? OutputTemplate { get; init; }
}