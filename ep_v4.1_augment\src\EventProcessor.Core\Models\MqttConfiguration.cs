using System.ComponentModel.DataAnnotations;

namespace EventProcessor.Core.Models;

/// <summary>
/// MQTT配置
/// </summary>
public record MqttConfiguration
{
    /// <summary>
    /// MQTT服务器主机地址
    /// </summary>
    [Required(ErrorMessage = "MQTT服务器主机地址不能为空")]
    public required string BrokerHost { get; init; }
    
    /// <summary>
    /// MQTT服务器端口
    /// </summary>
    [Range(1, 65535, ErrorMessage = "MQTT服务器端口必须在1-65535之间")]
    public int BrokerPort { get; init; } = 1883;
    
    /// <summary>
    /// 客户端ID
    /// </summary>
    [Required(ErrorMessage = "客户端ID不能为空")]
    public required string ClientId { get; init; }
    
    /// <summary>
    /// 用户名（可选）
    /// </summary>
    public string? Username { get; init; }
    
    /// <summary>
    /// 密码（可选）
    /// </summary>
    public string? Password { get; init; }
    
    /// <summary>
    /// 连接超时时间（秒）
    /// </summary>
    [Range(1, 300, ErrorMessage = "连接超时时间必须在1-300秒之间")]
    public int ConnectionTimeout { get; init; } = 30;
    
    /// <summary>
    /// 保持连接间隔（秒）
    /// </summary>
    [Range(10, 3600, ErrorMessage = "保持连接间隔必须在10-3600秒之间")]
    public int KeepAliveInterval { get; init; } = 60;
    
    /// <summary>
    /// 是否启用TLS/SSL
    /// </summary>
    public bool UseTls { get; init; } = false;
    
    /// <summary>
    /// 是否启用自动重连
    /// </summary>
    public bool AutoReconnect { get; init; } = true;
    
    /// <summary>
    /// 重连间隔（秒）
    /// </summary>
    [Range(1, 300, ErrorMessage = "重连间隔必须在1-300秒之间")]
    public int ReconnectInterval { get; init; } = 5;
    
    /// <summary>
    /// 最大重连次数（0表示无限重连）
    /// </summary>
    [Range(0, 100, ErrorMessage = "最大重连次数必须在0-100之间")]
    public int MaxReconnectAttempts { get; init; } = 0;
    
    /// <summary>
    /// 消息质量等级（QoS）
    /// </summary>
    [Range(0, 2, ErrorMessage = "消息质量等级必须在0-2之间")]
    public int QualityOfServiceLevel { get; init; } = 1;
    
    /// <summary>
    /// 是否保留消息
    /// </summary>
    public bool RetainMessages { get; init; } = false;
}