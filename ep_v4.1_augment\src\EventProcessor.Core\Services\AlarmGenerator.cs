using System.Text.RegularExpressions;
using EventProcessor.Core.Models;
using Microsoft.Extensions.Logging;

namespace EventProcessor.Core.Services;

/// <summary>
/// 告警生成器实现 - 支持动态字段映射和多数据源组合
/// </summary>
public class AlarmGenerator : IAlarmGenerator
{
    private readonly ILogger<AlarmGenerator> _logger;

    /// <summary>
    /// 初始化告警生成器
    /// </summary>
    /// <param name="logger">日志记录器</param>
    public AlarmGenerator(ILogger<AlarmGenerator> logger)
    {
        _logger = logger;
    }

    /// <summary>
    /// 生成告警消息
    /// </summary>
    /// <param name="context">事件上下文</param>
    /// <param name="config">告警配置</param>
    /// <returns>告警消息</returns>
    public async Task<AlarmMessage> GenerateAlarmAsync(EventContext context, AlarmConfiguration config)
    {
        try
        {
            var alarmFields = new Dictionary<string, object>();

            // 处理字段映射
            if (config.Fields != null)
            {
                foreach (var fieldMapping in config.Fields)
                {
                    var fieldValue = await ExtractFieldValueAsync(context, fieldMapping);
                    alarmFields[fieldMapping.AlarmFieldName] = fieldValue;
                }
            }

            var alarmMessage = new AlarmMessage
            {
                EventId = context.EventId,
                InstanceId = context.InstanceId,
                Fields = alarmFields,
                Template = config.CustomTemplate,
                AlarmType = "Alarm"
            };

            _logger.LogInformation("告警消息已生成: {EventId}, 实例: {InstanceId}, 字段数: {FieldCount}", 
                                 context.EventId, context.InstanceId, alarmFields.Count);

            return alarmMessage;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "生成告警消息失败: {EventId}, 实例: {InstanceId}", 
                           context.EventId, context.InstanceId);
            throw;
        }
    }

    /// <summary>
    /// 生成取消告警消息
    /// </summary>
    /// <param name="context">事件上下文</param>
    /// <param name="config">告警配置</param>
    /// <param name="reason">取消原因</param>
    /// <returns>取消告警消息</returns>
    public async Task<AlarmMessage> GenerateCancellationAsync(EventContext context, AlarmConfiguration config, string reason)
    {
        try
        {
            var cancellationFields = new Dictionary<string, object>
            {
                ["action"] = "cancel_alarm",
                ["reason"] = reason,
                ["timestamp"] = DateTime.UtcNow.ToString("yyyyMMddHHmmssfff"),
                ["original_event_id"] = context.EventId,
                ["original_instance_id"] = context.InstanceId
            };

            // 可选：包含原始告警的部分字段
            if (config.Fields != null)
            {
                foreach (var fieldMapping in config.Fields.Take(3)) // 只取前3个字段作为参考
                {
                    try
                    {
                        var fieldValue = await ExtractFieldValueAsync(context, fieldMapping);
                        cancellationFields[$"original_{fieldMapping.AlarmFieldName}"] = fieldValue;
                    }
                    catch
                    {
                        // 忽略字段提取错误
                    }
                }
            }

            var cancellationMessage = new AlarmMessage
            {
                EventId = context.EventId,
                InstanceId = context.InstanceId,
                Fields = cancellationFields,
                AlarmType = "Cancellation",
                CancellationReason = reason
            };

            _logger.LogInformation("告警撤销消息已生成: {EventId}, 实例: {InstanceId}, 原因: {Reason}", 
                                 context.EventId, context.InstanceId, reason);

            return cancellationMessage;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "生成告警撤销消息失败: {EventId}, 实例: {InstanceId}", 
                           context.EventId, context.InstanceId);
            throw;
        }
    }

    /// <summary>
    /// 验证告警配置
    /// </summary>
    /// <param name="config">告警配置</param>
    /// <returns>验证结果</returns>
    public ValidationResult ValidateConfiguration(AlarmConfiguration config)
    {
        var errors = new List<string>();
        var warnings = new List<string>();

        try
        {
            // 验证字段映射
            if (config.Fields == null || config.Fields.Count == 0)
            {
                warnings.Add("未配置字段映射，告警消息将为空");
            }
            else
            {
                foreach (var field in config.Fields)
                {
                    ValidateFieldMapping(field, errors, warnings);
                }
            }

            // 验证自定义模板
            if (!string.IsNullOrEmpty(config.CustomTemplate))
            {
                ValidateCustomTemplate(config.CustomTemplate, errors, warnings);
            }

            return new ValidationResult
            {
                IsValid = errors.Count == 0,
                Errors = errors.ToArray(),
                Warnings = warnings.ToArray()
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "验证告警配置时发生异常");
            return new ValidationResult
            {
                IsValid = false,
                Errors = new[] { $"验证过程异常: {ex.Message}" }
            };
        }
    }

    /// <summary>
    /// 提取字段值
    /// </summary>
    private Task<object> ExtractFieldValueAsync(EventContext context, FieldMapping mapping)
    {
        try
        {
            var sourceData = GetSourceData(context, mapping.SourceRuleType);

            if (string.IsNullOrEmpty(mapping.SourceFieldName))
            {
                return Task.FromResult<object>(mapping.DefaultValue ?? "");
            }

            // 多字段处理
            if (mapping.SourceFieldName.Contains(','))
            {
                return Task.FromResult<object>(ProcessMultipleFields(sourceData, mapping));
            }

            // 单字段处理
            if (sourceData.TryGetValue(mapping.SourceFieldName, out var value))
            {
                return Task.FromResult<object>(ApplyFormatTemplate(value, mapping.FormatTemplate));
            }

            return Task.FromResult<object>(mapping.DefaultValue ?? "");
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "提取字段值失败: {FieldName}, 使用默认值", mapping.AlarmFieldName);
            return Task.FromResult<object>(mapping.DefaultValue ?? "");
        }
    }

    /// <summary>
    /// 获取源数据
    /// </summary>
    private Dictionary<string, object> GetSourceData(EventContext context, string sourceRuleType)
    {
        return sourceRuleType switch
        {
            "ExclusionRules" => context.ExclusionData,
            "BusinessRules" => context.BusinessData,
            "AIResultRules" => context.AIResultData,
            "DeviceSignal" => context.DeviceData,
            _ => new Dictionary<string, object>()
        };
    }

    /// <summary>
    /// 处理多字段
    /// </summary>
    private object ProcessMultipleFields(Dictionary<string, object> sourceData, FieldMapping mapping)
    {
        var fieldNames = mapping.SourceFieldName.Split(',', StringSplitOptions.RemoveEmptyEntries);
        var fieldValues = new Dictionary<string, object>();

        foreach (var fieldName in fieldNames)
        {
            var trimmedFieldName = fieldName.Trim();
            if (sourceData.TryGetValue(trimmedFieldName, out var value))
            {
                fieldValues[trimmedFieldName] = value;
            }
            else
            {
                fieldValues[trimmedFieldName] = "";
            }
        }

        return ApplyFormatTemplate(fieldValues, mapping.FormatTemplate);
    }

    /// <summary>
    /// 应用格式化模板
    /// </summary>
    private object ApplyFormatTemplate(object value, string? formatTemplate)
    {
        if (string.IsNullOrEmpty(formatTemplate))
        {
            return value;
        }

        try
        {
            if (value is Dictionary<string, object> fieldValues)
            {
                // 多字段格式化
                var result = formatTemplate;
                foreach (var kvp in fieldValues)
                {
                    var placeholder = $"{{{kvp.Key}}}";
                    result = result.Replace(placeholder, kvp.Value?.ToString() ?? "");
                }
                return result;
            }
            else
            {
                // 单字段格式化
                if (formatTemplate.Contains("{") && formatTemplate.Contains("}"))
                {
                    // 检查是否为格式化字符串（如 {value:P2}）
                    var formatMatch = Regex.Match(formatTemplate, @"\{[^:}]+:([^}]+)\}");
                    if (formatMatch.Success)
                    {
                        var format = formatMatch.Groups[1].Value;
                        if (double.TryParse(value?.ToString(), out var numValue))
                        {
                            return numValue.ToString(format);
                        }
                    }
                    
                    // 普通占位符替换
                    return formatTemplate.Replace("{value}", value?.ToString() ?? "");
                }
                else
                {
                    // 直接格式化
                    return string.Format(formatTemplate, value);
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "应用格式化模板失败: {Template}, 值: {Value}", formatTemplate, value);
            return value;
        }
    }

    /// <summary>
    /// 验证字段映射
    /// </summary>
    private void ValidateFieldMapping(FieldMapping field, List<string> errors, List<string> warnings)
    {
        if (string.IsNullOrEmpty(field.AlarmFieldName))
        {
            errors.Add("告警字段名称不能为空");
        }

        if (string.IsNullOrEmpty(field.SourceRuleType))
        {
            errors.Add($"字段 {field.AlarmFieldName} 的源规则类型不能为空");
        }
        else if (!IsValidSourceRuleType(field.SourceRuleType))
        {
            errors.Add($"字段 {field.AlarmFieldName} 的源规则类型无效: {field.SourceRuleType}");
        }

        if (string.IsNullOrEmpty(field.SourceFieldName) && string.IsNullOrEmpty(field.DefaultValue))
        {
            warnings.Add($"字段 {field.AlarmFieldName} 既没有源字段名称也没有默认值");
        }

        if (!string.IsNullOrEmpty(field.FormatTemplate))
        {
            ValidateFormatTemplate(field.FormatTemplate, field.AlarmFieldName, warnings);
        }
    }

    /// <summary>
    /// 验证源规则类型
    /// </summary>
    private bool IsValidSourceRuleType(string sourceRuleType)
    {
        return sourceRuleType switch
        {
            "ExclusionRules" or "BusinessRules" or "AIResultRules" or "DeviceSignal" => true,
            _ => false
        };
    }

    /// <summary>
    /// 验证格式化模板
    /// </summary>
    private void ValidateFormatTemplate(string template, string fieldName, List<string> warnings)
    {
        try
        {
            // 检查占位符格式
            var placeholders = Regex.Matches(template, @"\{([^}]+)\}");
            if (placeholders.Count == 0)
            {
                warnings.Add($"字段 {fieldName} 的格式化模板可能无效：没有找到占位符");
            }

            // 检查格式化字符串语法
            foreach (Match match in placeholders)
            {
                var placeholder = match.Groups[1].Value;
                if (placeholder.Contains(":"))
                {
                    var parts = placeholder.Split(':');
                    if (parts.Length != 2)
                    {
                        warnings.Add($"字段 {fieldName} 的格式化占位符语法可能错误: {match.Value}");
                    }
                }
            }
        }
        catch (Exception ex)
        {
            warnings.Add($"字段 {fieldName} 的格式化模板验证失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 验证自定义模板
    /// </summary>
    private void ValidateCustomTemplate(string template, List<string> errors, List<string> warnings)
    {
        if (template.Length > 10000)
        {
            warnings.Add("自定义模板过长，可能影响性能");
        }

        // 检查是否包含潜在的安全风险
        var dangerousPatterns = new[] { "<script", "javascript:", "eval(", "function(" };
        foreach (var pattern in dangerousPatterns)
        {
            if (template.Contains(pattern, StringComparison.OrdinalIgnoreCase))
            {
                warnings.Add($"自定义模板包含潜在安全风险的内容: {pattern}");
            }
        }
    }
}
