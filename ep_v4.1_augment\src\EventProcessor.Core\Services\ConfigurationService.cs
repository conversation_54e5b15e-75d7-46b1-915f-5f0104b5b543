using System.ComponentModel.DataAnnotations;
using System.Text.Json;
using EventProcessor.Core.Models;
using EventProcessor.Core.Validation;
using Microsoft.Extensions.Logging;
using YamlDotNet.Serialization;
using YamlDotNet.Serialization.NamingConventions;

namespace EventProcessor.Core.Services;

/// <summary>
/// 配置服务实现 - 支持JSON/YAML配置加载、验证和热重载
/// </summary>
public class ConfigurationService : IConfigurationService, IDisposable
{
    private readonly ILogger<ConfigurationService> _logger;
    private readonly object _lockObject = new();
    private bool _disposed = false;

    // 配置状态
    private EventConfiguration _currentConfiguration = null!;
    private string? _configurationPath;
    private bool _isValid = false;
    private FileSystemWatcher? _fileWatcher;
    private Timer? _reloadTimer;

    // 统计信息
    private DateTime? _loadedAt;
    private DateTime? _lastModifiedAt;
    private long _fileSize;
    private int _reloadCount = 0;
    private int _validationFailureCount = 0;
    private bool _isMonitoring = false;
    private string? _configurationFormat;

    /// <summary>
    /// 配置变更事件
    /// </summary>
    public event EventHandler<ConfigurationChangedEventArgs>? ConfigurationChanged;
    
    /// <summary>
    /// 配置验证失败事件
    /// </summary>
    public event EventHandler<ConfigurationValidationFailedEventArgs>? ValidationFailed;

    private readonly ConfigurationValidator _validator;

    /// <summary>
    /// 初始化配置服务
    /// </summary>
    /// <param name="logger">日志记录器</param>
    public ConfigurationService(ILogger<ConfigurationService> logger)
    {
        _logger = logger;
        var validatorLogger = Microsoft.Extensions.Logging.LoggerFactory.Create(builder => builder.AddConsole()).CreateLogger<ConfigurationValidator>();
        _validator = new ConfigurationValidator(validatorLogger);
    }

    /// <summary>
    /// 当前配置
    /// </summary>
    public EventConfiguration CurrentConfiguration
    {
        get
        {
            lock (_lockObject)
            {
                return _currentConfiguration;
            }
        }
    }

    /// <summary>
    /// 配置是否有效
    /// </summary>
    public bool IsValid
    {
        get
        {
            lock (_lockObject)
            {
                return _isValid;
            }
        }
    }

    /// <summary>
    /// 加载配置文件
    /// </summary>
    /// <param name="configurationPath">配置文件路径</param>
    /// <param name="cancellationToken">取消令牌</param>
    public async Task LoadConfigurationAsync(string configurationPath, CancellationToken cancellationToken = default)
    {
        try
        {
            if (!File.Exists(configurationPath))
            {
                throw new FileNotFoundException($"配置文件不存在: {configurationPath}");
            }

            var fileInfo = new FileInfo(configurationPath);
            var content = await File.ReadAllTextAsync(configurationPath, cancellationToken);

            var configuration = await ParseConfigurationAsync(content, configurationPath);
            var validationResult = ValidateConfiguration(configuration);

            if (!validationResult.IsValid)
            {
                _validationFailureCount++;
                var validationException = new InvalidOperationException(
                    $"配置验证失败: {string.Join(", ", validationResult.Errors)}");

                ValidationFailed?.Invoke(this, new ConfigurationValidationFailedEventArgs
                {
                    ConfigurationPath = configurationPath,
                    Errors = validationResult.Errors,
                    Exception = validationException
                });

                throw validationException;
            }

            // 记录警告信息
            if (validationResult.Warnings.Length > 0)
            {
                foreach (var warning in validationResult.Warnings)
                {
                    _logger.LogWarning("配置警告: {Warning}", warning);
                }
            }

            lock (_lockObject)
            {
                var oldConfiguration = _currentConfiguration;
                _currentConfiguration = configuration;
                _configurationPath = configurationPath;
                _isValid = true;
                _loadedAt = DateTime.UtcNow;
                _lastModifiedAt = fileInfo.LastWriteTimeUtc;
                _fileSize = fileInfo.Length;
                _configurationFormat = DetermineConfigurationFormat(configurationPath);

                if (oldConfiguration != null)
                {
                    _reloadCount++;
                }

                _logger.LogInformation("配置已加载: {Path}, 格式: {Format}, 大小: {Size} 字节", 
                                     configurationPath, _configurationFormat, _fileSize);

                // 触发配置变更事件
                ConfigurationChanged?.Invoke(this, new ConfigurationChangedEventArgs
                {
                    OldConfiguration = oldConfiguration,
                    NewConfiguration = configuration,
                    ChangeReason = oldConfiguration == null ? "InitialLoad" : "Reload"
                });
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "加载配置失败: {Path}", configurationPath);
            
            lock (_lockObject)
            {
                _isValid = false;
                _validationFailureCount++;
            }

            throw;
        }
    }

    /// <summary>
    /// 重新加载配置
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    public async Task ReloadConfigurationAsync(CancellationToken cancellationToken = default)
    {
        if (string.IsNullOrEmpty(_configurationPath))
        {
            throw new InvalidOperationException("没有配置文件路径，无法重新加载");
        }

        await LoadConfigurationAsync(_configurationPath, cancellationToken);
    }

    /// <summary>
    /// 验证配置
    /// </summary>
    /// <param name="configuration">事件配置</param>
    /// <returns>验证结果</returns>
    public ValidationResult ValidateConfiguration(EventConfiguration configuration)
    {
        try
        {
            _logger.LogDebug("开始验证配置: {EventId}", configuration.EventId);

            // 使用数据注解验证基础字段
            var errors = new List<string>();
            var warnings = new List<string>();

            var validationContext = new ValidationContext(configuration);
            var validationResults = new List<System.ComponentModel.DataAnnotations.ValidationResult>();
            var isDataAnnotationValid = Validator.TryValidateObject(configuration, validationContext, validationResults, true);

            if (!isDataAnnotationValid)
            {
                errors.AddRange(validationResults.Select(r => r.ErrorMessage ?? "未知验证错误"));
            }

            // 使用增强的配置验证器进行全面验证
            var comprehensiveResult = _validator.ValidateConfiguration(configuration);

            // 合并验证结果
            errors.AddRange(comprehensiveResult.Errors);
            warnings.AddRange(comprehensiveResult.Warnings);

            var finalResult = new ValidationResult
            {
                IsValid = errors.Count == 0,
                Errors = errors.Distinct().ToArray(), // 去重
                Warnings = warnings.Distinct().ToArray() // 去重
            };

            if (finalResult.IsValid)
            {
                _logger.LogInformation("配置验证成功: {EventId}, 警告数: {WarningCount}",
                    configuration.EventId, finalResult.Warnings.Length);
            }
            else
            {
                _logger.LogWarning("配置验证失败: {EventId}, 错误数: {ErrorCount}, 警告数: {WarningCount}",
                    configuration.EventId, finalResult.Errors.Length, finalResult.Warnings.Length);

                foreach (var error in finalResult.Errors)
                {
                    _logger.LogError("配置错误: {Error}", error);
                }
            }

            return finalResult;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "配置验证过程中发生异常");
            return new ValidationResult
            {
                IsValid = false,
                Errors = new[] { $"配置验证异常: {ex.Message}" },
                Warnings = Array.Empty<string>()
            };
        }
    }

    /// <summary>
    /// 启动时验证配置 - 如果有严重错误则快速失败
    /// </summary>
    /// <param name="configurationPath">配置文件路径</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>验证任务</returns>
    public async Task ValidateStartupConfigurationAsync(string configurationPath, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("开始启动时配置验证: {ConfigurationPath}", configurationPath);

            if (!File.Exists(configurationPath))
            {
                throw new FileNotFoundException($"配置文件不存在: {configurationPath}");
            }

            var content = await File.ReadAllTextAsync(configurationPath, cancellationToken);
            if (string.IsNullOrWhiteSpace(content))
            {
                throw new InvalidOperationException($"配置文件为空: {configurationPath}");
            }

            var configuration = await ParseConfigurationAsync(content, configurationPath);
            var validationResult = ValidateConfiguration(configuration);

            if (!validationResult.IsValid)
            {
                var criticalErrors = validationResult.Errors.Where(IsCriticalError).ToArray();

                if (criticalErrors.Length > 0)
                {
                    var errorMessage = $"发现严重配置错误，系统无法启动:\n{string.Join("\n", criticalErrors)}";
                    _logger.LogCritical("启动时配置验证失败: {Errors}", string.Join("; ", criticalErrors));
                    throw new InvalidOperationException(errorMessage);
                }

                // 非严重错误记录警告但允许启动
                _logger.LogWarning("配置存在非严重错误，系统将尝试启动: {Errors}",
                    string.Join("; ", validationResult.Errors));
            }

            if (validationResult.Warnings.Length > 0)
            {
                _logger.LogWarning("配置存在警告: {Warnings}",
                    string.Join("; ", validationResult.Warnings));
            }

            _logger.LogInformation("启动时配置验证完成: {EventId}", configuration.EventId);
        }
        catch (Exception ex)
        {
            _logger.LogCritical(ex, "启动时配置验证失败: {ConfigurationPath}", configurationPath);
            throw;
        }
    }

    /// <summary>
    /// 判断是否为严重错误（会阻止系统启动）
    /// </summary>
    /// <param name="error">错误信息</param>
    /// <returns>是否为严重错误</returns>
    private bool IsCriticalError(string error)
    {
        var criticalPatterns = new[]
        {
            "EventId 不能为空",
            "EventName 不能为空",
            "CommId 不能为空",
            "PositionId 不能为空",
            "不支持的评估策略",
            "必须配置规则配置",
            "设备信号必须配置至少一个主题",
            "设备信号必须指定触发字段",
            "设备信号必须配置触发值映射",
            "业务逻辑评估策略需要配置BusinessRules",
            "AI 策略需要配置 AI 提示词",
            "AI 策略需要配置 AI 结果规则",
            "不支持的数据类型",
            "不支持操作符"
        };

        return criticalPatterns.Any(pattern => error.Contains(pattern, StringComparison.OrdinalIgnoreCase));
    }

    /// <summary>
    /// 开始监控配置文件变化
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    public Task StartMonitoringAsync(CancellationToken cancellationToken = default)
    {
        if (string.IsNullOrEmpty(_configurationPath))
        {
            throw new InvalidOperationException("没有配置文件路径，无法启动监控");
        }

        try
        {
            var directory = Path.GetDirectoryName(_configurationPath);
            var fileName = Path.GetFileName(_configurationPath);

            if (string.IsNullOrEmpty(directory))
            {
                throw new InvalidOperationException("无法确定配置文件目录");
            }

            _fileWatcher = new FileSystemWatcher(directory, fileName)
            {
                NotifyFilter = NotifyFilters.LastWrite | NotifyFilters.Size,
                EnableRaisingEvents = true
            };

            _fileWatcher.Changed += OnConfigurationFileChanged;

            lock (_lockObject)
            {
                _isMonitoring = true;
            }

            _logger.LogInformation("配置文件监控已启动: {Path}", _configurationPath);
            return Task.CompletedTask;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "启动配置监控失败: {Path}", _configurationPath);
            throw;
        }
    }

    /// <summary>
    /// 停止监控配置文件变化
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    public Task StopMonitoringAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            _fileWatcher?.Dispose();
            _reloadTimer?.Dispose();

            lock (_lockObject)
            {
                _isMonitoring = false;
            }

            _logger.LogInformation("配置文件监控已停止");
            return Task.CompletedTask;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "停止配置监控失败");
            throw;
        }
    }

    /// <summary>
    /// 获取配置服务统计信息
    /// </summary>
    /// <returns>配置统计信息</returns>
    public ConfigurationStatistics GetStatistics()
    {
        lock (_lockObject)
        {
            return new ConfigurationStatistics
            {
                ConfigurationPath = _configurationPath,
                LoadedAt = _loadedAt,
                LastModifiedAt = _lastModifiedAt,
                FileSize = _fileSize,
                ReloadCount = _reloadCount,
                ValidationFailureCount = _validationFailureCount,
                IsMonitoring = _isMonitoring,
                ConfigurationFormat = _configurationFormat
            };
        }
    }

    /// <summary>
    /// 解析配置文件
    /// </summary>
    private Task<EventConfiguration> ParseConfigurationAsync(string content, string filePath)
    {
        var extension = Path.GetExtension(filePath).ToLowerInvariant();

        var result = extension switch
        {
            ".json" => ParseJsonConfiguration(content),
            ".yaml" or ".yml" => ParseYamlConfiguration(content),
            _ => throw new NotSupportedException($"不支持的配置文件格式: {extension}")
        };
        return Task.FromResult(result);
    }

    /// <summary>
    /// 解析JSON配置
    /// </summary>
    private EventConfiguration ParseJsonConfiguration(string content)
    {
        try
        {
            var options = new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true,
                AllowTrailingCommas = true,
                ReadCommentHandling = JsonCommentHandling.Skip
            };

            var configuration = JsonSerializer.Deserialize<EventConfiguration>(content, options);
            if (configuration == null)
            {
                throw new InvalidOperationException("JSON配置反序列化结果为null");
            }

            return configuration;
        }
        catch (JsonException ex)
        {
            throw new InvalidOperationException($"JSON配置解析失败: {ex.Message}", ex);
        }
    }

    

    /// <summary>
    /// 解析YAML配置
    /// </summary>
    private EventConfiguration ParseYamlConfiguration(string content)
    {
        try
        {
            var deserializer = new DeserializerBuilder()
                .WithNamingConvention(CamelCaseNamingConvention.Instance)
                .IgnoreUnmatchedProperties()
                .Build();

            var configuration = deserializer.Deserialize<EventConfiguration>(content);
            if (configuration == null)
            {
                throw new InvalidOperationException("YAML配置反序列化结果为null");
            }

            return configuration;
        }
        catch (Exception ex)
        {
            throw new InvalidOperationException($"YAML配置解析失败: {ex.Message}", ex);
        }
    }

    /// <summary>
    /// 确定配置文件格式
    /// </summary>
    private string DetermineConfigurationFormat(string filePath)
    {
        var extension = Path.GetExtension(filePath).ToLowerInvariant();
        return extension switch
        {
            ".json" => "JSON",
            ".yaml" or ".yml" => "YAML",
            _ => "Unknown"
        };
    }

    /// <summary>
    /// 验证业务逻辑
    /// </summary>
    private void ValidateBusinessLogic(EventConfiguration configuration, List<string> errors, List<string> warnings)
    {
        // 验证评估策略与配置的一致性
        if (configuration.EvaluationStrategy == "AI" || configuration.EvaluationStrategy == "AIAndBusiness")
        {
            if (string.IsNullOrEmpty(configuration.AIPrompt))
            {
                errors.Add("AI评估策略需要配置AIPrompt");
            }

            if (!configuration.AIAnalysisDelaySec.HasValue)
            {
                warnings.Add("AI评估策略建议配置AIAnalysisDelaySec");
            }
        }

        // 验证规则配置
        if (configuration.RuleConfiguration.BusinessRules == null ||
            configuration.RuleConfiguration.BusinessRules.Length == 0)
        {
            if (configuration.EvaluationStrategy == "BusinessOnly" ||
                configuration.EvaluationStrategy == "AIAndBusiness")
            {
                errors.Add("业务逻辑评估策略需要配置BusinessRules");
            }
        }

        // 验证设备信号配置
        if (configuration.DeviceSignal != null)
        {
            if (configuration.DeviceSignal.Topics == null || configuration.DeviceSignal.Topics.Length == 0)
            {
                errors.Add("设备信号配置需要至少一个主题");
            }

            if (string.IsNullOrEmpty(configuration.DeviceSignal.TriggerField))
            {
                errors.Add("设备信号配置需要指定触发字段");
            }

            if (configuration.DeviceSignal.TriggerValues == null ||
                configuration.DeviceSignal.TriggerValues.Count == 0)
            {
                errors.Add("设备信号配置需要指定触发值映射");
            }
        }

        // 验证告警配置
        if (configuration.RuleConfiguration.AlarmConfig.Fields == null ||
            configuration.RuleConfiguration.AlarmConfig.Fields.Count == 0)
        {
            warnings.Add("未配置告警字段映射，告警消息可能为空");
        }

        // 验证时间窗口配置
        if (configuration.CorrelationTimeWindow == "custom" &&
            !configuration.CustomTimeWindowMinutes.HasValue)
        {
            errors.Add("自定义时间窗口策略需要指定CustomTimeWindowMinutes");
        }
    }

    /// <summary>
    /// 配置文件变更事件处理
    /// </summary>
    private void OnConfigurationFileChanged(object sender, FileSystemEventArgs e)
    {
        try
        {
            // 使用定时器延迟重新加载，避免文件写入过程中的多次触发
            _reloadTimer?.Dispose();
            _reloadTimer = new Timer(DelayedReload, null, TimeSpan.FromSeconds(2), Timeout.InfiniteTimeSpan);

            _logger.LogDebug("检测到配置文件变更: {Path}", e.FullPath);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "处理配置文件变更事件失败");
        }
    }

    /// <summary>
    /// 延迟重新加载
    /// </summary>
    private async void DelayedReload(object? state)
    {
        try
        {
            await ReloadConfigurationAsync();
            _logger.LogInformation("配置文件热重载完成");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "配置文件热重载失败");
        }
    }

    /// <summary>
    /// 释放配置服务资源
    /// </summary>
    public void Dispose()
    {
        Dispose(true);
        GC.SuppressFinalize(this);
    }

    /// <summary>
    /// 释放资源的具体实现
    /// </summary>
    /// <param name="disposing">是否释放托管资源</param>
    protected virtual void Dispose(bool disposing)
    {
        if (!_disposed && disposing)
        {
            try
            {
                _fileWatcher?.Dispose();
                _reloadTimer?.Dispose();
                _logger.LogDebug("ConfigurationService已释放");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "释放ConfigurationService时发生异常");
            }
            finally
            {
                _disposed = true;
            }
        }
    }
}
