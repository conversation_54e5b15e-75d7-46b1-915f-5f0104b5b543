using System.Collections.Concurrent;
using EventProcessor.Core.Models;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace EventProcessor.Core.Services;

/// <summary>
/// 错误处理服务实现
/// </summary>
public class ErrorHandlingService : IErrorHandlingService
{
    private readonly ErrorHandlingConfiguration _config;
    private readonly ILogger<ErrorHandlingService> _logger;
    private readonly object _lockObject = new();

    // 错误统计
    private readonly ConcurrentDictionary<string, long> _errorsByType = new();
    private readonly ConcurrentDictionary<string, long> _errorsByComponent = new();
    private readonly ConcurrentQueue<DateTime> _recentErrors = new();
    private long _totalErrors = 0;
    private long _totalRetries = 0;
    private long _totalDegradations = 0;
    private DateTime? _lastErrorAt;

    // 降级状态跟踪
    private readonly ConcurrentDictionary<string, int> _errorCounts = new();
    private readonly ConcurrentDictionary<string, DateTime> _lastErrorTimes = new();

    /// <summary>
    /// 错误发生事件
    /// </summary>
    public event EventHandler<ErrorOccurredEventArgs>? ErrorOccurred;
    /// <summary>
    /// 错误恢复事件
    /// </summary>
    public event EventHandler<ErrorRecoveredEventArgs>? ErrorRecovered;

    /// <summary>
    /// 初始化错误处理服务
    /// </summary>
    /// <param name="config">错误处理配置</param>
    /// <param name="logger">日志记录器</param>
    public ErrorHandlingService(IOptions<ErrorHandlingConfiguration> config, ILogger<ErrorHandlingService> logger)
    {
        _config = config.Value;
        _logger = logger;
    }

    /// <summary>
    /// 错误处理配置
    /// </summary>
    public ErrorHandlingConfiguration Configuration => _config;

    /// <summary>
    /// 处理异常
    /// </summary>
    /// <param name="exception">异常</param>
    /// <param name="context">错误上下文</param>
    /// <returns>错误处理结果</returns>
    public Task<ErrorHandlingResult> HandleExceptionAsync(Exception exception, ErrorContext context)
    {
        try
        {
            // 更新统计信息
            UpdateErrorStatistics(exception, context);

            // 确定错误类型
            var errorType = GetErrorType(exception);

            // 检查是否应该降级
            var shouldDegrade = ShouldDegrade(errorType, GetErrorCount(errorType));

            // 确定重试策略
            var shouldRetry = ShouldRetry(exception, context);
            var retryDelay = CalculateRetryDelay(context);

            var result = new ErrorHandlingResult
            {
                IsHandled = true,
                ShouldRetry = shouldRetry && !shouldDegrade,
                ShouldDegrade = shouldDegrade,
                DegradationStrategy = shouldDegrade ? GetDegradationStrategy(errorType) : null,
                Message = GetErrorMessage(exception, context),
                RetryDelayMs = retryDelay
            };

            // 记录错误
            LogError(exception, context, result);

            // 触发错误事件
            ErrorOccurred?.Invoke(this, new ErrorOccurredEventArgs
            {
                Exception = exception,
                Context = context,
                Result = result
            });

            return Task.FromResult(result);
        }
        catch (Exception ex)
        {
            _logger.LogCritical(ex, "错误处理服务本身发生异常");
            return Task.FromResult(new ErrorHandlingResult
            {
                IsHandled = false,
                ShouldRetry = false,
                ShouldDegrade = true,
                Message = "错误处理服务异常"
            });
        }
    }

    /// <summary>
    /// 带重试的执行操作（泛型版本）
    /// </summary>
    /// <typeparam name="T">返回类型</typeparam>
    /// <param name="operation">操作</param>
    /// <param name="context">错误上下文</param>
    /// <returns>操作结果</returns>
    public async Task<T> ExecuteWithRetryAsync<T>(Func<Task<T>> operation, ErrorContext context)
    {
        var maxRetries = _config.RetryPolicy.MaxRetries;
        var retryCount = 0;
        Exception? lastException = null;

        while (retryCount <= maxRetries)
        {
            try
            {
                var result = await operation();

                // 如果之前有错误，现在成功了，触发恢复事件
                if (retryCount > 0)
                {
                    TriggerRecoveryEvent(context, lastException);
                }

                return result;
            }
            catch (Exception ex)
            {
                lastException = ex;
                retryCount++;

                if (retryCount > maxRetries)
                {
                    // 最后一次重试失败，处理异常
                    var result = await HandleExceptionAsync(ex, context);
                    if (!result.ShouldRetry)
                    {
                        throw;
                    }
                }

                // 计算重试延迟
                var delay = CalculateRetryDelay(context, retryCount);
                if (delay > 0)
                {
                    await Task.Delay(delay);
                }

                Interlocked.Increment(ref _totalRetries);
                _logger.LogWarning("操作重试 {RetryCount}/{MaxRetries}: {Operation}, 异常: {Exception}", 
                                 retryCount, maxRetries, context.OperationName, ex.Message);
            }
        }

        throw lastException ?? new InvalidOperationException("操作失败且无异常信息");
    }

    /// <summary>
    /// 带重试的执行操作（无返回值版本）
    /// </summary>
    /// <param name="operation">操作</param>
    /// <param name="context">错误上下文</param>
    public async Task ExecuteWithRetryAsync(Func<Task> operation, ErrorContext context)
    {
        await ExecuteWithRetryAsync(async () =>
        {
            await operation();
            return true; // 返回一个虚拟值
        }, context);
    }

    /// <summary>
    /// 判断是否应该降级
    /// </summary>
    /// <param name="errorType">错误类型</param>
    /// <param name="errorCount">错误数量</param>
    /// <returns>是否应该降级</returns>
    public bool ShouldDegrade(string errorType, int errorCount)
    {
        // 根据错误容忍级别确定降级阈值
        var threshold = _config.ToleranceLevel switch
        {
            "Strict" => 1,
            "Normal" => 3,
            "Lenient" => 5,
            _ => 3
        };

        return errorCount >= threshold;
    }

    /// <summary>
    /// 获取错误统计信息
    /// </summary>
    /// <returns>错误统计信息</returns>
    public ErrorStatistics GetErrorStatistics()
    {
        lock (_lockObject)
        {
            // 清理过期的错误记录（只保留最近1小时的）
            CleanupRecentErrors();

            return new ErrorStatistics
            {
                TotalErrors = Interlocked.Read(ref _totalErrors),
                ErrorsByType = new Dictionary<string, long>(_errorsByType),
                ErrorsByComponent = new Dictionary<string, long>(_errorsByComponent),
                TotalRetries = Interlocked.Read(ref _totalRetries),
                TotalDegradations = Interlocked.Read(ref _totalDegradations),
                LastErrorAt = _lastErrorAt,
                ErrorRatePerMinute = CalculateErrorRate(),
                StatisticsWindow = TimeSpan.FromHours(1)
            };
        }
    }

    /// <summary>
    /// 重置错误统计
    /// </summary>
    public void ResetErrorStatistics()
    {
        lock (_lockObject)
        {
            _errorsByType.Clear();
            _errorsByComponent.Clear();
            _recentErrors.Clear();
            _errorCounts.Clear();
            _lastErrorTimes.Clear();

            Interlocked.Exchange(ref _totalErrors, 0);
            Interlocked.Exchange(ref _totalRetries, 0);
            Interlocked.Exchange(ref _totalDegradations, 0);
            _lastErrorAt = null;

            _logger.LogInformation("错误统计信息已重置");
        }
    }

    /// <summary>
    /// 更新错误统计信息
    /// </summary>
    private void UpdateErrorStatistics(Exception exception, ErrorContext context)
    {
        var errorType = GetErrorType(exception);
        var now = DateTime.UtcNow;

        lock (_lockObject)
        {
            Interlocked.Increment(ref _totalErrors);
            _errorsByType.AddOrUpdate(errorType, 1, (key, value) => value + 1);
            _errorsByComponent.AddOrUpdate(context.ComponentName, 1, (key, value) => value + 1);
            _recentErrors.Enqueue(now);
            _lastErrorAt = now;

            // 更新错误计数
            _errorCounts.AddOrUpdate(errorType, 1, (key, value) => value + 1);
            _lastErrorTimes[errorType] = now;
        }
    }

    /// <summary>
    /// 获取错误类型
    /// </summary>
    private string GetErrorType(Exception exception)
    {
        return exception switch
        {
            TimeoutException => "Timeout",
            OutOfMemoryException => "OutOfMemory",
            UnauthorizedAccessException => "Unauthorized",
            ArgumentException => "InvalidArgument",
            InvalidOperationException => "InvalidOperation",
            System.Net.NetworkInformation.NetworkInformationException => "Network",
            System.Net.Sockets.SocketException => "Network",
            System.IO.IOException => "IO",
            System.Text.Json.JsonException => "JsonParsing",
            _ => exception.GetType().Name
        };
    }

    /// <summary>
    /// 获取错误计数
    /// </summary>
    private int GetErrorCount(string errorType)
    {
        return _errorCounts.TryGetValue(errorType, out var count) ? count : 0;
    }

    /// <summary>
    /// 检查是否应该重试
    /// </summary>
    private bool ShouldRetry(Exception exception, ErrorContext context)
    {
        return exception switch
        {
            TimeoutException => true,
            System.Net.Sockets.SocketException => true,
            System.IO.IOException => true,
            OutOfMemoryException => false,
            ArgumentException => false,
            UnauthorizedAccessException => false,
            _ => true
        };
    }

    /// <summary>
    /// 计算重试延迟
    /// </summary>
    private int CalculateRetryDelay(ErrorContext context, int retryCount = 1)
    {
        var baseDelay = _config.RetryPolicy.RetryDelay;

        if (_config.RetryPolicy.UseExponentialBackoff)
        {
            var exponentialDelay = (int)(baseDelay * Math.Pow(2, retryCount - 1));
            return Math.Min(exponentialDelay, _config.RetryPolicy.MaxBackoffDelay);
        }

        return baseDelay;
    }

    /// <summary>
    /// 获取降级策略
    /// </summary>
    private string GetDegradationStrategy(string errorType)
    {
        return errorType switch
        {
            "Timeout" => _config.FallbackStrategy.OnTimerFailure,
            "Network" => _config.FallbackStrategy.OnMqttFailure,
            "OutOfMemory" => "ImmediateShutdown",
            _ => _config.FallbackStrategy.OnRuleFailure
        };
    }

    /// <summary>
    /// 获取错误消息
    /// </summary>
    private string GetErrorMessage(Exception exception, ErrorContext context)
    {
        return $"组件 {context.ComponentName} 执行操作 {context.OperationName} 时发生错误: {exception.Message}";
    }

    /// <summary>
    /// 记录错误日志
    /// </summary>
    private void LogError(Exception exception, ErrorContext context, ErrorHandlingResult result)
    {
        var logLevel = GetLogLevel(exception);

        using var scope = _logger.BeginScope(new Dictionary<string, object>
        {
            ["OperationName"] = context.OperationName,
            ["ComponentName"] = context.ComponentName,
            ["EventId"] = context.EventId ?? "N/A",
            ["CorrelationId"] = context.CorrelationId ?? "N/A",
            ["ShouldRetry"] = result.ShouldRetry,
            ["ShouldDegrade"] = result.ShouldDegrade
        });

        _logger.Log(logLevel, exception, "错误处理: {Message}", result.Message);

        if (_config.Logging.DetailedStackTrace && logLevel >= LogLevel.Error)
        {
            _logger.LogError("详细堆栈跟踪: {StackTrace}", exception.StackTrace);
        }
    }

    /// <summary>
    /// 获取日志级别
    /// </summary>
    private LogLevel GetLogLevel(Exception exception)
    {
        return exception switch
        {
            OutOfMemoryException => LogLevel.Critical,
            UnauthorizedAccessException => LogLevel.Error,
            TimeoutException => LogLevel.Warning,
            ArgumentException => LogLevel.Warning,
            _ => LogLevel.Error
        };
    }

    /// <summary>
    /// 触发恢复事件
    /// </summary>
    private void TriggerRecoveryEvent(ErrorContext context, Exception? lastException)
    {
        if (lastException != null)
        {
            var errorType = GetErrorType(lastException);
            var lastErrorTime = _lastErrorTimes.TryGetValue(errorType, out var time) ? time : DateTime.UtcNow;

            ErrorRecovered?.Invoke(this, new ErrorRecoveredEventArgs
            {
                Context = context,
                ErrorDuration = DateTime.UtcNow - lastErrorTime
            });

            _logger.LogInformation("操作已恢复: {Operation} in {Component}", 
                                 context.OperationName, context.ComponentName);
        }
    }

    /// <summary>
    /// 清理过期的错误记录
    /// </summary>
    private void CleanupRecentErrors()
    {
        var cutoff = DateTime.UtcNow.AddHours(-1);
        while (_recentErrors.TryPeek(out var errorTime) && errorTime < cutoff)
        {
            _recentErrors.TryDequeue(out _);
        }
    }

    /// <summary>
    /// 计算错误率
    /// </summary>
    private double CalculateErrorRate()
    {
        var recentErrorCount = _recentErrors.Count;
        return recentErrorCount; // 每小时错误数，可以转换为每分钟
    }
}
