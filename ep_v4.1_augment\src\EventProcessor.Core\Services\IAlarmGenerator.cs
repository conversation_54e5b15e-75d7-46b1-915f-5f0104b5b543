using EventProcessor.Core.Models;

namespace EventProcessor.Core.Services;

/// <summary>
/// 告警生成器接口
/// </summary>
public interface IAlarmGenerator
{
    /// <summary>
    /// 生成告警消息
    /// </summary>
    /// <param name="context">事件上下文</param>
    /// <param name="config">告警配置</param>
    /// <returns>告警消息</returns>
    Task<AlarmMessage> GenerateAlarmAsync(EventContext context, AlarmConfiguration config);

    /// <summary>
    /// 生成告警撤销消息
    /// </summary>
    /// <param name="context">事件上下文</param>
    /// <param name="config">告警配置</param>
    /// <param name="reason">撤销原因</param>
    /// <returns>告警撤销消息</returns>
    Task<AlarmMessage> GenerateCancellationAsync(EventContext context, AlarmConfiguration config, string reason);

    /// <summary>
    /// 验证告警配置
    /// </summary>
    /// <param name="config">告警配置</param>
    /// <returns>验证结果</returns>
    ValidationResult ValidateConfiguration(AlarmConfiguration config);
}

/// <summary>
/// 验证结果
/// </summary>
public record ValidationResult
{
    /// <summary>
    /// 是否有效
    /// </summary>
    public bool IsValid { get; init; }

    /// <summary>
    /// 错误消息列表
    /// </summary>
    public string[] Errors { get; init; } = Array.Empty<string>();

    /// <summary>
    /// 警告消息列表
    /// </summary>
    public string[] Warnings { get; init; } = Array.Empty<string>();
}
