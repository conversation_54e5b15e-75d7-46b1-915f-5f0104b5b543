using EventProcessor.Core.Models;

namespace EventProcessor.Core.Services;

/// <summary>
/// 配置服务接口
/// </summary>
public interface IConfigurationService
{
    /// <summary>
    /// 当前事件配置
    /// </summary>
    EventConfiguration CurrentConfiguration { get; }

    /// <summary>
    /// 配置是否有效
    /// </summary>
    bool IsValid { get; }

    /// <summary>
    /// 配置变更事件
    /// </summary>
    event EventHandler<ConfigurationChangedEventArgs>? ConfigurationChanged;

    /// <summary>
    /// 配置验证失败事件
    /// </summary>
    event EventHandler<ConfigurationValidationFailedEventArgs>? ValidationFailed;

    /// <summary>
    /// 加载配置
    /// </summary>
    /// <param name="configurationPath">配置文件路径</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>加载任务</returns>
    Task LoadConfigurationAsync(string configurationPath, CancellationToken cancellationToken = default);

    /// <summary>
    /// 重新加载配置
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>重新加载任务</returns>
    Task ReloadConfigurationAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// 验证配置
    /// </summary>
    /// <param name="configuration">事件配置</param>
    /// <returns>验证结果</returns>
    ValidationResult ValidateConfiguration(EventConfiguration configuration);

    /// <summary>
    /// 启动时验证配置 - 如果有严重错误则快速失败
    /// </summary>
    /// <param name="configurationPath">配置文件路径</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>验证任务</returns>
    Task ValidateStartupConfigurationAsync(string configurationPath, CancellationToken cancellationToken = default);

    /// <summary>
    /// 启动配置监控
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>启动任务</returns>
    Task StartMonitoringAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// 停止配置监控
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>停止任务</returns>
    Task StopMonitoringAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取配置统计信息
    /// </summary>
    /// <returns>配置统计信息</returns>
    ConfigurationStatistics GetStatistics();
}

/// <summary>
/// 配置变更事件参数
/// </summary>
public class ConfigurationChangedEventArgs : EventArgs
{
    /// <summary>
    /// 旧配置
    /// </summary>
    public EventConfiguration? OldConfiguration { get; init; }

    /// <summary>
    /// 新配置
    /// </summary>
    public required EventConfiguration NewConfiguration { get; init; }

    /// <summary>
    /// 变更原因
    /// </summary>
    public required string ChangeReason { get; init; }

    /// <summary>
    /// 变更时间
    /// </summary>
    public DateTime ChangedAt { get; init; } = DateTime.UtcNow;
}

/// <summary>
/// 配置验证失败事件参数
/// </summary>
public class ConfigurationValidationFailedEventArgs : EventArgs
{
    /// <summary>
    /// 配置文件路径
    /// </summary>
    public required string ConfigurationPath { get; init; }

    /// <summary>
    /// 错误信息
    /// </summary>
    public required string[] Errors { get; init; }

    /// <summary>
    /// 异常信息
    /// </summary>
    public Exception? Exception { get; init; }

    /// <summary>
    /// 失败时间
    /// </summary>
    public DateTime FailedAt { get; init; } = DateTime.UtcNow;
}

/// <summary>
/// 配置统计信息
/// </summary>
public record ConfigurationStatistics
{
    /// <summary>
    /// 配置文件路径
    /// </summary>
    public string? ConfigurationPath { get; init; }

    /// <summary>
    /// 配置加载时间
    /// </summary>
    public DateTime? LoadedAt { get; init; }

    /// <summary>
    /// 最后修改时间
    /// </summary>
    public DateTime? LastModifiedAt { get; init; }

    /// <summary>
    /// 配置文件大小（字节）
    /// </summary>
    public long FileSize { get; init; }

    /// <summary>
    /// 重新加载次数
    /// </summary>
    public int ReloadCount { get; init; }

    /// <summary>
    /// 验证失败次数
    /// </summary>
    public int ValidationFailureCount { get; init; }

    /// <summary>
    /// 是否正在监控
    /// </summary>
    public bool IsMonitoring { get; init; }

    /// <summary>
    /// 配置格式：JSON、YAML
    /// </summary>
    public string? ConfigurationFormat { get; init; }
}
