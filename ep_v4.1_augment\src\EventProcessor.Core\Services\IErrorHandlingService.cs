using EventProcessor.Core.Models;

namespace EventProcessor.Core.Services;

/// <summary>
/// 错误处理服务接口
/// </summary>
public interface IErrorHandlingService
{
    /// <summary>
    /// 错误处理配置
    /// </summary>
    ErrorHandlingConfiguration Configuration { get; }

    /// <summary>
    /// 错误发生事件
    /// </summary>
    event EventHandler<ErrorOccurredEventArgs>? ErrorOccurred;

    /// <summary>
    /// 错误恢复事件
    /// </summary>
    event EventHandler<ErrorRecoveredEventArgs>? ErrorRecovered;

    /// <summary>
    /// 处理异常
    /// </summary>
    /// <param name="exception">异常</param>
    /// <param name="context">错误上下文</param>
    /// <returns>处理结果</returns>
    Task<ErrorHandlingResult> HandleExceptionAsync(Exception exception, ErrorContext context);

    /// <summary>
    /// 执行带重试的操作
    /// </summary>
    /// <typeparam name="T">返回类型</typeparam>
    /// <param name="operation">操作</param>
    /// <param name="context">错误上下文</param>
    /// <returns>操作结果</returns>
    Task<T> ExecuteWithRetryAsync<T>(Func<Task<T>> operation, ErrorContext context);

    /// <summary>
    /// 执行带重试的操作（无返回值）
    /// </summary>
    /// <param name="operation">操作</param>
    /// <param name="context">错误上下文</param>
    /// <returns>执行任务</returns>
    Task ExecuteWithRetryAsync(Func<Task> operation, ErrorContext context);

    /// <summary>
    /// 检查是否应该降级
    /// </summary>
    /// <param name="errorType">错误类型</param>
    /// <param name="errorCount">错误次数</param>
    /// <returns>是否应该降级</returns>
    bool ShouldDegrade(string errorType, int errorCount);

    /// <summary>
    /// 获取错误统计信息
    /// </summary>
    /// <returns>错误统计信息</returns>
    ErrorStatistics GetErrorStatistics();

    /// <summary>
    /// 重置错误统计
    /// </summary>
    void ResetErrorStatistics();
}

/// <summary>
/// 错误上下文
/// </summary>
public record ErrorContext
{
    /// <summary>
    /// 操作名称
    /// </summary>
    public required string OperationName { get; init; }

    /// <summary>
    /// 组件名称
    /// </summary>
    public required string ComponentName { get; init; }

    /// <summary>
    /// 事件ID（可选）
    /// </summary>
    public string? EventId { get; init; }

    /// <summary>
    /// 关联ID（可选）
    /// </summary>
    public string? CorrelationId { get; init; }

    /// <summary>
    /// 附加数据
    /// </summary>
    public Dictionary<string, object> AdditionalData { get; init; } = new();

    /// <summary>
    /// 错误发生时间
    /// </summary>
    public DateTime OccurredAt { get; init; } = DateTime.UtcNow;
}

/// <summary>
/// 错误处理结果
/// </summary>
public record ErrorHandlingResult
{
    /// <summary>
    /// 是否成功处理
    /// </summary>
    public bool IsHandled { get; init; }

    /// <summary>
    /// 是否应该重试
    /// </summary>
    public bool ShouldRetry { get; init; }

    /// <summary>
    /// 是否应该降级
    /// </summary>
    public bool ShouldDegrade { get; init; }

    /// <summary>
    /// 降级策略
    /// </summary>
    public string? DegradationStrategy { get; init; }

    /// <summary>
    /// 处理消息
    /// </summary>
    public string? Message { get; init; }

    /// <summary>
    /// 重试延迟（毫秒）
    /// </summary>
    public int RetryDelayMs { get; init; }
}

/// <summary>
/// 错误发生事件参数
/// </summary>
public class ErrorOccurredEventArgs : EventArgs
{
    /// <summary>
    /// 异常
    /// </summary>
    public required Exception Exception { get; init; }

    /// <summary>
    /// 错误上下文
    /// </summary>
    public required ErrorContext Context { get; init; }

    /// <summary>
    /// 处理结果
    /// </summary>
    public required ErrorHandlingResult Result { get; init; }
}

/// <summary>
/// 错误恢复事件参数
/// </summary>
public class ErrorRecoveredEventArgs : EventArgs
{
    /// <summary>
    /// 错误上下文
    /// </summary>
    public required ErrorContext Context { get; init; }

    /// <summary>
    /// 恢复时间
    /// </summary>
    public DateTime RecoveredAt { get; init; } = DateTime.UtcNow;

    /// <summary>
    /// 错误持续时间
    /// </summary>
    public TimeSpan ErrorDuration { get; init; }
}

/// <summary>
/// 错误统计信息
/// </summary>
public record ErrorStatistics
{
    /// <summary>
    /// 总错误数
    /// </summary>
    public long TotalErrors { get; init; }

    /// <summary>
    /// 按类型分组的错误数
    /// </summary>
    public Dictionary<string, long> ErrorsByType { get; init; } = new();

    /// <summary>
    /// 按组件分组的错误数
    /// </summary>
    public Dictionary<string, long> ErrorsByComponent { get; init; } = new();

    /// <summary>
    /// 重试次数
    /// </summary>
    public long TotalRetries { get; init; }

    /// <summary>
    /// 降级次数
    /// </summary>
    public long TotalDegradations { get; init; }

    /// <summary>
    /// 最后错误时间
    /// </summary>
    public DateTime? LastErrorAt { get; init; }

    /// <summary>
    /// 错误率（每分钟）
    /// </summary>
    public double ErrorRatePerMinute { get; init; }

    /// <summary>
    /// 统计时间窗口
    /// </summary>
    public TimeSpan StatisticsWindow { get; init; }
}
