using EventProcessor.Core.Models;

namespace EventProcessor.Core.Services;

/// <summary>
/// MQTT服务接口
/// </summary>
public interface IMqttService : IDisposable
{
    /// <summary>
    /// 连接状态
    /// </summary>
    bool IsConnected { get; }

    /// <summary>
    /// 客户端ID
    /// </summary>
    string ClientId { get; }

    /// <summary>
    /// 连接事件
    /// </summary>
    event EventHandler<MqttConnectedEventArgs>? Connected;

    /// <summary>
    /// 断开连接事件
    /// </summary>
    event EventHandler<MqttDisconnectedEventArgs>? Disconnected;

    /// <summary>
    /// 消息接收事件
    /// </summary>
    event EventHandler<MqttMessageReceivedEventArgs>? MessageReceived;

    /// <summary>
    /// 启动MQTT服务
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>启动任务</returns>
    Task StartAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// 停止MQTT服务
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>停止任务</returns>
    Task StopAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// 订阅主题
    /// </summary>
    /// <param name="topic">主题</param>
    /// <param name="qos">服务质量等级</param>
    /// <returns>订阅任务</returns>
    Task SubscribeAsync(string topic, int qos = 1);

    /// <summary>
    /// 订阅多个主题
    /// </summary>
    /// <param name="topics">主题列表</param>
    /// <param name="qos">服务质量等级</param>
    /// <returns>订阅任务</returns>
    Task SubscribeAsync(string[] topics, int qos = 1);

    /// <summary>
    /// 取消订阅主题
    /// </summary>
    /// <param name="topic">主题</param>
    /// <returns>取消订阅任务</returns>
    Task UnsubscribeAsync(string topic);

    /// <summary>
    /// 发布消息
    /// </summary>
    /// <param name="topic">主题</param>
    /// <param name="payload">消息负载</param>
    /// <param name="qos">服务质量等级</param>
    /// <param name="retain">是否保留消息</param>
    /// <returns>发布任务</returns>
    Task PublishAsync(string topic, string payload, int qos = 1, bool retain = false);

    /// <summary>
    /// 发布消息（字节数组）
    /// </summary>
    /// <param name="topic">主题</param>
    /// <param name="payload">消息负载</param>
    /// <param name="qos">服务质量等级</param>
    /// <param name="retain">是否保留消息</param>
    /// <returns>发布任务</returns>
    Task PublishAsync(string topic, byte[] payload, int qos = 1, bool retain = false);

    /// <summary>
    /// 获取连接统计信息
    /// </summary>
    /// <returns>连接统计信息</returns>
    MqttConnectionStatistics GetConnectionStatistics();
}

/// <summary>
/// MQTT连接事件参数
/// </summary>
public class MqttConnectedEventArgs : EventArgs
{
    /// <summary>
    /// 客户端ID
    /// </summary>
    public required string ClientId { get; init; }

    /// <summary>
    /// 服务器地址
    /// </summary>
    public required string ServerAddress { get; init; }

    /// <summary>
    /// 连接时间
    /// </summary>
    public DateTime ConnectedAt { get; init; } = DateTime.UtcNow;
}

/// <summary>
/// MQTT断开连接事件参数
/// </summary>
public class MqttDisconnectedEventArgs : EventArgs
{
    /// <summary>
    /// 客户端ID
    /// </summary>
    public required string ClientId { get; init; }

    /// <summary>
    /// 断开原因
    /// </summary>
    public string? Reason { get; init; }

    /// <summary>
    /// 是否为异常断开
    /// </summary>
    public bool IsException { get; init; }

    /// <summary>
    /// 断开时间
    /// </summary>
    public DateTime DisconnectedAt { get; init; } = DateTime.UtcNow;
}

/// <summary>
/// MQTT消息接收事件参数
/// </summary>
public class MqttMessageReceivedEventArgs : EventArgs
{
    /// <summary>
    /// 主题
    /// </summary>
    public required string Topic { get; init; }

    /// <summary>
    /// 消息负载
    /// </summary>
    public required string Payload { get; init; }

    /// <summary>
    /// 服务质量等级
    /// </summary>
    public int QualityOfServiceLevel { get; init; }

    /// <summary>
    /// 是否保留消息
    /// </summary>
    public bool Retain { get; init; }

    /// <summary>
    /// 接收时间
    /// </summary>
    public DateTime ReceivedAt { get; init; } = DateTime.UtcNow;
}

/// <summary>
/// MQTT连接统计信息
/// </summary>
public record MqttConnectionStatistics
{
    /// <summary>
    /// 是否已连接
    /// </summary>
    public bool IsConnected { get; init; }

    /// <summary>
    /// 连接时间
    /// </summary>
    public DateTime? ConnectedAt { get; init; }

    /// <summary>
    /// 重连次数
    /// </summary>
    public int ReconnectCount { get; init; }

    /// <summary>
    /// 发送的消息数
    /// </summary>
    public long MessagesSent { get; init; }

    /// <summary>
    /// 接收的消息数
    /// </summary>
    public long MessagesReceived { get; init; }

    /// <summary>
    /// 订阅的主题数
    /// </summary>
    public int SubscribedTopicCount { get; init; }

    /// <summary>
    /// 最后活动时间
    /// </summary>
    public DateTime LastActivityAt { get; init; }
}
