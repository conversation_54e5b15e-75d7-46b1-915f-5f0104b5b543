namespace EventProcessor.Core.Services;

/// <summary>
/// 性能监控服务接口
/// </summary>
public interface IPerformanceMonitoringService : IDisposable
{
    /// <summary>
    /// 性能指标更新事件
    /// </summary>
    event EventHandler<PerformanceMetricsUpdatedEventArgs>? MetricsUpdated;

    /// <summary>
    /// 性能阈值超出事件
    /// </summary>
    event EventHandler<PerformanceThresholdExceededEventArgs>? ThresholdExceeded;

    /// <summary>
    /// 开始操作计时
    /// </summary>
    /// <param name="operationName">操作名称</param>
    /// <param name="componentName">组件名称</param>
    /// <returns>操作上下文</returns>
    IOperationContext StartOperation(string operationName, string componentName);

    /// <summary>
    /// 记录操作完成
    /// </summary>
    /// <param name="context">操作上下文</param>
    void CompleteOperation(IOperationContext context);

    /// <summary>
    /// 记录操作失败
    /// </summary>
    /// <param name="context">操作上下文</param>
    /// <param name="exception">异常</param>
    void FailOperation(IOperationContext context, Exception exception);

    /// <summary>
    /// 记录自定义指标
    /// </summary>
    /// <param name="metricName">指标名称</param>
    /// <param name="value">指标值</param>
    /// <param name="tags">标签</param>
    void RecordMetric(string metricName, double value, Dictionary<string, string>? tags = null);

    /// <summary>
    /// 增加计数器
    /// </summary>
    /// <param name="counterName">计数器名称</param>
    /// <param name="increment">增量</param>
    /// <param name="tags">标签</param>
    void IncrementCounter(string counterName, long increment = 1, Dictionary<string, string>? tags = null);

    /// <summary>
    /// 获取性能指标
    /// </summary>
    /// <returns>性能指标</returns>
    PerformanceMetrics GetMetrics();

    /// <summary>
    /// 获取系统健康状态
    /// </summary>
    /// <returns>健康状态</returns>
    HealthStatus GetHealthStatus();

    /// <summary>
    /// 重置指标
    /// </summary>
    void ResetMetrics();

    /// <summary>
    /// 启动监控
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>启动任务</returns>
    Task StartAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// 停止监控
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>停止任务</returns>
    Task StopAsync(CancellationToken cancellationToken = default);
}

/// <summary>
/// 操作上下文接口
/// </summary>
public interface IOperationContext : IDisposable
{
    /// <summary>
    /// 操作名称
    /// </summary>
    string OperationName { get; }

    /// <summary>
    /// 组件名称
    /// </summary>
    string ComponentName { get; }

    /// <summary>
    /// 开始时间
    /// </summary>
    DateTime StartTime { get; }

    /// <summary>
    /// 操作ID
    /// </summary>
    string OperationId { get; }

    /// <summary>
    /// 添加标签
    /// </summary>
    /// <param name="key">键</param>
    /// <param name="value">值</param>
    void AddTag(string key, string value);

    /// <summary>
    /// 设置结果
    /// </summary>
    /// <param name="success">是否成功</param>
    /// <param name="resultData">结果数据</param>
    void SetResult(bool success, object? resultData = null);
}

/// <summary>
/// 性能指标
/// </summary>
public record PerformanceMetrics
{
    /// <summary>
    /// CPU使用率（百分比）
    /// </summary>
    public double CpuUsagePercent { get; init; }

    /// <summary>
    /// 内存使用量（MB）
    /// </summary>
    public double MemoryUsageMB { get; init; }

    /// <summary>
    /// 内存使用率（百分比）
    /// </summary>
    public double MemoryUsagePercent { get; init; }

    /// <summary>
    /// 线程数
    /// </summary>
    public int ThreadCount { get; init; }

    /// <summary>
    /// GC回收次数
    /// </summary>
    public long GCCollectionCount { get; init; }

    /// <summary>
    /// 操作统计
    /// </summary>
    public Dictionary<string, OperationStatistics> OperationStats { get; init; } = new();

    /// <summary>
    /// 自定义指标
    /// </summary>
    public Dictionary<string, double> CustomMetrics { get; init; } = new();

    /// <summary>
    /// 计数器
    /// </summary>
    public Dictionary<string, long> Counters { get; init; } = new();

    /// <summary>
    /// 采集时间
    /// </summary>
    public DateTime CollectedAt { get; init; } = DateTime.UtcNow;
}

/// <summary>
/// 操作统计
/// </summary>
public record OperationStatistics
{
    /// <summary>
    /// 总调用次数
    /// </summary>
    public long TotalCalls { get; init; }

    /// <summary>
    /// 成功次数
    /// </summary>
    public long SuccessfulCalls { get; init; }

    /// <summary>
    /// 失败次数
    /// </summary>
    public long FailedCalls { get; init; }

    /// <summary>
    /// 平均执行时间（毫秒）
    /// </summary>
    public double AverageExecutionTimeMs { get; init; }

    /// <summary>
    /// 最小执行时间（毫秒）
    /// </summary>
    public double MinExecutionTimeMs { get; init; }

    /// <summary>
    /// 最大执行时间（毫秒）
    /// </summary>
    public double MaxExecutionTimeMs { get; init; }

    /// <summary>
    /// 95百分位执行时间（毫秒）
    /// </summary>
    public double P95ExecutionTimeMs { get; init; }

    /// <summary>
    /// 每秒调用次数
    /// </summary>
    public double CallsPerSecond { get; init; }

    /// <summary>
    /// 成功率（百分比）
    /// </summary>
    public double SuccessRatePercent { get; init; }
}

/// <summary>
/// 健康状态
/// </summary>
public record HealthStatus
{
    /// <summary>
    /// 整体健康状态
    /// </summary>
    public HealthState OverallHealth { get; init; }

    /// <summary>
    /// 组件健康状态
    /// </summary>
    public Dictionary<string, ComponentHealth> ComponentHealths { get; init; } = new();

    /// <summary>
    /// 健康检查时间
    /// </summary>
    public DateTime CheckedAt { get; init; } = DateTime.UtcNow;

    /// <summary>
    /// 健康消息
    /// </summary>
    public string? Message { get; init; }
}

/// <summary>
/// 组件健康状态
/// </summary>
public record ComponentHealth
{
    /// <summary>
    /// 健康状态
    /// </summary>
    public HealthState State { get; init; }

    /// <summary>
    /// 健康消息
    /// </summary>
    public string? Message { get; init; }

    /// <summary>
    /// 检查时间
    /// </summary>
    public DateTime CheckedAt { get; init; } = DateTime.UtcNow;

    /// <summary>
    /// 响应时间（毫秒）
    /// </summary>
    public double ResponseTimeMs { get; init; }
}

/// <summary>
/// 健康状态枚举
/// </summary>
public enum HealthState
{
    /// <summary>
    /// 健康
    /// </summary>
    Healthy,

    /// <summary>
    /// 警告
    /// </summary>
    Warning,

    /// <summary>
    /// 不健康
    /// </summary>
    Unhealthy,

    /// <summary>
    /// 未知
    /// </summary>
    Unknown
}

/// <summary>
/// 性能指标更新事件参数
/// </summary>
public class PerformanceMetricsUpdatedEventArgs : EventArgs
{
    /// <summary>
    /// 性能指标
    /// </summary>
    public required PerformanceMetrics Metrics { get; init; }
}

/// <summary>
/// 性能阈值超出事件参数
/// </summary>
public class PerformanceThresholdExceededEventArgs : EventArgs
{
    /// <summary>
    /// 指标名称
    /// </summary>
    public required string MetricName { get; init; }

    /// <summary>
    /// 当前值
    /// </summary>
    public double CurrentValue { get; init; }

    /// <summary>
    /// 阈值
    /// </summary>
    public double Threshold { get; init; }

    /// <summary>
    /// 组件名称
    /// </summary>
    public string? ComponentName { get; init; }

    /// <summary>
    /// 超出时间
    /// </summary>
    public DateTime ExceededAt { get; init; } = DateTime.UtcNow;
}
