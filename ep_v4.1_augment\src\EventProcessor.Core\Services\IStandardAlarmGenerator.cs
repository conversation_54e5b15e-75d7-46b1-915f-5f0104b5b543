using EventProcessor.Core.Models;

namespace EventProcessor.Core.Services;

/// <summary>
/// 标准告警生成器接口
/// </summary>
public interface IStandardAlarmGenerator
{
    /// <summary>
    /// 生成标准告警消息
    /// </summary>
    /// <param name="context">事件上下文</param>
    /// <param name="config">告警配置</param>
    /// <param name="eventConfig">事件配置</param>
    /// <returns>标准告警消息</returns>
    Task<StandardAlarmMessage> GenerateStandardAlarmAsync(
        EventContext context, 
        AlarmConfiguration config, 
        EventConfiguration eventConfig);

    /// <summary>
    /// 生成标准告警撤销消息
    /// </summary>
    /// <param name="context">事件上下文</param>
    /// <param name="config">告警配置</param>
    /// <param name="eventConfig">事件配置</param>
    /// <param name="reason">撤销原因</param>
    /// <returns>标准告警撤销消息</returns>
    Task<StandardAlarmMessage> GenerateStandardCancellationAsync(
        EventContext context, 
        AlarmConfiguration config, 
        EventConfiguration eventConfig, 
        string reason);
}
