using System.Collections.Concurrent;
using System.Text;
using EventProcessor.Core.Models;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using MQTTnet;
using MQTTnet.Client;
using MQTTnet.Extensions.ManagedClient;
using MQTTnet.Packets;

namespace EventProcessor.Core.Services;

/// <summary>
/// MQTT服务实现
/// </summary>
public class MqttService : IMqttService
{
    private readonly MqttConfiguration _config;
    private readonly ILogger<MqttService> _logger;
    private readonly IManagedMqttClient _mqttClient;
    private readonly ConcurrentDictionary<string, int> _subscribedTopics = new();
    private readonly object _lockObject = new();
    private bool _disposed = false;

    // 统计信息
    private DateTime? _connectedAt;
    private int _reconnectCount = 0;
    private long _messagesSent = 0;
    private long _messagesReceived = 0;
    private DateTime _lastActivityAt = DateTime.UtcNow;

    /// <summary>
    /// MQTT连接成功事件
    /// </summary>
    public event EventHandler<MqttConnectedEventArgs>? Connected;
    
    /// <summary>
    /// MQTT断开连接事件
    /// </summary>
    public event EventHandler<MqttDisconnectedEventArgs>? Disconnected;
    
    /// <summary>
    /// MQTT消息接收事件
    /// </summary>
    public event EventHandler<MqttMessageReceivedEventArgs>? MessageReceived;

    /// <summary>
    /// 初始化MQTT服务
    /// </summary>
    /// <param name="config">MQTT配置</param>
    /// <param name="logger">日志记录器</param>
    public MqttService(IOptions<MqttConfiguration> config, ILogger<MqttService> logger)
    {
        _logger = logger;
        
        var originalConfig = config.Value;
        var randomSuffix = Guid.NewGuid().ToString("N")[..8];
        var newClientId = $"{originalConfig.ClientId}_{randomSuffix}";

        _config = originalConfig with { ClientId = newClientId };

        // 创建MQTT客户端
        var factory = new MqttFactory();
        _mqttClient = factory.CreateManagedMqttClient();

        // 订阅事件
        _mqttClient.ConnectedAsync += OnConnectedAsync;
        _mqttClient.DisconnectedAsync += OnDisconnectedAsync;
        _mqttClient.ApplicationMessageReceivedAsync += OnMessageReceivedAsync;

        _logger.LogInformation("MqttService已初始化, 原始客户端ID: {OriginalClientId}, 使用的客户端ID: {UsedClientId}", originalConfig.ClientId, _config.ClientId);
    }

    /// <summary>
    /// 获取MQTT客户端连接状态
    /// </summary>
    public bool IsConnected => _mqttClient.IsConnected;

    /// <summary>
    /// 获取MQTT客户端ID
    /// </summary>
    public string ClientId => _config.ClientId;

    /// <summary>
    /// 启动MQTT服务
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>异步任务</returns>
    public async Task StartAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("正在配置MQTT客户端连接...");
            _logger.LogInformation("服务器: {BrokerHost}:{BrokerPort}", _config.BrokerHost, _config.BrokerPort);
            _logger.LogInformation("客户端ID: {ClientId}", _config.ClientId);
            _logger.LogInformation("用户名: {Username}", _config.Username ?? "未设置");
            _logger.LogInformation("密码: {PasswordStatus}", string.IsNullOrEmpty(_config.Password) ? "未设置" : "已设置");
            _logger.LogInformation("保活间隔: {KeepAlive}秒", _config.KeepAliveInterval);
            _logger.LogInformation("重连延迟: {ReconnectInterval}秒", _config.ReconnectInterval);

            var clientOptions = new MqttClientOptionsBuilder()
                .WithTcpServer(_config.BrokerHost, _config.BrokerPort)
                .WithClientId(_config.ClientId)
                .WithKeepAlivePeriod(TimeSpan.FromSeconds(_config.KeepAliveInterval))
                .WithCleanSession(true);

            if (!string.IsNullOrEmpty(_config.Username))
            {
                clientOptions.WithCredentials(_config.Username, _config.Password);
                _logger.LogInformation("已设置MQTT认证信息");
            }
            else
            {
                _logger.LogWarning("未设置MQTT认证信息，将使用匿名连接");
            }

            var managedOptions = new ManagedMqttClientOptionsBuilder()
                .WithClientOptions(clientOptions.Build())
                .WithAutoReconnectDelay(TimeSpan.FromSeconds(_config.ReconnectInterval))
                .Build();

            _logger.LogInformation("正在启动MQTT客户端...");
            await _mqttClient.StartAsync(managedOptions);

            _logger.LogInformation("MQTT服务已启动，连接到: {BrokerHost}:{BrokerPort}",
                                 _config.BrokerHost, _config.BrokerPort);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "启动MQTT服务失败");
            _logger.LogError("MQTT配置详情 - 服务器: {BrokerHost}:{BrokerPort}, 客户端ID: {ClientId}, 用户名: {Username}",
                           _config.BrokerHost, _config.BrokerPort, _config.ClientId, _config.Username);
            throw;
        }
    }

    /// <summary>
    /// 停止MQTT服务
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>异步任务</returns>
    public async Task StopAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            await _mqttClient.StopAsync();
            _logger.LogInformation("MQTT服务已停止");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "停止MQTT服务失败");
            throw;
        }
    }

    /// <summary>
    /// 订阅单个MQTT主题
    /// </summary>
    /// <param name="topic">主题名称</param>
    /// <param name="qos">服务质量等级</param>
    /// <returns>异步任务</returns>
    public async Task SubscribeAsync(string topic, int qos = 1)
    {
        try
        {
            var topicFilter = new MqttTopicFilterBuilder()
                .WithTopic(topic)
                .WithQualityOfServiceLevel((MQTTnet.Protocol.MqttQualityOfServiceLevel)qos)
                .Build();

            await _mqttClient.SubscribeAsync(new[] { topicFilter });

            _subscribedTopics[topic] = qos;
            _logger.LogInformation("已订阅主题: {Topic}, QoS: {QoS}", topic, qos);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "订阅主题失败: {Topic}", topic);
            throw;
        }
    }

    /// <summary>
    /// 订阅多个MQTT主题
    /// </summary>
    /// <param name="topics">主题名称数组</param>
    /// <param name="qos">服务质量等级</param>
    /// <returns>异步任务</returns>
    public async Task SubscribeAsync(string[] topics, int qos = 1)
    {
        try
        {
            var topicFilters = topics.Select(topic => new MqttTopicFilterBuilder()
                .WithTopic(topic)
                .WithQualityOfServiceLevel((MQTTnet.Protocol.MqttQualityOfServiceLevel)qos)
                .Build()).ToArray();

            await _mqttClient.SubscribeAsync(topicFilters);

            foreach (var topic in topics)
            {
                _subscribedTopics[topic] = qos;
            }

            _logger.LogInformation("已订阅 {Count} 个主题，QoS: {QoS}", topics.Length, qos);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "批量订阅主题失败");
            throw;
        }
    }

    /// <summary>
    /// 取消订阅MQTT主题
    /// </summary>
    /// <param name="topic">主题名称</param>
    /// <returns>异步任务</returns>
    public async Task UnsubscribeAsync(string topic)
    {
        try
        {
            await _mqttClient.UnsubscribeAsync(topic);
            _subscribedTopics.TryRemove(topic, out _);
            _logger.LogInformation("已取消订阅主题: {Topic}", topic);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "取消订阅主题失败: {Topic}", topic);
            throw;
        }
    }

    /// <summary>
    /// 发布MQTT消息
    /// </summary>
    /// <param name="topic">主题名称</param>
    /// <param name="payload">消息负载</param>
    /// <param name="qos">服务质量等级</param>
    /// <param name="retain">是否保留消息</param>
    /// <returns>异步任务</returns>
    public async Task PublishAsync(string topic, string payload, int qos = 1, bool retain = false)
    {
        try
        {
            var message = new MqttApplicationMessageBuilder()
                .WithTopic(topic)
                .WithPayload(payload)
                .WithQualityOfServiceLevel((MQTTnet.Protocol.MqttQualityOfServiceLevel)qos)
                .WithRetainFlag(retain)
                .Build();

            await _mqttClient.EnqueueAsync(message);

            Interlocked.Increment(ref _messagesSent);
            _lastActivityAt = DateTime.UtcNow;

            _logger.LogDebug("消息已发布: {Topic}, 长度: {Length}", topic, payload.Length);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "发布消息失败: {Topic}", topic);
            throw;
        }
    }

    /// <summary>
    /// 发布MQTT消息（字节数组格式）
    /// </summary>
    /// <param name="topic">主题名称</param>
    /// <param name="payload">消息负载（字节数组）</param>
    /// <param name="qos">服务质量等级</param>
    /// <param name="retain">是否保留消息</param>
    /// <returns>异步任务</returns>
    public async Task PublishAsync(string topic, byte[] payload, int qos = 1, bool retain = false)
    {
        try
        {
            var message = new MqttApplicationMessageBuilder()
                .WithTopic(topic)
                .WithPayload(payload)
                .WithQualityOfServiceLevel((MQTTnet.Protocol.MqttQualityOfServiceLevel)qos)
                .WithRetainFlag(retain)
                .Build();

            await _mqttClient.EnqueueAsync(message);

            Interlocked.Increment(ref _messagesSent);
            _lastActivityAt = DateTime.UtcNow;

            _logger.LogDebug("消息已发布: {Topic}, 字节数: {Length}", topic, payload.Length);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "发布消息失败: {Topic}", topic);
            throw;
        }
    }

    /// <summary>
    /// 获取MQTT连接统计信息
    /// </summary>
    /// <returns>连接统计信息</returns>
    public MqttConnectionStatistics GetConnectionStatistics()
    {
        return new MqttConnectionStatistics
        {
            IsConnected = IsConnected,
            ConnectedAt = _connectedAt,
            ReconnectCount = _reconnectCount,
            MessagesSent = Interlocked.Read(ref _messagesSent),
            MessagesReceived = Interlocked.Read(ref _messagesReceived),
            SubscribedTopicCount = _subscribedTopics.Count,
            LastActivityAt = _lastActivityAt
        };
    }

    private Task OnConnectedAsync(MqttClientConnectedEventArgs e)
    {
        lock (_lockObject)
        {
            _connectedAt = DateTime.UtcNow;
            _lastActivityAt = _connectedAt.Value;

            if (_reconnectCount > 0)
            {
                _logger.LogInformation("MQTT重连成功，重连次数: {ReconnectCount}", _reconnectCount);
            }
            else
            {
                _logger.LogInformation("MQTT连接成功");
            }

            Connected?.Invoke(this, new MqttConnectedEventArgs
            {
                ClientId = _config.ClientId,
                ServerAddress = $"{_config.BrokerHost}:{_config.BrokerPort}"
            });
        }

        return Task.CompletedTask;
    }

    private Task OnDisconnectedAsync(MqttClientDisconnectedEventArgs e)
    {
        lock (_lockObject)
        {
            _reconnectCount++;
            _lastActivityAt = DateTime.UtcNow;

            var isException = e.Exception != null;
            var reason = e.Reason.ToString();

            if (isException)
            {
                _logger.LogWarning("MQTT连接断开（异常）: {Reason}, 异常: {Exception}", reason, e.Exception?.Message);
            }
            else
            {
                _logger.LogInformation("MQTT连接断开: {Reason}", reason);
            }

            Disconnected?.Invoke(this, new MqttDisconnectedEventArgs
            {
                ClientId = _config.ClientId,
                Reason = reason,
                IsException = isException
            });
        }

        return Task.CompletedTask;
    }

    private Task OnMessageReceivedAsync(MqttApplicationMessageReceivedEventArgs e)
    {
        try
        {
            var topic = e.ApplicationMessage.Topic;
            var payload = Encoding.UTF8.GetString(e.ApplicationMessage.PayloadSegment);
            var qos = (int)e.ApplicationMessage.QualityOfServiceLevel;
            var retain = e.ApplicationMessage.Retain;

            Interlocked.Increment(ref _messagesReceived);
            _lastActivityAt = DateTime.UtcNow;

            _logger.LogDebug("收到MQTT消息: {Topic}, 长度: {Length}", topic, payload.Length);

            MessageReceived?.Invoke(this, new MqttMessageReceivedEventArgs
            {
                Topic = topic,
                Payload = payload,
                QualityOfServiceLevel = qos,
                Retain = retain
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "处理MQTT消息时发生异常");
        }

        return Task.CompletedTask;
    }

    /// <summary>
    /// 释放MQTT服务资源
    /// </summary>
    public void Dispose()
    {
        Dispose(true);
        GC.SuppressFinalize(this);
    }

    /// <summary>
    /// 释放资源的具体实现
    /// </summary>
    /// <param name="disposing">是否释放托管资源</param>
    protected virtual void Dispose(bool disposing)
    {
        if (!_disposed && disposing)
        {
            try
            {
                _mqttClient?.StopAsync().Wait(TimeSpan.FromSeconds(5));
                _mqttClient?.Dispose();
                _logger.LogInformation("MqttService已释放");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "释放MqttService时发生异常");
            }
            finally
            {
                _disposed = true;
            }
        }
    }
}
