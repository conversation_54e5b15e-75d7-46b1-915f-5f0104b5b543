using EventProcessor.Core.Models;
using Microsoft.Extensions.Logging;
using System.Text;
using System.Text.RegularExpressions;

namespace EventProcessor.Core.Services;

/// <summary>
/// 标准告警生成器 - 生成符合接收方规范的告警消息
/// </summary>
public class StandardAlarmGenerator : IStandardAlarmGenerator
{
    private readonly ILogger<StandardAlarmGenerator> _logger;

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="logger">日志记录器</param>
    public StandardAlarmGenerator(ILogger<StandardAlarmGenerator> logger)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    /// <summary>
    /// 生成标准告警消息
    /// </summary>
    /// <param name="context">事件上下文</param>
    /// <param name="config">告警配置</param>
    /// <param name="eventConfig">事件配置</param>
    /// <returns>标准告警消息</returns>
    public async Task<StandardAlarmMessage> GenerateStandardAlarmAsync(
        EventContext context, 
        AlarmConfiguration config, 
        EventConfiguration eventConfig)
    {
        try
        {
            // 构建详情数据字符串
            var dataContent = await BuildDataContentAsync(context, config);

            // 构建图片URL列表（暂时为空，后续可扩展）
            var imageUrls = BuildImageUrls(context);

            var standardAlarm = new StandardAlarmMessage
            {
                EventId = eventConfig.EventId,
                CommId = eventConfig.CommId,
                PositionId = eventConfig.PositionId,
                Data = dataContent,
                Urls = imageUrls
            };

            _logger.LogInformation("标准告警消息已生成: {EventId}, 小区: {CommId}, 点位: {PositionId}, 数据行数: {DataLines}", 
                                 standardAlarm.EventId, standardAlarm.CommId, standardAlarm.PositionId, 
                                 dataContent.Split("\r\n").Length);

            return standardAlarm;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "生成标准告警消息失败: {EventId}", context.EventId);
            throw;
        }
    }

    /// <summary>
    /// 生成标准告警撤销消息
    /// </summary>
    /// <param name="context">事件上下文</param>
    /// <param name="config">告警配置</param>
    /// <param name="eventConfig">事件配置</param>
    /// <param name="reason">撤销原因</param>
    /// <returns>标准告警撤销消息</returns>
    public Task<StandardAlarmMessage> GenerateStandardCancellationAsync(
        EventContext context, 
        AlarmConfiguration config, 
        EventConfiguration eventConfig, 
        string reason)
    {
        try
        {
            // 构建撤销消息的详情数据
            var dataContent = BuildCancellationDataContent(context, reason);

            var cancellationMessage = new StandardAlarmMessage
            {
                EventId = eventConfig.EventId,
                CommId = eventConfig.CommId,
                PositionId = eventConfig.PositionId,
                Data = dataContent,
                Urls = Array.Empty<string>()
            };

            _logger.LogInformation("标准告警撤销消息已生成: {EventId}, 小区: {CommId}, 点位: {PositionId}, 原因: {Reason}",
                                 cancellationMessage.EventId, cancellationMessage.CommId, cancellationMessage.PositionId, reason);

            return Task.FromResult(cancellationMessage);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "生成标准告警撤销消息失败: {EventId}, 原因: {Reason}", context.EventId, reason);
            return Task.FromException<StandardAlarmMessage>(ex);
        }
    }

    /// <summary>
    /// 构建详情数据内容
    /// </summary>
    /// <param name="context">事件上下文</param>
    /// <param name="config">告警配置</param>
    /// <returns>格式化的详情数据字符串</returns>
    private Task<string> BuildDataContentAsync(EventContext context, AlarmConfiguration config)
    {
        var dataLines = new List<string>();

        try
        {
            // 处理配置的字段映射
            if (config.Fields != null)
            {
                foreach (var fieldMapping in config.Fields.Take(5)) // 最多5行数据字段，为时间字段留出空间
                {
                    try
                    {
                        var fieldValue = ExtractFieldValue(context, fieldMapping);
                        if (!string.IsNullOrEmpty(fieldValue))
                        {
                            dataLines.Add($"[{fieldMapping.AlarmFieldName}]: {fieldValue}");
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "提取字段值失败: {FieldName}", fieldMapping.AlarmFieldName);
                    }
                }
            }

            // 添加时间字段（必须是最后一行）
            var currentTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
            dataLines.Add($"[时间]: {currentTime}");

            // 确保不超过6行
            if (dataLines.Count > 6)
            {
                _logger.LogWarning("详情数据超过6行限制，将截取前5行数据字段加时间字段");
                dataLines = dataLines.Take(5).Concat(new[] { $"[时间]: {currentTime}" }).ToList();
            }

            return Task.FromResult(string.Join("\r\n", dataLines));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "构建详情数据内容失败");
            // 返回基本的时间信息
            return Task.FromResult($"[时间]: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
        }
    }

    /// <summary>
    /// 构建撤销消息的详情数据内容
    /// </summary>
    /// <param name="context">事件上下文</param>
    /// <param name="reason">撤销原因</param>
    /// <returns>格式化的撤销详情数据字符串</returns>
    private string BuildCancellationDataContent(EventContext context, string reason)
    {
        var dataLines = new List<string>
        {
            "[操作]: 告警撤销",
            $"[原因]: {reason}",
            $"[原始事件]: {context.EventId}",
            $"[时间]: {DateTime.Now:yyyy-MM-dd HH:mm:ss}"
        };

        return string.Join("\r\n", dataLines);
    }

    /// <summary>
    /// 构建图片URL列表
    /// </summary>
    /// <param name="context">事件上下文</param>
    /// <returns>图片URL数组</returns>
    private string[] BuildImageUrls(EventContext context)
    {
        var urls = new List<string>();

        try
        {
            // 从设备数据中提取图片路径
            if (context.DeviceData?.TryGetValue("ImagePath", out var imagePath) == true &&
                !string.IsNullOrEmpty(imagePath?.ToString()))
            {
                urls.Add(imagePath.ToString()!);
            }

            // 可以扩展其他图片来源
            // TODO: 从AI结果或其他数据源中提取图片URL

            _logger.LogDebug("构建图片URL列表完成: {Count}个URL", urls.Count);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "构建图片URL列表失败");
        }

        return urls.ToArray();
    }

    /// <summary>
    /// 提取字段值
    /// </summary>
    /// <param name="context">事件上下文</param>
    /// <param name="fieldMapping">字段映射配置</param>
    /// <returns>字段值</returns>
    private string ExtractFieldValue(EventContext context, FieldMapping fieldMapping)
    {
        try
        {
            // 如果没有指定源字段名，使用默认值
            if (string.IsNullOrEmpty(fieldMapping.SourceFieldName))
            {
                return fieldMapping.DefaultValue ?? "";
            }

            // 获取源数据
            var sourceData = GetSourceData(context, fieldMapping.SourceRuleType);

            // 处理多字段或单字段
            object fieldValue;
            if (fieldMapping.SourceFieldName.Contains(','))
            {
                // 多字段处理
                fieldValue = ProcessMultipleFields(sourceData, fieldMapping);
            }
            else
            {
                // 单字段处理
                if (sourceData.TryGetValue(fieldMapping.SourceFieldName, out var value))
                {
                    fieldValue = ApplyFormatTemplate(value, fieldMapping.FormatTemplate);
                }
                else
                {
                    fieldValue = fieldMapping.DefaultValue ?? "";
                }
            }

            return fieldValue?.ToString() ?? "";
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "提取字段值失败: {SourceType}.{SourceField}",
                             fieldMapping.SourceRuleType, fieldMapping.SourceFieldName);
            return fieldMapping.DefaultValue ?? "";
        }
    }

    /// <summary>
    /// 获取源数据
    /// </summary>
    /// <param name="context">事件上下文</param>
    /// <param name="sourceRuleType">源规则类型</param>
    /// <returns>源数据字典</returns>
    private Dictionary<string, object> GetSourceData(EventContext context, string sourceRuleType)
    {
        return sourceRuleType switch
        {
            "ExclusionRules" => context.ExclusionData,
            "BusinessRules" => context.BusinessData,
            "AIResultRules" => context.AIResultData,
            "DeviceSignal" => context.DeviceData,
            _ => new Dictionary<string, object>()
        };
    }

    /// <summary>
    /// 处理多字段
    /// </summary>
    /// <param name="sourceData">源数据</param>
    /// <param name="mapping">字段映射</param>
    /// <returns>处理后的字段值</returns>
    private object ProcessMultipleFields(Dictionary<string, object> sourceData, FieldMapping mapping)
    {
        var fieldNames = mapping.SourceFieldName.Split(',', StringSplitOptions.RemoveEmptyEntries);
        var fieldValues = new Dictionary<string, object>();

        foreach (var fieldName in fieldNames)
        {
            var trimmedFieldName = fieldName.Trim();
            if (sourceData.TryGetValue(trimmedFieldName, out var value))
            {
                fieldValues[trimmedFieldName] = value;
            }
            else
            {
                fieldValues[trimmedFieldName] = "";
            }
        }

        return ApplyFormatTemplate(fieldValues, mapping.FormatTemplate);
    }

    /// <summary>
    /// 应用格式化模板
    /// </summary>
    /// <param name="value">字段值</param>
    /// <param name="formatTemplate">格式化模板</param>
    /// <returns>格式化后的值</returns>
    private object ApplyFormatTemplate(object value, string? formatTemplate)
    {
        if (string.IsNullOrEmpty(formatTemplate))
        {
            return value;
        }

        try
        {
            if (value is Dictionary<string, object> fieldValues)
            {
                // 多字段格式化
                var result = formatTemplate;
                foreach (var kvp in fieldValues)
                {
                    var placeholder = $"{{{kvp.Key}}}";
                    result = result.Replace(placeholder, kvp.Value?.ToString() ?? "");
                }
                return result;
            }
            else
            {
                // 单字段格式化
                if (formatTemplate.Contains("{") && formatTemplate.Contains("}"))
                {
                    // 普通占位符替换
                    return formatTemplate.Replace("{value}", value?.ToString() ?? "");
                }
                else
                {
                    // 直接格式化
                    return string.Format(formatTemplate, value);
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "应用格式化模板失败: {Template}, 值: {Value}", formatTemplate, value);
            return value;
        }
    }

    /// <summary>
    /// 从业务数据中提取字段值
    /// </summary>
    private string ExtractFromBusinessData(EventContext context, string fieldName)
    {
        if (context.BusinessData?.TryGetValue(fieldName, out var value) == true)
        {
            return value?.ToString() ?? "";
        }
        return "";
    }

    /// <summary>
    /// 从设备数据中提取字段值
    /// </summary>
    private string ExtractFromDeviceSignal(EventContext context, string fieldName)
    {
        if (context.DeviceData?.TryGetValue(fieldName, out var value) == true)
        {
            return value?.ToString() ?? "";
        }
        return "";
    }

    /// <summary>
    /// 从排除数据中提取字段值
    /// </summary>
    private string ExtractFromExclusionData(EventContext context, string fieldName)
    {
        if (context.ExclusionData?.TryGetValue(fieldName, out var value) == true)
        {
            return value?.ToString() ?? "";
        }
        return "";
    }

    /// <summary>
    /// 从AI结果中提取字段值
    /// </summary>
    private string ExtractFromAIResult(EventContext context, string fieldName)
    {
        if (context.AIResultData?.TryGetValue(fieldName, out var value) == true)
        {
            return value?.ToString() ?? "";
        }
        return "";
    }
}
