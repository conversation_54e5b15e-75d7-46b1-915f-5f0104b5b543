using EventProcessor.Core.Models;
using EventProcessor.Core.Services;
using Microsoft.Extensions.Logging;
using System.Text.RegularExpressions;

namespace EventProcessor.Core.Validation;

/// <summary>
/// 配置验证器 - 提供全面的EventConfiguration验证
/// </summary>
public class ConfigurationValidator
{
    private readonly ILogger<ConfigurationValidator> _logger;

    /// <summary>
    /// 初始化配置验证器
    /// </summary>
    /// <param name="logger">日志记录器</param>
    public ConfigurationValidator(ILogger<ConfigurationValidator> logger)
    {
        _logger = logger;
    }

    /// <summary>
    /// 验证事件配置
    /// </summary>
    /// <param name="configuration">事件配置</param>
    /// <returns>验证结果</returns>
    public ValidationResult ValidateConfiguration(EventConfiguration configuration)
    {
        var errors = new List<string>();
        var warnings = new List<string>();

        try
        {
            // 基础配置验证
            ValidateBasicConfiguration(configuration, errors, warnings);

            // 设备信号配置验证
            ValidateDeviceSignalConfiguration(configuration.DeviceSignal, errors, warnings);

            // 规则配置验证
            ValidateRuleConfiguration(configuration.RuleConfiguration, errors, warnings);

            // 评估策略一致性验证
            ValidateEvaluationStrategyConsistency(configuration, errors, warnings);

            // 告警配置验证
            ValidateAlarmConfiguration(configuration.RuleConfiguration?.AlarmConfig, errors, warnings);

            return new ValidationResult
            {
                IsValid = errors.Count == 0,
                Errors = errors.ToArray(),
                Warnings = warnings.ToArray()
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "配置验证过程中发生异常");
            return new ValidationResult
            {
                IsValid = false,
                Errors = new[] { $"配置验证异常: {ex.Message}" }
            };
        }
    }

    /// <summary>
    /// 验证基础配置
    /// </summary>
    private void ValidateBasicConfiguration(EventConfiguration config, List<string> errors, List<string> warnings)
    {
        // 必填字段验证
        if (string.IsNullOrEmpty(config.EventId))
            errors.Add("EventId 不能为空");

        if (string.IsNullOrEmpty(config.EventName))
            errors.Add("EventName 不能为空");

        if (string.IsNullOrEmpty(config.CommId))
            errors.Add("CommId 不能为空");

        if (string.IsNullOrEmpty(config.PositionId))
            errors.Add("PositionId 不能为空");

        // 评估策略验证
        if (!ValidationConstants.SupportedEvaluationStrategies.Contains(config.EvaluationStrategy))
        {
            errors.Add($"不支持的评估策略: {config.EvaluationStrategy}。支持的策略: {string.Join(", ", ValidationConstants.SupportedEvaluationStrategies)}");
        }

        // 优先级验证
        if (!ValidationConstants.SupportedPriorities.Contains(config.Priority))
        {
            errors.Add($"不支持的优先级: {config.Priority}。支持的优先级: {string.Join(", ", ValidationConstants.SupportedPriorities)}");
        }

        // 时间窗口验证
        if (!ValidationConstants.SupportedTimeWindows.Contains(config.CorrelationTimeWindow))
        {
            errors.Add($"不支持的时间窗口: {config.CorrelationTimeWindow}。支持的窗口: {string.Join(", ", ValidationConstants.SupportedTimeWindows)}");
        }

        // 自定义时间窗口验证
        if (config.CorrelationTimeWindow == "custom" && !config.CustomTimeWindowMinutes.HasValue)
        {
            errors.Add("使用自定义时间窗口时必须指定 CustomTimeWindowMinutes");
        }

        // 告警静默期验证
        if (config.AlarmGracePeriodSeconds < 0 || config.AlarmGracePeriodSeconds > 300)
        {
            warnings.Add($"告警静默期 {config.AlarmGracePeriodSeconds} 秒可能不合理，建议范围: 0-300秒");
        }

        // EventId格式验证
        if (!Regex.IsMatch(config.EventId, @"^EV\d{6}$"))
        {
            warnings.Add($"EventId格式建议使用 EV + 6位数字格式，当前: {config.EventId}");
        }
    }

    /// <summary>
    /// 验证设备信号配置
    /// </summary>
    private void ValidateDeviceSignalConfiguration(DeviceSignalConfiguration? deviceSignal, List<string> errors, List<string> warnings)
    {
        if (deviceSignal == null)
        {
            warnings.Add("未配置设备信号，事件将无法被触发");
            return;
        }

        // 主题验证
        if (deviceSignal.Topics == null || deviceSignal.Topics.Length == 0)
        {
            errors.Add("设备信号必须配置至少一个主题");
        }
        else
        {
            foreach (var topic in deviceSignal.Topics)
            {
                if (string.IsNullOrEmpty(topic))
                {
                    errors.Add("设备信号主题不能为空");
                }
                else if (!IsValidMqttTopic(topic))
                {
                    warnings.Add($"设备信号主题格式可能不正确: {topic}");
                }
            }
        }

        // 触发字段验证
        if (string.IsNullOrEmpty(deviceSignal.TriggerField))
        {
            errors.Add("设备信号必须指定触发字段 TriggerField");
        }

        // 触发值验证
        if (deviceSignal.TriggerValues == null || deviceSignal.TriggerValues.Count == 0)
        {
            errors.Add("设备信号必须配置触发值映射 TriggerValues");
        }
        else
        {
            if (!deviceSignal.TriggerValues.ContainsKey("true") || !deviceSignal.TriggerValues.ContainsKey("false"))
            {
                warnings.Add("建议为设备信号配置 true 和 false 两种触发值");
            }
        }

        // 保持超时验证
        if (deviceSignal.HoldingTimeoutSec <= 0 || deviceSignal.HoldingTimeoutSec > 3600)
        {
            warnings.Add($"设备信号保持超时 {deviceSignal.HoldingTimeoutSec} 秒可能不合理，建议范围: 1-3600秒");
        }
    }

    /// <summary>
    /// 验证规则配置
    /// </summary>
    private void ValidateRuleConfiguration(RuleConfiguration? ruleConfig, List<string> errors, List<string> warnings)
    {
        if (ruleConfig == null)
        {
            errors.Add("必须配置规则配置 RuleConfiguration");
            return;
        }

        // 验证排除规则
        if (ruleConfig.ExclusionRules != null)
        {
            for (int i = 0; i < ruleConfig.ExclusionRules.Length; i++)
            {
                ValidateExclusionRuleGroup(ruleConfig.ExclusionRules[i], i, errors, warnings);
            }
        }

        // 验证业务规则
        if (ruleConfig.BusinessRules != null)
        {
            for (int i = 0; i < ruleConfig.BusinessRules.Length; i++)
            {
                ValidateBusinessRuleGroup(ruleConfig.BusinessRules[i], i, errors, warnings);
            }
        }

        // 验证AI结果规则
        if (ruleConfig.AIResultRules != null)
        {
            for (int i = 0; i < ruleConfig.AIResultRules.Length; i++)
            {
                ValidateAIResultRuleGroup(ruleConfig.AIResultRules[i], i, errors, warnings);
            }
        }
    }

    /// <summary>
    /// 验证排除规则组
    /// </summary>
    private void ValidateExclusionRuleGroup(ExclusionRuleGroup ruleGroup, int index, List<string> errors, List<string> warnings)
    {
        var prefix = $"排除规则[{index}]";

        // 数据源类型验证
        if (!ValidationConstants.SupportedSourceTypes.Contains(ruleGroup.SourceType))
        {
            errors.Add($"{prefix}: 不支持的数据源类型 {ruleGroup.SourceType}");
        }

        // 主题验证
        if (string.IsNullOrEmpty(ruleGroup.SourceTopic))
        {
            errors.Add($"{prefix}: 必须指定源主题 SourceTopic");
        }
        else if (!IsValidMqttTopic(ruleGroup.SourceTopic))
        {
            warnings.Add($"{prefix}: 源主题格式可能不正确: {ruleGroup.SourceTopic}");
        }

        // 逻辑操作符验证
        if (!ValidationConstants.SupportedLogicOperators.Contains(ruleGroup.LogicOperator))
        {
            errors.Add($"{prefix}: 不支持的逻辑操作符 {ruleGroup.LogicOperator}");
        }

        // 条件验证
        if (ruleGroup.Conditions == null || ruleGroup.Conditions.Length == 0)
        {
            errors.Add($"{prefix}: 必须配置至少一个条件");
        }
        else
        {
            for (int i = 0; i < ruleGroup.Conditions.Length; i++)
            {
                ValidateCondition(ruleGroup.Conditions[i], $"{prefix}.条件[{i}]", errors, warnings);
            }
        }
    }

    /// <summary>
    /// 验证业务规则组
    /// </summary>
    private void ValidateBusinessRuleGroup(BusinessRuleGroup ruleGroup, int index, List<string> errors, List<string> warnings)
    {
        var prefix = $"业务规则[{index}]";

        // 主题验证
        if (string.IsNullOrEmpty(ruleGroup.SourceTopic))
        {
            errors.Add($"{prefix}: 必须指定源主题 SourceTopic");
        }
        else if (!IsValidMqttTopic(ruleGroup.SourceTopic))
        {
            warnings.Add($"{prefix}: 源主题格式可能不正确: {ruleGroup.SourceTopic}");
        }

        // 逻辑操作符验证
        if (!ValidationConstants.SupportedLogicOperators.Contains(ruleGroup.LogicOperator))
        {
            errors.Add($"{prefix}: 不支持的逻辑操作符 {ruleGroup.LogicOperator}");
        }

        // 条件和条件组验证
        bool hasConditions = ruleGroup.Conditions != null && ruleGroup.Conditions.Length > 0;
        bool hasConditionGroups = ruleGroup.ConditionGroups != null && ruleGroup.ConditionGroups.Length > 0;

        if (!hasConditions && !hasConditionGroups)
        {
            errors.Add($"{prefix}: 必须配置条件或条件组");
        }

        // 验证直接条件
        if (hasConditions)
        {
            for (int i = 0; i < ruleGroup.Conditions!.Length; i++)
            {
                ValidateCondition(ruleGroup.Conditions[i], $"{prefix}.条件[{i}]", errors, warnings);
            }
        }

        // 验证条件组
        if (hasConditionGroups)
        {
            for (int i = 0; i < ruleGroup.ConditionGroups!.Length; i++)
            {
                ValidateConditionGroup(ruleGroup.ConditionGroups[i], $"{prefix}.条件组[{i}]", errors, warnings);
            }
        }
    }

    /// <summary>
    /// 验证AI结果规则组
    /// </summary>
    private void ValidateAIResultRuleGroup(AIResultRuleGroup ruleGroup, int index, List<string> errors, List<string> warnings)
    {
        var prefix = $"AI结果规则[{index}]";

        // 逻辑操作符验证
        if (!ValidationConstants.SupportedLogicOperators.Contains(ruleGroup.LogicOperator))
        {
            errors.Add($"{prefix}: 不支持的逻辑操作符 {ruleGroup.LogicOperator}");
        }

        // 条件验证
        if (ruleGroup.Conditions == null || ruleGroup.Conditions.Length == 0)
        {
            errors.Add($"{prefix}: 必须配置至少一个条件");
        }
        else
        {
            for (int i = 0; i < ruleGroup.Conditions.Length; i++)
            {
                ValidateCondition(ruleGroup.Conditions[i], $"{prefix}.条件[{i}]", errors, warnings);
            }
        }
    }

    /// <summary>
    /// 验证条件组
    /// </summary>
    private void ValidateConditionGroup(ConditionGroup conditionGroup, string prefix, List<string> errors, List<string> warnings)
    {
        // 逻辑操作符验证
        if (!ValidationConstants.SupportedLogicOperators.Contains(conditionGroup.LogicOperator))
        {
            errors.Add($"{prefix}: 不支持的逻辑操作符 {conditionGroup.LogicOperator}");
        }

        // 条件验证
        if (conditionGroup.Conditions == null || conditionGroup.Conditions.Length == 0)
        {
            errors.Add($"{prefix}: 必须配置至少一个条件");
        }
        else
        {
            for (int i = 0; i < conditionGroup.Conditions.Length; i++)
            {
                ValidateCondition(conditionGroup.Conditions[i], $"{prefix}.条件[{i}]", errors, warnings);
            }
        }
    }

    /// <summary>
    /// 验证单个条件
    /// </summary>
    private void ValidateCondition(Condition condition, string prefix, List<string> errors, List<string> warnings)
    {
        // 字段名验证
        if (string.IsNullOrEmpty(condition.FieldName))
        {
            errors.Add($"{prefix}: 字段名 FieldName 不能为空");
        }

        // 数据类型验证
        if (!ValidationConstants.SupportedDataTypes.Contains(condition.DataType))
        {
            errors.Add($"{prefix}: 不支持的数据类型 {condition.DataType}。支持的类型: {string.Join(", ", ValidationConstants.SupportedDataTypes)}");
            return; // 数据类型错误时跳过后续验证
        }

        // 操作符验证
        if (!ValidationConstants.IsOperatorSupportedForDataType(condition.Operator, condition.DataType))
        {
            var supportedOps = ValidationConstants.GetSupportedOperators(condition.DataType);
            errors.Add($"{prefix}: 数据类型 {condition.DataType} 不支持操作符 {condition.Operator}。支持的操作符: {string.Join(", ", supportedOps)}");
        }

        // 操作符值验证
        var (isValid, errorMessage) = ValidationConstants.ValidateOperatorValue(condition.Operator, condition.Value, condition.DataType);
        if (!isValid)
        {
            errors.Add($"{prefix}: {errorMessage}");
        }

        // 描述建议
        if (string.IsNullOrEmpty(condition.Description))
        {
            warnings.Add($"{prefix}: 建议为条件添加描述信息");
        }
    }

    /// <summary>
    /// 验证评估策略一致性
    /// </summary>
    private void ValidateEvaluationStrategyConsistency(EventConfiguration config, List<string> errors, List<string> warnings)
    {
        var hasBusinessRules = config.RuleConfiguration?.BusinessRules != null && config.RuleConfiguration.BusinessRules.Length > 0;
        var hasAIResultRules = config.RuleConfiguration?.AIResultRules != null && config.RuleConfiguration.AIResultRules.Length > 0;
        var hasAIPrompt = !string.IsNullOrEmpty(config.AIPrompt);

        switch (config.EvaluationStrategy)
        {
            case "BusinessOnly":
                if (!hasBusinessRules)
                {
                    errors.Add("BusinessOnly 策略需要配置业务规则");
                }
                if (hasAIPrompt)
                {
                    warnings.Add("BusinessOnly 策略不需要 AI 提示词配置");
                }
                break;

            case "AI":
                if (!hasAIPrompt)
                {
                    errors.Add("AI 策略需要配置 AI 提示词");
                }
                if (!hasAIResultRules)
                {
                    errors.Add("AI 策略需要配置 AI 结果规则");
                }
                if (!config.AIAnalysisDelaySec.HasValue)
                {
                    warnings.Add("AI 策略建议配置 AI 分析延迟时间");
                }
                break;

            case "AIAndBusiness":
                if (!hasBusinessRules)
                {
                    errors.Add("AIAndBusiness 策略需要配置业务规则");
                }
                if (!hasAIPrompt)
                {
                    errors.Add("AIAndBusiness 策略需要配置 AI 提示词");
                }
                if (!hasAIResultRules)
                {
                    errors.Add("AIAndBusiness 策略需要配置 AI 结果规则");
                }
                break;
        }
    }

    /// <summary>
    /// 验证告警配置
    /// </summary>
    private void ValidateAlarmConfiguration(AlarmConfiguration? alarmConfig, List<string> errors, List<string> warnings)
    {
        if (alarmConfig == null)
        {
            warnings.Add("未配置告警配置，将无法生成告警消息");
            return;
        }

        if (alarmConfig.Fields == null || alarmConfig.Fields.Count == 0)
        {
            warnings.Add("未配置告警字段映射，告警消息将为空");
            return;
        }

        for (int i = 0; i < alarmConfig.Fields.Count; i++)
        {
            ValidateFieldMapping(alarmConfig.Fields[i], i, errors, warnings);
        }

        // 自定义模板验证
        if (!string.IsNullOrEmpty(alarmConfig.CustomTemplate))
        {
            if (alarmConfig.CustomTemplate.Length > 10000)
            {
                warnings.Add("自定义告警模板过长，可能影响性能");
            }

            // 检查潜在的安全风险
            var dangerousPatterns = new[] { "<script", "javascript:", "eval(", "function(" };
            foreach (var pattern in dangerousPatterns)
            {
                if (alarmConfig.CustomTemplate.Contains(pattern, StringComparison.OrdinalIgnoreCase))
                {
                    warnings.Add($"自定义告警模板包含潜在安全风险的内容: {pattern}");
                    break;
                }
            }
        }
    }

    /// <summary>
    /// 验证字段映射
    /// </summary>
    private void ValidateFieldMapping(FieldMapping fieldMapping, int index, List<string> errors, List<string> warnings)
    {
        var prefix = $"告警字段映射[{index}]";

        // 告警字段名验证
        if (string.IsNullOrEmpty(fieldMapping.AlarmFieldName))
        {
            errors.Add($"{prefix}: 告警字段名 AlarmFieldName 不能为空");
        }

        // 源规则类型验证
        if (!ValidationConstants.SupportedSourceRuleTypes.Contains(fieldMapping.SourceRuleType))
        {
            errors.Add($"{prefix}: 不支持的源规则类型 {fieldMapping.SourceRuleType}。支持的类型: {string.Join(", ", ValidationConstants.SupportedSourceRuleTypes)}");
        }

        // 源字段名和默认值验证
        if (string.IsNullOrEmpty(fieldMapping.SourceFieldName) && string.IsNullOrEmpty(fieldMapping.DefaultValue))
        {
            warnings.Add($"{prefix}: 既没有源字段名也没有默认值，字段可能为空");
        }

        // 格式化模板验证
        if (!string.IsNullOrEmpty(fieldMapping.FormatTemplate))
        {
            ValidateFormatTemplate(fieldMapping.FormatTemplate, $"{prefix}.格式化模板", warnings);
        }
    }

    /// <summary>
    /// 验证格式化模板
    /// </summary>
    private void ValidateFormatTemplate(string template, string prefix, List<string> warnings)
    {
        try
        {
            // 检查占位符格式
            var placeholders = Regex.Matches(template, @"\{([^}]+)\}");
            if (placeholders.Count == 0)
            {
                warnings.Add($"{prefix}: 没有找到占位符，模板可能无效");
            }

            // 检查格式化字符串语法
            foreach (Match match in placeholders)
            {
                var placeholder = match.Groups[1].Value;
                if (placeholder.Contains(':'))
                {
                    var parts = placeholder.Split(':');
                    if (parts.Length != 2)
                    {
                        warnings.Add($"{prefix}: 格式化占位符语法可能错误: {match.Value}");
                    }
                }
            }
        }
        catch (Exception)
        {
            warnings.Add($"{prefix}: 格式化模板验证失败");
        }
    }

    /// <summary>
    /// 验证MQTT主题格式
    /// </summary>
    private bool IsValidMqttTopic(string topic)
    {
        if (string.IsNullOrEmpty(topic))
            return false;

        // MQTT主题基本规则验证
        if (topic.Contains('#') && !topic.EndsWith('#'))
            return false;

        if (topic.Contains('+') && topic.Split('/').Any(part => part.Contains('+') && part != "+"))
            return false;

        return !topic.Contains('\0') && topic.Length <= 65535;
    }
}
