using System.Text.RegularExpressions;

namespace EventProcessor.Core.Validation;

/// <summary>
/// 验证常量定义 - 包含所有支持的操作符和数据类型
/// </summary>
public static class ValidationConstants
{
    /// <summary>
    /// 支持的数据类型
    /// </summary>
    public static readonly string[] SupportedDataTypes = { "string", "number", "datetime" };

    /// <summary>
    /// 支持的逻辑操作符
    /// </summary>
    public static readonly string[] SupportedLogicOperators = { "AND", "OR", "NOT" };

    /// <summary>
    /// 支持的评估策略
    /// </summary>
    public static readonly string[] SupportedEvaluationStrategies = { "BusinessOnly", "AI", "AIAndBusiness" };

    /// <summary>
    /// 支持的优先级
    /// </summary>
    public static readonly string[] SupportedPriorities = { "P1", "P2", "P3" };

    /// <summary>
    /// 支持的时间窗口策略
    /// </summary>
    public static readonly string[] SupportedTimeWindows = { "minute", "hour", "day", "custom" };

    /// <summary>
    /// 支持的数据源类型
    /// </summary>
    public static readonly string[] SupportedSourceTypes = { "MQTT", "HTTP", "Database" };

    /// <summary>
    /// 支持的告警字段源规则类型
    /// </summary>
    public static readonly string[] SupportedSourceRuleTypes = { "ExclusionRules", "BusinessRules", "AIResultRules", "DeviceSignal" };

    /// <summary>
    /// 字符串类型支持的操作符
    /// </summary>
    public static readonly string[] StringOperators = 
    {
        "Equals", "NotEquals", "Contains", "StartsWith", "EndsWith", 
        "Regex", "In", "IsEmpty", "IsNotEmpty"
    };

    /// <summary>
    /// 数字类型支持的操作符
    /// </summary>
    public static readonly string[] NumberOperators = 
    {
        "Equals", "NotEquals", "GreaterThan", "LessThan", 
        "GreaterThanOrEqual", "LessThanOrEqual", "Between"
    };

    /// <summary>
    /// 日期时间类型支持的操作符
    /// </summary>
    public static readonly string[] DateTimeOperators = 
    {
        "Equals", "Before", "After", "Between", 
        "WithinMinutes", "WithinHours", "WithinDays"
    };

    /// <summary>
    /// 所有支持的操作符
    /// </summary>
    public static readonly string[] AllSupportedOperators = 
        StringOperators.Concat(NumberOperators).Concat(DateTimeOperators).Distinct().ToArray();

    /// <summary>
    /// 获取指定数据类型支持的操作符
    /// </summary>
    /// <param name="dataType">数据类型</param>
    /// <returns>支持的操作符数组</returns>
    public static string[] GetSupportedOperators(string dataType)
    {
        return dataType?.ToLower() switch
        {
            "string" => StringOperators,
            "number" => NumberOperators,
            "datetime" => DateTimeOperators,
            _ => Array.Empty<string>()
        };
    }

    /// <summary>
    /// 验证操作符是否支持指定数据类型
    /// </summary>
    /// <param name="operatorName">操作符名称</param>
    /// <param name="dataType">数据类型</param>
    /// <returns>是否支持</returns>
    public static bool IsOperatorSupportedForDataType(string operatorName, string dataType)
    {
        var supportedOperators = GetSupportedOperators(dataType);
        return supportedOperators.Contains(operatorName);
    }

    /// <summary>
    /// 需要范围值的操作符（格式：value1,value2）
    /// </summary>
    public static readonly string[] RangeOperators = { "Between" };

    /// <summary>
    /// 需要分隔符值的操作符（格式：value1|value2|value3）
    /// </summary>
    public static readonly string[] DelimitedOperators = { "In" };

    /// <summary>
    /// 需要正则表达式的操作符
    /// </summary>
    public static readonly string[] RegexOperators = { "Regex" };

    /// <summary>
    /// 不需要值的操作符
    /// </summary>
    public static readonly string[] NoValueOperators = { "IsEmpty", "IsNotEmpty" };

    /// <summary>
    /// 验证操作符值格式
    /// </summary>
    /// <param name="operatorName">操作符名称</param>
    /// <param name="value">操作符值</param>
    /// <param name="dataType">数据类型</param>
    /// <returns>验证结果</returns>
    public static (bool IsValid, string ErrorMessage) ValidateOperatorValue(string operatorName, string value, string dataType)
    {
        // 不需要值的操作符
        if (NoValueOperators.Contains(operatorName))
        {
            return (true, string.Empty);
        }

        // 检查值是否为空
        if (string.IsNullOrEmpty(value))
        {
            return (false, $"操作符 {operatorName} 需要提供值");
        }

        // 范围操作符验证
        if (RangeOperators.Contains(operatorName))
        {
            var parts = value.Split(',');
            if (parts.Length != 2)
            {
                return (false, $"操作符 {operatorName} 需要两个值，格式：value1,value2");
            }

            if (dataType == "number")
            {
                if (!double.TryParse(parts[0], out _) || !double.TryParse(parts[1], out _))
                {
                    return (false, $"操作符 {operatorName} 的数字范围值格式错误");
                }
            }
        }

        // 分隔符操作符验证
        if (DelimitedOperators.Contains(operatorName))
        {
            if (!value.Contains('|'))
            {
                return (false, $"操作符 {operatorName} 需要使用 | 分隔多个值");
            }
        }

        // 正则表达式操作符验证
        if (RegexOperators.Contains(operatorName))
        {
            try
            {
                _ = new System.Text.RegularExpressions.Regex(value);
            }
            catch (ArgumentException)
            {
                return (false, $"操作符 {operatorName} 的正则表达式格式错误");
            }
        }

        // 数字类型值验证
        if (dataType == "number" && !NoValueOperators.Contains(operatorName) && !RangeOperators.Contains(operatorName))
        {
            if (!double.TryParse(value, out _))
            {
                return (false, $"数字类型的操作符 {operatorName} 需要有效的数字值");
            }
        }

        return (true, string.Empty);
    }
}
