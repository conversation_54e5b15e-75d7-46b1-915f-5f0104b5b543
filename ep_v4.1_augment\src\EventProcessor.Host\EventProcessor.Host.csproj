<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <LangVersion>latest</LangVersion>
    <PublishSingleFile>true</PublishSingleFile>
    <SelfContained>false</SelfContained>
    <RuntimeIdentifier>win-x64</RuntimeIdentifier>
    <UseAppHost>true</UseAppHost>
    <NoWarn>$(NoWarn);1591</NoWarn>
  </PropertyGroup>

  <PropertyGroup>
    <AssemblyTitle>Event Processor V4.1 Host</AssemblyTitle>
    <AssemblyDescription>EP_V4.1增强规则配置系统主机程序</AssemblyDescription>
    <AssemblyCompany>Augment Code</AssemblyCompany>
    <AssemblyProduct>Event Processor V4.1</AssemblyProduct>
    <AssemblyVersion>*******</AssemblyVersion>
    <FileVersion>*******</FileVersion>
    <InformationalVersion>4.1.0</InformationalVersion>
  </PropertyGroup>

  <!-- 自动生成构建版本号 -->
  <PropertyGroup>
    <!-- 基础版本号 -->
    <VersionPrefix>4.1.0</VersionPrefix>
    <!-- 使用自 2000-01-01 以来的天数作为修订号 (符合int32) -->
    <DaysSince2000>$([System.Convert]::ToInt32($([System.DateTime]::UtcNow.Subtract($([System.DateTime]::new(2000, 1, 1))).TotalDays)))</DaysSince2000>
    <!-- 使用UTC时间的分钟数作为构建号 (符合int32) -->
    <MinutesToday>$([System.Convert]::ToInt32($([System.DateTime]::UtcNow.TimeOfDay.TotalMinutes)))</MinutesToday>
    <!-- 最终版本号，例如: 4.1.9348.870 -->
    <Version>$(VersionPrefix).$(DaysSince2000)</Version>
    <AssemblyVersion>$(VersionPrefix).$(DaysSince2000)</AssemblyVersion>
    <FileVersion>$(VersionPrefix).$(DaysSince2000)</FileVersion>
    <!-- 更详细的、人类可读的版本信息 -->
    <InformationalVersion>$(VersionPrefix)-build.$([System.DateTime]::UtcNow.ToString("yyyyMMdd.HHmm"))</InformationalVersion>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\EventProcessor.Core\EventProcessor.Core.csproj" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Extensions.Hosting" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Hosting.WindowsServices" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration.CommandLine" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Logging.Console" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Diagnostics.HealthChecks" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Caching.Memory" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Http" Version="8.0.0" />
    <PackageReference Include="Serilog" Version="3.1.1" />
    <PackageReference Include="Serilog.Extensions.Hosting" Version="8.0.0" />
    <PackageReference Include="Serilog.Settings.Configuration" Version="8.0.0" />
    <PackageReference Include="Serilog.Sinks.Console" Version="5.0.1" />
    <PackageReference Include="Serilog.Sinks.File" Version="5.0.0" />
    <PackageReference Include="NetEscapades.Configuration.Yaml" Version="3.1.0" />
  </ItemGroup>

  <ItemGroup>
    <None Update="appsettings.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="appsettings.yaml">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="appsettings.Development.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="appsettings.Development.yaml">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="appsettings.Production.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="appsettings.Production.yaml">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="*.yaml">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
  </ItemGroup>

</Project>
