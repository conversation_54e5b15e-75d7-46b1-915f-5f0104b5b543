using EventProcessor.Core.Services;
using Microsoft.Extensions.Diagnostics.HealthChecks;
using Microsoft.Extensions.Logging;
using HealthStatus = Microsoft.Extensions.Diagnostics.HealthChecks.HealthStatus;

namespace EventProcessor.Host.HealthChecks;

/// <summary>
/// Event Processor 健康检查
/// </summary>
public class EventProcessorHealthCheck : IHealthCheck
{
    private readonly IConfigurationService _configService;
    private readonly IErrorHandlingService _errorHandlingService;
    private readonly ILogger<EventProcessorHealthCheck> _logger;

    public EventProcessorHealthCheck(
        IConfigurationService configService,
        IErrorHandlingService errorHandlingService,
        ILogger<EventProcessorHealthCheck> logger)
    {
        _configService = configService;
        _errorHandlingService = errorHandlingService;
        _logger = logger;
    }

    public async Task<HealthCheckResult> CheckHealthAsync(
        HealthCheckContext context,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var healthData = new Dictionary<string, object>();
            var issues = new List<string>();

            // 检查配置服务
            CheckConfigurationService(healthData, issues);

            // 检查错误统计
            CheckErrorStatistics(healthData, issues);

            // 检查系统资源
            CheckSystemResources(healthData, issues);

            // 确定健康状态
            var status = DetermineHealthStatus(issues);

            var description = issues.Count > 0 
                ? $"发现 {issues.Count} 个问题: {string.Join(", ", issues)}"
                : "所有检查通过";

            return new HealthCheckResult(status, description, data: healthData);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "健康检查执行失败");
            return HealthCheckResult.Unhealthy("健康检查执行异常", ex);
        }
    }

    /// <summary>
    /// 检查配置服务
    /// </summary>
    private void CheckConfigurationService(Dictionary<string, object> healthData, List<string> issues)
    {
        try
        {
            var configStats = _configService.GetStatistics();
            
            healthData["config_valid"] = _configService.IsValid;
            healthData["config_loaded_at"] = configStats.LoadedAt?.ToString("yyyy-MM-dd HH:mm:ss") ?? "未加载";
            healthData["config_reload_count"] = configStats.ReloadCount;
            healthData["config_validation_failures"] = configStats.ValidationFailureCount;

            if (!_configService.IsValid)
            {
                issues.Add("配置无效");
            }

            if (configStats.ValidationFailureCount > 5)
            {
                issues.Add($"配置验证失败次数过多: {configStats.ValidationFailureCount}");
            }
        }
        catch (Exception ex)
        {
            issues.Add($"配置服务检查失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 检查错误统计
    /// </summary>
    private void CheckErrorStatistics(Dictionary<string, object> healthData, List<string> issues)
    {
        try
        {
            var errorStats = _errorHandlingService.GetErrorStatistics();
            
            healthData["total_errors"] = errorStats.TotalErrors;
            healthData["error_rate_per_minute"] = errorStats.ErrorRatePerMinute;
            healthData["last_error_at"] = errorStats.LastErrorAt?.ToString("yyyy-MM-dd HH:mm:ss") ?? "无";

            // 检查错误率
            if (errorStats.ErrorRatePerMinute > 10)
            {
                issues.Add($"错误率过高: {errorStats.ErrorRatePerMinute:F2}/分钟");
            }

            // 检查最近错误
            if (errorStats.LastErrorAt.HasValue && 
                DateTime.UtcNow - errorStats.LastErrorAt.Value < TimeSpan.FromMinutes(5))
            {
                issues.Add("最近5分钟内有错误发生");
            }
        }
        catch (Exception ex)
        {
            issues.Add($"错误统计检查失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 检查系统资源
    /// </summary>
    private void CheckSystemResources(Dictionary<string, object> healthData, List<string> issues)
    {
        try
        {
            // 检查内存使用
            var process = System.Diagnostics.Process.GetCurrentProcess();
            var memoryMB = process.WorkingSet64 / 1024 / 1024;
            
            healthData["memory_usage_mb"] = memoryMB;
            healthData["thread_count"] = process.Threads.Count;
            healthData["gc_collection_count"] = GC.CollectionCount(0) + GC.CollectionCount(1) + GC.CollectionCount(2);

            // 内存使用检查
            if (memoryMB > 512)
            {
                issues.Add($"内存使用过高: {memoryMB}MB");
            }

            // 线程数检查
            if (process.Threads.Count > 100)
            {
                issues.Add($"线程数过多: {process.Threads.Count}");
            }

            // 检查磁盘空间
            var logDirectory = new DirectoryInfo("logs");
            if (logDirectory.Exists)
            {
                var drive = new DriveInfo(logDirectory.Root.FullName);
                var freeSpaceGB = drive.AvailableFreeSpace / 1024 / 1024 / 1024;
                
                healthData["disk_free_space_gb"] = freeSpaceGB;
                
                if (freeSpaceGB < 1)
                {
                    issues.Add($"磁盘空间不足: {freeSpaceGB:F2}GB");
                }
            }
        }
        catch (Exception ex)
        {
            issues.Add($"系统资源检查失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 确定健康状态
    /// </summary>
    private HealthStatus DetermineHealthStatus(List<string> issues)
    {
        if (issues.Count == 0)
        {
            return HealthStatus.Healthy;
        }

        // 检查是否有严重问题
        var criticalIssues = issues.Where(i => 
            i.Contains("配置无效") || 
            i.Contains("内存使用过高") || 
            i.Contains("磁盘空间不足")).ToList();

        if (criticalIssues.Any())
        {
            return HealthStatus.Unhealthy;
        }

        return HealthStatus.Degraded;
    }
}

/// <summary>
/// MQTT 连接健康检查
/// </summary>
public class MqttHealthCheck : IHealthCheck
{
    private readonly IMqttService _mqttService;
    private readonly ILogger<MqttHealthCheck> _logger;

    public MqttHealthCheck(IMqttService mqttService, ILogger<MqttHealthCheck> logger)
    {
        _mqttService = mqttService;
        _logger = logger;
    }

    public async Task<HealthCheckResult> CheckHealthAsync(
        HealthCheckContext context,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var healthData = new Dictionary<string, object>();
            var issues = new List<string>();

            // 检查MQTT连接状态
            CheckMqttConnection(healthData, issues);

            // 检查MQTT统计信息
            CheckMqttStatistics(healthData, issues);

            // 确定健康状态
            var status = issues.Count == 0 ? HealthStatus.Healthy : 
                        issues.Any(i => i.Contains("未连接")) ? HealthStatus.Unhealthy : HealthStatus.Degraded;

            var description = issues.Count > 0 
                ? $"MQTT问题: {string.Join(", ", issues)}"
                : "MQTT连接正常";

            return new HealthCheckResult(status, description, data: healthData);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "MQTT健康检查执行失败");
            return HealthCheckResult.Unhealthy("MQTT健康检查异常", ex);
        }
    }

    /// <summary>
    /// 检查MQTT连接状态
    /// </summary>
    private void CheckMqttConnection(Dictionary<string, object> healthData, List<string> issues)
    {
        try
        {
            healthData["mqtt_connected"] = _mqttService.IsConnected;
            healthData["mqtt_client_id"] = _mqttService.ClientId;

            if (!_mqttService.IsConnected)
            {
                issues.Add("MQTT未连接");
            }
        }
        catch (Exception ex)
        {
            issues.Add($"MQTT连接检查失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 检查MQTT统计信息
    /// </summary>
    private void CheckMqttStatistics(Dictionary<string, object> healthData, List<string> issues)
    {
        try
        {
            var stats = _mqttService.GetConnectionStatistics();
            
            healthData["mqtt_messages_sent"] = stats.MessagesSent;
            healthData["mqtt_messages_received"] = stats.MessagesReceived;
            healthData["mqtt_reconnect_count"] = stats.ReconnectCount;
            healthData["mqtt_subscribed_topics"] = stats.SubscribedTopicCount;
            healthData["mqtt_last_activity"] = stats.LastActivityAt.ToString("yyyy-MM-dd HH:mm:ss");

            // 检查重连次数
            if (stats.ReconnectCount > 10)
            {
                issues.Add($"MQTT重连次数过多: {stats.ReconnectCount}");
            }

            // 检查最后活动时间
            if (DateTime.UtcNow - stats.LastActivityAt > TimeSpan.FromMinutes(10))
            {
                issues.Add("MQTT长时间无活动");
            }

            // 检查订阅主题数
            if (stats.SubscribedTopicCount == 0)
            {
                issues.Add("未订阅任何MQTT主题");
            }
        }
        catch (Exception ex)
        {
            issues.Add($"MQTT统计信息检查失败: {ex.Message}");
        }
    }
}
