using EventProcessor.Core.Engine;
using EventProcessor.Core.Models;
using EventProcessor.Core.Services;
using EventProcessor.Host.HealthChecks;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using NetEscapades.Configuration.Yaml;
using Serilog;
using System.Reflection;

namespace EventProcessor.Host;

/// <summary>
/// Event Processor V4.1 主程序
/// </summary>
public class Program
{
    public static async Task<int> Main(string[] args)
    {
        // 配置Serilog作为启动日志
        Log.Logger = new LoggerConfiguration()
            .WriteTo.Console()
            .WriteTo.File("logs/startup-.log", rollingInterval: RollingInterval.Day)
            .CreateBootstrapLogger();

        try
        {
            // --- 获取并记录详细版本信息 ---
            var assembly = Assembly.GetExecutingAssembly();
            var informationalVersion = assembly.GetCustomAttribute<AssemblyInformationalVersionAttribute>()?.InformationalVersion ?? "N/A";
            
            Log.Information("=================================================");
            Log.Information("Event Processor V4.1 正在启动...");
            Log.Information("版本: {Version}", informationalVersion);
            Log.Information("运行时: {Runtime}", Environment.Version);
            Log.Information("操作系统: {OS}", Environment.OSVersion);
            Log.Information("=================================================");

            var host = CreateHostBuilder(args).Build();

            // 验证配置
            await ValidateConfiguration(host);

            // 启动服务
            await host.RunAsync();

            Log.Information("Event Processor V4.1 正常退出");
            return 0;
        }
        catch (Exception ex)
        {
            Log.Fatal(ex, "Event Processor V4.1 启动失败");
            return 1;
        }
        finally
        {
            await Log.CloseAndFlushAsync();
        }
    }

    public static IHostBuilder CreateHostBuilder(string[] args) =>
        Microsoft.Extensions.Hosting.Host.CreateDefaultBuilder(args)
            .UseWindowsService(options =>
            {
                options.ServiceName = "EventProcessor.V4.1";
            })
            .UseSerilog((context, services, configuration) => 
            {
                // --- 动态日志文件路径的完整实现 ---

                // 1. 从配置中动态获取 MQTT ClientId
                var mqttClientId = context.Configuration.GetValue<string>("Mqtt:ClientId");
                if (string.IsNullOrWhiteSpace(mqttClientId))
                {
                    mqttClientId = "default-client-id";
                    Log.Warning("在配置中未找到有效的 Mqtt:ClientId，将使用默认日志文件名: log_{ClientId}.log", mqttClientId);
                }

                // 2. 构造动态的日志文件路径
                var logFilePath = $"logs/log_{mqttClientId}_.log"; // 添加下划线以兼容Serilog的日期格式

                // 3. 从配置中读取 File sink 的其他参数，并提供合理的默认值
                var fileConfigSection = context.Configuration.GetSection("Serilog:WriteTo").GetChildren()
                                           .FirstOrDefault(s => s.GetValue<string>("Name") == "File");

                var outputTemplate = fileConfigSection?.GetValue<string>("Args:outputTemplate") 
                                     ?? "[{Timestamp:HH:mm:ss.fff} {Level:u3}] [{SourceContext}] {Message:lj}{NewLine}{Exception}";
                
                var retainedFileCountLimit = fileConfigSection?.GetValue<int?>("Args:retainedFileCountLimit") 
                                             ?? 30;

                var rollingIntervalStr = fileConfigSection?.GetValue<string>("Args:rollingInterval") 
                                         ?? "Day";
                var rollingInterval = Enum.TryParse<RollingInterval>(rollingIntervalStr, true, out var interval) 
                                      ? interval 
                                      : RollingInterval.Day;

                // 4. 完整配置 Serilog
                configuration
                    // 读取除 WriteTo:File 之外的所有配置 (如 MinimumLevel, Enrich, WriteTo:Console)
                    .ReadFrom.Configuration(context.Configuration) 
                    .Enrich.FromLogContext()
                    .Enrich.WithProperty("Application", "EventProcessor.V4.1")
                    .Enrich.WithProperty("Version", Assembly.GetExecutingAssembly().GetName().Version?.ToString())
                    // 使用我们动态构建的参数，显式地、一次性地配置 File sink
                    .WriteTo.File(logFilePath,
                                  rollingInterval: rollingInterval,
                                  retainedFileCountLimit: retainedFileCountLimit,
                                  outputTemplate: outputTemplate);
                
                Log.Information("日志文件将被写入: {LogFilePath}", logFilePath.Replace("_.log", "_<date>.log"));
            })
            .ConfigureAppConfiguration((context, config) =>
            {
                var env = context.HostingEnvironment;

                // 获取配置文件路径
                var configFile = Program.GetConfigurationFilePath(args);

                // 加载主配置文件（统一使用 YAML 格式）
                config.AddYamlFile(configFile, optional: false, reloadOnChange: true);

                // 环境特定配置文件（如果存在）
                var envConfigFile = Program.GetEnvironmentConfigFile(configFile, env.EnvironmentName);
                if (File.Exists(envConfigFile))
                {
                    config.AddYamlFile(envConfigFile, optional: true, reloadOnChange: true);
                }

                // 本地配置文件（如果存在）
                var localConfigFile = Program.GetLocalConfigFile(configFile);
                if (File.Exists(localConfigFile))
                {
                    config.AddYamlFile(localConfigFile, optional: true, reloadOnChange: true);
                }

                config.AddEnvironmentVariables("EP_")
                      .AddCommandLine(args);
            })
            .ConfigureServices((context, services) =>
            {
                // 配置选项 - 从统一的 YAML 配置文件中绑定
                services.Configure<EventConfiguration>(context.Configuration.GetSection("EventProcessor"));
                services.Configure<MqttConfiguration>(context.Configuration.GetSection("Mqtt"));
                services.Configure<ErrorHandlingConfiguration>(context.Configuration.GetSection("ErrorHandling"));

                // 注册核心服务
                services.AddSingleton<ConditionEvaluator>();
                services.AddSingleton<RuleEngine>();
                services.AddSingleton<IMqttService, MqttService>();
                services.AddSingleton<IAlarmGenerator, AlarmGenerator>();
                services.AddSingleton<IStandardAlarmGenerator, StandardAlarmGenerator>();
                services.AddSingleton<IErrorHandlingService, ErrorHandlingService>();

                // 注册事件管理器 - 从 IOptions<EventConfiguration> 中提取配置
                services.AddSingleton<EventManager>(provider =>
                {
                    var eventConfig = provider.GetRequiredService<IOptions<EventConfiguration>>().Value;
                    var logger = provider.GetRequiredService<ILogger<EventManager>>();
                    return new EventManager(eventConfig, provider, logger);
                });

                // 注册主机服务
                services.AddHostedService<EventProcessorHostedService>();

                // 健康检查
                services.AddHealthChecks()
                    .AddCheck<EventProcessorHealthCheck>("event_processor")
                    .AddCheck<MqttHealthCheck>("mqtt_connection");

                // 添加内存缓存
                services.AddMemoryCache();

                // 添加HTTP客户端（用于AI服务调用等）
                services.AddHttpClient();
            });

    /// <summary>
    /// 验证配置
    /// </summary>
    private static async Task ValidateConfiguration(IHost host)
    {
        using var scope = host.Services.CreateScope();
        var logger = scope.ServiceProvider.GetRequiredService<ILogger<Program>>();
        var eventConfig = scope.ServiceProvider.GetRequiredService<IOptions<EventConfiguration>>().Value;

        try
        {
            // 验证事件配置
            if (string.IsNullOrEmpty(eventConfig.EventId))
            {
                throw new InvalidOperationException("事件ID不能为空");
            }

            if (string.IsNullOrEmpty(eventConfig.EventName))
            {
                throw new InvalidOperationException("事件名称不能为空");
            }

            if (string.IsNullOrEmpty(eventConfig.EvaluationStrategy))
            {
                throw new InvalidOperationException("评估策略不能为空");
            }

            // 显示详细的EventProcessor配置参数
            logger.LogInformation("==========================================");
            logger.LogInformation("EventProcessor 配置参数:");
            logger.LogInformation("==========================================");
            logger.LogInformation("事件ID: {EventId}", eventConfig.EventId);
            logger.LogInformation("事件名称: {EventName}", eventConfig.EventName);
            logger.LogInformation("评估策略: {EvaluationStrategy}", eventConfig.EvaluationStrategy);
            logger.LogInformation("优先级: {Priority}", eventConfig.Priority);
            logger.LogInformation("小区ID: {CommId}", eventConfig.CommId);
            logger.LogInformation("位置ID: {PositionId}", eventConfig.PositionId);
            logger.LogInformation("公司名称: {CompanyName}", eventConfig.CompanyName);
            logger.LogInformation("告警宽限期: {AlarmGracePeriodSeconds}秒", eventConfig.AlarmGracePeriodSeconds);
            logger.LogInformation("启用告警取消: {EnableAlarmCancellation}", eventConfig.EnableAlarmCancellation);
            logger.LogInformation("自定义时间窗口: {CustomTimeWindowMinutes}分钟", eventConfig.CustomTimeWindowMinutes);
            logger.LogInformation("==========================================");
            logger.LogInformation("配置验证成功");
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "配置验证失败");
            throw;
        }
    }

    /// <summary>
    /// 获取配置文件路径
    /// </summary>
    /// <param name="args">命令行参数</param>
    /// <returns>配置文件路径</returns>
    public static string GetConfigurationFilePath(string[] args)
    {
        // 解析命令行参数
        for (int i = 0; i < args.Length - 1; i++)
        {
            if (args[i].Equals("-ConfigFile", StringComparison.OrdinalIgnoreCase) ||
                args[i].Equals("--config-file", StringComparison.OrdinalIgnoreCase))
            {
                var configFile = args[i + 1];
                if (File.Exists(configFile))
                {
                    Log.Information("使用指定的配置文件: {ConfigFile}", configFile);
                    return configFile;
                }
                else
                {
                    throw new FileNotFoundException($"指定的配置文件不存在: {configFile}");
                }
            }
        }

        // 检查环境变量
        var envConfigFile = Environment.GetEnvironmentVariable("EP_CONFIG_FILE");
        if (!string.IsNullOrEmpty(envConfigFile))
        {
            if (File.Exists(envConfigFile))
            {
                Log.Information("使用环境变量指定的配置文件: {ConfigFile}", envConfigFile);
                return envConfigFile;
            }
            else
            {
                Log.Warning("环境变量指定的配置文件不存在: {ConfigFile}", envConfigFile);
            }
        }

        // 默认配置文件
        const string defaultConfigFile = "appsettings.yaml";
        if (File.Exists(defaultConfigFile))
        {
            Log.Information("使用默认配置文件: {ConfigFile}", defaultConfigFile);
            return defaultConfigFile;
        }

        throw new FileNotFoundException($"找不到配置文件。请确保存在 '{defaultConfigFile}' 或使用 -ConfigFile 参数指定配置文件路径。");
    }

    /// <summary>
    /// 获取环境特定配置文件路径
    /// </summary>
    /// <param name="baseConfigFile">基础配置文件路径</param>
    /// <param name="environmentName">环境名称</param>
    /// <returns>环境特定配置文件路径</returns>
    public static string GetEnvironmentConfigFile(string baseConfigFile, string environmentName)
    {
        var directory = Path.GetDirectoryName(baseConfigFile) ?? "";
        var fileNameWithoutExtension = Path.GetFileNameWithoutExtension(baseConfigFile);
        var extension = Path.GetExtension(baseConfigFile);

        return Path.Combine(directory, $"{fileNameWithoutExtension}.{environmentName}{extension}");
    }

    /// <summary>
    /// 获取本地配置文件路径
    /// </summary>
    /// <param name="baseConfigFile">基础配置文件路径</param>
    /// <returns>本地配置文件路径</returns>
    public static string GetLocalConfigFile(string baseConfigFile)
    {
        var directory = Path.GetDirectoryName(baseConfigFile) ?? "";
        var fileNameWithoutExtension = Path.GetFileNameWithoutExtension(baseConfigFile);
        var extension = Path.GetExtension(baseConfigFile);

        return Path.Combine(directory, $"{fileNameWithoutExtension}.local{extension}");
    }
}

/// <summary>
/// Event Processor 主机服务
/// </summary>
public class EventProcessorHostedService : BackgroundService
{
    private readonly ILogger<EventProcessorHostedService> _logger;
    private readonly EventConfiguration _eventConfig;
    private readonly IMqttService _mqttService;
    private readonly EventManager _eventManager;
    private readonly IErrorHandlingService _errorHandlingService;
    private readonly IServiceProvider _serviceProvider;

    public EventProcessorHostedService(
        ILogger<EventProcessorHostedService> logger,
        IOptions<EventConfiguration> eventConfig,
        IMqttService mqttService,
        EventManager eventManager,
        IErrorHandlingService errorHandlingService,
        IServiceProvider serviceProvider)
    {
        _logger = logger;
        _eventConfig = eventConfig.Value;
        _mqttService = mqttService;
        _eventManager = eventManager;
        _errorHandlingService = errorHandlingService;
        _serviceProvider = serviceProvider;
    }

    public override async Task StartAsync(CancellationToken cancellationToken)
    {
        _logger.LogInformation("Event Processor 服务正在启动...");

        try
        {
            // 启动MQTT服务
            await _mqttService.StartAsync(cancellationToken);

            // 订阅MQTT消息
            await SubscribeToMqttTopics();

            // 订阅MQTT消息接收事件
            _mqttService.MessageReceived += OnMqttMessageReceived;

            _logger.LogInformation("Event Processor 服务启动完成");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Event Processor 服务启动失败");
            throw;
        }

        await base.StartAsync(cancellationToken);
    }

    public override async Task StopAsync(CancellationToken cancellationToken)
    {
        _logger.LogInformation("Event Processor 服务正在停止...");

        try
        {
            // 取消订阅MQTT消息
            _mqttService.MessageReceived -= OnMqttMessageReceived;

            // 停止MQTT服务
            await _mqttService.StopAsync(cancellationToken);

            // 清理事件管理器
            _eventManager.ClearAllEvents();

            _logger.LogInformation("Event Processor 服务停止完成");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Event Processor 服务停止时发生异常");
        }

        await base.StopAsync(cancellationToken);
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        _logger.LogInformation("Event Processor 主循环开始");

        try
        {
            while (!stoppingToken.IsCancellationRequested)
            {
                // 定期输出统计信息
                LogStatistics();

                // 等待30秒
                await Task.Delay(TimeSpan.FromSeconds(30), stoppingToken);
            }
        }
        catch (OperationCanceledException)
        {
            _logger.LogInformation("Event Processor 主循环被取消");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Event Processor 主循环异常");
        }

        _logger.LogInformation("Event Processor 主循环结束");
    }

    /// <summary>
    /// 订阅MQTT主题
    /// </summary>
    private async Task SubscribeToMqttTopics()
    {
        var topics = new List<string>();

        // 设备信号主题
        if (_eventConfig.DeviceSignal?.Topics != null)
        {
            topics.AddRange(_eventConfig.DeviceSignal.Topics);
        }

        // 业务规则主题
        if (_eventConfig.RuleConfiguration.BusinessRules != null)
        {
            foreach (var rule in _eventConfig.RuleConfiguration.BusinessRules)
            {
                topics.Add(rule.SourceTopic);
            }
        }

        // 排除规则主题
        if (_eventConfig.RuleConfiguration.ExclusionRules != null)
        {
            foreach (var rule in _eventConfig.RuleConfiguration.ExclusionRules)
            {
                topics.Add(rule.SourceTopic);
            }
        }

        // AI结果主题
        var aiResultTopic = $"ai/{_eventConfig.CommId}/{_eventConfig.PositionId}/result";
        topics.Add(aiResultTopic);

        // 去重并订阅
        var uniqueTopics = topics.Distinct().ToArray();
        await _mqttService.SubscribeAsync(uniqueTopics);

        _logger.LogInformation("已订阅 {Count} 个MQTT主题: {Topics}",
                             uniqueTopics.Length, string.Join(", ", uniqueTopics));
    }

    /// <summary>
    /// 处理MQTT消息
    /// </summary>
    private async void OnMqttMessageReceived(object? sender, MqttMessageReceivedEventArgs e)
    {
        try
        {
            var messageType = DetermineMessageType(e.Topic, _eventConfig);

            var eventMessage = new EventMessage
            {
                MessageType = messageType,
                Topic = e.Topic,
                Payload = e.Payload,
                EventId = _eventConfig.EventId,
                CommId = _eventConfig.CommId,
                PositionId = _eventConfig.PositionId
            };

            // 获取或创建事件聚合器
            var aggregator = _eventManager.GetOrCreateAggregator(eventMessage);

            // 处理消息
            await aggregator.ProcessMessage(eventMessage);

            _logger.LogDebug("MQTT消息已处理: {Topic}, 类型: {MessageType}", e.Topic, messageType);
        }
        catch (Exception ex)
        {
            var context = new ErrorContext
            {
                OperationName = "ProcessMqttMessage",
                ComponentName = "EventProcessorHostedService",
                AdditionalData = new Dictionary<string, object>
                {
                    ["Topic"] = e.Topic,
                    ["PayloadLength"] = e.Payload.Length
                }
            };

            await _errorHandlingService.HandleExceptionAsync(ex, context);
        }
    }

    /// <summary>
    /// 确定消息类型
    /// </summary>
    private string DetermineMessageType(string topic, EventConfiguration config)
    {
        // 设备信号
        if (config.DeviceSignal?.Topics?.Contains(topic) == true)
        {
            return "DeviceSignal";
        }

        // AI结果
        if (topic.Contains("/ai/") && topic.EndsWith("/result"))
        {
            return "AIResult";
        }

        // 业务规则
        if (config.RuleConfiguration.BusinessRules?.Any(r => r.SourceTopic == topic) == true)
        {
            return "BusinessData";
        }

        // 排除规则
        if (config.RuleConfiguration.ExclusionRules?.Any(r => r.SourceTopic == topic) == true)
        {
            return "ExclusionData";
        }

        return "Unknown";
    }

    /// <summary>
    /// 输出统计信息
    /// </summary>
    private void LogStatistics()
    {
        try
        {
            var eventStats = _eventManager.GetStatistics();
            var mqttStats = _mqttService.GetConnectionStatistics();
            var errorStats = _errorHandlingService.GetErrorStatistics();

            _logger.LogInformation("统计信息 - 活跃事件: {ActiveEvents}, MQTT消息: 发送{Sent}/接收{Received}, 错误: {Errors}",
                                 eventStats.ActiveEventCount,
                                 mqttStats.MessagesSent,
                                 mqttStats.MessagesReceived,
                                 errorStats.TotalErrors);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "输出统计信息时发生异常");
        }
    }
}
