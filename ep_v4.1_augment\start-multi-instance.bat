@echo off
REM EventProcessor.Host 多实例启动脚本
REM 同时启动多个事件处理实例

echo 启动多个 EventProcessor.Host 实例...
echo.

echo 启动实例 1: EV001001 (月租车未过期超时滞留出口)
start "EP-EV001001" cmd /k "dotnet run --project src\EventProcessor.Host\EventProcessor.Host.csproj --configuration Release -- -ConfigFile appsettings.yaml"

timeout /t 3 /nobreak >nul

echo 启动实例 2: EV001008 (三轮车快递车检测)
start "EP-EV001008" cmd /k "dotnet run --project src\EventProcessor.Host\EventProcessor.Host.csproj --configuration Release -- -ConfigFile EV001008.yaml"

echo.
echo 多实例启动完成！
echo 实例 1 窗口标题: EP-EV001001
echo 实例 2 窗口标题: EP-EV001008
echo.
echo 按任意键退出此脚本（不会影响已启动的实例）...
pause
