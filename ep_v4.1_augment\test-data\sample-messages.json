{"EV001001_TestMessages": {"description": "月租车未过期超时滞留出口测试消息", "deviceSignal": {"topic": "device/BDN861290073715232/event", "payload": {"I2": "0", "timestamp": "20241201143025123", "device_id": "BDN861290073715232", "position": "P002LfyBmOut"}}, "businessData": {"topic": "ajb/101013/out/P002LfyBmOut/time_log", "payload": {"CardType": "月租卡", "log_car_no": "京A12345", "log_user_name": "张三", "log_end_time": "2024-12-01 14:30:25", "log_remain_days": 15, "duration": 25, "log_position_name": "出口闸机", "log_time": "2024-12-01 14:30:25"}}, "exclusionData": {"topic": "ajb/101013/out/P002LfyBmOut/time_log", "payload": {"CardType": "临时卡", "log_remain_days": 0, "maintenance_mode": "inactive"}}}, "EV001036_TestMessages": {"description": "车辆逆行检测测试消息", "deviceSignal": {"topic": "device/BDN861290073715232/event", "payload": {"motion_detected": "1", "timestamp": "20241201143030456", "device_id": "BDN861290073715232", "position": "P001MainGate"}}, "aiResult": {"topic": "ai/101013/P001MainGate/result", "payload": {"event_id": "EV001036", "request_id": "101013_P001MainGate_EV001036_20241201143000", "result": {"has_reverse_driving": true, "vehicle_count": 1, "confidence": 0.92, "description": "检测到一辆白色轿车逆向行驶"}, "timestamp": "20241201143033789", "processing_time": 2.5, "success": true}}, "exclusionData": {"topic": "system/maintenance/status", "payload": {"maintenance_mode": "inactive", "camera_status": "online", "system_status": "normal"}}}, "EV001037_TestMessages": {"description": "无卡人员尾随进入检测测试消息", "deviceSignal": {"topic": "device/BDN861290073715232/event", "payload": {"person_detected": "1", "timestamp": "20241201143035789", "device_id": "BDN861290073715232", "position": "P003SecureEntry"}}, "businessData": {"topic": "access/101013/in/P003SecureEntry/card_log", "payload": {"card_valid": "true", "access_granted": "true", "person_count_sensor": 2, "door_open_duration": 8, "card_user_name": "李四", "card_number": "1234567890", "access_time": "2024-12-01 14:30:35"}}, "aiResult": {"topic": "ai/101013/P003SecureEntry/result", "payload": {"event_id": "EV001037", "request_id": "101013_P003SecureEntry_EV001037_20241201143000", "result": {"has_tailgating": true, "person_count": 2, "card_holders": 1, "confidence": 0.89, "description": "检测到一人持卡进入，另一人尾随"}, "timestamp": "20241201143037123", "processing_time": 1.8, "success": true}}, "exclusionData": {"topic": "system/security/status", "payload": {"security_level": "high", "emergency_mode": "inactive", "access_control_active": "true"}}}, "CommonTestScenarios": {"description": "通用测试场景", "messageSequences": {"normalFlow": ["deviceSignal", "businessData", "aiResult (if applicable)"], "exclusionFlow": ["deviceSignal", "exclusionData", "businessData"], "outOfOrderFlow": ["businessData", "deviceSignal", "aiResult (if applicable)"], "lateExclusionFlow": ["deviceSignal", "businessData", "aiResult (if applicable)", "exclusionData (after alarm)"]}, "timingTests": {"gracePeriodTest": {"description": "测试告警静默期机制", "steps": ["发送设备信号", "发送业务数据（满足告警条件）", "等待静默期时间", "在静默期内发送排除数据", "验证告警是否被取消"]}, "aiTimeoutTest": {"description": "测试AI分析超时处理", "steps": ["发送设备信号", "等待AI分析超时", "验证降级策略执行"]}}}}