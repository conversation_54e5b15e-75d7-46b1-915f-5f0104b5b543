# MQTT连接测试脚本
# 用于验证MQTT认证信息是否正确

param(
    [string]$BrokerHost = "mq.bangdouni.com",
    [int]$BrokerPort = 1883,
    [string]$Username = "bdn_event_processor",
    [string]$Password = "Bdn@2024",
    [string]$ClientId = "EP_V4.1_TEST_" + (Get-Random -Maximum 9999)
)

Write-Host "MQTT连接测试工具" -ForegroundColor Green
Write-Host "===========================================" -ForegroundColor Green
Write-Host "服务器: $BrokerHost`:$BrokerPort"
Write-Host "用户名: $Username"
Write-Host "密码: $($Password.Substring(0,3))***"
Write-Host "客户端ID: $ClientId"
Write-Host "===========================================" -ForegroundColor Green

# 检查是否安装了mosquitto客户端工具
$mosquittoPath = Get-Command mosquitto_pub -ErrorAction SilentlyContinue
if (-not $mosquittoPath) {
    Write-Host "错误: 未找到mosquitto客户端工具" -ForegroundColor Red
    Write-Host "请安装mosquitto客户端工具或使用以下替代方案:" -ForegroundColor Yellow
    Write-Host "1. 下载mosquitto客户端: https://mosquitto.org/download/" -ForegroundColor Yellow
    Write-Host "2. 或使用在线MQTT客户端测试工具" -ForegroundColor Yellow
    Write-Host "3. 或使用MQTT Explorer等图形化工具" -ForegroundColor Yellow
    exit 1
}

Write-Host "正在测试MQTT连接..." -ForegroundColor Yellow

# 测试连接和发布消息
$testTopic = "test/connection"
$testMessage = "Connection test from PowerShell at $(Get-Date)"

try {
    # 使用mosquitto_pub测试连接
    $result = & mosquitto_pub -h $BrokerHost -p $BrokerPort -u $Username -P $Password -i $ClientId -t $testTopic -m $testMessage -d 2>&1
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ MQTT连接测试成功!" -ForegroundColor Green
        Write-Host "认证信息正确，可以正常连接到MQTT服务器" -ForegroundColor Green
    } else {
        Write-Host "❌ MQTT连接测试失败!" -ForegroundColor Red
        Write-Host "错误信息: $result" -ForegroundColor Red
        
        # 分析常见错误
        if ($result -match "Connection refused") {
            Write-Host "可能原因: 服务器拒绝连接，请检查服务器地址和端口" -ForegroundColor Yellow
        } elseif ($result -match "not authorized" -or $result -match "authentication") {
            Write-Host "可能原因: 认证失败，请检查用户名和密码" -ForegroundColor Yellow
        } elseif ($result -match "timeout") {
            Write-Host "可能原因: 网络超时，请检查网络连接" -ForegroundColor Yellow
        }
    }
} catch {
    Write-Host "❌ 执行测试时发生异常: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "===========================================" -ForegroundColor Green
Write-Host "测试完成" -ForegroundColor Green
