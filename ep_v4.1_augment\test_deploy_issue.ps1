# 部署脚本问题诊断工具
# 用于测试远程连接和进程管理功能

param(
    [switch]$TestRemoteConnection = $false,
    [switch]$TestProcessManagement = $false,
    [switch]$TestNetworkPath = $false,
    [switch]$All = $false
)

Write-Host "=== 部署脚本问题诊断工具 ===" -ForegroundColor Green

if ($TestRemoteConnection -or $All) {
    Write-Host "`n1. 测试远程连接..." -ForegroundColor Yellow
    
    # 测试网络连接
    Write-Host "   - 测试网络连接 (ping bdnserver)..."
    try {
        $pingResult = Test-Connection -ComputerName "bdnserver" -Count 1 -Quiet
        if ($pingResult) {
            Write-Host "   ✅ 网络连接正常" -ForegroundColor Green
        } else {
            Write-Host "   ❌ 网络连接失败" -ForegroundColor Red
        }
    }
    catch {
        Write-Host "   ❌ 网络连接测试异常: $($_.Exception.Message)" -ForegroundColor Red
    }
    
    # 测试网络共享访问
    Write-Host "   - 测试网络共享访问 (\\bdnserver\BDN)..."
    try {
        $sharePath = "\\bdnserver\BDN"
        if (Test-Path $sharePath) {
            Write-Host "   ✅ 网络共享访问正常" -ForegroundColor Green
            $items = Get-ChildItem $sharePath | Measure-Object
            Write-Host "   📁 共享目录包含 $($items.Count) 个项目" -ForegroundColor Cyan
        } else {
            Write-Host "   ❌ 网络共享访问失败" -ForegroundColor Red
        }
    }
    catch {
        Write-Host "   ❌ 网络共享访问异常: $($_.Exception.Message)" -ForegroundColor Red
    }
}

if ($TestProcessManagement -or $All) {
    Write-Host "`n2. 测试进程管理..." -ForegroundColor Yellow
    
    # 测试本地进程查询
    Write-Host "   - 测试本地进程查询..."
    try {
        $localProcesses = Get-Process -Name "EventProcessor.Host" -ErrorAction SilentlyContinue
        if ($localProcesses) {
            Write-Host "   ✅ 本地找到 EventProcessor.Host 进程" -ForegroundColor Green
            foreach ($proc in $localProcesses) {
                Write-Host "      - PID: $($proc.Id), CPU: $($proc.CPU), Memory: $([math]::Round($proc.WorkingSet64/1MB, 2)) MB" -ForegroundColor Cyan
            }
        } else {
            Write-Host "   ℹ️  本地未找到 EventProcessor.Host 进程" -ForegroundColor Yellow
        }
    }
    catch {
        Write-Host "   ❌ 本地进程查询异常: $($_.Exception.Message)" -ForegroundColor Red
    }
    
    # 测试远程进程查询
    Write-Host "   - 测试远程进程查询 (bdnserver)..."
    try {
        $remoteProcesses = Get-Process -Name "EventProcessor.Host" -ComputerName "bdnserver" -ErrorAction SilentlyContinue
        if ($remoteProcesses) {
            Write-Host "   ✅ 远程找到 EventProcessor.Host 进程" -ForegroundColor Green
            foreach ($proc in $remoteProcesses) {
                Write-Host "      - PID: $($proc.Id), CPU: $($proc.CPU), Memory: $([math]::Round($proc.WorkingSet64/1MB, 2)) MB" -ForegroundColor Cyan
            }
        } else {
            Write-Host "   ℹ️  远程未找到 EventProcessor.Host 进程" -ForegroundColor Yellow
        }
    }
    catch {
        Write-Host "   ❌ 远程进程查询异常: $($_.Exception.Message)" -ForegroundColor Red
        Write-Host "     这可能是因为:" -ForegroundColor Yellow
        Write-Host "     - 远程计算机不可达" -ForegroundColor Yellow
        Write-Host "     - 权限不足" -ForegroundColor Yellow
        Write-Host "     - 防火墙阻止" -ForegroundColor Yellow
        Write-Host "     - WMI服务未启用" -ForegroundColor Yellow
    }
}

if ($TestNetworkPath -or $All) {
    Write-Host "`n3. 测试目标路径..." -ForegroundColor Yellow
    
    $targetPath = "\\bdnserver\BDN\EP_V4.1"
    Write-Host "   - 测试目标路径: $targetPath"
    
    try {
        if (Test-Path $targetPath) {
            Write-Host "   ✅ 目标路径可访问" -ForegroundColor Green
            
            # 检查目录内容
            $items = Get-ChildItem $targetPath
            Write-Host "   📁 目录包含 $($items.Count) 个项目:" -ForegroundColor Cyan
            foreach ($item in $items | Select-Object -First 10) {
                $type = if ($item.PSIsContainer) { "DIR" } else { "FILE" }
                $size = if ($item.PSIsContainer) { "" } else { " ($([math]::Round($item.Length/1KB, 1)) KB)" }
                Write-Host "      [$type] $($item.Name)$size" -ForegroundColor Gray
            }
            if ($items.Count -gt 10) {
                Write-Host "      ... 还有 $($items.Count - 10) 个项目" -ForegroundColor Gray
            }
        } else {
            Write-Host "   ❌ 目标路径不可访问" -ForegroundColor Red
        }
    }
    catch {
        Write-Host "   ❌ 目标路径测试异常: $($_.Exception.Message)" -ForegroundColor Red
    }
}

Write-Host "`n=== 诊断完成 ===" -ForegroundColor Green
Write-Host "建议:" -ForegroundColor Yellow
Write-Host "1. 如果远程进程管理失败，可以使用 -Force 参数跳过此步骤" -ForegroundColor Cyan
Write-Host "2. 确保目标服务器上的 WMI 服务正在运行" -ForegroundColor Cyan
Write-Host "3. 检查网络防火墙设置" -ForegroundColor Cyan
Write-Host "4. 验证用户权限" -ForegroundColor Cyan 