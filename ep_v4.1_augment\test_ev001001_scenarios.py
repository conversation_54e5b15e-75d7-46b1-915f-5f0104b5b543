#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
EP_V4.1 EV001001 月租车未过期超时滞留出口测试脚本
基于HoldingTimeout修改计划的完整测试场景
测试超时驱动告警机制的正确实现
"""

import json
import time
import paho.mqtt.client as mqtt
from datetime import datetime, timedelta
import threading
import sys

# MQTT配置 - 与appsettings.yaml保持一致
MQTT_CONFIG = {
    'host': 'mq.bangdouni.com',
    'port': 1883,
    'username': 'bdn_ai_process',
    'password': 'Bdn@2024',
    'keepalive': 60,
    'client_id': 'EP_V4.1_EV001001_TESTER'
}

# 测试主题配置 - 与appsettings.yaml保持一致
TEST_TOPICS = {
    'device_signal': 'device/BDN888/event',
    'business_data': 'ajb/101013/out/P088LfyBmOut/time_log',
    'alarm_output': 'hq/101013/P002LfyBmOut/event'
}

class EV001001Tester:
    def __init__(self):
        self.client = None
        self.alarm_received = False
        self.alarm_message = None
        self.test_start_time = None
        
    def on_connect(self, client, userdata, flags, rc):
        if rc == 0:
            print(f"[{self._timestamp()}] ✅ MQTT连接成功")
            # 订阅告警主题以监控结果
            client.subscribe(TEST_TOPICS['alarm_output'], qos=1)
            print(f"[{self._timestamp()}] 📡 已订阅告警主题: {TEST_TOPICS['alarm_output']}")
        else:
            print(f"[{self._timestamp()}] ❌ MQTT连接失败，错误码: {rc}")

    def on_message(self, client, userdata, msg):
        """接收到告警消息的回调"""
        if msg.topic == TEST_TOPICS['alarm_output']:
            self.alarm_received = True
            self.alarm_message = msg.payload.decode('utf-8')
            alarm_time = datetime.now()
            duration = (alarm_time - self.test_start_time).total_seconds()
            print(f"[{self._timestamp()}] 🚨 收到告警消息！耗时: {duration:.1f}秒")
            print(f"[{self._timestamp()}] 📄 告警内容: {self.alarm_message}")

    def on_publish(self, client, userdata, mid):
        print(f"[{self._timestamp()}] 📤 消息发布成功，消息ID: {mid}")

    def _timestamp(self):
        return datetime.now().strftime('%H:%M:%S.%f')[:-3]

    def connect_mqtt(self):
        """连接MQTT服务器"""
        self.client = mqtt.Client(MQTT_CONFIG['client_id'])
        self.client.username_pw_set(MQTT_CONFIG['username'], MQTT_CONFIG['password'])
        self.client.on_connect = self.on_connect
        self.client.on_message = self.on_message
        self.client.on_publish = self.on_publish
        
        print(f"[{self._timestamp()}] 🔌 连接到MQTT服务器...")
        self.client.connect(MQTT_CONFIG['host'], MQTT_CONFIG['port'], MQTT_CONFIG['keepalive'])
        self.client.loop_start()
        time.sleep(2)  # 等待连接建立

    def disconnect_mqtt(self):
        """断开MQTT连接"""
        if self.client:
            self.client.loop_stop()
            self.client.disconnect()

    def send_device_signal(self, trigger_value="0"):
        """发送设备信号"""
        device_message = {
            "I2": trigger_value,
            "timestamp": int(time.time()),
            "device_id": "BDN888",
            "position": "P002LfyBmOut"
        }
        
        action = "触发" if trigger_value == "0" else "解除"
        print(f"[{self._timestamp()}] 📡 发送设备信号({action}): I2={trigger_value}")
        
        self.client.publish(TEST_TOPICS['device_signal'], json.dumps(device_message), qos=1)
        time.sleep(0.5)

    def send_business_data(self, card_type="月租卡", remain_days=15, expire_flag="0"):
        """发送业务数据"""
        current_time = datetime.now()
        
        business_message = {
            "log_original_timestamp": current_time.strftime("%H:%M:%S:%f")[:-3],
            "log_event_type": "出场查询系统返回结果",
            "response_payload": {
                "status": True,
                "code": "0000",
                "msg": "操作成功",
                "confirm": "1",
                "data": {
                    "CarInfo": {
                        "CardId": "52",
                        "CardSnId": "52",
                        "CarNo": "京A12345",
                        "CardType": card_type,
                        "Intime": (current_time - timedelta(hours=2)).strftime("%Y-%m-%d %H:%M:%S"),
                        "PStatus": "离场",
                        "DoorName": "出口闸机",
                        "Balance": 0,
                        "Starttime": "2020-04-01",
                        "Endtime": "2025-12-31",
                        "Name": "张三",
                        "PositionNum": "",
                        "NColor": "蓝色",
                        "ByCarType": "汽车",
                        "BindFeeType": "月租车辆",
                        "UserID": "",
                        "BillId": "",
                        "NoSensePay": "0"
                    },
                    "Charge": {
                        "AllFee": "0",
                        "ParkTime": "2时0分",
                        "FeeTime": "0时0分",
                        "FavMoney": "0",
                        "FavTime": "0",
                        "TotalFee": "0",
                        "CurrFavMoney": "0",
                        "Overtime": "0",
                        "IsFree": "1",
                        "IsTime": "0",
                        "ChargeMoney": "0",
                        "ChargeTime": "",
                        "ChargeType": "",
                        "StartTime": (current_time - timedelta(hours=2)).strftime("%Y-%m-%d %H:%M:%S"),
                        "EndTime": current_time.strftime("%Y-%m-%d %H:%M:%S"),
                        "ChargingCar": "月租车辆",
                        "OrderID": "TEST_ORDER_" + str(int(time.time())),
                        "Expire": expire_flag,
                        "IsAutoCharge": "0",
                        "lastPaidTime": ""
                    }
                }
            }
        }
        
        # 添加顶层字段供规则匹配
        business_message["CardType"] = card_type
        business_message["log_car_no"] = "京A12345"
        business_message["log_user_name"] = "张三"
        business_message["log_end_time"] = "2025-12-31"
        business_message["log_remain_days"] = remain_days
        business_message["Expire"] = expire_flag
        
        print(f"[{self._timestamp()}] 📊 发送业务数据: {card_type}, 剩余{remain_days}天, Expire={expire_flag}")
        
        self.client.publish(TEST_TOPICS['business_data'], json.dumps(business_message), qos=1)
        time.sleep(0.5)

    def send_exclusion_data(self):
        """发送排除数据（过期卡）"""
        exclusion_message = {
            "log_original_timestamp": datetime.now().strftime("%H:%M:%S:%f")[:-3],
            "log_event_type": "出场查询系统返回结果",
            "CardType": "月租卡",
            "log_car_no": "京A12345",
            "log_user_name": "张三",
            "log_end_time": "2023-12-31",  # 已过期
            "log_remain_days": "0",  # 关键：剩余天数为0
            "Expire": "1"  # 过期标志
        }
        
        print(f"[{self._timestamp()}] 🚫 发送排除数据: 过期卡")
        
        self.client.publish(TEST_TOPICS['business_data'], json.dumps(exclusion_message), qos=1)
        time.sleep(0.5)

    def wait_for_result(self, timeout_seconds=25):
        """等待测试结果"""
        print(f"[{self._timestamp()}] ⏳ 等待{timeout_seconds}秒观察结果...")
        
        for i in range(timeout_seconds):
            time.sleep(1)
            if self.alarm_received:
                return True
            if (i + 1) % 5 == 0:
                print(f"[{self._timestamp()}] ⏳ 已等待{i + 1}秒...")
        
        return False

    def scenario_1_normal_alarm(self):
        """场景1：正常告警场景（月租车滞留20秒）"""
        print("\n" + "="*60)
        print("🔥 场景1：正常告警场景（月租车滞留20秒）")
        print("预期：20秒后生成告警")
        print("="*60)
        
        self.alarm_received = False
        self.alarm_message = None
        self.test_start_time = datetime.now()
        
        # 1. 发送设备信号触发
        self.send_device_signal("0")
        
        # 2. 发送业务数据（月租卡未过期）
        self.send_business_data(card_type="月租卡", remain_days=15, expire_flag="0")
        
        # 3. 等待20秒观察是否生成告警
        result = self.wait_for_result(25)
        
        if result:
            print(f"[{self._timestamp()}] ✅ 场景1测试通过：成功生成告警")
            return True
        else:
            print(f"[{self._timestamp()}] ❌ 场景1测试失败：未在预期时间内收到告警")
            return False

    def scenario_2_release_signal(self):
        """场景2：解除信号场景（10秒后离开）"""
        print("\n" + "="*60)
        print("🚫 场景2：解除信号场景（10秒后离开）")
        print("预期：事件取消，无告警")
        print("="*60)
        
        self.alarm_received = False
        self.alarm_message = None
        self.test_start_time = datetime.now()
        
        # 1. 发送设备信号触发
        self.send_device_signal("0")
        
        # 2. 发送业务数据（月租卡未过期）
        self.send_business_data(card_type="月租卡", remain_days=15, expire_flag="0")
        
        # 3. 等待10秒
        print(f"[{self._timestamp()}] ⏳ 等待10秒后发送解除信号...")
        time.sleep(10)
        
        # 4. 发送解除信号
        self.send_device_signal("1")
        
        # 5. 再等待15秒观察是否有告警
        result = self.wait_for_result(15)
        
        if not result:
            print(f"[{self._timestamp()}] ✅ 场景2测试通过：事件被正确取消，无告警")
            return True
        else:
            print(f"[{self._timestamp()}] ❌ 场景2测试失败：收到了不应该出现的告警")
            return False

    def scenario_3_exclusion(self):
        """场景3：排除场景（过期卡）"""
        print("\n" + "="*60)
        print("❌ 场景3：排除场景（过期卡）")
        print("预期：事件被排除，无告警")
        print("="*60)
        
        self.alarm_received = False
        self.alarm_message = None
        self.test_start_time = datetime.now()
        
        # 1. 发送设备信号触发
        self.send_device_signal("0")
        
        # 2. 发送排除数据（过期卡）
        self.send_exclusion_data()
        
        # 3. 等待25秒观察是否有告警
        result = self.wait_for_result(25)
        
        if not result:
            print(f"[{self._timestamp()}] ✅ 场景3测试通过：事件被正确排除，无告警")
            return True
        else:
            print(f"[{self._timestamp()}] ❌ 场景3测试失败：收到了不应该出现的告警")
            return False

def main():
    """主测试函数"""
    print("🎯 EP_V4.1 EV001001 月租车未过期超时滞留出口测试")
    print(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"测试主题配置:")
    print(f"  设备信号: {TEST_TOPICS['device_signal']}")
    print(f"  业务数据: {TEST_TOPICS['business_data']}")
    print(f"  告警输出: {TEST_TOPICS['alarm_output']}")
    
    tester = EV001001Tester()
    
    try:
        # 连接MQTT
        tester.connect_mqtt()
        
        # 询问用户要执行哪个场景
        print("\n请选择要执行的测试场景:")
        print("1. 场景1：正常告警场景（月租车滞留20秒）")
        print("2. 场景2：解除信号场景（10秒后离开）")
        print("3. 场景3：排除场景（过期卡）")
        print("4. 执行所有场景")
        
        choice = input("请输入选择 (1-4): ").strip()
        
        results = []
        
        if choice == "1":
            results.append(tester.scenario_1_normal_alarm())
        elif choice == "2":
            results.append(tester.scenario_2_release_signal())
        elif choice == "3":
            results.append(tester.scenario_3_exclusion())
        elif choice == "4":
            print("\n🚀 执行所有测试场景...")
            results.append(tester.scenario_1_normal_alarm())
            time.sleep(5)  # 场景间隔
            results.append(tester.scenario_2_release_signal())
            time.sleep(5)  # 场景间隔
            results.append(tester.scenario_3_exclusion())
        else:
            print("无效选择，退出测试")
            return
        
        # 输出测试结果
        print("\n" + "="*60)
        print("📊 测试结果汇总")
        print("="*60)
        passed = sum(results)
        total = len(results)
        print(f"通过: {passed}/{total}")
        
        if passed == total:
            print("🎉 所有测试场景都通过了！")
        else:
            print("⚠️  部分测试场景失败，请检查日志")
            
    except KeyboardInterrupt:
        print("\n用户中断测试")
    except Exception as e:
        print(f"测试过程中发生错误: {e}")
    finally:
        tester.disconnect_mqtt()
        print(f"\n测试结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    main()
