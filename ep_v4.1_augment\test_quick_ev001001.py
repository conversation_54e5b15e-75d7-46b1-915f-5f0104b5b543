#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
EP_V4.1 EV001001 快速测试脚本
简化版本，用于快速验证HoldingTimeout修改是否生效
"""

import json
import time
import paho.mqtt.client as mqtt
from datetime import datetime
import threading

# MQTT配置
MQTT_CONFIG = {
    'host': 'mq.bangdouni.com',
    'port': 1883,
    'username': 'bdn_ai_process',
    'password': 'Bdn@2024',
    'keepalive': 60,
    'client_id': 'EP_V4.1_QUICK_TESTER'
}

# 测试主题
DEVICE_TOPIC = 'device/BDN888/event'
BUSINESS_TOPIC = 'ajb/101013/out/P088LfyBmOut/time_log'
ALARM_TOPIC = 'hq/101013/P002LfyBmOut/event'

class QuickTester:
    def __init__(self):
        self.client = None
        self.alarm_received = False
        self.test_start_time = None
        
    def on_connect(self, client, userdata, flags, rc):
        if rc == 0:
            print(f"✅ MQTT连接成功")
            client.subscribe(ALARM_TOPIC, qos=1)
            print(f"📡 监控告警主题: {ALARM_TOPIC}")
        else:
            print(f"❌ MQTT连接失败: {rc}")

    def on_message(self, client, userdata, msg):
        if msg.topic == ALARM_TOPIC:
            self.alarm_received = True
            duration = (datetime.now() - self.test_start_time).total_seconds()
            print(f"🚨 收到告警！耗时: {duration:.1f}秒")
            print(f"📄 内容: {msg.payload.decode('utf-8')}")

    def on_publish(self, client, userdata, mid):
        print(f"📤 消息发送成功")

    def connect(self):
        self.client = mqtt.Client(MQTT_CONFIG['client_id'])
        self.client.username_pw_set(MQTT_CONFIG['username'], MQTT_CONFIG['password'])
        self.client.on_connect = self.on_connect
        self.client.on_message = self.on_message
        self.client.on_publish = self.on_publish
        
        print("🔌 连接MQTT...")
        self.client.connect(MQTT_CONFIG['host'], MQTT_CONFIG['port'], MQTT_CONFIG['keepalive'])
        self.client.loop_start()
        time.sleep(2)

    def disconnect(self):
        if self.client:
            self.client.loop_stop()
            self.client.disconnect()

    def send_trigger(self):
        """发送触发信号"""
        message = {
            "I2": "0",
            "timestamp": int(time.time()),
            "device_id": "BDN888"
        }
        print("📡 发送设备触发信号...")
        self.client.publish(DEVICE_TOPIC, json.dumps(message), qos=1)
        time.sleep(1)

    def send_business_data(self):
        """发送真实格式的未过期月租车业务数据"""
        current_time = datetime.now()

        # 模拟真实的AJB消息格式，参考YAML文件中的结构
        message = {
            "log_original_timestamp": current_time.strftime("%H:%M:%S:%f")[:-3],
            "log_event_type": "出场查询系统返回结果",
            "response_payload": {
                "status": True,
                "code": "0000",
                "msg": "操作成功",
                "confirm": "1",
                "data": {
                    "CarInfo": {
                        "CardId": "52",
                        "CardSnId": "52",
                        "CarNo": "京A12345",
                        "CardType": "月租卡",
                        "Intime": "2025-08-05 06:30:00",
                        "PStatus": "离场",
                        "DoorName": "出口闸机",
                        "Balance": 0,
                        "Starttime": "2025-01-01",
                        "Endtime": "2025-12-31",
                        "Name": "张三",
                        "PositionNum": "",
                        "NColor": "蓝色",
                        "ByCarType": "汽车",
                        "BindFeeType": "月租车辆"
                    },
                    "Charge": {
                        "AllFee": "0",
                        "ParkTime": "2时0分",
                        "FeeTime": "0时0分",
                        "FavMoney": "0",
                        "FavTime": "0",
                        "TotalFee": "0",
                        "Expire": "0",  # 关键：未过期标志
                        "IsAutoCharge": "0",
                        "ChargingCar": "月租车辆"
                    }
                }
            },
            # 添加顶层字段供EV001001规则匹配
            "CardType": "月租卡",
            "log_car_no": "京A12345",
            "log_user_name": "张三",
            "log_end_time": "2025-12-31",
            "log_remain_days": 15,  # 剩余15天，未过期
            "Expire": "0"  # 未过期
        }

        print("📊 发送真实格式的未过期月租车业务数据...")
        print(f"   卡类型: {message['CardType']}")
        print(f"   车牌号: {message['log_car_no']}")
        print(f"   剩余天数: {message['log_remain_days']}天")
        print(f"   过期标志: {message['Expire']} (0=未过期)")

        self.client.publish(BUSINESS_TOPIC, json.dumps(message, ensure_ascii=False), qos=1)
        time.sleep(1)

    def send_release(self):
        """发送解除信号"""
        message = {
            "I2": "1",
            "timestamp": int(time.time()),
            "device_id": "BDN888"
        }
        print("📡 发送设备解除信号...")
        self.client.publish(DEVICE_TOPIC, json.dumps(message), qos=1)

    def test_normal_alarm(self):
        """测试正常告警"""
        print("\n🔥 测试场景：正常告警（20秒超时）")
        print("="*50)
        
        self.alarm_received = False
        self.test_start_time = datetime.now()
        
        # 发送触发和业务数据
        self.send_trigger()
        self.send_business_data()
        
        # 等待25秒观察结果
        print("⏳ 等待25秒观察告警...")
        for i in range(25):
            time.sleep(1)
            if self.alarm_received:
                print(f"✅ 测试通过：在{(datetime.now() - self.test_start_time).total_seconds():.1f}秒时收到告警")
                return True
            if (i + 1) % 5 == 0:
                print(f"⏳ 已等待{i + 1}秒...")
        
        print("❌ 测试失败：未收到预期告警")
        return False

    def test_release_signal(self):
        """测试解除信号"""
        print("\n🚫 测试场景：解除信号（10秒后解除）")
        print("="*50)
        
        self.alarm_received = False
        self.test_start_time = datetime.now()
        
        # 发送触发和业务数据
        self.send_trigger()
        self.send_business_data()
        
        # 等待10秒后发送解除信号
        print("⏳ 等待10秒后发送解除信号...")
        time.sleep(10)
        self.send_release()
        
        # 再等待15秒观察是否有告警
        print("⏳ 等待15秒观察是否有告警...")
        for i in range(15):
            time.sleep(1)
            if self.alarm_received:
                print("❌ 测试失败：收到了不应该出现的告警")
                return False
            if (i + 1) % 5 == 0:
                print(f"⏳ 已等待{i + 1}秒...")
        
        print("✅ 测试通过：解除信号生效，无告警")
        return True

def main():
    print("⚡ EP_V4.1 EV001001 快速测试工具")
    print(f"时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"设备信号: {DEVICE_TOPIC}")
    print(f"业务数据: {BUSINESS_TOPIC}")
    print(f"告警监控: {ALARM_TOPIC}")

    tester = QuickTester()

    try:
        tester.connect()

        # 自动执行正常告警测试（重点测试未过期月租车20秒超时）
        print("\n🚀 自动执行EV001001正常告警测试...")
        print("📋 测试场景：未过期月租车滞留20秒后应生成告警")

        result = tester.test_normal_alarm()

        print(f"\n📊 测试结果:")
        print(f"EV001001正常告警测试: {'✅ 通过' if result else '❌ 失败'}")

        if result:
            print("🎉 HoldingTimeout修改验证成功！")
            print("✅ 事件在20秒超时后正确触发告警")
        else:
            print("⚠️  测试失败，可能的原因：")
            print("   - EP_V4.1服务未运行")
            print("   - 规则配置不匹配")
            print("   - HoldingTimeout修改未生效")

    except KeyboardInterrupt:
        print("\n用户中断测试")
    except Exception as e:
        print(f"测试出错: {e}")
    finally:
        tester.disconnect()

if __name__ == "__main__":
    main()
