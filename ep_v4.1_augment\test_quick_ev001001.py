#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
EP_V4.1 EV001001 快速测试脚本
简化版本，用于快速验证HoldingTimeout修改是否生效
"""

import json
import time
import paho.mqtt.client as mqtt
from datetime import datetime
import threading

# MQTT配置
MQTT_CONFIG = {
    'host': 'mq.bangdouni.com',
    'port': 1883,
    'username': 'bdn_ai_process',
    'password': 'Bdn@2024',
    'keepalive': 60,
    'client_id': 'EP_V4.1_QUICK_TESTER'
}

# 测试主题
DEVICE_TOPIC = 'device/BDN888/event'
BUSINESS_TOPIC = 'ajb/101013/out/P088LfyBmOut/time_log'
ALARM_TOPIC = 'hq/101013/P002LfyBmOut/event'

class QuickTester:
    def __init__(self):
        self.client = None
        self.alarm_received = False
        self.test_start_time = None
        
    def on_connect(self, client, userdata, flags, rc):
        if rc == 0:
            print(f"✅ MQTT连接成功")
            client.subscribe(ALARM_TOPIC, qos=1)
            print(f"📡 监控告警主题: {ALARM_TOPIC}")
        else:
            print(f"❌ MQTT连接失败: {rc}")

    def on_message(self, client, userdata, msg):
        if msg.topic == ALARM_TOPIC:
            self.alarm_received = True
            duration = (datetime.now() - self.test_start_time).total_seconds()
            print(f"🚨 收到告警！耗时: {duration:.1f}秒")
            print(f"📄 内容: {msg.payload.decode('utf-8')}")

    def on_publish(self, client, userdata, mid):
        print(f"📤 消息发送成功")

    def connect(self):
        self.client = mqtt.Client(MQTT_CONFIG['client_id'])
        self.client.username_pw_set(MQTT_CONFIG['username'], MQTT_CONFIG['password'])
        self.client.on_connect = self.on_connect
        self.client.on_message = self.on_message
        self.client.on_publish = self.on_publish
        
        print("🔌 连接MQTT...")
        self.client.connect(MQTT_CONFIG['host'], MQTT_CONFIG['port'], MQTT_CONFIG['keepalive'])
        self.client.loop_start()
        time.sleep(2)

    def disconnect(self):
        if self.client:
            self.client.loop_stop()
            self.client.disconnect()

    def send_trigger(self):
        """发送触发信号"""
        message = {
            "I2": "0",
            "timestamp": int(time.time()),
            "device_id": "BDN888"
        }
        print("📡 发送设备触发信号...")
        self.client.publish(DEVICE_TOPIC, json.dumps(message), qos=1)
        time.sleep(1)

    def send_business_data(self):
        """发送业务数据"""
        message = {
            "CardType": "月租卡",
            "log_car_no": "京A12345",
            "log_user_name": "张三",
            "log_end_time": "2025-12-31",
            "log_remain_days": 15,
            "Expire": "0"
        }
        print("📊 发送业务数据...")
        self.client.publish(BUSINESS_TOPIC, json.dumps(message), qos=1)
        time.sleep(1)

    def send_release(self):
        """发送解除信号"""
        message = {
            "I2": "1",
            "timestamp": int(time.time()),
            "device_id": "BDN888"
        }
        print("📡 发送设备解除信号...")
        self.client.publish(DEVICE_TOPIC, json.dumps(message), qos=1)

    def test_normal_alarm(self):
        """测试正常告警"""
        print("\n🔥 测试场景：正常告警（20秒超时）")
        print("="*50)
        
        self.alarm_received = False
        self.test_start_time = datetime.now()
        
        # 发送触发和业务数据
        self.send_trigger()
        self.send_business_data()
        
        # 等待25秒观察结果
        print("⏳ 等待25秒观察告警...")
        for i in range(25):
            time.sleep(1)
            if self.alarm_received:
                print(f"✅ 测试通过：在{(datetime.now() - self.test_start_time).total_seconds():.1f}秒时收到告警")
                return True
            if (i + 1) % 5 == 0:
                print(f"⏳ 已等待{i + 1}秒...")
        
        print("❌ 测试失败：未收到预期告警")
        return False

    def test_release_signal(self):
        """测试解除信号"""
        print("\n🚫 测试场景：解除信号（10秒后解除）")
        print("="*50)
        
        self.alarm_received = False
        self.test_start_time = datetime.now()
        
        # 发送触发和业务数据
        self.send_trigger()
        self.send_business_data()
        
        # 等待10秒后发送解除信号
        print("⏳ 等待10秒后发送解除信号...")
        time.sleep(10)
        self.send_release()
        
        # 再等待15秒观察是否有告警
        print("⏳ 等待15秒观察是否有告警...")
        for i in range(15):
            time.sleep(1)
            if self.alarm_received:
                print("❌ 测试失败：收到了不应该出现的告警")
                return False
            if (i + 1) % 5 == 0:
                print(f"⏳ 已等待{i + 1}秒...")
        
        print("✅ 测试通过：解除信号生效，无告警")
        return True

def main():
    print("⚡ EP_V4.1 EV001001 快速测试工具")
    print(f"时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"设备信号: {DEVICE_TOPIC}")
    print(f"业务数据: {BUSINESS_TOPIC}")
    print(f"告警监控: {ALARM_TOPIC}")
    
    tester = QuickTester()
    
    try:
        tester.connect()
        
        print("\n请选择测试场景:")
        print("1. 正常告警测试（20秒超时）")
        print("2. 解除信号测试（10秒后解除）")
        print("3. 执行两个测试")
        
        choice = input("请选择 (1-3): ").strip()
        
        if choice == "1":
            tester.test_normal_alarm()
        elif choice == "2":
            tester.test_release_signal()
        elif choice == "3":
            print("🚀 执行完整测试...")
            result1 = tester.test_normal_alarm()
            time.sleep(3)
            result2 = tester.test_release_signal()
            
            print(f"\n📊 测试结果:")
            print(f"正常告警测试: {'✅ 通过' if result1 else '❌ 失败'}")
            print(f"解除信号测试: {'✅ 通过' if result2 else '❌ 失败'}")
        else:
            print("无效选择")
            
    except KeyboardInterrupt:
        print("\n用户中断测试")
    except Exception as e:
        print(f"测试出错: {e}")
    finally:
        tester.disconnect()

if __name__ == "__main__":
    main()
