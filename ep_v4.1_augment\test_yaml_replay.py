#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
EP_V4.1 基于YAML文件的事件复现脚本
根据EV001001Replay.yaml文件复现真实事件场景
调整为使用测试主题避免干扰
"""

import json
import time
import yaml
import paho.mqtt.client as mqtt
from datetime import datetime
import sys
import os

# MQTT配置 - 与appsettings.yaml保持一致
MQTT_CONFIG = {
    'host': 'mq.bangdouni.com',
    'port': 1883,
    'username': 'bdn_ai_process',
    'password': 'Bdn@2024',
    'keepalive': 60,
    'client_id': 'EP_V4.1_YAML_REPLAYER'
}

# 主题映射：将YAML中的真实主题映射到测试主题
TOPIC_MAPPING = {
    'device/BDN861290073715232/event': 'device/BDN888/event',
    'ajb/101013/out/P002LfyBmOut/time_log': 'ajb/101013/out/P088LfyBmOut/time_log'
}

# 告警监控主题
ALARM_TOPIC = 'hq/101013/P002LfyBmOut/event'

class YAMLReplayer:
    def __init__(self, yaml_file_path):
        self.yaml_file_path = yaml_file_path
        self.client = None
        self.events = []
        self.metadata = {}
        self.alarm_received = False
        self.alarm_message = None
        self.replay_start_time = None
        
    def load_yaml_file(self):
        """加载YAML文件"""
        try:
            with open(self.yaml_file_path, 'r', encoding='utf-8') as f:
                data = yaml.safe_load(f)
                self.metadata = data.get('metadata', {})
                self.events = data.get('events', [])
                
            print(f"✅ 成功加载YAML文件: {self.yaml_file_path}")
            print(f"📋 场景ID: {self.metadata.get('scene_id', 'Unknown')}")
            print(f"📋 事件ID: {self.metadata.get('event_id', 'Unknown')}")
            print(f"📋 描述: {self.metadata.get('description', 'No description')}")
            print(f"📋 事件数量: {len(self.events)}")
            print(f"📋 原始持续时间: {self.metadata.get('original_duration_sec', 'Unknown')}秒")
            
            return True
        except Exception as e:
            print(f"❌ 加载YAML文件失败: {e}")
            return False
    
    def on_connect(self, client, userdata, flags, rc):
        if rc == 0:
            print(f"[{self._timestamp()}] ✅ MQTT连接成功")
            # 订阅告警主题以监控结果
            client.subscribe(ALARM_TOPIC, qos=1)
            print(f"[{self._timestamp()}] 📡 已订阅告警主题: {ALARM_TOPIC}")
        else:
            print(f"[{self._timestamp()}] ❌ MQTT连接失败，错误码: {rc}")

    def on_message(self, client, userdata, msg):
        """接收到告警消息的回调"""
        if msg.topic == ALARM_TOPIC:
            self.alarm_received = True
            self.alarm_message = msg.payload.decode('utf-8')
            alarm_time = datetime.now()
            if self.replay_start_time:
                duration = (alarm_time - self.replay_start_time).total_seconds()
                print(f"[{self._timestamp()}] 🚨 收到告警消息！复现耗时: {duration:.1f}秒")
            else:
                print(f"[{self._timestamp()}] 🚨 收到告警消息！")
            print(f"[{self._timestamp()}] 📄 告警内容: {self.alarm_message}")

    def on_publish(self, client, userdata, mid):
        print(f"[{self._timestamp()}] 📤 消息发布成功，消息ID: {mid}")

    def _timestamp(self):
        return datetime.now().strftime('%H:%M:%S.%f')[:-3]

    def connect_mqtt(self):
        """连接MQTT服务器"""
        self.client = mqtt.Client(MQTT_CONFIG['client_id'])
        self.client.username_pw_set(MQTT_CONFIG['username'], MQTT_CONFIG['password'])
        self.client.on_connect = self.on_connect
        self.client.on_message = self.on_message
        self.client.on_publish = self.on_publish
        
        print(f"[{self._timestamp()}] 🔌 连接到MQTT服务器...")
        self.client.connect(MQTT_CONFIG['host'], MQTT_CONFIG['port'], MQTT_CONFIG['keepalive'])
        self.client.loop_start()
        time.sleep(2)  # 等待连接建立

    def disconnect_mqtt(self):
        """断开MQTT连接"""
        if self.client:
            self.client.loop_stop()
            self.client.disconnect()

    def map_topic(self, original_topic):
        """映射主题到测试主题"""
        return TOPIC_MAPPING.get(original_topic, original_topic)

    def enhance_payload(self, original_payload, topic):
        """增强payload，添加EV001001需要的字段"""
        try:
            payload_dict = json.loads(original_payload)
            
            # 如果是AJB消息，确保包含EV001001需要的顶层字段
            if 'ajb' in topic and 'time_log' in topic:
                # 从response_payload中提取信息到顶层
                if 'response_payload' in payload_dict:
                    response_data = payload_dict['response_payload']
                    if isinstance(response_data, dict) and 'data' in response_data:
                        car_info = response_data['data'].get('CarInfo', {})
                        charge_info = response_data['data'].get('Charge', {})
                        
                        # 添加EV001001规则需要的顶层字段
                        payload_dict['CardType'] = car_info.get('CardType', '储值卡')
                        payload_dict['log_car_no'] = car_info.get('CarNo', '')
                        payload_dict['log_user_name'] = car_info.get('Name', '')
                        payload_dict['log_end_time'] = car_info.get('Endtime', '')
                        payload_dict['Expire'] = charge_info.get('Expire', '0')
                        
                        # 计算剩余天数（简化处理）
                        expire_flag = charge_info.get('Expire', '0')
                        if expire_flag == '0':
                            payload_dict['log_remain_days'] = 15  # 未过期，设置为15天
                        else:
                            payload_dict['log_remain_days'] = 0   # 已过期
            
            return json.dumps(payload_dict, ensure_ascii=False)
        except:
            # 如果解析失败，返回原始payload
            return original_payload

    def replay_events(self):
        """复现事件序列"""
        if not self.events:
            print("❌ 没有事件可以复现")
            return False
            
        print(f"\n🚀 开始复现事件序列...")
        print(f"📊 总共 {len(self.events)} 个事件")
        
        # 询问用户是否要调整时间间隔
        print(f"\n原始事件持续时间: {self.metadata.get('original_duration_sec', 'Unknown')}秒")
        speed_factor = input("请输入时间加速倍数 (1=原速度, 2=2倍速, 0.5=半速, 回车=原速度): ").strip()
        
        try:
            speed_factor = float(speed_factor) if speed_factor else 1.0
        except:
            speed_factor = 1.0
            
        print(f"⚡ 使用 {speed_factor}x 速度复现")
        
        # 重置告警状态
        self.alarm_received = False
        self.alarm_message = None
        self.replay_start_time = datetime.now()
        
        last_offset = 0
        
        for i, event in enumerate(self.events):
            offset_ms = event.get('offset_ms', 0)
            original_topic = event.get('topic', '')
            original_payload = event.get('payload', '{}')
            
            # 计算等待时间
            wait_time = (offset_ms - last_offset) / 1000.0 / speed_factor
            
            if wait_time > 0:
                print(f"[{self._timestamp()}] ⏳ 等待 {wait_time:.2f}秒...")
                time.sleep(wait_time)
            
            # 映射主题
            mapped_topic = self.map_topic(original_topic)
            
            # 增强payload
            enhanced_payload = self.enhance_payload(original_payload, mapped_topic)
            
            # 发布消息
            print(f"[{self._timestamp()}] 📤 事件 {i+1}/{len(self.events)}: {mapped_topic}")
            if mapped_topic != original_topic:
                print(f"[{self._timestamp()}] 🔄 主题映射: {original_topic} → {mapped_topic}")
            
            self.client.publish(mapped_topic, enhanced_payload, qos=1)
            
            last_offset = offset_ms
            
            # 短暂延迟确保消息发送
            time.sleep(0.1)
        
        print(f"[{self._timestamp()}] ✅ 所有事件已发送完成")
        
        # 等待额外时间观察结果
        additional_wait = 30 / speed_factor  # 额外等待30秒（按速度调整）
        print(f"[{self._timestamp()}] ⏳ 等待 {additional_wait:.1f}秒 观察告警结果...")
        
        for i in range(int(additional_wait)):
            time.sleep(1)
            if self.alarm_received:
                break
            if (i + 1) % 5 == 0:
                print(f"[{self._timestamp()}] ⏳ 已等待{i + 1}秒...")
        
        return True

    def show_event_summary(self):
        """显示事件摘要"""
        print("\n📋 事件序列摘要:")
        print("-" * 60)
        
        for i, event in enumerate(self.events):
            offset_ms = event.get('offset_ms', 0)
            original_topic = event.get('topic', '')
            mapped_topic = self.map_topic(original_topic)
            
            offset_sec = offset_ms / 1000.0
            
            print(f"{i+1:2d}. [{offset_sec:6.1f}s] {mapped_topic}")
            
            # 显示关键信息
            try:
                payload = json.loads(event.get('payload', '{}'))
                if 'I2' in payload:
                    i2_value = payload['I2'].get('value', payload['I2']) if isinstance(payload['I2'], dict) else payload['I2']
                    action = "触发" if i2_value == "0" else "解除"
                    print(f"     🔧 设备信号: I2={i2_value} ({action})")
                elif 'log_event_type' in payload:
                    print(f"     📊 业务事件: {payload['log_event_type']}")
            except:
                pass
        
        print("-" * 60)

def main():
    """主函数"""
    print("🎬 EP_V4.1 YAML事件复现工具")
    print(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 检查YAML文件路径
    yaml_file = r"utils\scenes\EV001001-20250805075258707\EV001001Replay.yaml"
    
    if len(sys.argv) > 1:
        yaml_file = sys.argv[1]
    
    if not os.path.exists(yaml_file):
        print(f"❌ YAML文件不存在: {yaml_file}")
        print("请确保文件路径正确，或者作为命令行参数提供")
        return
    
    replayer = YAMLReplayer(yaml_file)
    
    try:
        # 加载YAML文件
        if not replayer.load_yaml_file():
            return
        
        # 显示事件摘要
        replayer.show_event_summary()
        
        # 询问用户是否继续
        confirm = input(f"\n是否开始复现事件? (y/N): ").strip().lower()
        if confirm not in ['y', 'yes']:
            print("用户取消复现")
            return
        
        # 连接MQTT
        replayer.connect_mqtt()
        
        # 复现事件
        success = replayer.replay_events()
        
        # 输出结果
        print("\n" + "="*60)
        print("📊 复现结果")
        print("="*60)
        
        if replayer.alarm_received:
            print("✅ 成功触发告警")
            print(f"📄 告警消息: {replayer.alarm_message}")
        else:
            print("⚠️  未收到告警消息")
            print("可能原因:")
            print("  - 事件处理器未运行")
            print("  - 规则配置不匹配")
            print("  - 超时时间未到")
            
    except KeyboardInterrupt:
        print("\n用户中断复现")
    except Exception as e:
        print(f"复现过程中发生错误: {e}")
    finally:
        replayer.disconnect_mqtt()
        print(f"\n复现结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    main()
