using System;
using System.IO;
using System.Threading.Tasks;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Xunit;
using Xunit.Abstractions;
using MQTTnet;
using MQTTnet.Client;
using System.Text;
using System.Text.Json;

namespace EventProcessor.IntegrationTests
{
    /// <summary>
    /// 快递车违规检测集成测试
    /// 测试EP + AIProcessor完整链路：设备信号 -> AI分析 -> 告警输出
    /// </summary>
    public class DeliveryVehicleIntegrationTest : IDisposable
    {
        private readonly ITestOutputHelper _output;
        private readonly string _testImagePath;
        private readonly string _configPath;
        private IMqttClient? _mqttClient;
        private IHost? _eventProcessorHost;
        private readonly ILoggerFactory _loggerFactory;

        public DeliveryVehicleIntegrationTest(ITestOutputHelper output)
        {
            _output = output;
            _testImagePath = Path.Combine(GetProjectRoot(), "test_image.jpg");
            _configPath = Path.Combine(GetProjectRoot(), "ep_v4.1_augment", "config", "delivery-vehicle-test-config.yaml");
            
            _loggerFactory = LoggerFactory.Create(builder =>
                builder.AddConsole().SetMinimumLevel(LogLevel.Information));
        }

        [Fact]
        public async Task TestDeliveryVehicleDetection_ShouldTriggerAlarm()
        {
            // Arrange
            _output.WriteLine($"开始快递车检测集成测试");
            _output.WriteLine($"测试图片路径: {_testImagePath}");
            _output.WriteLine($"配置文件路径: {_configPath}");

            Assert.True(File.Exists(_testImagePath), "测试图片文件不存在");
            Assert.True(File.Exists(_configPath), "配置文件不存在");

            // 初始化MQTT客户端
            await InitializeMqttClientAsync();

            var alarmReceived = false;
            var alarmMessage = string.Empty;

            // 订阅告警主题
            await _mqttClient!.SubscribeAsync("hq/101013/P001LfyBmIn/alarm");
            
            _mqttClient.ApplicationMessageReceivedAsync += args =>
            {
                if (args.ApplicationMessage.Topic == "hq/101013/P001LfyBmIn/alarm")
                {
                    alarmMessage = Encoding.UTF8.GetString(args.ApplicationMessage.PayloadSegment);
                    alarmReceived = true;
                    _output.WriteLine($"接收到告警消息: {alarmMessage}");
                }
                return Task.CompletedTask;
            };

            // Act - 发送设备触发信号
            await SendDeviceSignalAsync();
            _output.WriteLine("已发送设备触发信号");

            // 等待AI处理图片
            await SendAIAnalysisRequestAsync();
            _output.WriteLine("已发送AI分析请求");

            // 模拟AI分析结果
            await SendAIAnalysisResultAsync();
            _output.WriteLine("已发送AI分析结果");

            // 等待告警生成
            var timeout = TimeSpan.FromSeconds(30);
            var startTime = DateTime.UtcNow;
            
            while (!alarmReceived && DateTime.UtcNow - startTime < timeout)
            {
                await Task.Delay(500);
            }

            // Assert
            Assert.True(alarmReceived, "未收到预期的告警消息");
            Assert.False(string.IsNullOrEmpty(alarmMessage), "告警消息为空");
            
            // 验证告警消息内容
            var alarmJson = JsonDocument.Parse(alarmMessage);
            var root = alarmJson.RootElement;
            
            Assert.True(root.TryGetProperty("AlarmId", out var alarmId), "告警消息缺少AlarmId字段");
            Assert.Equal("EV001010", alarmId.GetString());
            
            Assert.True(root.TryGetProperty("VehicleType", out var vehicleType), "告警消息缺少VehicleType字段");
            Assert.Contains("快递", vehicleType.GetString() ?? "");
            
            _output.WriteLine($"✅ 集成测试通过 - 成功检测快递车违规并发出告警");
        }

        [Fact]
        public async Task TestAIProcessorImageAnalysis_ShouldReturnDeliveryVehicleDetection()
        {
            // Arrange
            _output.WriteLine("开始AI图片分析测试");
            
            await InitializeMqttClientAsync();
            
            var aiResponseReceived = false;
            var aiResponse = string.Empty;

            // 订阅AI响应主题
            await _mqttClient!.SubscribeAsync("ai/101013/P001LfyBmIn/response");
            
            _mqttClient.ApplicationMessageReceivedAsync += args =>
            {
                if (args.ApplicationMessage.Topic.StartsWith("ai/101013/P001LfyBmIn/response"))
                {
                    aiResponse = Encoding.UTF8.GetString(args.ApplicationMessage.PayloadSegment);
                    aiResponseReceived = true;
                    _output.WriteLine($"收到AI响应: {aiResponse}");
                }
                return Task.CompletedTask;
            };

            // Act - 发送AI分析请求
            var aiRequest = new
            {
                requestId = Guid.NewGuid().ToString(),
                imageUrl = $"file://{_testImagePath}",
                prompt = "分析图片中是否有快递车、货车等商用车辆违规停放在住宅小区内。如果发现违规停放的商用车辆，请回复'违规'，并描述车辆类型和违规情况。否则回复'正常'。",
                timestamp = DateTime.UtcNow.ToString("yyyy-MM-dd HH:mm:ss")
            };

            var requestJson = JsonSerializer.Serialize(aiRequest);
            var message = new MqttApplicationMessageBuilder()
                .WithTopic("ai/101013/P001LfyBmIn/request")
                .WithPayload(requestJson)
                .WithQualityOfServiceLevel(MQTTnet.Protocol.MqttQualityOfServiceLevel.AtLeastOnce)
                .Build();

            await _mqttClient.PublishAsync(message);
            _output.WriteLine("已发送AI分析请求");

            // 等待AI响应
            var timeout = TimeSpan.FromSeconds(60);
            var startTime = DateTime.UtcNow;
            
            while (!aiResponseReceived && DateTime.UtcNow - startTime < timeout)
            {
                await Task.Delay(1000);
            }

            // Assert
            Assert.True(aiResponseReceived, "未收到AI分析响应");
            Assert.False(string.IsNullOrEmpty(aiResponse), "AI响应为空");
            
            // 验证AI响应内容
            var responseJson = JsonDocument.Parse(aiResponse);
            var root = responseJson.RootElement;
            
            Assert.True(root.TryGetProperty("result", out var result), "AI响应缺少result字段");
            var resultText = result.GetString() ?? "";
            
            // 期望AI能识别出快递车或商用车辆
            Assert.True(resultText.Contains("违规") || resultText.Contains("快递") || resultText.Contains("商用"), 
                $"AI未能正确识别快递车，响应内容: {resultText}");
            
            _output.WriteLine($"✅ AI分析测试通过 - 成功识别快递车违规");
        }

        private async Task InitializeMqttClientAsync()
        {
            var factory = new MqttFactory();
            _mqttClient = factory.CreateMqttClient();

            var options = new MqttClientOptionsBuilder()
                .WithTcpServer("mq.bangdouni.com", 1883)
                .WithCredentials("bdn_event_processor", "Bdn@2024")
                .WithClientId($"DeliveryTest_{Guid.NewGuid():N}")
                .WithCleanSession(true)
                .Build();

            await _mqttClient.ConnectAsync(options);
            _output.WriteLine("MQTT客户端连接成功");
        }

        private async Task SendDeviceSignalAsync()
        {
            var deviceSignal = new
            {
                eventId = "EV001010",
                commId = "101013",
                positionId = "P001LfyBmIn",
                deviceId = "camera_001",
                signal = "motion_detected",
                value = "1",
                timestamp = DateTime.UtcNow.ToString("yyyy-MM-dd HH:mm:ss"),
                imageUrl = $"file://{_testImagePath}"
            };

            var signalJson = JsonSerializer.Serialize(deviceSignal);
            var message = new MqttApplicationMessageBuilder()
                .WithTopic("device/101013/P001LfyBmIn/event")
                .WithPayload(signalJson)
                .WithQualityOfServiceLevel(MQTTnet.Protocol.MqttQualityOfServiceLevel.AtLeastOnce)
                .Build();

            await _mqttClient!.PublishAsync(message);
        }

        private async Task SendAIAnalysisRequestAsync()
        {
            var aiRequest = new
            {
                requestId = Guid.NewGuid().ToString(),
                eventId = "EV001010",
                imageUrl = $"file://{_testImagePath}",
                prompt = "分析图片中是否有快递车、货车等商用车辆违规停放在住宅小区内。如果发现违规停放的商用车辆，请回复'违规'，并描述车辆类型和违规情况。否则回复'正常'。",
                timestamp = DateTime.UtcNow.ToString("yyyy-MM-dd HH:mm:ss")
            };

            var requestJson = JsonSerializer.Serialize(aiRequest);
            var message = new MqttApplicationMessageBuilder()
                .WithTopic("ai/101013/P001LfyBmIn/request")
                .WithPayload(requestJson)
                .WithQualityOfServiceLevel(MQTTnet.Protocol.MqttQualityOfServiceLevel.AtLeastOnce)
                .Build();

            await _mqttClient!.PublishAsync(message);
        }

        private async Task SendAIAnalysisResultAsync()
        {
            // 模拟AI分析结果（基于test_image.jpg中的快递车）
            var aiResult = new
            {
                requestId = Guid.NewGuid().ToString(),
                eventId = "EV001010",
                result = "违规",
                confidence = 0.85,
                description = "检测到一辆蓝白色快递车违规停放在住宅小区内的停车位上。该车辆疑似为快递或货运车辆，不符合住宅小区停车规定。",
                detectedObjects = new[]
                {
                    new { type = "delivery_vehicle", confidence = 0.85, location = "center" },
                    new { type = "commercial_vehicle", confidence = 0.80, location = "center" }
                },
                violation_level = "MEDIUM",
                detected_vehicle_type = "快递车",
                violation_type = "商用车违规停放",
                timestamp = DateTime.UtcNow.ToString("yyyy-MM-dd HH:mm:ss")
            };

            var resultJson = JsonSerializer.Serialize(aiResult);
            var message = new MqttApplicationMessageBuilder()
                .WithTopic("ai/101013/P001LfyBmIn/response")
                .WithPayload(resultJson)
                .WithQualityOfServiceLevel(MQTTnet.Protocol.MqttQualityOfServiceLevel.AtLeastOnce)
                .Build();

            await _mqttClient!.PublishAsync(message);
        }

        private static string GetProjectRoot()
        {
            var currentDir = Directory.GetCurrentDirectory();
            var parent = Directory.GetParent(currentDir);
            
            while (parent != null)
            {
                if (File.Exists(Path.Combine(parent.FullName, "test_image.jpg")))
                {
                    return parent.FullName;
                }
                parent = parent.Parent;
            }
            
            throw new DirectoryNotFoundException("无法找到项目根目录");
        }

        public void Dispose()
        {
            _mqttClient?.DisconnectAsync().GetAwaiter().GetResult();
            _mqttClient?.Dispose();
            _eventProcessorHost?.StopAsync().GetAwaiter().GetResult();
            _eventProcessorHost?.Dispose();
            _loggerFactory.Dispose();
        }
    }
}