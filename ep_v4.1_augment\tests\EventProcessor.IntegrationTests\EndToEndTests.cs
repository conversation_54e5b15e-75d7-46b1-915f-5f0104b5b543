using EventProcessor.Core.Models;
using EventProcessor.IntegrationTests.TestFixtures;
using EventProcessor.IntegrationTests.TestServices;
using FluentAssertions;
using Microsoft.Extensions.DependencyInjection;
using System.Text.Json;
using Xunit;

namespace EventProcessor.IntegrationTests;

/// <summary>
/// 端到端集成测试
/// </summary>
public class EndToEndTests : IClassFixture<EventProcessorTestFixture>
{
    private readonly EventProcessorTestFixture _fixture;

    public EndToEndTests(EventProcessorTestFixture fixture)
    {
        _fixture = fixture;
    }

    [Fact]
    public async Task BusinessOnlyFlow_NormalSequence_ShouldGenerateAlarm()
    {
        // Arrange
        var testMqttService = (TestMqttService)_fixture.MqttService;
        testMqttService.ClearPublishedMessages();

        var aggregator = await _fixture.CreateTestAggregatorAsync();
        var alarmGenerated = false;
        var alarmMessage = default(AlarmMessage);

        aggregator.OnAlarmGenerated += (sender, args) =>
        {
            alarmGenerated = true;
            alarmMessage = args.AlarmMessage;
        };

        // Act - 发送设备信号
        var deviceMessage = _fixture.CreateDeviceSignalMessage();
        await aggregator.ProcessMessage(deviceMessage);

        // 验证状态转换
        aggregator.CurrentState.Should().Be(EventProcessingState.Collecting);

        // Act - 发送业务数据（满足告警条件）
        var businessMessage = _fixture.CreateBusinessDataMessage(new Dictionary<string, object>
        {
            ["amount"] = 150 // 大于100，满足业务规则
        });
        await aggregator.ProcessMessage(businessMessage);

        // 等待告警静默期结束
        await Task.Delay(TimeSpan.FromSeconds(1.5));

        // Assert
        aggregator.CurrentState.Should().Be(EventProcessingState.Alarmed);
        alarmGenerated.Should().BeTrue();
        alarmMessage.Should().NotBeNull();
        alarmMessage!.EventId.Should().Be("EV001001");
        alarmMessage.AlarmType.Should().Be("Alarm");
        alarmMessage.Fields.Should().ContainKey("详情");
        alarmMessage.Fields["详情"].Should().Be("金额: 150");

        // 验证MQTT告警消息已发布
        var publishedMessages = testMqttService.GetPublishedMessagesForTopic($"alarm/{_fixture.ConfigurationService.CurrentConfiguration.CommId}/{_fixture.ConfigurationService.CurrentConfiguration.PositionId}/event");
        publishedMessages.Should().HaveCount(1);
    }

    [Fact]
    public async Task BusinessOnlyFlow_ExclusionRule_ShouldNotGenerateAlarm()
    {
        // Arrange
        var testMqttService = (TestMqttService)_fixture.MqttService;
        testMqttService.ClearPublishedMessages();

        var aggregator = await _fixture.CreateTestAggregatorAsync();
        var alarmGenerated = false;

        aggregator.OnAlarmGenerated += (sender, args) =>
        {
            alarmGenerated = true;
        };

        // Act - 发送设备信号
        var deviceMessage = _fixture.CreateDeviceSignalMessage();
        await aggregator.ProcessMessage(deviceMessage);

        // Act - 发送排除数据
        var exclusionMessage = _fixture.CreateExclusionDataMessage(new Dictionary<string, object>
        {
            ["excluded"] = "true" // 满足排除条件
        });
        await aggregator.ProcessMessage(exclusionMessage);

        // Act - 发送业务数据（本来会满足告警条件）
        var businessMessage = _fixture.CreateBusinessDataMessage(new Dictionary<string, object>
        {
            ["amount"] = 150
        });
        await aggregator.ProcessMessage(businessMessage);

        // 等待处理完成
        await Task.Delay(TimeSpan.FromSeconds(0.5));

        // Assert
        aggregator.CurrentState.Should().Be(EventProcessingState.Excluded);
        alarmGenerated.Should().BeFalse();

        // 验证没有发布告警消息
        var publishedMessages = testMqttService.GetPublishedMessagesForTopic($"alarm/{_fixture.ConfigurationService.CurrentConfiguration.CommId}/{_fixture.ConfigurationService.CurrentConfiguration.PositionId}/event");
        publishedMessages.Should().BeEmpty();
    }

    [Fact]
    public async Task BusinessOnlyFlow_LateExclusion_ShouldCancelAlarm()
    {
        // Arrange
        var testMqttService = (TestMqttService)_fixture.MqttService;
        testMqttService.ClearPublishedMessages();

        var aggregator = await _fixture.CreateTestAggregatorAsync();
        var alarmGenerated = false;
        var cancellationGenerated = false;

        aggregator.OnAlarmGenerated += (sender, args) =>
        {
            if (args.AlarmType == "Alarm")
                alarmGenerated = true;
            else if (args.AlarmType == "Cancellation")
                cancellationGenerated = true;
        };

        // Act - 正常流程生成告警
        var deviceMessage = _fixture.CreateDeviceSignalMessage();
        await aggregator.ProcessMessage(deviceMessage);

        var businessMessage = _fixture.CreateBusinessDataMessage(new Dictionary<string, object>
        {
            ["amount"] = 150
        });
        await aggregator.ProcessMessage(businessMessage);

        // 等待告警生成
        await Task.Delay(TimeSpan.FromSeconds(1.5));
        alarmGenerated.Should().BeTrue();
        aggregator.CurrentState.Should().Be(EventProcessingState.Alarmed);

        // Act - 发送迟到的排除数据
        var exclusionMessage = _fixture.CreateExclusionDataMessage(new Dictionary<string, object>
        {
            ["excluded"] = "true"
        });
        await aggregator.ProcessMessage(exclusionMessage);

        // 等待处理完成
        await Task.Delay(TimeSpan.FromSeconds(0.5));

        // Assert
        aggregator.CurrentState.Should().Be(EventProcessingState.Excluded);
        cancellationGenerated.Should().BeTrue();

        // 验证发布了告警和撤销消息
        var alarmMessages = testMqttService.GetPublishedMessagesForTopic($"alarm/{_fixture.ConfigurationService.CurrentConfiguration.CommId}/{_fixture.ConfigurationService.CurrentConfiguration.PositionId}/event");
        var cancellationMessages = testMqttService.GetPublishedMessagesForTopic($"alarm/{_fixture.ConfigurationService.CurrentConfiguration.CommId}/{_fixture.ConfigurationService.CurrentConfiguration.PositionId}/cancellation");
        
        alarmMessages.Should().HaveCount(1);
        cancellationMessages.Should().HaveCount(1);
    }

    [Fact]
    public async Task BusinessOnlyFlow_OutOfOrderMessages_ShouldHandleCorrectly()
    {
        // Arrange
        var aggregator = await _fixture.CreateTestAggregatorAsync();
        var alarmGenerated = false;

        aggregator.OnAlarmGenerated += (sender, args) =>
        {
            alarmGenerated = true;
        };

        // Act - 先发送业务数据（乱序）
        var businessMessage = _fixture.CreateBusinessDataMessage(new Dictionary<string, object>
        {
            ["amount"] = 150
        });
        await aggregator.ProcessMessage(businessMessage);

        // 验证状态仍为初始化（等待设备信号）
        aggregator.CurrentState.Should().Be(EventProcessingState.Initializing);

        // Act - 后发送设备信号
        var deviceMessage = _fixture.CreateDeviceSignalMessage();
        await aggregator.ProcessMessage(deviceMessage);

        // 等待告警静默期结束
        await Task.Delay(TimeSpan.FromSeconds(1.5));

        // Assert - 应该正常生成告警
        aggregator.CurrentState.Should().Be(EventProcessingState.Alarmed);
        alarmGenerated.Should().BeTrue();
    }

    [Fact]
    public async Task BusinessOnlyFlow_InsufficientData_ShouldNotGenerateAlarm()
    {
        // Arrange
        var aggregator = await _fixture.CreateTestAggregatorAsync();
        var alarmGenerated = false;

        aggregator.OnAlarmGenerated += (sender, args) =>
        {
            alarmGenerated = true;
        };

        // Act - 只发送设备信号，不发送业务数据
        var deviceMessage = _fixture.CreateDeviceSignalMessage();
        await aggregator.ProcessMessage(deviceMessage);

        // 等待保持定时器超时
        await Task.Delay(TimeSpan.FromSeconds(11)); // 超过HoldingTimeoutSec

        // Assert - 不应该生成告警（业务规则不满足）
        alarmGenerated.Should().BeFalse();
        aggregator.CurrentState.Should().Be(EventProcessingState.Collecting);
    }

    [Fact]
    public async Task BusinessOnlyFlow_BusinessRuleNotMet_ShouldNotGenerateAlarm()
    {
        // Arrange
        var aggregator = await _fixture.CreateTestAggregatorAsync();
        var alarmGenerated = false;

        aggregator.OnAlarmGenerated += (sender, args) =>
        {
            alarmGenerated = true;
        };

        // Act - 发送设备信号
        var deviceMessage = _fixture.CreateDeviceSignalMessage();
        await aggregator.ProcessMessage(deviceMessage);

        // Act - 发送业务数据（不满足告警条件）
        var businessMessage = _fixture.CreateBusinessDataMessage(new Dictionary<string, object>
        {
            ["amount"] = 50 // 小于100，不满足业务规则
        });
        await aggregator.ProcessMessage(businessMessage);

        // 等待告警静默期结束
        await Task.Delay(TimeSpan.FromSeconds(1.5));

        // Assert
        alarmGenerated.Should().BeFalse();
        aggregator.CurrentState.Should().Be(EventProcessingState.Collecting);
    }

    [Fact]
    public async Task GracePeriod_ExclusionDuringGracePeriod_ShouldCancelPendingAlarm()
    {
        // Arrange
        var aggregator = await _fixture.CreateTestAggregatorAsync();
        var alarmGenerated = false;

        aggregator.OnAlarmGenerated += (sender, args) =>
        {
            alarmGenerated = true;
        };

        // Act - 触发告警条件
        var deviceMessage = _fixture.CreateDeviceSignalMessage();
        await aggregator.ProcessMessage(deviceMessage);

        var businessMessage = _fixture.CreateBusinessDataMessage(new Dictionary<string, object>
        {
            ["amount"] = 150
        });
        await aggregator.ProcessMessage(businessMessage);

        // 验证进入静默期
        aggregator.CurrentState.Should().Be(EventProcessingState.PendingAlarm);

        // Act - 在静默期内发送排除数据
        await Task.Delay(TimeSpan.FromMilliseconds(500)); // 静默期内
        var exclusionMessage = _fixture.CreateExclusionDataMessage(new Dictionary<string, object>
        {
            ["excluded"] = "true"
        });
        await aggregator.ProcessMessage(exclusionMessage);

        // 等待静默期结束
        await Task.Delay(TimeSpan.FromSeconds(1));

        // Assert - 应该被排除，不生成告警
        aggregator.CurrentState.Should().Be(EventProcessingState.Excluded);
        alarmGenerated.Should().BeFalse();
    }

    [Fact]
    public async Task EventManager_MultipleEvents_ShouldHandleIndependently()
    {
        // Arrange
        var eventManager = _fixture.EventManager;
        var config = _fixture.ConfigurationService.CurrentConfiguration;

        // 创建两个不同的消息（不同时间窗口）
        var message1 = new EventMessage
        {
            MessageType = "DeviceSignal",
            Topic = "device/test/event",
            Payload = JsonSerializer.Serialize(new { I2 = "0", timestamp = DateTime.UtcNow.ToString("yyyyMMddHHmmssfff") }),
            EventId = config.EventId,
            CommId = config.CommId,
            PositionId = config.PositionId,
            Timestamp = DateTime.UtcNow
        };

        var message2 = new EventMessage
        {
            MessageType = "DeviceSignal",
            Topic = "device/test/event",
            Payload = JsonSerializer.Serialize(new { I2 = "0", timestamp = DateTime.UtcNow.AddMinutes(2).ToString("yyyyMMddHHmmssfff") }),
            EventId = config.EventId,
            CommId = config.CommId,
            PositionId = config.PositionId,
            Timestamp = DateTime.UtcNow.AddMinutes(2)
        };

        // Act
        var aggregator1 = eventManager.GetOrCreateAggregator(message1);
        var aggregator2 = eventManager.GetOrCreateAggregator(message2);

        // Assert
        aggregator1.Should().NotBeSameAs(aggregator2);
        aggregator1.CorrelationId.Should().NotBe(aggregator2.CorrelationId);
        eventManager.ActiveEventCount.Should().Be(2);
    }

    [Fact]
    public async Task RuleEvaluationFailure_ShouldUseFallbackBehavior()
    {
        // Arrange
        var testMqttService = (TestMqttService)_fixture.MqttService;
        testMqttService.ClearPublishedMessages();

        var aggregator = await _fixture.CreateTestAggregatorAsync();
        var alarmGenerated = false;

        aggregator.OnAlarmGenerated += (sender, args) =>
        {
            alarmGenerated = true;
        };

        // Act - 发送设备信号
        var deviceMessage = _fixture.CreateDeviceSignalMessage();
        await aggregator.ProcessMessage(deviceMessage);

        // 发送包含无效数据的业务消息（可能导致规则评估失败）
        var businessMessage = _fixture.CreateBusinessDataMessage(new Dictionary<string, object>
        {
            ["amount"] = "invalid_number_format" // 这可能导致数字转换失败
        });
        await aggregator.ProcessMessage(businessMessage);

        // 等待处理完成
        await Task.Delay(TimeSpan.FromSeconds(1.5));

        // Assert - 即使规则评估失败，系统也应该继续运行而不崩溃
        aggregator.CurrentState.Should().BeOneOf(
            EventProcessingState.Collecting,
            EventProcessingState.PendingAlarm,
            EventProcessingState.Alarmed);

        // 验证熔断器统计信息
        var circuitBreakerStats = aggregator.GetCircuitBreakerStatistics();
        circuitBreakerStats.Should().NotBeEmpty();
    }

    [Fact]
    public async Task CircuitBreaker_ShouldPreventCascadingFailures()
    {
        // Arrange
        var aggregator = await _fixture.CreateTestAggregatorAsync();

        // Act - 模拟多次规则评估失败
        for (int i = 0; i < 15; i++) // 超过熔断器阈值
        {
            var businessMessage = _fixture.CreateBusinessDataMessage(new Dictionary<string, object>
            {
                ["amount"] = $"invalid_data_{i}"
            });

            try
            {
                await aggregator.ProcessMessage(businessMessage);
            }
            catch
            {
                // 忽略异常，继续测试
            }

            await Task.Delay(50); // 短暂延迟
        }

        // Assert - 验证熔断器已激活
        var circuitBreakerStats = aggregator.GetCircuitBreakerStatistics();
        circuitBreakerStats.Should().NotBeEmpty();

        // 至少有一个熔断器应该处于开启状态或有失败记录
        circuitBreakerStats.Values.Should().Contain(stat =>
            stat.FailureCount > 0 || stat.IsOpen);
    }

    [Fact]
    public async Task ConfigurationValidation_WithInvalidOperator_ShouldFailFast()
    {
        // Arrange
        var configService = _fixture.ConfigurationService;
        var invalidConfigPath = Path.GetTempFileName();

        var invalidConfig = new
        {
            EventId = "EV001001",
            EventName = "测试事件",
            EvaluationStrategy = "BusinessOnly",
            Priority = "P1",
            CommId = "101013",
            PositionId = "P002Test",
            AlarmGracePeriodSeconds = 3,
            EnableAlarmCancellation = true,
            CorrelationTimeWindow = "minute",
            DeviceSignal = new
            {
                Topics = new[] { "device/test/event" },
                TriggerField = "I2",
                TriggerValues = new Dictionary<string, string> { ["true"] = "0" },
                HoldingTimeoutSec = 30
            },
            RuleConfiguration = new
            {
                BusinessRules = new[]
                {
                    new
                    {
                        SourceTopic = "business/test/data",
                        LogicOperator = "AND",
                        Conditions = new[]
                        {
                            new
                            {
                                FieldName = "amount",
                                DataType = "string",
                                Operator = "GreaterThan", // 无效：字符串不支持GreaterThan
                                Value = "100"
                            }
                        }
                    }
                },
                AlarmConfig = new
                {
                    Fields = new[]
                    {
                        new
                        {
                            AlarmFieldName = "详情",
                            SourceRuleType = "BusinessRules",
                            SourceFieldName = "amount"
                        }
                    }
                }
            }
        };

        await File.WriteAllTextAsync(invalidConfigPath, System.Text.Json.JsonSerializer.Serialize(invalidConfig));

        try
        {
            // Act & Assert - 启动时验证应该失败
            var exception = await Assert.ThrowsAsync<InvalidOperationException>(
                () => configService.ValidateStartupConfigurationAsync(invalidConfigPath));

            exception.Message.Should().Contain("严重配置错误");
        }
        finally
        {
            // Cleanup
            if (File.Exists(invalidConfigPath))
            {
                File.Delete(invalidConfigPath);
            }
        }
    }
}
