using EventProcessor.Core.Engine;
using EventProcessor.Core.Models;
using EventProcessor.Core.Services;
using EventProcessor.IntegrationTests.TestServices;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Moq;
using System.Text.Json;
using Host = Microsoft.Extensions.Hosting.Host;

namespace EventProcessor.IntegrationTests.TestFixtures;

/// <summary>
/// Event Processor 集成测试基础设施
/// </summary>
public class EventProcessorTestFixture : IDisposable
{
    private readonly IHost _host;
    private bool _disposed = false;

    public IServiceProvider Services => _host.Services;
    public EventManager EventManager => Services.GetRequiredService<EventManager>();
    public IMqttService MqttService => Services.GetRequiredService<IMqttService>();
    public IConfigurationService ConfigurationService => Services.GetRequiredService<IConfigurationService>();
    public IAlarmGenerator AlarmGenerator => Services.GetRequiredService<IAlarmGenerator>();
    public RuleEngine RuleEngine => Services.GetRequiredService<RuleEngine>();

    public EventProcessorTestFixture()
    {
        _host = CreateTestHost();
    }

    private IHost CreateTestHost()
    {
        var configuration = new ConfigurationBuilder()
            .AddInMemoryCollection(new Dictionary<string, string?>
            {
                ["EventProcessor:EventId"] = "EV001001",
                ["EventProcessor:EventName"] = "测试事件",
                ["EventProcessor:EvaluationStrategy"] = "BusinessOnly",
                ["EventProcessor:Priority"] = "P3",
                ["EventProcessor:CommId"] = "101013",
                ["EventProcessor:PositionId"] = "P002LfyBmOut",
                ["EventProcessor:AlarmGracePeriodSeconds"] = "1",
                ["EventProcessor:EnableAlarmCancellation"] = "true",
                ["EventProcessor:CorrelationTimeWindow"] = "minute",
                ["Mqtt:BrokerHost"] = "localhost",
                ["Mqtt:BrokerPort"] = "1883",
                ["Mqtt:ClientId"] = "TestClient",
                ["Mqtt:Username"] = "test",
                ["Mqtt:Password"] = "test"
            })
            .Build();

        return Microsoft.Extensions.Hosting.Host.CreateDefaultBuilder()
            .ConfigureServices((context, services) =>
            {
                // 配置选项
                services.Configure<EventConfiguration>(configuration.GetSection("EventProcessor"));
                services.Configure<MqttConfiguration>(configuration.GetSection("Mqtt"));
                services.Configure<ErrorHandlingConfiguration>(config =>
                {
                    // Note: These properties might be init-only, so we'll use object initializer
                });

                // Register ErrorHandlingConfiguration directly
                services.AddSingleton(new ErrorHandlingConfiguration
                {
                    ToleranceLevel = "Normal",
                    RetryPolicy = new RetryPolicyConfiguration
                    {
                        MaxRetries = 3,
                        RetryDelay = 100
                    }
                });

                // 注册核心服务
                services.AddSingleton<ConditionEvaluator>();
                services.AddSingleton<RuleEngine>();
                services.AddSingleton<IAlarmGenerator, AlarmGenerator>();
                services.AddSingleton<IErrorHandlingService, ErrorHandlingService>();

                // 使用Mock的MQTT服务进行测试
                services.AddSingleton<IMqttService>(provider =>
                {
                    var logger = provider.GetRequiredService<ILogger<TestMqttService>>();
                    return new TestMqttService(logger);
                });

                // 使用Mock的配置服务
                services.AddSingleton<IConfigurationService>(provider =>
                {
                    var logger = provider.GetRequiredService<ILogger<TestConfigurationService>>();
                    return new TestConfigurationService(logger, CreateTestEventConfiguration());
                });

                // 注册事件管理器
                services.AddSingleton<EventManager>();

                // 添加日志
                services.AddLogging(builder =>
                {
                    builder.AddConsole();
                    builder.SetMinimumLevel(LogLevel.Debug);
                });
            })
            .Build();
    }

    private EventConfiguration CreateTestEventConfiguration()
    {
        return new EventConfiguration
        {
            EventId = "EV001001",
            EventName = "测试事件",
            EvaluationStrategy = "BusinessOnly",
            Priority = "P3",
            CommId = "101013",
            PositionId = "P002LfyBmOut",
            CompanyName = "test",
            AlarmGracePeriodSeconds = 1,
            EnableAlarmCancellation = true,
            CorrelationTimeWindow = "minute",
            DeviceSignal = new DeviceSignalConfiguration
            {
                Topics = new[] { "device/test/event" },
                TriggerField = "I2",
                TriggerValues = new Dictionary<string, string>
                {
                    ["true"] = "0",
                    ["false"] = "1"
                },
                HoldingTimeoutSec = 10
            },
            RuleConfiguration = new RuleConfiguration
            {
                BusinessRules = new[]
                {
                    new BusinessRuleGroup
                    {
                        SourceTopic = "business/test/data",
                        LogicOperator = "AND",
                        Conditions = new[]
                        {
                            new Condition
                            {
                                FieldName = "amount",
                                DataType = "number",
                                Operator = "GreaterThan",
                                Value = "100"
                            }
                        }
                    }
                },
                ExclusionRules = new[]
                {
                    new ExclusionRuleGroup
                    {
                        SourceType = "MQTT",
                        SourceTopic = "exclusion/test/data",
                        LogicOperator = "OR",
                        Conditions = new[]
                        {
                            new Condition
                            {
                                FieldName = "excluded",
                                DataType = "string",
                                Operator = "Equals",
                                Value = "true"
                            }
                        }
                    }
                },
                AlarmConfig = new AlarmConfiguration
                {
                    Fields = new[]
                    {
                        new FieldMapping
                        {
                            AlarmFieldName = "详情",
                            SourceRuleType = "BusinessRules",
                            SourceFieldName = "amount",
                            FormatTemplate = "金额: {amount}"
                        }
                    }
                }
            }
        };
    }

    public async Task<EventStateAggregator> CreateTestAggregatorAsync()
    {
        var correlationId = $"test-{Guid.NewGuid():N}";
        var config = ConfigurationService.CurrentConfiguration;
        
        var message = new EventMessage
        {
            MessageType = "DeviceSignal",
            Topic = "device/test/event",
            Payload = JsonSerializer.Serialize(new { I2 = "0", timestamp = DateTime.UtcNow.ToString("yyyyMMddHHmmssfff") }),
            EventId = config.EventId,
            CommId = config.CommId,
            PositionId = config.PositionId
        };

        return EventManager.GetOrCreateAggregator(message);
    }

    public EventMessage CreateDeviceSignalMessage(Dictionary<string, object>? additionalData = null)
    {
        var config = ConfigurationService.CurrentConfiguration;
        var data = new Dictionary<string, object>
        {
            ["I2"] = "0",
            ["timestamp"] = DateTime.UtcNow.ToString("yyyyMMddHHmmssfff")
        };

        if (additionalData != null)
        {
            foreach (var kvp in additionalData)
            {
                data[kvp.Key] = kvp.Value;
            }
        }

        return new EventMessage
        {
            MessageType = "DeviceSignal",
            Topic = "device/test/event",
            Payload = JsonSerializer.Serialize(data),
            EventId = config.EventId,
            CommId = config.CommId,
            PositionId = config.PositionId
        };
    }

    public EventMessage CreateBusinessDataMessage(Dictionary<string, object> businessData)
    {
        var config = ConfigurationService.CurrentConfiguration;

        return new EventMessage
        {
            MessageType = "BusinessData",
            Topic = "business/test/data",
            Payload = JsonSerializer.Serialize(businessData),
            EventId = config.EventId,
            CommId = config.CommId,
            PositionId = config.PositionId
        };
    }

    public EventMessage CreateExclusionDataMessage(Dictionary<string, object> exclusionData)
    {
        var config = ConfigurationService.CurrentConfiguration;

        return new EventMessage
        {
            MessageType = "ExclusionData",
            Topic = "exclusion/test/data",
            Payload = JsonSerializer.Serialize(exclusionData),
            EventId = config.EventId,
            CommId = config.CommId,
            PositionId = config.PositionId
        };
    }

    public EventMessage CreateAIResultMessage(Dictionary<string, object> aiResult)
    {
        var config = ConfigurationService.CurrentConfiguration;
        var correlationId = $"{config.CommId}_{config.PositionId}_{config.EventId}";

        var aiResultMessage = new AIResultMessage
        {
            EventId = config.EventId,
            RequestId = correlationId,
            Result = aiResult,
            Timestamp = DateTime.UtcNow.ToString("yyyyMMddHHmmssfff"),
            ProcessingTime = 1.5,
            Success = true
        };

        return new EventMessage
        {
            MessageType = "AIResult",
            Topic = $"ai/{config.CommId}/{config.PositionId}/result",
            Payload = JsonSerializer.Serialize(aiResultMessage),
            EventId = config.EventId,
            CommId = config.CommId,
            PositionId = config.PositionId
        };
    }

    public void Dispose()
    {
        Dispose(true);
        GC.SuppressFinalize(this);
    }

    protected virtual void Dispose(bool disposing)
    {
        if (!_disposed && disposing)
        {
            _host?.Dispose();
            _disposed = true;
        }
    }
}
