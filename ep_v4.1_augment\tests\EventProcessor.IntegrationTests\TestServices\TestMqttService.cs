using EventProcessor.Core.Models;
using EventProcessor.Core.Services;
using Microsoft.Extensions.Logging;
using System.Collections.Concurrent;

namespace EventProcessor.IntegrationTests.TestServices;

/// <summary>
/// 测试用的MQTT服务实现
/// </summary>
public class TestMqttService : IMqttService
{
    private readonly ILogger<TestMqttService> _logger;
    private readonly ConcurrentDictionary<string, int> _subscribedTopics = new();
    private readonly ConcurrentQueue<PublishedMessage> _publishedMessages = new();
    private bool _isConnected = false;
    private bool _disposed = false;

    public event EventHandler<MqttConnectedEventArgs>? Connected;
    public event EventHandler<MqttDisconnectedEventArgs>? Disconnected;
    public event EventHandler<MqttMessageReceivedEventArgs>? MessageReceived;

    public bool IsConnected => _isConnected;
    public string ClientId { get; } = "TestClient";

    // 测试辅助属性
    public IReadOnlyList<PublishedMessage> PublishedMessages => _publishedMessages.ToArray();
    public IReadOnlyDictionary<string, int> SubscribedTopics => _subscribedTopics.ToDictionary(kvp => kvp.Key, kvp => kvp.Value);

    public TestMqttService(ILogger<TestMqttService> logger)
    {
        _logger = logger;
    }

    public async Task StartAsync(CancellationToken cancellationToken = default)
    {
        await Task.Delay(10, cancellationToken); // 模拟连接延迟
        _isConnected = true;
        
        Connected?.Invoke(this, new MqttConnectedEventArgs
        {
            ClientId = ClientId,
            ServerAddress = "localhost:1883"
        });

        _logger.LogInformation("测试MQTT服务已启动");
    }

    public async Task StopAsync(CancellationToken cancellationToken = default)
    {
        await Task.Delay(10, cancellationToken); // 模拟断开延迟
        _isConnected = false;
        
        Disconnected?.Invoke(this, new MqttDisconnectedEventArgs
        {
            ClientId = ClientId,
            Reason = "Stopped",
            IsException = false
        });

        _logger.LogInformation("测试MQTT服务已停止");
    }

    public async Task SubscribeAsync(string topic, int qos = 1)
    {
        await Task.Delay(1); // 模拟订阅延迟
        _subscribedTopics[topic] = qos;
        _logger.LogDebug("已订阅主题: {Topic}, QoS: {QoS}", topic, qos);
    }

    public async Task SubscribeAsync(string[] topics, int qos = 1)
    {
        foreach (var topic in topics)
        {
            await SubscribeAsync(topic, qos);
        }
    }

    public async Task UnsubscribeAsync(string topic)
    {
        await Task.Delay(1); // 模拟取消订阅延迟
        _subscribedTopics.TryRemove(topic, out _);
        _logger.LogDebug("已取消订阅主题: {Topic}", topic);
    }

    public async Task PublishAsync(string topic, string payload, int qos = 1, bool retain = false)
    {
        await Task.Delay(1); // 模拟发布延迟
        
        var message = new PublishedMessage
        {
            Topic = topic,
            Payload = payload,
            QualityOfServiceLevel = qos,
            Retain = retain,
            PublishedAt = DateTime.UtcNow
        };

        _publishedMessages.Enqueue(message);
        _logger.LogDebug("已发布消息: {Topic}, 长度: {Length}", topic, payload.Length);
    }

    public async Task PublishAsync(string topic, byte[] payload, int qos = 1, bool retain = false)
    {
        var payloadString = System.Text.Encoding.UTF8.GetString(payload);
        await PublishAsync(topic, payloadString, qos, retain);
    }

    public MqttConnectionStatistics GetConnectionStatistics()
    {
        return new MqttConnectionStatistics
        {
            IsConnected = _isConnected,
            ConnectedAt = DateTime.UtcNow.AddMinutes(-1), // 假设1分钟前连接
            ReconnectCount = 0,
            MessagesSent = _publishedMessages.Count,
            MessagesReceived = 0, // 测试中不模拟接收
            SubscribedTopicCount = _subscribedTopics.Count,
            LastActivityAt = DateTime.UtcNow
        };
    }

    // 测试辅助方法
    public void SimulateMessageReceived(string topic, string payload, int qos = 1, bool retain = false)
    {
        if (!_isConnected)
        {
            throw new InvalidOperationException("MQTT服务未连接");
        }

        MessageReceived?.Invoke(this, new MqttMessageReceivedEventArgs
        {
            Topic = topic,
            Payload = payload,
            QualityOfServiceLevel = qos,
            Retain = retain
        });

        _logger.LogDebug("模拟接收消息: {Topic}, 长度: {Length}", topic, payload.Length);
    }

    public void SimulateDisconnection(string reason = "Connection lost")
    {
        if (_isConnected)
        {
            _isConnected = false;
            Disconnected?.Invoke(this, new MqttDisconnectedEventArgs
            {
                ClientId = ClientId,
                Reason = reason,
                IsException = true
            });
        }
    }

    public void SimulateReconnection()
    {
        if (!_isConnected)
        {
            _isConnected = true;
            Connected?.Invoke(this, new MqttConnectedEventArgs
            {
                ClientId = ClientId,
                ServerAddress = "localhost:1883"
            });
        }
    }

    public void ClearPublishedMessages()
    {
        while (_publishedMessages.TryDequeue(out _)) { }
    }

    public PublishedMessage? GetLastPublishedMessage()
    {
        return _publishedMessages.LastOrDefault();
    }

    public PublishedMessage[] GetPublishedMessagesForTopic(string topic)
    {
        return _publishedMessages.Where(m => m.Topic == topic).ToArray();
    }

    public void Dispose()
    {
        Dispose(true);
        GC.SuppressFinalize(this);
    }

    protected virtual void Dispose(bool disposing)
    {
        if (!_disposed && disposing)
        {
            _isConnected = false;
            _disposed = true;
        }
    }
}

/// <summary>
/// 已发布的消息记录
/// </summary>
public record PublishedMessage
{
    public required string Topic { get; init; }
    public required string Payload { get; init; }
    public int QualityOfServiceLevel { get; init; }
    public bool Retain { get; init; }
    public DateTime PublishedAt { get; init; }
}

/// <summary>
/// 测试用的配置服务实现
/// </summary>
public class TestConfigurationService : IConfigurationService
{
    private readonly ILogger<TestConfigurationService> _logger;
    private readonly EventConfiguration _configuration;

    public event EventHandler<ConfigurationChangedEventArgs>? ConfigurationChanged;
    public event EventHandler<ConfigurationValidationFailedEventArgs>? ValidationFailed;

    public EventConfiguration CurrentConfiguration => _configuration;
    public bool IsValid => true;

    public TestConfigurationService(ILogger<TestConfigurationService> logger, EventConfiguration configuration)
    {
        _logger = logger;
        _configuration = configuration;
    }

    public Task LoadConfigurationAsync(string configurationPath, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("测试配置服务：配置已加载");
        return Task.CompletedTask;
    }

    public Task ReloadConfigurationAsync(CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("测试配置服务：配置已重新加载");
        return Task.CompletedTask;
    }

    public ValidationResult ValidateConfiguration(EventConfiguration configuration)
    {
        return new ValidationResult
        {
            IsValid = true,
            Errors = Array.Empty<string>(),
            Warnings = Array.Empty<string>()
        };
    }

    public Task ValidateStartupConfigurationAsync(string configurationPath, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("测试配置服务：启动配置已验证");
        return Task.CompletedTask;
    }

    public Task StartMonitoringAsync(CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("测试配置服务：监控已启动");
        return Task.CompletedTask;
    }

    public Task StopMonitoringAsync(CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("测试配置服务：监控已停止");
        return Task.CompletedTask;
    }

    public ConfigurationStatistics GetStatistics()
    {
        return new ConfigurationStatistics
        {
            ConfigurationPath = "test-config.json",
            LoadedAt = DateTime.UtcNow.AddMinutes(-1),
            LastModifiedAt = DateTime.UtcNow.AddMinutes(-1),
            FileSize = 1024,
            ReloadCount = 0,
            ValidationFailureCount = 0,
            IsMonitoring = false,
            ConfigurationFormat = "JSON"
        };
    }
}
