using EventProcessor.Core.Engine;
using EventProcessor.Core.Models;
using FluentAssertions;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;

namespace EventProcessor.Tests.Engine;

public class ConditionEvaluatorTests
{
    private readonly ConditionEvaluator _evaluator;
    private readonly Mock<ILogger<ConditionEvaluator>> _mockLogger;

    public ConditionEvaluatorTests()
    {
        _mockLogger = new Mock<ILogger<ConditionEvaluator>>();
        _evaluator = new ConditionEvaluator(_mockLogger.Object);
    }

    [Theory]
    [InlineData("test", "Equals", "test", true)]
    [InlineData("test", "Equals", "other", false)]
    [InlineData("hello world", "Contains", "world", true)]
    [InlineData("hello world", "Contains", "xyz", false)]
    [InlineData("hello", "StartsWith", "hel", true)]
    [InlineData("hello", "StartsWith", "llo", false)]
    [InlineData("hello", "EndsWith", "llo", true)]
    [InlineData("hello", "EndsWith", "hel", false)]
    [InlineData("", "IsEmpty", "", true)]
    [InlineData("test", "IsEmpty", "", false)]
    [InlineData("test", "IsNotEmpty", "", true)]
    [InlineData("", "IsNotEmpty", "", false)]
    public void EvaluateCondition_StringOperators_ShouldReturnExpectedResult(
        string fieldValue, string operatorName, string conditionValue, bool expected)
    {
        // Arrange
        var condition = new Condition
        {
            FieldName = "testField",
            DataType = "string",
            Operator = operatorName,
            Value = conditionValue
        };

        var data = new Dictionary<string, object>
        {
            ["testField"] = fieldValue
        };

        // Act
        var result = _evaluator.EvaluateCondition(condition, data);

        // Assert
        result.Should().Be(expected);
    }

    [Theory]
    [InlineData(10, "Equals", "10", true)]
    [InlineData(10, "Equals", "5", false)]
    [InlineData(10, "GreaterThan", "5", true)]
    [InlineData(10, "GreaterThan", "15", false)]
    [InlineData(10, "LessThan", "15", true)]
    [InlineData(10, "LessThan", "5", false)]
    [InlineData(10, "GreaterThanOrEqual", "10", true)]
    [InlineData(10, "GreaterThanOrEqual", "15", false)]
    [InlineData(10, "LessThanOrEqual", "10", true)]
    [InlineData(10, "LessThanOrEqual", "5", false)]
    public void EvaluateCondition_NumberOperators_ShouldReturnExpectedResult(
        double fieldValue, string operatorName, string conditionValue, bool expected)
    {
        // Arrange
        var condition = new Condition
        {
            FieldName = "testField",
            DataType = "number",
            Operator = operatorName,
            Value = conditionValue
        };

        var data = new Dictionary<string, object>
        {
            ["testField"] = fieldValue
        };

        // Act
        var result = _evaluator.EvaluateCondition(condition, data);

        // Assert
        result.Should().Be(expected);
    }

    [Fact]
    public void EvaluateCondition_NumberBetween_ShouldReturnCorrectResult()
    {
        // Arrange
        var condition = new Condition
        {
            FieldName = "testField",
            DataType = "number",
            Operator = "Between",
            Value = "5,15"
        };

        var data = new Dictionary<string, object>
        {
            ["testField"] = 10
        };

        // Act
        var result = _evaluator.EvaluateCondition(condition, data);

        // Assert
        result.Should().BeTrue();
    }

    [Fact]
    public void EvaluateCondition_StringIn_ShouldReturnCorrectResult()
    {
        // Arrange
        var condition = new Condition
        {
            FieldName = "cardType",
            DataType = "string",
            Operator = "In",
            Value = "月租卡|万全卡|贵宾卡"
        };

        var data = new Dictionary<string, object>
        {
            ["cardType"] = "月租卡"
        };

        // Act
        var result = _evaluator.EvaluateCondition(condition, data);

        // Assert
        result.Should().BeTrue();
    }

    [Fact]
    public void EvaluateCondition_StringRegex_ShouldReturnCorrectResult()
    {
        // Arrange
        var condition = new Condition
        {
            FieldName = "plateNumber",
            DataType = "string",
            Operator = "Regex",
            Value = @"^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领A-Z]{1}[A-Z]{1}[A-Z0-9]{4}[A-Z0-9挂学警港澳]{1}$"
        };

        var data = new Dictionary<string, object>
        {
            ["plateNumber"] = "京A12345"
        };

        // Act
        var result = _evaluator.EvaluateCondition(condition, data);

        // Assert
        result.Should().BeTrue();
    }

    [Theory]
    [InlineData("20241201143025", "Equals", "20241201143025", true)]
    [InlineData("20241201143025", "Before", "20241201143030", true)]
    [InlineData("20241201143025", "After", "20241201143020", true)]
    [InlineData("20241201143025", "Before", "20241201143020", false)]
    public void EvaluateCondition_DateTimeOperators_ShouldReturnExpectedResult(
        string fieldValue, string operatorName, string conditionValue, bool expected)
    {
        // Arrange
        var condition = new Condition
        {
            FieldName = "timestamp",
            DataType = "datetime",
            Operator = operatorName,
            Value = conditionValue
        };

        var data = new Dictionary<string, object>
        {
            ["timestamp"] = fieldValue
        };

        // Act
        var result = _evaluator.EvaluateCondition(condition, data);

        // Assert
        result.Should().Be(expected);
    }

    [Fact]
    public void EvaluateCondition_DateTimeBetween_ShouldReturnCorrectResult()
    {
        // Arrange
        var condition = new Condition
        {
            FieldName = "timestamp",
            DataType = "datetime",
            Operator = "Between",
            Value = "20241201143020,20241201143030"
        };

        var data = new Dictionary<string, object>
        {
            ["timestamp"] = "20241201143025"
        };

        // Act
        var result = _evaluator.EvaluateCondition(condition, data);

        // Assert
        result.Should().BeTrue();
    }

    [Fact]
    public void EvaluateCondition_FieldNotExists_ShouldReturnFalse()
    {
        // Arrange
        var condition = new Condition
        {
            FieldName = "nonExistentField",
            DataType = "string",
            Operator = "Equals",
            Value = "test"
        };

        var data = new Dictionary<string, object>
        {
            ["otherField"] = "value"
        };

        // Act
        var result = _evaluator.EvaluateCondition(condition, data);

        // Assert
        result.Should().BeFalse();
    }

    [Fact]
    public void EvaluateCondition_InvalidDataType_ShouldThrowException()
    {
        // Arrange
        var condition = new Condition
        {
            FieldName = "testField",
            DataType = "invalidType",
            Operator = "Equals",
            Value = "test"
        };

        var data = new Dictionary<string, object>
        {
            ["testField"] = "value"
        };

        // Act & Assert
        Assert.Throws<InvalidOperationException>(() => _evaluator.EvaluateCondition(condition, data));
    }

    [Fact]
    public void EvaluateConditionGroup_ANDOperator_ShouldReturnCorrectResult()
    {
        // Arrange
        var group = new ConditionGroup
        {
            LogicOperator = "AND",
            Conditions = new[]
            {
                new Condition
                {
                    FieldName = "field1",
                    DataType = "string",
                    Operator = "Equals",
                    Value = "value1"
                },
                new Condition
                {
                    FieldName = "field2",
                    DataType = "number",
                    Operator = "GreaterThan",
                    Value = "5"
                }
            }
        };

        var data = new Dictionary<string, object>
        {
            ["field1"] = "value1",
            ["field2"] = 10
        };

        // Act
        var result = _evaluator.EvaluateConditionGroup(group, data);

        // Assert
        result.Should().BeTrue();
    }

    [Fact]
    public void EvaluateConditionGroup_OROperator_ShouldReturnCorrectResult()
    {
        // Arrange
        var group = new ConditionGroup
        {
            LogicOperator = "OR",
            Conditions = new[]
            {
                new Condition
                {
                    FieldName = "field1",
                    DataType = "string",
                    Operator = "Equals",
                    Value = "wrongValue"
                },
                new Condition
                {
                    FieldName = "field2",
                    DataType = "number",
                    Operator = "GreaterThan",
                    Value = "5"
                }
            }
        };

        var data = new Dictionary<string, object>
        {
            ["field1"] = "value1",
            ["field2"] = 10
        };

        // Act
        var result = _evaluator.EvaluateConditionGroup(group, data);

        // Assert
        result.Should().BeTrue();
    }

    [Fact]
    public void EvaluateConditionGroup_NOTOperator_ShouldReturnCorrectResult()
    {
        // Arrange
        var group = new ConditionGroup
        {
            LogicOperator = "NOT",
            Conditions = new[]
            {
                new Condition
                {
                    FieldName = "field1",
                    DataType = "string",
                    Operator = "Equals",
                    Value = "wrongValue"
                }
            }
        };

        var data = new Dictionary<string, object>
        {
            ["field1"] = "value1"
        };

        // Act
        var result = _evaluator.EvaluateConditionGroup(group, data);

        // Assert
        result.Should().BeTrue();
    }

    [Fact]
    public void EvaluateCondition_InvalidNumberFormat_ShouldReturnFalse()
    {
        // Arrange
        var condition = new Condition
        {
            FieldName = "testField",
            DataType = "number",
            Operator = "GreaterThan",
            Value = "5"
        };

        var data = new Dictionary<string, object>
        {
            ["testField"] = "not_a_number"
        };

        // Act
        var result = _evaluator.EvaluateCondition(condition, data);

        // Assert
        result.Should().BeFalse();
    }

    [Fact]
    public void EvaluateCondition_WithinMinutes_ShouldReturnCorrectResult()
    {
        // Arrange
        var now = DateTime.UtcNow;
        var fiveMinutesAgo = now.AddMinutes(-5);
        
        var condition = new Condition
        {
            FieldName = "timestamp",
            DataType = "datetime",
            Operator = "WithinMinutes",
            Value = "10"
        };

        var data = new Dictionary<string, object>
        {
            ["timestamp"] = fiveMinutesAgo.ToString("yyyyMMddHHmmss")
        };

        // Act
        var result = _evaluator.EvaluateCondition(condition, data);

        // Assert
        result.Should().BeTrue();
    }
}
