using EventProcessor.Core.Engine;
using EventProcessor.Core.Models;
using FluentAssertions;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;

namespace EventProcessor.Tests.Engine;

public class RuleEngineTests
{
    private readonly RuleEngine _ruleEngine;
    private readonly Mock<ConditionEvaluator> _mockConditionEvaluator;
    private readonly Mock<ILogger<RuleEngine>> _mockLogger;

    public RuleEngineTests()
    {
        _mockLogger = new Mock<ILogger<RuleEngine>>();
        var mockConditionLogger = new Mock<ILogger<ConditionEvaluator>>();
        _mockConditionEvaluator = new Mock<ConditionEvaluator>(mockConditionLogger.Object);
        _ruleEngine = new RuleEngine(_mockConditionEvaluator.Object, _mockLogger.Object);
    }

    [Fact]
    public void EvaluateExclusionRules_NoRules_ShouldReturnFalse()
    {
        // Arrange
        ExclusionRuleGroup[]? exclusionRules = null;
        var data = new Dictionary<string, object>();

        // Act
        var result = _ruleEngine.EvaluateExclusionRules(exclusionRules, data);

        // Assert
        result.Should().BeFalse();
    }

    [Fact]
    public void EvaluateExclusionRules_EmptyRules_ShouldReturnFalse()
    {
        // Arrange
        var exclusionRules = Array.Empty<ExclusionRuleGroup>();
        var data = new Dictionary<string, object>();

        // Act
        var result = _ruleEngine.EvaluateExclusionRules(exclusionRules, data);

        // Assert
        result.Should().BeFalse();
    }

    [Fact]
    public void EvaluateExclusionRules_MatchingRule_ShouldReturnTrue()
    {
        // Arrange
        var exclusionRules = new[]
        {
            new ExclusionRuleGroup
            {
                SourceType = "MQTT",
                SourceTopic = "test/topic",
                LogicOperator = "AND",
                Conditions = new[]
                {
                    new Condition
                    {
                        FieldName = "status",
                        DataType = "string",
                        Operator = "Equals",
                        Value = "excluded"
                    }
                }
            }
        };

        var data = new Dictionary<string, object>
        {
            ["status"] = "excluded"
        };

        _mockConditionEvaluator
            .Setup(x => x.EvaluateCondition(It.IsAny<Condition>(), It.IsAny<Dictionary<string, object>>()))
            .Returns(true);

        // Act
        var result = _ruleEngine.EvaluateExclusionRules(exclusionRules, data);

        // Assert
        result.Should().BeTrue();
    }

    [Fact]
    public void EvaluateBusinessRules_NoRules_ShouldReturnFalse()
    {
        // Arrange
        BusinessRuleGroup[]? businessRules = null;
        var data = new Dictionary<string, object>();

        // Act
        var result = _ruleEngine.EvaluateBusinessRules(businessRules, data);

        // Assert
        result.Should().BeFalse();
    }

    [Fact]
    public void EvaluateBusinessRules_MatchingRule_ShouldReturnTrue()
    {
        // Arrange
        var businessRules = new[]
        {
            new BusinessRuleGroup
            {
                SourceTopic = "business/topic",
                LogicOperator = "AND",
                Conditions = new[]
                {
                    new Condition
                    {
                        FieldName = "amount",
                        DataType = "number",
                        Operator = "GreaterThan",
                        Value = "100"
                    }
                }
            }
        };

        var data = new Dictionary<string, object>
        {
            ["amount"] = 150
        };

        _mockConditionEvaluator
            .Setup(x => x.EvaluateCondition(It.IsAny<Condition>(), It.IsAny<Dictionary<string, object>>()))
            .Returns(true);

        // Act
        var result = _ruleEngine.EvaluateBusinessRules(businessRules, data);

        // Assert
        result.Should().BeTrue();
    }

    [Fact]
    public void EvaluateBusinessRules_WithConditionGroups_ShouldEvaluateCorrectly()
    {
        // Arrange
        var businessRules = new[]
        {
            new BusinessRuleGroup
            {
                SourceTopic = "business/topic",
                LogicOperator = "AND",
                ConditionGroups = new[]
                {
                    new ConditionGroup
                    {
                        LogicOperator = "OR",
                        Conditions = new[]
                        {
                            new Condition
                            {
                                FieldName = "type",
                                DataType = "string",
                                Operator = "Equals",
                                Value = "premium"
                            }
                        }
                    }
                }
            }
        };

        var data = new Dictionary<string, object>
        {
            ["type"] = "premium"
        };

        _mockConditionEvaluator
            .Setup(x => x.EvaluateConditionGroup(It.IsAny<ConditionGroup>(), It.IsAny<Dictionary<string, object>>()))
            .Returns(true);

        // Act
        var result = _ruleEngine.EvaluateBusinessRules(businessRules, data);

        // Assert
        result.Should().BeTrue();
    }

    [Fact]
    public void EvaluateAIResultRules_NoRules_ShouldReturnFalse()
    {
        // Arrange
        AIResultRuleGroup[]? aiRules = null;
        var data = new Dictionary<string, object>();

        // Act
        var result = _ruleEngine.EvaluateAIResultRules(aiRules, data);

        // Assert
        result.Should().BeFalse();
    }

    [Fact]
    public void EvaluateAIResultRules_MatchingRule_ShouldReturnTrue()
    {
        // Arrange
        var aiRules = new[]
        {
            new AIResultRuleGroup
            {
                LogicOperator = "AND",
                Conditions = new[]
                {
                    new Condition
                    {
                        FieldName = "confidence",
                        DataType = "number",
                        Operator = "GreaterThan",
                        Value = "0.8"
                    },
                    new Condition
                    {
                        FieldName = "detected",
                        DataType = "string",
                        Operator = "Equals",
                        Value = "true"
                    }
                }
            }
        };

        var data = new Dictionary<string, object>
        {
            ["confidence"] = 0.95,
            ["detected"] = "true"
        };

        _mockConditionEvaluator
            .Setup(x => x.EvaluateCondition(It.IsAny<Condition>(), It.IsAny<Dictionary<string, object>>()))
            .Returns(true);

        // Act
        var result = _ruleEngine.EvaluateAIResultRules(aiRules, data);

        // Assert
        result.Should().BeTrue();
    }

    [Fact]
    public void EvaluateBusinessRules_ANDLogic_AllConditionsMustMatch()
    {
        // Arrange
        var businessRules = new[]
        {
            new BusinessRuleGroup
            {
                SourceTopic = "business/topic",
                LogicOperator = "AND",
                Conditions = new[]
                {
                    new Condition
                    {
                        FieldName = "field1",
                        DataType = "string",
                        Operator = "Equals",
                        Value = "value1"
                    },
                    new Condition
                    {
                        FieldName = "field2",
                        DataType = "number",
                        Operator = "GreaterThan",
                        Value = "10"
                    }
                }
            }
        };

        var data = new Dictionary<string, object>
        {
            ["field1"] = "value1",
            ["field2"] = 5 // This should fail the second condition
        };

        _mockConditionEvaluator
            .SetupSequence(x => x.EvaluateCondition(It.IsAny<Condition>(), It.IsAny<Dictionary<string, object>>()))
            .Returns(true)  // First condition passes
            .Returns(false); // Second condition fails

        // Act
        var result = _ruleEngine.EvaluateBusinessRules(businessRules, data);

        // Assert
        result.Should().BeFalse();
    }

    [Fact]
    public void EvaluateBusinessRules_ORLogic_OneConditionSufficient()
    {
        // Arrange
        var businessRules = new[]
        {
            new BusinessRuleGroup
            {
                SourceTopic = "business/topic",
                LogicOperator = "OR",
                Conditions = new[]
                {
                    new Condition
                    {
                        FieldName = "field1",
                        DataType = "string",
                        Operator = "Equals",
                        Value = "value1"
                    },
                    new Condition
                    {
                        FieldName = "field2",
                        DataType = "number",
                        Operator = "GreaterThan",
                        Value = "10"
                    }
                }
            }
        };

        var data = new Dictionary<string, object>
        {
            ["field1"] = "wrong_value",
            ["field2"] = 15 // This should pass the second condition
        };

        _mockConditionEvaluator
            .SetupSequence(x => x.EvaluateCondition(It.IsAny<Condition>(), It.IsAny<Dictionary<string, object>>()))
            .Returns(false) // First condition fails
            .Returns(true); // Second condition passes

        // Act
        var result = _ruleEngine.EvaluateBusinessRules(businessRules, data);

        // Assert
        result.Should().BeTrue();
    }

    [Fact]
    public void EvaluateExclusionRules_ExceptionInEvaluation_ShouldReturnFalse()
    {
        // Arrange
        var exclusionRules = new[]
        {
            new ExclusionRuleGroup
            {
                SourceType = "MQTT",
                SourceTopic = "test/topic",
                LogicOperator = "AND",
                Conditions = new[]
                {
                    new Condition
                    {
                        FieldName = "status",
                        DataType = "string",
                        Operator = "Equals",
                        Value = "excluded"
                    }
                }
            }
        };

        var data = new Dictionary<string, object>();

        _mockConditionEvaluator
            .Setup(x => x.EvaluateCondition(It.IsAny<Condition>(), It.IsAny<Dictionary<string, object>>()))
            .Throws(new InvalidOperationException("Test exception"));

        // Act
        var result = _ruleEngine.EvaluateExclusionRules(exclusionRules, data);

        // Assert
        result.Should().BeFalse();
    }

    [Fact]
    public void GetMatchedConditions_ShouldReturnCorrectDescriptions()
    {
        // Arrange
        var businessRule = new BusinessRuleGroup
        {
            SourceTopic = "business/topic",
            LogicOperator = "AND",
            Conditions = new[]
            {
                new Condition
                {
                    FieldName = "amount",
                    DataType = "number",
                    Operator = "GreaterThan",
                    Value = "100",
                    Description = "Amount greater than 100"
                }
            }
        };

        var data = new Dictionary<string, object>
        {
            ["amount"] = 150
        };

        _mockConditionEvaluator
            .Setup(x => x.EvaluateCondition(It.IsAny<Condition>(), It.IsAny<Dictionary<string, object>>()))
            .Returns(true);

        // Act
        var matchedConditions = _ruleEngine.GetMatchedConditions(businessRule, data);

        // Assert
        matchedConditions.Should().Contain("Amount greater than 100");
    }

    [Fact]
    public void EvaluateBusinessRules_MultipleRules_FirstMatchWins()
    {
        // Arrange
        var businessRules = new[]
        {
            new BusinessRuleGroup
            {
                SourceTopic = "business/topic1",
                LogicOperator = "AND",
                Conditions = new[]
                {
                    new Condition
                    {
                        FieldName = "field1",
                        DataType = "string",
                        Operator = "Equals",
                        Value = "value1"
                    }
                }
            },
            new BusinessRuleGroup
            {
                SourceTopic = "business/topic2",
                LogicOperator = "AND",
                Conditions = new[]
                {
                    new Condition
                    {
                        FieldName = "field2",
                        DataType = "string",
                        Operator = "Equals",
                        Value = "value2"
                    }
                }
            }
        };

        var data = new Dictionary<string, object>
        {
            ["field1"] = "value1",
            ["field2"] = "value2"
        };

        _mockConditionEvaluator
            .Setup(x => x.EvaluateCondition(It.IsAny<Condition>(), It.IsAny<Dictionary<string, object>>()))
            .Returns(true);

        // Act
        var result = _ruleEngine.EvaluateBusinessRules(businessRules, data);

        // Assert
        result.Should().BeTrue();
        
        // Verify that only the first rule was evaluated
        _mockConditionEvaluator.Verify(
            x => x.EvaluateCondition(It.IsAny<Condition>(), It.IsAny<Dictionary<string, object>>()), 
            Times.Once);
    }
}
