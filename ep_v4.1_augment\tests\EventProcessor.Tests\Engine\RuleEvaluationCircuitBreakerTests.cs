using EventProcessor.Core.Engine;
using FluentAssertions;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;

namespace EventProcessor.Tests.Engine;

public class RuleEvaluationCircuitBreakerTests
{
    private readonly RuleEvaluationCircuitBreaker _circuitBreaker;
    private readonly Mock<ILogger<RuleEvaluationCircuitBreaker>> _mockLogger;

    public RuleEvaluationCircuitBreakerTests()
    {
        _mockLogger = new Mock<ILogger<RuleEvaluationCircuitBreaker>>();
        _circuitBreaker = new RuleEvaluationCircuitBreaker(_mockLogger.Object);
    }

    [Fact]
    public void Execute_WithSuccessfulFunction_ShouldReturnResult()
    {
        // Arrange
        var ruleKey = "test_rule";
        var expectedResult = true;

        // Act
        var result = _circuitBreaker.Execute<bool>(ruleKey, () => expectedResult);

        // Assert
        result.Should().Be(expectedResult);
    }

    [Fact]
    public void Execute_WithFailingFunction_ShouldReturnFallbackResult()
    {
        // Arrange
        var ruleKey = "test_rule";
        var fallbackResult = false;

        // Act
        var result = _circuitBreaker.Execute<bool>(ruleKey, () => throw new InvalidOperationException("Test exception"), fallbackResult);

        // Assert
        result.Should().Be(fallbackResult);
    }

    [Fact]
    public void Execute_WithRepeatedFailures_ShouldOpenCircuitBreaker()
    {
        // Arrange
        var ruleKey = "test_rule";
        var fallbackResult = false;

        // Act - 触发多次失败以达到阈值
        for (int i = 0; i < 15; i++) // 超过默认阈值10
        {
            _circuitBreaker.Execute<bool>(ruleKey, () => throw new InvalidOperationException("Test exception"), fallbackResult);
        }

        // 再次执行应该直接返回回退结果（熔断器已开启）
        var result = _circuitBreaker.Execute<bool>(ruleKey, () => true, fallbackResult);

        // Assert
        result.Should().Be(fallbackResult);
        
        var statistics = _circuitBreaker.GetStatistics();
        statistics[ruleKey].IsOpen.Should().BeTrue();
        statistics[ruleKey].Status.Should().Be("Open");
    }

    [Fact]
    public void Execute_AfterRecoveryTimeout_ShouldEnterHalfOpenState()
    {
        // Arrange
        var ruleKey = "test_rule";
        var fallbackResult = false;

        // 先触发熔断器开启
        for (int i = 0; i < 15; i++)
        {
            _circuitBreaker.Execute<bool>(ruleKey, () => throw new InvalidOperationException("Test exception"), fallbackResult);
        }

        // 验证熔断器已开启
        var statistics = _circuitBreaker.GetStatistics();
        statistics[ruleKey].IsOpen.Should().BeTrue();

        // 模拟等待恢复超时（这里我们需要修改内部状态或使用更短的超时时间进行测试）
        // 由于我们无法直接控制时间，这里我们测试重置功能
        _circuitBreaker.Reset(ruleKey);

        // Act - 重置后应该能正常执行
        var result = _circuitBreaker.Execute<bool>(ruleKey, () => true, fallbackResult);

        // Assert
        result.Should().BeTrue();
        
        statistics = _circuitBreaker.GetStatistics();
        statistics[ruleKey].IsOpen.Should().BeFalse();
    }

    [Fact]
    public void Execute_WithSuccessAfterFailures_ShouldReduceFailureCount()
    {
        // Arrange
        var ruleKey = "test_rule";

        // 先触发几次失败
        for (int i = 0; i < 5; i++)
        {
            _circuitBreaker.Execute<bool>(ruleKey, () => throw new InvalidOperationException("Test exception"), false);
        }

        var statisticsAfterFailures = _circuitBreaker.GetStatistics();
        var failureCountAfterFailures = statisticsAfterFailures[ruleKey].FailureCount;

        // Act - 执行成功的操作
        _circuitBreaker.Execute<bool>(ruleKey, () => true, false);

        // Assert
        var statisticsAfterSuccess = _circuitBreaker.GetStatistics();
        var failureCountAfterSuccess = statisticsAfterSuccess[ruleKey].FailureCount;
        
        failureCountAfterSuccess.Should().BeLessThan(failureCountAfterFailures);
        statisticsAfterSuccess[ruleKey].SuccessCount.Should().BeGreaterThan(0);
    }

    [Fact]
    public async Task ExecuteAsync_WithSuccessfulAsyncFunction_ShouldReturnResult()
    {
        // Arrange
        var ruleKey = "test_rule_async";
        var expectedResult = true;

        // Act
        var result = await _circuitBreaker.ExecuteAsync<bool>(ruleKey, async () =>
        {
            await Task.Delay(10);
            return expectedResult;
        });

        // Assert
        result.Should().Be(expectedResult);
    }

    [Fact]
    public async Task ExecuteAsync_WithFailingAsyncFunction_ShouldReturnFallbackResult()
    {
        // Arrange
        var ruleKey = "test_rule_async";
        var fallbackResult = false;

        // Act
        var result = await _circuitBreaker.ExecuteAsync<bool>(ruleKey, async () =>
        {
            await Task.Delay(10);
            throw new InvalidOperationException("Async test exception");
        }, fallbackResult);

        // Assert
        result.Should().Be(fallbackResult);
    }

    [Fact]
    public void GetStatistics_ShouldReturnCorrectInformation()
    {
        // Arrange
        var ruleKey1 = "rule1";
        var ruleKey2 = "rule2";

        // 执行一些操作
        _circuitBreaker.Execute<bool>(ruleKey1, () => true);
        _circuitBreaker.Execute<bool>(ruleKey1, () => throw new Exception("Test"), false);
        _circuitBreaker.Execute<bool>(ruleKey2, () => true);

        // Act
        var statistics = _circuitBreaker.GetStatistics();

        // Assert
        statistics.Should().ContainKey(ruleKey1);
        statistics.Should().ContainKey(ruleKey2);
        
        statistics[ruleKey1].SuccessCount.Should().Be(1);
        statistics[ruleKey1].FailureCount.Should().BeGreaterThan(0);
        statistics[ruleKey2].SuccessCount.Should().Be(1);
        statistics[ruleKey2].FailureCount.Should().Be(0);
    }

    [Fact]
    public void Reset_ShouldResetSpecificCircuitBreaker()
    {
        // Arrange
        var ruleKey = "test_rule";

        // 触发一些失败
        for (int i = 0; i < 5; i++)
        {
            _circuitBreaker.Execute<bool>(ruleKey, () => throw new Exception("Test"), false);
        }

        var statisticsBeforeReset = _circuitBreaker.GetStatistics();
        statisticsBeforeReset[ruleKey].FailureCount.Should().BeGreaterThan(0);

        // Act
        _circuitBreaker.Reset(ruleKey);

        // Assert
        var statisticsAfterReset = _circuitBreaker.GetStatistics();
        statisticsAfterReset[ruleKey].FailureCount.Should().Be(0);
        statisticsAfterReset[ruleKey].SuccessCount.Should().Be(0);
        statisticsAfterReset[ruleKey].IsOpen.Should().BeFalse();
    }

    [Fact]
    public void ResetAll_ShouldResetAllCircuitBreakers()
    {
        // Arrange
        var ruleKey1 = "rule1";
        var ruleKey2 = "rule2";

        // 触发一些失败
        _circuitBreaker.Execute<bool>(ruleKey1, () => throw new Exception("Test"), false);
        _circuitBreaker.Execute<bool>(ruleKey2, () => throw new Exception("Test"), false);

        var statisticsBeforeReset = _circuitBreaker.GetStatistics();
        statisticsBeforeReset[ruleKey1].FailureCount.Should().BeGreaterThan(0);
        statisticsBeforeReset[ruleKey2].FailureCount.Should().BeGreaterThan(0);

        // Act
        _circuitBreaker.ResetAll();

        // Assert
        var statisticsAfterReset = _circuitBreaker.GetStatistics();
        statisticsAfterReset[ruleKey1].FailureCount.Should().Be(0);
        statisticsAfterReset[ruleKey2].FailureCount.Should().Be(0);
    }

    [Fact]
    public void Execute_WithDifferentRuleKeys_ShouldMaintainSeparateStates()
    {
        // Arrange
        var ruleKey1 = "rule1";
        var ruleKey2 = "rule2";

        // Act - 让rule1失败，rule2成功
        _circuitBreaker.Execute<bool>(ruleKey1, () => throw new Exception("Test"), false);
        _circuitBreaker.Execute<bool>(ruleKey2, () => true, false);

        // Assert
        var statistics = _circuitBreaker.GetStatistics();
        
        statistics[ruleKey1].FailureCount.Should().BeGreaterThan(0);
        statistics[ruleKey1].SuccessCount.Should().Be(0);
        
        statistics[ruleKey2].FailureCount.Should().Be(0);
        statistics[ruleKey2].SuccessCount.Should().Be(1);
    }

    [Fact]
    public void CircuitBreakerStatistics_ShouldCalculateFailureRateCorrectly()
    {
        // Arrange
        var ruleKey = "test_rule";

        // 执行2次成功，3次失败
        _circuitBreaker.Execute<bool>(ruleKey, () => true);
        _circuitBreaker.Execute<bool>(ruleKey, () => true);
        _circuitBreaker.Execute<bool>(ruleKey, () => throw new Exception("Test"), false);
        _circuitBreaker.Execute<bool>(ruleKey, () => throw new Exception("Test"), false);
        _circuitBreaker.Execute<bool>(ruleKey, () => throw new Exception("Test"), false);

        // Act
        var statistics = _circuitBreaker.GetStatistics();

        // Assert
        var stats = statistics[ruleKey];
        stats.SuccessCount.Should().Be(2);
        stats.FailureCount.Should().Be(2); // 成功会减少失败计数
        stats.FailureRate.Should().BeApproximately(0.5, 0.1); // 大约50%失败率
    }
}
