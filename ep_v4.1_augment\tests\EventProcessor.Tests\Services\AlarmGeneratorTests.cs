using EventProcessor.Core.Models;
using EventProcessor.Core.Services;
using FluentAssertions;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;

namespace EventProcessor.Tests.Services;

public class AlarmGeneratorTests
{
    private readonly AlarmGenerator _alarmGenerator;
    private readonly Mock<ILogger<AlarmGenerator>> _mockLogger;

    public AlarmGeneratorTests()
    {
        _mockLogger = new Mock<ILogger<AlarmGenerator>>();
        _alarmGenerator = new AlarmGenerator(_mockLogger.Object);
    }

    [Fact]
    public async Task GenerateAlarmAsync_WithValidConfiguration_ShouldGenerateAlarm()
    {
        // Arrange
        var context = new EventContext
        {
            EventId = "EV001001",
            InstanceId = "test-instance-123",
            BusinessData = new Dictionary<string, object>
            {
                ["CardType"] = "月租卡",
                ["log_car_no"] = "京A12345",
                ["log_user_name"] = "张三",
                ["duration"] = 25
            },
            DeviceData = new Dictionary<string, object>
            {
                ["device_id"] = "BDN861290073715232"
            }
        };

        var config = new AlarmConfiguration
        {
            Fields = new[]
            {
                new FieldMapping
                {
                    AlarmFieldName = "详情",
                    SourceRuleType = "BusinessRules",
                    SourceFieldName = "CardType,log_car_no,log_user_name",
                    FormatTemplate = "[{CardType}][{log_car_no}][{log_user_name}]"
                },
                new FieldMapping
                {
                    AlarmFieldName = "事件",
                    SourceRuleType = "BusinessRules",
                    SourceFieldName = "duration",
                    FormatTemplate = "停留时间{duration}秒"
                },
                new FieldMapping
                {
                    AlarmFieldName = "设备",
                    SourceRuleType = "DeviceSignal",
                    SourceFieldName = "",
                    DefaultValue = "帮豆你门岗智能监测"
                }
            }
        };

        // Act
        var result = await _alarmGenerator.GenerateAlarmAsync(context, config);

        // Assert
        result.Should().NotBeNull();
        result.EventId.Should().Be("EV001001");
        result.InstanceId.Should().Be("test-instance-123");
        result.AlarmType.Should().Be("Alarm");
        result.Fields.Should().ContainKey("详情");
        result.Fields.Should().ContainKey("事件");
        result.Fields.Should().ContainKey("设备");
        
        result.Fields["详情"].Should().Be("[月租卡][京A12345][张三]");
        result.Fields["事件"].Should().Be("停留时间25秒");
        result.Fields["设备"].Should().Be("帮豆你门岗智能监测");
    }

    [Fact]
    public async Task GenerateAlarmAsync_WithMissingFields_ShouldUseDefaultValues()
    {
        // Arrange
        var context = new EventContext
        {
            EventId = "EV001001",
            InstanceId = "test-instance-123",
            BusinessData = new Dictionary<string, object>
            {
                ["CardType"] = "月租卡"
                // Missing other fields
            }
        };

        var config = new AlarmConfiguration
        {
            Fields = new[]
            {
                new FieldMapping
                {
                    AlarmFieldName = "详情",
                    SourceRuleType = "BusinessRules",
                    SourceFieldName = "CardType,log_car_no,log_user_name",
                    FormatTemplate = "[{CardType}][{log_car_no}][{log_user_name}]",
                    DefaultValue = "默认详情"
                },
                new FieldMapping
                {
                    AlarmFieldName = "未知字段",
                    SourceRuleType = "BusinessRules",
                    SourceFieldName = "non_existent_field",
                    DefaultValue = "默认值"
                }
            }
        };

        // Act
        var result = await _alarmGenerator.GenerateAlarmAsync(context, config);

        // Assert
        result.Should().NotBeNull();
        result.Fields["详情"].Should().Be("[月租卡][][]");
        result.Fields["未知字段"].Should().Be("默认值");
    }

    [Fact]
    public async Task GenerateAlarmAsync_WithSingleField_ShouldFormatCorrectly()
    {
        // Arrange
        var context = new EventContext
        {
            EventId = "EV001001",
            InstanceId = "test-instance-123",
            BusinessData = new Dictionary<string, object>
            {
                ["duration"] = 30
            }
        };

        var config = new AlarmConfiguration
        {
            Fields = new[]
            {
                new FieldMapping
                {
                    AlarmFieldName = "事件",
                    SourceRuleType = "BusinessRules",
                    SourceFieldName = "duration",
                    FormatTemplate = "停留时间{value}秒"
                }
            }
        };

        // Act
        var result = await _alarmGenerator.GenerateAlarmAsync(context, config);

        // Assert
        result.Fields["事件"].Should().Be("停留时间30秒");
    }

    [Fact]
    public async Task GenerateCancellationAsync_ShouldGenerateCancellationMessage()
    {
        // Arrange
        var context = new EventContext
        {
            EventId = "EV001001",
            InstanceId = "test-instance-123",
            BusinessData = new Dictionary<string, object>
            {
                ["CardType"] = "月租卡"
            }
        };

        var config = new AlarmConfiguration
        {
            Fields = new[]
            {
                new FieldMapping
                {
                    AlarmFieldName = "详情",
                    SourceRuleType = "BusinessRules",
                    SourceFieldName = "CardType"
                }
            }
        };

        var reason = "late_exclusion_condition_met";

        // Act
        var result = await _alarmGenerator.GenerateCancellationAsync(context, config, reason);

        // Assert
        result.Should().NotBeNull();
        result.EventId.Should().Be("EV001001");
        result.InstanceId.Should().Be("test-instance-123");
        result.AlarmType.Should().Be("Cancellation");
        result.CancellationReason.Should().Be(reason);
        result.Fields.Should().ContainKey("action");
        result.Fields.Should().ContainKey("reason");
        result.Fields.Should().ContainKey("timestamp");
        result.Fields["action"].Should().Be("cancel_alarm");
        result.Fields["reason"].Should().Be(reason);
    }

    [Fact]
    public void ValidateConfiguration_WithValidConfig_ShouldReturnValid()
    {
        // Arrange
        var config = new AlarmConfiguration
        {
            Fields = new[]
            {
                new FieldMapping
                {
                    AlarmFieldName = "详情",
                    SourceRuleType = "BusinessRules",
                    SourceFieldName = "CardType",
                    FormatTemplate = "{CardType}"
                }
            }
        };

        // Act
        var result = _alarmGenerator.ValidateConfiguration(config);

        // Assert
        result.IsValid.Should().BeTrue();
        result.Errors.Should().BeEmpty();
    }

    [Fact]
    public void ValidateConfiguration_WithInvalidSourceRuleType_ShouldReturnInvalid()
    {
        // Arrange
        var config = new AlarmConfiguration
        {
            Fields = new[]
            {
                new FieldMapping
                {
                    AlarmFieldName = "详情",
                    SourceRuleType = "InvalidType",
                    SourceFieldName = "CardType"
                }
            }
        };

        // Act
        var result = _alarmGenerator.ValidateConfiguration(config);

        // Assert
        result.IsValid.Should().BeFalse();
        result.Errors.Should().Contain(e => e.Contains("源规则类型无效"));
    }

    [Fact]
    public void ValidateConfiguration_WithEmptyAlarmFieldName_ShouldReturnInvalid()
    {
        // Arrange
        var config = new AlarmConfiguration
        {
            Fields = new[]
            {
                new FieldMapping
                {
                    AlarmFieldName = "",
                    SourceRuleType = "BusinessRules",
                    SourceFieldName = "CardType"
                }
            }
        };

        // Act
        var result = _alarmGenerator.ValidateConfiguration(config);

        // Assert
        result.IsValid.Should().BeFalse();
        result.Errors.Should().Contain("告警字段名称不能为空");
    }

    [Fact]
    public void ValidateConfiguration_WithNoFields_ShouldReturnWarning()
    {
        // Arrange
        var config = new AlarmConfiguration
        {
            Fields = null
        };

        // Act
        var result = _alarmGenerator.ValidateConfiguration(config);

        // Assert
        result.IsValid.Should().BeTrue();
        result.Warnings.Should().Contain("未配置字段映射，告警消息将为空");
    }

    [Fact]
    public void ValidateConfiguration_WithDangerousCustomTemplate_ShouldReturnWarning()
    {
        // Arrange
        var config = new AlarmConfiguration
        {
            CustomTemplate = "<script>alert('xss')</script>",
            Fields = new[]
            {
                new FieldMapping
                {
                    AlarmFieldName = "test",
                    SourceRuleType = "BusinessRules",
                    SourceFieldName = "field"
                }
            }
        };

        // Act
        var result = _alarmGenerator.ValidateConfiguration(config);

        // Assert
        result.IsValid.Should().BeTrue();
        result.Warnings.Should().Contain(w => w.Contains("潜在安全风险"));
    }

    [Fact]
    public async Task GenerateAlarmAsync_WithNumberFormatting_ShouldFormatCorrectly()
    {
        // Arrange
        var context = new EventContext
        {
            EventId = "EV001001",
            InstanceId = "test-instance-123",
            AIResultData = new Dictionary<string, object>
            {
                ["confidence"] = 0.8567
            }
        };

        var config = new AlarmConfiguration
        {
            Fields = new[]
            {
                new FieldMapping
                {
                    AlarmFieldName = "置信度",
                    SourceRuleType = "AIResultRules",
                    SourceFieldName = "confidence",
                    FormatTemplate = "{confidence:P2}"
                }
            }
        };

        // Act
        var result = await _alarmGenerator.GenerateAlarmAsync(context, config);

        // Assert
        result.Fields["置信度"].Should().Be("85.67%");
    }

    [Fact]
    public async Task GenerateAlarmAsync_WithExceptionInFieldExtraction_ShouldUseDefaultValue()
    {
        // Arrange
        var context = new EventContext
        {
            EventId = "EV001001",
            InstanceId = "test-instance-123",
            BusinessData = new Dictionary<string, object>()
        };

        var config = new AlarmConfiguration
        {
            Fields = new[]
            {
                new FieldMapping
                {
                    AlarmFieldName = "测试字段",
                    SourceRuleType = "BusinessRules",
                    SourceFieldName = "non_existent_field",
                    DefaultValue = "备用值"
                }
            }
        };

        // Act
        var result = await _alarmGenerator.GenerateAlarmAsync(context, config);

        // Assert
        result.Fields["测试字段"].Should().Be("备用值");
    }
}
