using EventProcessor.Core.Models;
using EventProcessor.Core.Validation;
using FluentAssertions;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;

namespace EventProcessor.Tests.Validation;

public class ConfigurationValidatorTests
{
    private readonly ConfigurationValidator _validator;
    private readonly Mock<ILogger<ConfigurationValidator>> _mockLogger;

    public ConfigurationValidatorTests()
    {
        _mockLogger = new Mock<ILogger<ConfigurationValidator>>();
        _validator = new ConfigurationValidator(_mockLogger.Object);
    }

    [Fact]
    public void ValidateConfiguration_WithValidConfiguration_ShouldReturnValid()
    {
        // Arrange
        var config = CreateValidConfiguration();

        // Act
        var result = _validator.ValidateConfiguration(config);

        // Assert
        result.IsValid.Should().BeTrue();
        result.Errors.Should().BeEmpty();
    }

    [Fact]
    public void ValidateConfiguration_WithInvalidOperator_ShouldReturnError()
    {
        // Arrange
        var config = CreateValidConfiguration();
        config.RuleConfiguration.BusinessRules![0].Conditions![0] = new Condition
        {
            FieldName = "amount",
            DataType = "string",
            Operator = "GreaterThan", // 字符串类型不支持GreaterThan
            Value = "100"
        };

        // Act
        var result = _validator.ValidateConfiguration(config);

        // Assert
        result.IsValid.Should().BeFalse();
        result.Errors.Should().Contain(e => e.Contains("数据类型 string 不支持操作符 GreaterThan"));
    }

    [Fact]
    public void ValidateConfiguration_WithInvalidDataType_ShouldReturnError()
    {
        // Arrange
        var config = CreateValidConfiguration();
        config.RuleConfiguration.BusinessRules![0].Conditions![0] = new Condition
        {
            FieldName = "amount",
            DataType = "invalid_type",
            Operator = "Equals",
            Value = "100"
        };

        // Act
        var result = _validator.ValidateConfiguration(config);

        // Assert
        result.IsValid.Should().BeFalse();
        result.Errors.Should().Contain(e => e.Contains("不支持的数据类型 invalid_type"));
    }

    [Fact]
    public void ValidateConfiguration_WithInvalidSourceRuleType_ShouldReturnError()
    {
        // Arrange
        var config = CreateValidConfiguration();
        config.RuleConfiguration.AlarmConfig.Fields![0] = new FieldMapping
        {
            AlarmFieldName = "test",
            SourceRuleType = "InvalidType",
            SourceFieldName = "field"
        };

        // Act
        var result = _validator.ValidateConfiguration(config);

        // Assert
        result.IsValid.Should().BeFalse();
        result.Errors.Should().Contain(e => e.Contains("不支持的源规则类型 InvalidType"));
    }

    [Fact]
    public void ValidateConfiguration_WithInvalidRegexOperator_ShouldReturnError()
    {
        // Arrange
        var config = CreateValidConfiguration();
        config.RuleConfiguration.BusinessRules![0].Conditions![0] = new Condition
        {
            FieldName = "pattern",
            DataType = "string",
            Operator = "Regex",
            Value = "[invalid regex" // 无效的正则表达式
        };

        // Act
        var result = _validator.ValidateConfiguration(config);

        // Assert
        result.IsValid.Should().BeFalse();
        result.Errors.Should().Contain(e => e.Contains("正则表达式格式错误"));
    }

    [Fact]
    public void ValidateConfiguration_WithInvalidBetweenOperator_ShouldReturnError()
    {
        // Arrange
        var config = CreateValidConfiguration();
        config.RuleConfiguration.BusinessRules![0].Conditions![0] = new Condition
        {
            FieldName = "amount",
            DataType = "number",
            Operator = "Between",
            Value = "100" // 应该是 "100,200" 格式
        };

        // Act
        var result = _validator.ValidateConfiguration(config);

        // Assert
        result.IsValid.Should().BeFalse();
        result.Errors.Should().Contain(e => e.Contains("需要两个值，格式：value1,value2"));
    }

    [Fact]
    public void ValidateConfiguration_WithMissingRequiredFields_ShouldReturnErrors()
    {
        // Arrange
        var config = new EventConfiguration
        {
            EventId = "", // 空的必填字段
            EventName = "",
            EvaluationStrategy = "BusinessOnly",
            Priority = "P1",
            CommId = "",
            PositionId = "",
            CompanyName = "",
            AlarmGracePeriodSeconds = 3,
            EnableAlarmCancellation = true,
            CorrelationTimeWindow = "minute",
            DeviceSignal = null,
            RuleConfiguration = new RuleConfiguration
            {
                AlarmConfig = new AlarmConfiguration()
            }
        };

        // Act
        var result = _validator.ValidateConfiguration(config);

        // Assert
        result.IsValid.Should().BeFalse();
        result.Errors.Should().Contain(e => e.Contains("EventId 不能为空"));
        result.Errors.Should().Contain(e => e.Contains("EventName 不能为空"));
        result.Errors.Should().Contain(e => e.Contains("CommId 不能为空"));
        result.Errors.Should().Contain(e => e.Contains("PositionId 不能为空"));
    }

    [Fact]
    public void ValidateConfiguration_WithInconsistentEvaluationStrategy_ShouldReturnErrors()
    {
        // Arrange
        var config = CreateValidConfiguration();
        config = config with 
        { 
            EvaluationStrategy = "AI",
            AIPrompt = null, // AI策略需要AI提示词
            RuleConfiguration = config.RuleConfiguration with
            {
                AIResultRules = null // AI策略需要AI结果规则
            }
        };

        // Act
        var result = _validator.ValidateConfiguration(config);

        // Assert
        result.IsValid.Should().BeFalse();
        result.Errors.Should().Contain(e => e.Contains("AI 策略需要配置 AI 提示词"));
        result.Errors.Should().Contain(e => e.Contains("AI 策略需要配置 AI 结果规则"));
    }

    [Fact]
    public void ValidateConfiguration_WithInvalidMqttTopic_ShouldReturnWarning()
    {
        // Arrange
        var originalConfig = CreateValidConfiguration();
        var config = originalConfig with
        {
            DeviceSignal = originalConfig.DeviceSignal! with
            {
                Topics = new[] { "invalid#topic#format" }
            }
        };

        // Act
        var result = _validator.ValidateConfiguration(config);

        // Assert
        result.Warnings.Should().Contain(w => w.Contains("主题格式可能不正确"));
    }

    [Fact]
    public void ValidateConfiguration_WithDangerousCustomTemplate_ShouldReturnWarning()
    {
        // Arrange
        var originalConfig = CreateValidConfiguration();
        var config = originalConfig with
        {
            RuleConfiguration = originalConfig.RuleConfiguration! with
            {
                AlarmConfig = originalConfig.RuleConfiguration.AlarmConfig! with
                {
                    CustomTemplate = "<script>alert('xss')</script>"
                }
            }
        };

        // Act
        var result = _validator.ValidateConfiguration(config);

        // Assert
        result.Warnings.Should().Contain(w => w.Contains("潜在安全风险"));
    }

    [Fact]
    public void ValidateConfiguration_WithMissingConditionDescription_ShouldReturnWarning()
    {
        // Arrange
        var config = CreateValidConfiguration();
        config.RuleConfiguration.BusinessRules![0].Conditions![0] = new Condition
        {
            FieldName = "amount",
            DataType = "number",
            Operator = "GreaterThan",
            Value = "100",
            Description = null // 缺少描述
        };

        // Act
        var result = _validator.ValidateConfiguration(config);

        // Assert
        result.Warnings.Should().Contain(w => w.Contains("建议为条件添加描述信息"));
    }

    [Theory]
    [InlineData("P6")] // 无效优先级
    [InlineData("invalid")]
    public void ValidateConfiguration_WithInvalidPriority_ShouldReturnError(string priority)
    {
        // Arrange
        var config = CreateValidConfiguration();
        config = config with { Priority = priority };

        // Act
        var result = _validator.ValidateConfiguration(config);

        // Assert
        result.IsValid.Should().BeFalse();
        result.Errors.Should().Contain(e => e.Contains($"不支持的优先级: {priority}"));
    }

    [Theory]
    [InlineData("InvalidStrategy")]
    [InlineData("")]
    public void ValidateConfiguration_WithInvalidEvaluationStrategy_ShouldReturnError(string strategy)
    {
        // Arrange
        var config = CreateValidConfiguration();
        config = config with { EvaluationStrategy = strategy };

        // Act
        var result = _validator.ValidateConfiguration(config);

        // Assert
        result.IsValid.Should().BeFalse();
        result.Errors.Should().Contain(e => e.Contains($"不支持的评估策略: {strategy}"));
    }

    private EventConfiguration CreateValidConfiguration()
    {
        return new EventConfiguration
        {
            EventId = "EV001001",
            EventName = "测试事件",
            EvaluationStrategy = "BusinessOnly",
            Priority = "P1",
            CommId = "101013",
            PositionId = "P002Test",
            CompanyName = "test",
            AlarmGracePeriodSeconds = 3,
            EnableAlarmCancellation = true,
            CorrelationTimeWindow = "minute",
            DeviceSignal = new DeviceSignalConfiguration
            {
                Topics = new[] { "device/test/event" },
                TriggerField = "I2",
                TriggerValues = new Dictionary<string, string>
                {
                    ["true"] = "0",
                    ["false"] = "1"
                },
                HoldingTimeoutSec = 30
            },
            RuleConfiguration = new RuleConfiguration
            {
                BusinessRules = new[]
                {
                    new BusinessRuleGroup
                    {
                        SourceTopic = "business/test/data",
                        LogicOperator = "AND",
                        Conditions = new[]
                        {
                            new Condition
                            {
                                FieldName = "amount",
                                DataType = "number",
                                Operator = "GreaterThan",
                                Value = "100",
                                Description = "金额大于100"
                            }
                        }
                    }
                },
                ExclusionRules = new[]
                {
                    new ExclusionRuleGroup
                    {
                        SourceType = "MQTT",
                        SourceTopic = "exclusion/test/data",
                        LogicOperator = "OR",
                        Conditions = new[]
                        {
                            new Condition
                            {
                                FieldName = "excluded",
                                DataType = "string",
                                Operator = "Equals",
                                Value = "true",
                                Description = "排除标志"
                            }
                        }
                    }
                },
                AlarmConfig = new AlarmConfiguration
                {
                    Fields = new[]
                    {
                        new FieldMapping
                        {
                            AlarmFieldName = "详情",
                            SourceRuleType = "BusinessRules",
                            SourceFieldName = "amount",
                            FormatTemplate = "金额: {amount}"
                        }
                    }
                }
            }
        };
    }
}
