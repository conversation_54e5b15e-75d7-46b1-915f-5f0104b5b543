Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = ********
MinimumVisualStudioVersion = 10.0.40219.1
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "ep_cfg_gui", "ep_cfg_gui", "{B7E6BA7C-9464-1FE0-5C6E-5713F14276C4}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "EPConfigTool", "ep_cfg_gui\EPConfigTool\EPConfigTool.csproj", "{551718BC-4425-B77E-F38B-DB5D4DAEFA5F}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "AIProcessor", "AIProcessor\AIProcessor.csproj", "{A1A1A1A1-1111-1111-1111-111111111111}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "EventGeneratorGUI", "utils\EventGeneratorGUI\EventGenerator.csproj", "{B2B2B2B2-2222-2222-2222-222222222222}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "EPConfigTool_Main", "EPConfigTool\src\EPConfigTool\EPConfigTool.csproj", "{C3C3C3C3-3333-3333-3333-333333333333}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "EventProcessor.Core", "ep_v4.1_augment\src\EventProcessor.Core\EventProcessor.Core.csproj", "{D4D4D4D4-4444-4444-4444-444444444444}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "EventProcessor.Host", "ep_v4.1_augment\src\EventProcessor.Host\EventProcessor.Host.csproj", "{E5E5E5E5-5555-5555-5555-555555555555}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "EventProcessor.Tests", "ep_v4.1_augment\tests\EventProcessor.Tests\EventProcessor.Tests.csproj", "{F6F6F6F6-6666-6666-6666-666666666666}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "EventProcessor.IntegrationTests", "ep_v4.1_augment\tests\EventProcessor.IntegrationTests\EventProcessor.IntegrationTests.csproj", "{A7A7A7A7-**************-************}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{551718BC-4425-B77E-F38B-DB5D4DAEFA5F}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{551718BC-4425-B77E-F38B-DB5D4DAEFA5F}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{551718BC-4425-B77E-F38B-DB5D4DAEFA5F}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{551718BC-4425-B77E-F38B-DB5D4DAEFA5F}.Release|Any CPU.Build.0 = Release|Any CPU
		{A1A1A1A1-1111-1111-1111-111111111111}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A1A1A1A1-1111-1111-1111-111111111111}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A1A1A1A1-1111-1111-1111-111111111111}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A1A1A1A1-1111-1111-1111-111111111111}.Release|Any CPU.Build.0 = Release|Any CPU
		{B2B2B2B2-2222-2222-2222-222222222222}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B2B2B2B2-2222-2222-2222-222222222222}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B2B2B2B2-2222-2222-2222-222222222222}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B2B2B2B2-2222-2222-2222-222222222222}.Release|Any CPU.Build.0 = Release|Any CPU
		{C3C3C3C3-3333-3333-3333-333333333333}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C3C3C3C3-3333-3333-3333-333333333333}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C3C3C3C3-3333-3333-3333-333333333333}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C3C3C3C3-3333-3333-3333-333333333333}.Release|Any CPU.Build.0 = Release|Any CPU
		{D4D4D4D4-4444-4444-4444-444444444444}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{D4D4D4D4-4444-4444-4444-444444444444}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{D4D4D4D4-4444-4444-4444-444444444444}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{D4D4D4D4-4444-4444-4444-444444444444}.Release|Any CPU.Build.0 = Release|Any CPU
		{E5E5E5E5-5555-5555-5555-555555555555}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{E5E5E5E5-5555-5555-5555-555555555555}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{E5E5E5E5-5555-5555-5555-555555555555}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{E5E5E5E5-5555-5555-5555-555555555555}.Release|Any CPU.Build.0 = Release|Any CPU
		{F6F6F6F6-6666-6666-6666-666666666666}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F6F6F6F6-6666-6666-6666-666666666666}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F6F6F6F6-6666-6666-6666-666666666666}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F6F6F6F6-6666-6666-6666-666666666666}.Release|Any CPU.Build.0 = Release|Any CPU
		{A7A7A7A7-**************-************}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A7A7A7A7-**************-************}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A7A7A7A7-**************-************}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A7A7A7A7-**************-************}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{551718BC-4425-B77E-F38B-DB5D4DAEFA5F} = {B7E6BA7C-9464-1FE0-5C6E-5713F14276C4}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {D6D1CF2A-40BC-46A4-8CC7-F9E4B43C4BA5}
	EndGlobalSection
EndGlobal
