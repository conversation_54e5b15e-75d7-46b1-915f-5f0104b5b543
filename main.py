#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Event Processor 主程序

1. 渐进式信息收集机制
2. 多事件AI整合分析功能  
3. 排除匹配信息配置
event_processor
核心特性：
- 支持同一图片目录多事件聚合AI分析
"""

import os
import sys
import signal
import logging
import argparse
import time
import threading
from typing import Optional

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = current_dir
sys.path.insert(0, project_root)

from src.core.config_manager import ConfigManager, ConfigValidationError
from src.core.multi_event_processor import MultiEventProcessor


# 全局变量
processor: Optional[MultiEventProcessor] = None
shutdown_event = threading.Event()
current_log_level = logging.INFO


def setup_logging(log_filename: str = "event_processor_v3.log"):
    """设置日志配置，支持自定义日志文件名"""
    handlers = [logging.StreamHandler(sys.stdout)]
    
    # 添加文件处理器
    try:
        file_handler = logging.FileHandler(log_filename, encoding='utf-8')
        file_handler.setFormatter(
            logging.Formatter('%(asctime)s - %(levelname)s - %(message)s @ %(name)s:%(lineno)d')
        )
        handlers.append(file_handler)
        print(f"日志文件: {log_filename}")
    except Exception as e:
        print(f"警告: 无法创建日志文件 {log_filename}: {e}")
    
    logging.basicConfig(
        level=logging.INFO, 
        format='%(asctime)s - %(levelname)s - %(message)s @ %(name)s:%(lineno)d',
        datefmt='%H:%M:%S',
        handlers=handlers
    )
    return True


def setup_signal_handlers():
    """设置增强的信号处理器"""
    logger = logging.getLogger(__name__)
    
    try:
        signal.signal(signal.SIGINT, enhanced_signal_handler)
        signal.signal(signal.SIGTERM, enhanced_signal_handler)
        
        # Windows下可能不支持这些信号，需要检查
        if hasattr(signal, 'SIGHUP'):
            signal.signal(signal.SIGHUP, enhanced_signal_handler)
        if hasattr(signal, 'SIGUSR1'):
            signal.signal(signal.SIGUSR1, enhanced_signal_handler)
        if hasattr(signal, 'SIGUSR2'):
            signal.signal(signal.SIGUSR2, enhanced_signal_handler)
            
        logger.info("增强的信号处理器设置完成")
    except Exception as e:
        logger.warning(f"设置信号处理器时发生错误: {e}")


def enhanced_signal_handler(signum, frame):
    """增强的统一信号处理函数"""
    logger = logging.getLogger(__name__)
    
    try:
        signal_name = signal.Signals(signum).name if hasattr(signal.Signals, str(signum)) else str(signum)
        logger.info(f"接收到信号: {signal_name} ({signum})")
        
        if signum in (signal.SIGINT, signal.SIGTERM):
            logger.info("准备优雅关闭服务...")
            shutdown_event.set()
            
            # 立即停止处理器
            global processor
            if processor:
                try:
                    logger.info("正在停止事件处理器...")
                    processor.stop()
                    logger.info("事件处理器已停止")
                except Exception as e:
                    logger.error(f"停止事件处理器时发生错误: {e}")
            
            logger.info("程序已优雅关闭")
            
        elif hasattr(signal, 'SIGHUP') and signum == signal.SIGHUP:
            logger.info("接收到配置重载信号 (暂未实现)")
            
        elif hasattr(signal, 'SIGUSR1') and signum == signal.SIGUSR1:
            logger.info("接收到状态报告信号")
            handle_status_report()
            
        elif hasattr(signal, 'SIGUSR2') and signum == signal.SIGUSR2:
            logger.info("接收到日志级别切换信号")
            handle_toggle_log_level()
            
    except Exception as e:
        logger.error(f"信号处理过程中发生错误: {e}")


def handle_status_report():
    """处理状态报告"""
    logger = logging.getLogger(__name__)
    
    try:
        logger.info("=== 服务状态报告 ===")
        logger.info(f"进程ID: {os.getpid()}")
        logger.info(f"关闭事件状态: {'已设置' if shutdown_event.is_set() else '未设置'}")
        logger.info(f"事件处理器: {'运行中' if processor else '未运行'}")
        
        # 尝试获取系统资源信息
        try:
            import psutil
            process = psutil.Process()
            memory_info = process.memory_info()
            logger.info(f"内存使用: {memory_info.rss / 1024 / 1024:.2f} MB")
            logger.info(f"CPU使用: {process.cpu_percent():.2f}%")
        except ImportError:
            logger.info("系统资源信息: psutil未安装，无法获取详细信息")
        except Exception as e:
            logger.warning(f"获取系统资源信息失败: {e}")
        
        logger.info("=== 状态报告结束 ===")
        
    except Exception as e:
        logger.error(f"生成状态报告失败: {e}")


def handle_toggle_log_level():
    """处理日志级别切换"""
    global current_log_level
    logger = logging.getLogger(__name__)
    
    try:
        if current_log_level == logging.INFO:
            current_log_level = logging.DEBUG
            level_name = "DEBUG"
        else:
            current_log_level = logging.INFO
            level_name = "INFO"
        
        logging.getLogger().setLevel(current_log_level)
        logger.info(f"日志级别已切换为: {level_name}")
        
    except Exception as e:
        logger.error(f"切换日志级别失败: {e}")


def check_processor_health(processor):
    """检查处理器健康状态"""
    logger = logging.getLogger(__name__)
    
    try:
        if not processor:
            return False
        
        # 使用处理器自己的健康检查方法
        if hasattr(processor, 'is_healthy'):
            return processor.is_healthy()
        
        # 如果没有专门的健康检查方法，假设处理器存在就是健康的
        return True
        
    except Exception as e:
        logger.error(f"健康检查失败: {e}")
        return False


def create_and_load_config(env_file, cli_args=None, max_retries=3):
    """创建配置管理器并加载配置（V3统一配置架构）
    
    单一职责：配置管理（创建+加载）
    """
    logger = logging.getLogger(__name__)
    
    # 创建配置管理器（带重试机制）
    for attempt in range(max_retries):
        try:
            logger.info(f"尝试初始化配置管理器 (第 {attempt + 1}/{max_retries} 次) - V3统一配置架构")
            config_manager = ConfigManager(env_file, cli_args)
            logger.info("配置管理器初始化成功")
            break
            
        except FileNotFoundError as e:
            error_msg = f"配置文件未找到: {e.filename}"
            logger.error(error_msg)
            if attempt == max_retries - 1:
                return None, None, error_msg
            logger.info(f"等待 {(attempt + 1) * 5} 秒后重试...")
            time.sleep((attempt + 1) * 5)
            
        except ValueError as e:
            error_msg = f"配置值类型错误: {str(e)}"
            logger.error(error_msg)
            return None, None, error_msg
            
        except Exception as e:
            error_msg = f"配置管理器初始化失败: {type(e).__name__}: {str(e)}"
            logger.error(error_msg)
            if attempt == max_retries - 1:
                return None, None, error_msg
            logger.info(f"等待 {(attempt + 1) * 5} 秒后重试...")
            time.sleep((attempt + 1) * 5)
    else:
        return None, None, "配置管理器初始化失败，已达到最大重试次数"
    
    # 加载配置
    logger.info("加载配置文件...")
    try:
        events_config = config_manager.load_config()
        logger.info(f"配置加载成功，共加载了{len(events_config)}个事件配置")
        return config_manager, events_config, None
    except Exception as e:
        error_msg = f"配置加载失败: {e}"
        logger.error(error_msg)
        return None, None, error_msg


def create_event_processor(config_manager, events_config):
    """创建并启动事件处理器
    
    单一职责：事件处理器创建和启动
    """
    logger = logging.getLogger(__name__)
    
    try:
        logger.info("初始化多事件处理器...\t\t")
        processor = MultiEventProcessor(config_manager, events_config)
        
        if processor.start():
            logger.info("多事件处理器启动成功\t\t")
            return processor, None
        else:
            error_msg = "多事件处理器启动失败"
            logger.error(error_msg)
            return None, error_msg
            
    except Exception as e:
        error_msg = f"启动处理器时发生异常: {type(e).__name__}: {str(e)}"
        logger.error(error_msg, exc_info=True)
        return None, error_msg


def run_service_loop(processor):
    """运行服务主循环
    
    单一职责：服务运行控制
    """
    logger = logging.getLogger(__name__)
    
    try:
        logger.info("启动主循环...\t\t\t")
        processor.run_main_loop()
        return 0
    except KeyboardInterrupt:
        logger.info("接收到键盘中断信号")
        return 0
    except Exception as e:
        logger.error(f"主循环异常: {type(e).__name__}: {str(e)}")
        return 1
    finally:
        logger.info("停止事件处理器...")
        processor.stop()


def parse_arguments():
    """解析命令行参数"""
    try:
        parser = argparse.ArgumentParser(description='Event Processor - 多事件智能处理系统')
    except Exception as e:
        logger.error(f"解析命令行参数失败: {e}")
        exit(1)
    try:
        parser.add_argument('--env', required=True, help='环境变量文件路径（.env文件）')
    except Exception as e:
        logger.error(f"解析命令行参数--env失败: {e}")
        exit(1)
    # V3统一配置架构：移除--events-ini参数
    try:
        parser.add_argument('--debug', action='store_true', help='启用调试模式')
    except Exception as e:
        logger.error(f"解析命令行参数--debug失败: {e}")
        exit(1)
    try:
        parser.add_argument('-t', '--test', action='store_true', help='配置测试模式：仅验证.env配置，不启动服务')
    except Exception as e:
        logger.error(f"解析命令行参数-t失败: {e}")
        exit(1)
    try:
        parser.add_argument('-l', '--log', help='指定日志文件名（默认: event_processor_v3.log）')
    except Exception as e:
        logger.error(f"解析命令行参数-l失败: {e}")
        exit(1)
    
    return parser.parse_args()


def validate_files(env_file):
    """验证配置文件（V3统一配置架构）"""
    validation_result = {
        'valid': True,
        'errors': [],
        'warnings': [],
        'solutions': []
    }
    
    # 检查环境变量文件
    if not os.path.exists(env_file):
        validation_result['valid'] = False
        validation_result['errors'].append(f"环境变量文件不存在: {env_file}")
        
        # 检查示例文件
        example_env = env_file + '.example'
        unified_example = env_file.replace('.env', '.env.unified.example')
        
        if os.path.exists(unified_example):
            validation_result['solutions'].append(f"发现V3统一配置示例文件 {unified_example}")
            validation_result['solutions'].append(f"命令: copy \"{unified_example}\" \"{env_file}\"")
        elif os.path.exists(example_env):
            validation_result['solutions'].append(f"发现示例文件 {example_env}，可复制并重命名为 {env_file}")
            validation_result['solutions'].append(f"命令: copy \"{example_env}\" \"{env_file}\"")
            validation_result['solutions'].append("注意：V3版本已将AI提示词合并到.env文件中，请参考.env.unified.example")
        else:
            validation_result['solutions'].append(f"请创建环境变量文件: {env_file}")
            validation_result['solutions'].append("参考项目文档中的V3统一配置说明")
    else:
        # 检查文件可读性
        try:
            with open(env_file, 'r', encoding='utf-8') as f:
                content = f.read()
                if not content.strip():
                    validation_result['warnings'].append(f"环境变量文件 {env_file} 为空")
                    
                # 检查是否包含AI提示词配置
                if 'EP_AI_PROMPT' not in content:
                    validation_result['warnings'].append(f"V3配置提示：未发现AI提示词配置（EVENT_ID_*_EP_AI_PROMPT）")
                    validation_result['solutions'].append("请参考.env.unified.example中的V3统一配置格式")
                    
        except PermissionError:
            validation_result['errors'].append(f"无权限读取环境变量文件: {env_file}")
            validation_result['solutions'].append("请检查文件权限")
        except UnicodeDecodeError:
            validation_result['warnings'].append(f"环境变量文件 {env_file} 编码格式可能有问题")
            validation_result['solutions'].append("请确保文件使用UTF-8编码")
    
    return validation_result


def log_validation_result(result):
    """记录验证结果的详细日志"""
    logger = logging.getLogger(__name__)
    
    if result['valid']:
        logger.info("配置文件验证通过")
        if result['warnings']:
            for warning in result['warnings']:
                logger.warning(warning)
    else:
        logger.error("配置文件验证失败:")
        for error in result['errors']:
            logger.error(f"  - {error}")
        
        if result['solutions']:
            logger.info("建议解决方案:")
            for solution in result['solutions']:
                logger.info(f"- {solution}")


# 函数已合并到 create_and_load_config() 中


def validate_config_values(config_manager):
    """验证配置值的类型和有效性"""
    logger = logging.getLogger(__name__)
    validation_errors = []
    
    # 配置项到文件和说明的映射
    config_guidance = {
        'get_comm_id': {
            'env_var': 'COMM_ID',
            'description': '小区编号',
            'example': 'COMM_ID=12345',
            'file': '.env文件'
        },
        'get_position_id': {
            'env_var': 'POSITION_ID', 
            'description': '点位ID',
            'example': 'POSITION_ID=POS001',
            'file': '.env文件'
        },
        'get_event_ids': {
            'env_var': 'EVENT_IDS',
            'description': '事件ID列表（JSON格式）',
            'example': 'EVENT_IDS=["event1", "event2"]',
            'file': '.env文件'
        },
        'get_mqtt_config': {
            'env_var': 'MQTT_BROKER_HOST等',
            'description': 'MQTT服务器配置',
            'example': 'MQTT_BROKER_HOST=localhost\nMQTT_BROKER_PORT=1883\nMQTT_USERNAME=user\nMQTT_PASSWORD=pass',
            'file': '.env文件'
        }
    }
    
    try:
        # 验证COMM_ID
        try:
            comm_id = config_manager.get_comm_id()
            if not isinstance(comm_id, str) or not comm_id.strip():
                logger.error(f"COMM_ID当前值: {comm_id} ({type(comm_id)}), 来源: config_manager.global_config, os.environ: {os.environ.get('COMM_ID')}")
                validation_errors.append(f"COMM_ID配置错误: 期望非空字符串，实际获得: {type(comm_id).__name__}({repr(comm_id)})")
        except AttributeError:
            guidance = config_guidance['get_comm_id']
            validation_errors.append(f"FAIL 配置方法缺失: {guidance['description']}获取方法不存在")
            validation_errors.append(f"[TIP] 解决方案: 请在{guidance['file']}中添加: {guidance['example']}")
        
        # 验证POSITION_ID
        try:
            position_id = config_manager.get_position_id()
            if not isinstance(position_id, str) or not position_id.strip():
                logger.error(f"POSITION_ID当前值: {position_id} ({type(position_id)}), 来源: config_manager.global_config, os.environ: {os.environ.get('POSITION_ID')}")
                validation_errors.append(f"POSITION_ID配置错误: 期望非空字符串，实际获得: {type(position_id).__name__}({repr(position_id)})")
        except AttributeError:
            guidance = config_guidance['get_position_id']
            validation_errors.append(f"FAIL 配置方法缺失: {guidance['description']}获取方法不存在")
            validation_errors.append(f"[TIP] 解决方案: 请在{guidance['file']}中添加: {guidance['example']}")
        
        # 验证EVENT_IDS
        try:
            event_ids = config_manager.get_event_ids()
            if not isinstance(event_ids, list) or not event_ids:
                logger.error(f"EVENT_IDS当前值: {event_ids} ({type(event_ids)}), 来源: config_manager.global_config, os.environ: {os.environ.get('EVENT_IDS')}")
                validation_errors.append(f"EVENT_IDS配置错误: 期望非空列表，实际获得: {type(event_ids).__name__}({repr(event_ids)})")
        except AttributeError:
            guidance = config_guidance['get_event_ids']
            validation_errors.append(f"FAIL 配置方法缺失: {guidance['description']}获取方法不存在")
            validation_errors.append(f"[TIP] 解决方案: 请在{guidance['file']}中添加: {guidance['example']}")
        
        # 验证MQTT配置
        try:
            mqtt_config = config_manager.get_mqtt_config()
            required_mqtt_keys = ['host', 'port', 'username', 'password']
            for key in required_mqtt_keys:
                if key not in mqtt_config:
                    validation_errors.append(f"MQTT配置缺少必需字段: {key}")
                elif key == 'port':
                    if not isinstance(mqtt_config[key], int) or not (1 <= mqtt_config[key] <= 65535):
                        validation_errors.append(f"MQTT端口配置错误: 期望1-65535的整数，实际获得: {type(mqtt_config[key]).__name__}({repr(mqtt_config[key])})")
        except AttributeError:
            guidance = config_guidance['get_mqtt_config']
            validation_errors.append(f"FAIL 配置方法缺失: {guidance['description']}获取方法不存在")
            validation_errors.append(f"[TIP] 解决方案: 请在{guidance['file']}中添加:")
            validation_errors.append(f"   {guidance['example']}")
        
    except Exception as e:
        validation_errors.append(f"[WARNING] 配置验证过程中发生异常: {type(e).__name__}: {str(e)}")
        validation_errors.append(f"[TIP] 建议: 请检查配置文件格式是否正确，确保所有必需的配置项都已设置")
    
    return validation_errors


def comprehensive_config_test(env_file, debug=False):
    """
    全面的配置测试模式（V3统一配置架构）
    检查.env配置（包含AI提示词），报告详细的配置状态
    """
    logger = logging.getLogger(__name__)
    
    print("=" * 80)
    print("Event Processor V3统一配置测试报告")
    print("=" * 80)
    print(f"环境变量文件: {env_file}")
    print("配置架构: V3统一配置（AI提示词在.env文件中）")
    print(f"调试模式: {'启用' if debug else '禁用'}")
    print("=" * 80)
    
    # 阶段1: 文件存在性和基本验证
    print("\n[FILE] 阶段1: 配置文件基本检查")
    print("-" * 40)
    
    file_validation = validate_files(env_file)
    if not file_validation['valid']:
        print("FAIL 配置文件验证失败:")
        for error in file_validation['errors']:
            print(f"   - {error}")
        if file_validation['solutions']:
            print("\n[TIP] 建议解决方案:")
            for solution in file_validation['solutions']:
                print(f"   - {solution}")
        return 1
    else:
        print("PASS 配置文件存在性检查通过")
    
    # 阶段2: 配置管理器初始化
    print("\n[CONFIG]  阶段2: 配置管理器初始化")
    print("-" * 40)
    
    try:
        config_manager = ConfigManager(env_file)
        print("PASS 配置管理器初始化成功")
    except Exception as e:
        print(f"FAIL 配置管理器初始化失败: {e}")
        return 1
    
    # 阶段3: 配置加载和解析
    print("\n[PARSE] 阶段3: 配置加载和解析")
    print("-" * 40)
    
    try:
        events_config = config_manager.load_config()
        event_count = len(events_config)
        print(f"PASS 配置加载成功，发现 {event_count} 个事件配置")
    except Exception as e:
        print(f"FAIL 配置加载失败: {e}")
        return 1
    
    # 阶段4: 全局配置验证
    print("\n[GLOBAL] 阶段4: 全局配置验证")
    print("-" * 40)
    
    global_issues = []
    
    # 检查必需的全局配置
    required_global_checks = [
        ('COMM_ID', 'get_comm_id', '小区编号'),
        ('POSITION_ID', 'get_position_id', '点位编号'),
        ('EVENT_IDS', 'get_event_ids', '事件ID列表'),
        ('MQTT配置', 'get_mqtt_config', 'MQTT服务器配置'),
    ]
    
    for config_name, method_name, description in required_global_checks:
        try:
            if hasattr(config_manager, method_name):
                value = getattr(config_manager, method_name)()
                if config_name == 'EVENT_IDS':
                    if isinstance(value, list) and len(value) > 0:
                        print(f"PASS {description}: {value}")
                    else:
                        print(f"FAIL {description}: 期望非空列表，实际: {type(value).__name__}({value})")
                        global_issues.append(f"{config_name}配置错误")
                elif config_name == 'MQTT配置':
                    if isinstance(value, dict) and 'host' in value:
                        print(f"PASS {description}: 主机={value.get('host')}, 端口={value.get('port')}")
                    else:
                        print(f"FAIL {description}: 配置不完整")
                        global_issues.append(f"{config_name}配置错误")
                else:
                    if isinstance(value, str) and value.strip():
                        print(f"PASS {description}: {value}")
                    else:
                        print(f"FAIL {description}: 期望非空字符串，实际: {type(value).__name__}({value})")
                        global_issues.append(f"{config_name}配置错误")
            else:
                print(f"FAIL {description}: 配置方法不存在")
                global_issues.append(f"{config_name}方法缺失")
        except Exception as e:
            print(f"FAIL {description}: 检查时发生错误 - {e}")
            global_issues.append(f"{config_name}检查异常")
    
    # 阶段5: 逐个事件配置验证
    print(f"\n[EVENT] 阶段5: 事件配置详细验证 ({event_count}个事件)")
    print("-" * 40)
    
    event_issues = {}
    
    for event_id, event_config in events_config.items():
        print(f"\n[CHECK] 检查事件: {event_id}")
        issues = []
        
        # 检查必需配置
        required_event_configs = [
            'EP_START_DEVICE_EVENT_TOPIC',
            'EP_START_FIELD_FROM_DEVICE_EVENT',
            'EP_POSITION_START_VALUE_FROM_DEVICE_EVENT',
            'EP_PV_HOLDING_TIMEOUT'
        ]
        
        for config_key in required_event_configs:
            if config_key in event_config:
                value = event_config[config_key]
                if config_key == 'EP_PV_HOLDING_TIMEOUT':
                    if isinstance(value, int) and value > 0:
                        print(f"   PASS {config_key}: {value}秒")
                    else:
                        print(f"   FAIL {config_key}: 期望正整数，实际: {type(value).__name__}({value})")
                        issues.append(f"{config_key}类型错误")
                elif config_key in ['EP_START_DEVICE_EVENT_TOPIC', 'EP_POSITION_START_VALUE_FROM_DEVICE_EVENT']:
                    if isinstance(value, (list, dict)):
                        print(f"   PASS {config_key}: {value}")
                    else:
                        print(f"   FAIL {config_key}: JSON解析可能失败，实际: {type(value).__name__}")
                        issues.append(f"{config_key}格式错误")
                else:
                    if value and str(value).strip():
                        print(f"   PASS {config_key}: {value}")
                    else:
                        print(f"   FAIL {config_key}: 值为空")
                        issues.append(f"{config_key}为空")
            else:
                print(f"   FAIL {config_key}: 配置缺失")
                issues.append(f"{config_key}缺失")
        
        # 检查可选配置
        optional_configs = [
            'EP_PV_ALARM_TOPIC',
            'EP_PV_DETAIL_INFO_TOPIC',
            'EP_PV_DETAIL_INFO_FIELDS'
        ]
        
        for config_key in optional_configs:
            if config_key in event_config:
                value = event_config[config_key]
                if config_key == 'EP_PV_DETAIL_INFO_FIELDS':
                    if isinstance(value, list):
                        print(f"   PASS {config_key}: {len(value)}个字段配置")
                    else:
                        print(f"   [WARNING]  {config_key}: 期望列表格式，实际: {type(value).__name__}")
                else:
                    print(f"   PASS {config_key}: {value}")
        
        # 检查MQTT排除匹配配置（新的简化格式）
        exclude_configs = [
            'EP_MQTT_EXCLUDE_INFO_SOURCE',
            'EP_MQTT_EXCLUDE_INFO_FIELD', 
            'EP_MQTT_EXCLUDE_LOGIC'
        ]
        
        # 检查排除匹配配置：支持None值禁用排除匹配
        has_exclude_config = any(config_key in event_config for config_key in exclude_configs)
        if has_exclude_config:
            # 检查是否所有排除配置都明确设置为None（表示禁用排除匹配）
            required_exclude_keys = exclude_configs[:3]  # 前3个是必需的
            configured_exclude_keys = [key for key in required_exclude_keys if key in event_config]
            
            if configured_exclude_keys and all(event_config.get(key) is None for key in configured_exclude_keys):
                print(f"   [EXCLUDE]  排除匹配配置: 已禁用 (所有配置设置为None)")
            else:
                print(f"   [EXCLUDE]  排除匹配配置:")
                for config_key in exclude_configs:
                    if config_key in event_config:
                        value = event_config[config_key]
                        if value is None:
                            print(f"      INFO {config_key}: None (禁用)")
                        elif config_key == 'EP_MQTT_EXCLUDE_INFO_FIELD':
                            if value and isinstance(value, str):
                                print(f"      PASS {config_key}: {value}")
                            else:
                                print(f"      FAIL {config_key}: 期望非空字符串，实际: {type(value).__name__}")
                                issues.append(f"{config_key}格式错误")
                        else:
                            print(f"      PASS {config_key}: {value}")
                    else:
                        if config_key in ['EP_MQTT_EXCLUDE_INFO_SOURCE', 'EP_MQTT_EXCLUDE_INFO_FIELD']:
                            print(f"      FAIL {config_key}: MQTT排除配置不完整")
                            issues.append(f"{config_key}缺失")
        
        if issues:
            event_issues[event_id] = issues
        else:
            print(f"   PASS 事件 {event_id} 配置完整且正确")
    
    # 阶段6: 事件ID一致性检查
    print(f"\n[CHECK] 阶段6: 事件ID一致性检查")
    print("-" * 40)
    
    consistency_issues = []
    try:
        # 获取EVENT_IDS列表中的事件ID
        declared_event_ids = set(config_manager.get_event_ids())
        print(f"[PARSE] EVENT_IDS中声明的事件: {sorted(declared_event_ids)}")
        
        # 扫描环境变量，找出所有EVENT_ID_开头的配置
        detected_event_ids = set()
        env_vars = dict(os.environ)
        
        for env_var in env_vars.keys():
            if env_var.startswith('EVENT_ID_') and '_EP_' in env_var:
                # 提取事件ID，格式: EVENT_ID_{event_id}_EP_xxx
                parts = env_var.split('_')
                if len(parts) >= 4:  # EVENT, ID, {event_id}, EP, ...
                    event_id = parts[2]  # 获取event_id部分
                    detected_event_ids.add(event_id)
        
        print(f"[CHECK] 环境变量中检测到的事件: {sorted(detected_event_ids)}")
        
        # 检查孤立的事件配置（在环境变量中配置但未在EVENT_IDS中声明）
        orphaned_events = detected_event_ids - declared_event_ids
        if orphaned_events:
            print(f"[WARNING]  发现孤立的事件配置 ({len(orphaned_events)}个):")
            for event_id in sorted(orphaned_events):
                print(f"   - {event_id}: 已配置环境变量但未添加到EVENT_IDS中")
                consistency_issues.append(f"事件{event_id}未添加到EVENT_IDS")
                
                # 显示该事件的具体配置项
                event_configs = [var for var in env_vars.keys() if var.startswith(f'EVENT_ID_{event_id}_EP_')]
                if event_configs:
                    print(f"     配置项: {', '.join([var.split('_', 3)[-1] for var in event_configs[:3]])}{'...' if len(event_configs) > 3 else ''}")
        
        # 检查缺失的事件配置（在EVENT_IDS中声明但环境变量中无配置）
        missing_events = declared_event_ids - detected_event_ids
        if missing_events:
            print(f"[WARNING]  发现缺失配置的事件 ({len(missing_events)}个):")
            for event_id in sorted(missing_events):
                print(f"   - {event_id}: 在EVENT_IDS中声明但无环境变量配置")
                consistency_issues.append(f"事件{event_id}缺少环境变量配置")
        
        # 完全匹配的情况
        matching_events = declared_event_ids & detected_event_ids
        if matching_events:
            print(f"PASS 配置一致的事件 ({len(matching_events)}个): {sorted(matching_events)}")
        
        if not consistency_issues:
            print("PASS 所有事件ID配置一致性检查通过")
        
    except Exception as e:
        print(f"FAIL 事件ID一致性检查失败: {e}")
        consistency_issues.append("事件ID一致性检查异常")
    
    # 阶段7: AI配置验证
    print(f"\n[AI] 阶段7: AI提示词配置验证")
    print("-" * 40)
    
    ai_issues = []
    try:
        event_ids = config_manager.get_event_ids()
        for event_id in event_ids:
            try:
                prompt = config_manager.get_ai_prompt(event_id)
                # 检查是否是业务逻辑事件（有业务逻辑配置且AI提示词为空）
                bl_source_key = f'EVENT_ID_{event_id}_EP_BusinessLogic_JUDGE_SOURCE'
                has_business_logic = os.getenv(bl_source_key, '').strip() != ''
                
                if prompt and prompt.strip():
                    print(f"PASS 事件 {event_id} AI提示词: 已配置 ({len(prompt)}字符)")
                elif has_business_logic:
                    print(f"PASS 事件 {event_id} AI提示词: 业务逻辑事件（无需AI分析）")
                else:
                    print(f"FAIL 事件 {event_id} AI提示词: 未配置或为空")
                    ai_issues.append(f"事件{event_id}缺少AI提示词")
            except Exception as e:
                print(f"FAIL 事件 {event_id} AI提示词: 检查失败 - {e}")
                ai_issues.append(f"事件{event_id}提示词检查异常")
    except Exception as e:
        print(f"FAIL AI提示词配置整体检查失败: {e}")
        ai_issues.append("AI提示词配置检查异常")
    
    # 最终报告
    print("\n" + "=" * 80)
    print("[SUMMARY] 配置测试总结报告")
    print("=" * 80)
    
    total_issues = len(global_issues) + len(consistency_issues) + len(ai_issues) + sum(len(issues) for issues in event_issues.values())
    
    if total_issues == 0:
        print("[SUCCESS] 恭喜！配置完全正确，所有检查均通过")
        print(f"PASS 发现 {event_count} 个事件，所有配置项验证成功")
        print("PASS 系统可以正常启动运行")
        return_code = 0
    else:
        print(f"[WARNING]  发现 {total_issues} 个配置问题需要修复")
        return_code = 1
    
    # 详细问题报告
    if global_issues:
        print(f"\n[GLOBAL] 全局配置问题 ({len(global_issues)}个):")
        for issue in global_issues:
            print(f"   - {issue}")
    
    if event_issues:
        print(f"\n[EVENT] 事件配置问题:")
        for event_id, issues in event_issues.items():
            print(f"   事件 {event_id} ({len(issues)}个问题):")
            for issue in issues:
                print(f"      - {issue}")
    
    if consistency_issues:
        print(f"\n[CHECK] 事件ID一致性问题 ({len(consistency_issues)}个):")
        for issue in consistency_issues:
            print(f"   - {issue}")
    
    if ai_issues:
        print(f"\n[AI] AI配置问题 ({len(ai_issues)}个):")
        for issue in ai_issues:
            print(f"   - {issue}")
    
    # 修复建议
    if total_issues > 0:
        print(f"\n[TIP] 修复建议:")
        print(f"   1. 检查 {env_file} 文件中的环境变量配置")
        print(f"   2. 检查 {env_file} 文件中的AI提示词配置 (EVENT_ID_*_EP_AI_PROMPT参数)")
        
        # 针对一致性问题的特定建议
        if consistency_issues:
            orphaned_events = detected_event_ids - declared_event_ids if 'detected_event_ids' in locals() and 'declared_event_ids' in locals() else set()
            if orphaned_events:
                current_event_ids = sorted(declared_event_ids) if 'declared_event_ids' in locals() else []
                suggested_event_ids = sorted(list(declared_event_ids | detected_event_ids)) if 'declared_event_ids' in locals() and 'detected_event_ids' in locals() else []
                print(f"   3. 将遗漏的事件ID添加到EVENT_IDS列表:")
                print(f"      当前: EVENT_IDS={current_event_ids}")
                print(f"      建议: EVENT_IDS={suggested_event_ids}")
        
        print(f"   4. 参考 REQ_EP_v3.md 文档中的配置格式说明")
        print(f"   5. 使用 --debug 参数获取更详细的错误信息")
    
    print("=" * 80)
    return return_code


def main():
    """主函数 - 重构版本，遵循SOLID、DRY、SRP原则
    
    架构说明:
    1. 单一职责分离: 配置管理、处理器创建、服务运行各司其职
    2. 消除重复加载: 配置只加载一次，通过参数传递
    3. 简化集成: 移除冗余包装函数，提高可测试性
    """
    try:
        # 解析命令行参数
        args = parse_arguments()
        
        # 设置日志，使用命令行指定的日志文件名
        log_filename = args.log or "event_processor_v3.log"
        log_setup_success = setup_logging(log_filename)
        logger = logging.getLogger(__name__)
        
        # 设置调试模式
        if args.debug:
            global current_log_level
            current_log_level = logging.DEBUG
            logging.getLogger().setLevel(logging.DEBUG)
            logger.info("调试模式已启用")
        
        # 检查是否为测试模式
        if args.test:
            logger.info("进入配置测试模式")
            return comprehensive_config_test(args.env, args.debug)
        
        # 初始化文件验证
        validation_result = validate_files(args.env)
        log_validation_result(validation_result)
        
        if not validation_result['valid']:
            logger.error("配置文件验证失败，程序无法启动")
            return 1
        
        logger.info("="*60)
        logger.info("Event Processor启动")
        logger.info("="*60)
        logger.info(f"当前进程PID: {os.getpid()}")
        logger.info(f"环境变量文件: {args.env}")
        logger.info("V3统一配置架构：AI提示词已合并到.env文件")
        
        # 设置信号处理器
        setup_signal_handlers()
        
        # 创建并加载配置
        config_manager, events_config, error = create_and_load_config(args.env, args)
        if config_manager is None:
            logger.error(f"配置初始化失败: {error}")
            return 1
        
        # 验证配置值
        validation_errors = validate_config_values(config_manager)
        if validation_errors:
            logger.error("配置值验证失败:")
            for error in validation_errors:
                logger.error(f"  - {error}")
            return 1
        
        # 创建事件处理器
        global processor
        processor, error = create_event_processor(config_manager, events_config)
        if processor is None:
            logger.error(f"事件处理器创建失败: {error}")
            return 1
        
        # 运行服务
        return run_service_loop(processor)
        
    except KeyboardInterrupt:
        logger.info("接收到键盘中断")
        return 0
    except Exception as e:
        logger.error(f"主函数发生未处理异常: {type(e).__name__}: {str(e)}", exc_info=True)
        return 1



if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)