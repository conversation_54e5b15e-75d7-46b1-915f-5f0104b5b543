# Event Processor Dependencies

# Core dependencies
paho-mqtt>=1.6.0
requests>=2.25.0
python-dotenv>=0.19.0
redis>=4.0.0

# Image processing
Pillow>=8.0.0

# Configuration
configparser>=5.0.0
PyYAML>=6.0

# Database
mysql-connector-python>=8.0.0

# System monitoring (optional for enhanced status reporting)
# psutil>=5.9.0

# Development dependencies (optional)
# pylint>=2.12.0
# black>=22.0.0
# pytest>=6.0.0

# Note: The following are Python standard library modules and don't need to be installed:
# - os, sys, signal, logging, argparse, time, threading, typing