# EV001001 配置修复脚本
# 基于 docs/PLAN.md 中的修改方案
# 作者: Augment Agent
# 日期: 2025-08-04

param(
    [string]$ConfigPath = "appsettings.yaml",
    [switch]$DryRun = $false
)

Write-Host "=== EV001001 配置修复脚本 ===" -ForegroundColor Green
Write-Host "配置文件路径: $ConfigPath"
Write-Host "干运行模式: $DryRun"
Write-Host ""

# 检查文件是否存在
if (-not (Test-Path $ConfigPath)) {
    Write-Error "配置文件不存在: $ConfigPath"
    exit 1
}

# 读取原始配置
$originalContent = Get-Content $ConfigPath -Raw
Write-Host "原始配置文件大小: $($originalContent.Length) 字符"

# 创建备份
$backupPath = "$ConfigPath.backup.$(Get-Date -Format 'yyyyMMdd-HHmmss')"
if (-not $DryRun) {
    Copy-Item $ConfigPath $backupPath
    Write-Host "已创建备份文件: $backupPath" -ForegroundColor Yellow
}

# 修改1: 移除BusinessRules中的duration条件
Write-Host "正在移除BusinessRules中的duration条件..." -ForegroundColor Cyan

$modifiedContent = $originalContent

# 使用正则表达式移除duration条件块
$durationPattern = @'
              - FieldName: "duration"
                DataType: "number"
                Operator: "GreaterThan"
                Value: "20"
                Description: "滞留时间超过20秒"
'@

$modifiedContent = $modifiedContent -replace [regex]::Escape($durationPattern), ""

# 修改2: 更改告警配置中duration的数据源
Write-Host "正在修改告警配置中duration的数据源..." -ForegroundColor Cyan

$alarmPattern = @'
        - AlarmFieldName: "事件"
          SourceRuleType: "BusinessRules"
          SourceFieldName: "duration"
          FormatTemplate: "停留时间{duration}秒"
'@

$alarmReplacement = @'
        - AlarmFieldName: "事件"
          SourceRuleType: "DeviceSignal"
          SourceFieldName: "duration"
          FormatTemplate: "停留时间{duration}秒"
'@

$modifiedContent = $modifiedContent -replace [regex]::Escape($alarmPattern), $alarmReplacement

# 验证修改
$changes = @()
if ($originalContent -ne $modifiedContent) {
    if ($originalContent.Contains('FieldName: "duration"') -and $originalContent.Contains('SourceRuleType: "BusinessRules"')) {
        if (-not $modifiedContent.Contains('FieldName: "duration"') -or $modifiedContent.Contains('SourceRuleType: "DeviceSignal"')) {
            $changes += "✅ 成功移除BusinessRules中的duration条件"
        }
    }
    
    if ($originalContent.Contains('SourceRuleType: "BusinessRules"') -and $modifiedContent.Contains('SourceRuleType: "DeviceSignal"')) {
        $changes += "✅ 成功修改告警配置中duration的数据源"
    }
}

# 显示修改摘要
Write-Host ""
Write-Host "=== 修改摘要 ===" -ForegroundColor Green
if ($changes.Count -gt 0) {
    foreach ($change in $changes) {
        Write-Host $change -ForegroundColor Green
    }
} else {
    Write-Host "❌ 未检测到预期的修改" -ForegroundColor Red
    Write-Host "可能的原因:"
    Write-Host "  1. 配置文件格式与预期不符"
    Write-Host "  2. 目标内容已经被修改过"
    Write-Host "  3. 正则表达式匹配失败"
}

Write-Host ""
Write-Host "修改前文件大小: $($originalContent.Length) 字符"
Write-Host "修改后文件大小: $($modifiedContent.Length) 字符"
Write-Host "字符差异: $($modifiedContent.Length - $originalContent.Length)"

# 应用修改
if (-not $DryRun) {
    if ($changes.Count -gt 0) {
        Set-Content $ConfigPath $modifiedContent -Encoding UTF8
        Write-Host ""
        Write-Host "✅ 配置文件修改已应用" -ForegroundColor Green
        Write-Host "备份文件: $backupPath"
        
        # 验证YAML语法
        Write-Host ""
        Write-Host "正在验证YAML语法..." -ForegroundColor Cyan
        try {
            # 尝试使用PowerShell的ConvertFrom-Yaml（如果可用）
            # 或者建议手动验证
            Write-Host "请手动验证YAML语法，或运行以下命令:" -ForegroundColor Yellow
            Write-Host "dotnet build src/EventProcessor.Core --configuration Release" -ForegroundColor Yellow
        }
        catch {
            Write-Host "无法自动验证YAML语法，请手动检查" -ForegroundColor Yellow
        }
    } else {
        Write-Host "❌ 由于未检测到预期修改，跳过文件写入" -ForegroundColor Red
    }
} else {
    Write-Host ""
    Write-Host "🔍 干运行模式 - 未实际修改文件" -ForegroundColor Yellow
    Write-Host "要应用修改，请运行: .\fix_ev001001_config.ps1 -ConfigPath '$ConfigPath'"
}

Write-Host ""
Write-Host "=== 后续步骤建议 ===" -ForegroundColor Green
Write-Host "1. 验证配置语法: dotnet build src/EventProcessor.Core --configuration Release"
Write-Host "2. 重启EventProcessor服务"
Write-Host "3. 检查日志确认配置加载正确"
Write-Host "4. 进行功能测试验证告警触发"
Write-Host ""
Write-Host "详细信息请参考: docs/PLAN.md"
