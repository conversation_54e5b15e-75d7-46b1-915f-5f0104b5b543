using System.Text.Json;
using System.Text.RegularExpressions;
using Microsoft.Extensions.Logging;

namespace EventProcessor.Core.Engine
{
    /// <summary>
    /// AJB消息适配器 - 专门处理ajb系统的嵌套JSON消息格式
    /// 解决报告 REPORT01-Rule-Engine-Fails-To-Parse-Upstream-Data.md 中的问题
    /// </summary>
    public static class AjbMessageAdapter
    {
        /// <summary>
        /// 检查消息是否来自ajb系统
        /// </summary>
        public static bool IsAjbMessage(string topic)
        {
            return topic.StartsWith("ajb/", StringComparison.OrdinalIgnoreCase) && 
                   topic.EndsWith("/time_log", StringComparison.OrdinalIgnoreCase);
        }

        /// <summary>
        /// 适配ajb消息格式，将嵌套JSON转换为扁平字典
        /// </summary>
        public static Dictionary<string, object> AdaptAjbMessage(string jsonPayload, ILogger? logger = null)
        {
            var result = new Dictionary<string, object>();

            try
            {
                using var document = JsonDocument.Parse(jsonPayload);
                var root = document.RootElement;

                // 1. 提取CarInfo字段
                ExtractCarInfo(root, result, logger);

                // 2. 提取Charge信息并计算duration
                ExtractChargeInfo(root, result, logger);

                // 3. 保留其他有用字段
                ExtractOtherFields(root, result, logger);

                logger?.LogDebug("AJB消息适配完成: 提取字段数={FieldCount}", result.Count);

                // 🔧 增强：详细字段日志（Debug级别）- 便于问题排查
                if (logger?.IsEnabled(LogLevel.Debug) == true)
                {
                    var keyFields = new[] { "CardType", "Expire", "log_car_no", "log_remain_days", "duration" };
                    var fieldSummary = string.Join(", ", keyFields.Select(key =>
                        $"{key}={result.GetValueOrDefault(key, "MISSING")}"));
                    logger.LogDebug("AJB关键字段: {KeyFields}", fieldSummary);
                }
            }
            catch (JsonException ex)
            {
                logger?.LogError(ex, "AJB消息JSON解析失败");
            }
            catch (Exception ex)
            {
                logger?.LogError(ex, "AJB消息适配异常");
            }

            return result;
        }

        /// <summary>
        /// 提取CarInfo字段
        /// </summary>
        private static void ExtractCarInfo(JsonElement root, Dictionary<string, object> result, ILogger? logger)
        {
            if (TryGetNestedElement(root, "response_payload.data.CarInfo", out var carInfo))
            {
                var fieldMappings = new Dictionary<string, string>
                {
                    // 核心字段映射
                    ["CardType"] = "CardType",
                    ["CarNo"] = "log_car_no",
                    ["Name"] = "log_user_name",
                    ["UserName"] = "log_user_name",
                    ["RemainDays"] = "log_remain_days",
                    ["EndTime"] = "log_end_time",
                    ["Intime"] = "log_in_time",

                    // 🔧 增强：添加更多可能需要的CarInfo字段
                    ["CardId"] = "CardId",
                    ["CardSnId"] = "CardSnId",
                    ["Balance"] = "Balance",
                    ["Starttime"] = "log_card_start_time",
                    ["Endtime"] = "log_card_end_time",
                    ["PStatus"] = "PStatus",
                    ["DoorName"] = "DoorName",
                    ["PositionNum"] = "PositionNum",
                    ["NColor"] = "NColor",
                    ["ByCarType"] = "ByCarType",
                    ["BindFeeType"] = "BindFeeType",
                    ["UserID"] = "UserID",
                    ["BillId"] = "BillId",
                    ["NoSensePay"] = "NoSensePay",
                    ["Per_Full"] = "Per_Full",
                    ["MoreCarNoInfo"] = "MoreCarNoInfo",
                    ["IsMoreCarNo"] = "IsMoreCarNo",
                    ["Acctime"] = "Acctime"
                };

                foreach (var mapping in fieldMappings)
                {
                    if (carInfo.TryGetProperty(mapping.Key, out var property))
                    {
                        var value = ExtractJsonValue(property);
                        if (value != null)
                        {
                            result[mapping.Value] = value;
                        }
                    }
                }
            }
        }

        /// <summary>
        /// 提取Charge信息并计算duration
        /// </summary>
        private static void ExtractChargeInfo(JsonElement root, Dictionary<string, object> result, ILogger? logger)
        {
            if (TryGetNestedElement(root, "response_payload.data.Charge", out var charge))
            {
                if (charge.TryGetProperty("ParkTime", out var parkTimeProperty))
                {
                    var parkTimeText = parkTimeProperty.GetString();
                    if (!string.IsNullOrEmpty(parkTimeText))
                    {
                        var durationMinutes = ParseDurationFromParkTime(parkTimeText);
                        result["duration"] = durationMinutes;
                        result["ParkTime"] = parkTimeText;

                        logger?.LogDebug("计算duration: ParkTime='{ParkTime}' -> duration={Duration}分钟",
                            parkTimeText, durationMinutes);
                    }
                }

                if (charge.TryGetProperty("StartTime", out var startTime))
                {
                    result["log_start_time"] = startTime.GetString() ?? "";
                }

                if (charge.TryGetProperty("EndTime", out var endTime))
                {
                    result["log_charge_end_time"] = endTime.GetString() ?? "";
                }

                // 🔧 修复：提取Expire字段 - 解决EV001001业务规则评估失败问题
                if (charge.TryGetProperty("Expire", out var expire))
                {
                    result["Expire"] = expire.GetString() ?? "";
                    logger?.LogDebug("提取Expire字段: Expire='{Expire}'", result["Expire"]);
                }

                // 🔧 增强：提取其他可能需要的Charge字段
                var additionalChargeFields = new Dictionary<string, string>
                {
                    ["AllFee"] = "AllFee",
                    ["TotalFee"] = "TotalFee",
                    ["IsFree"] = "IsFree",
                    ["IsTime"] = "IsTime",
                    ["ChargeMoney"] = "ChargeMoney",
                    ["ChargeType"] = "ChargeType",
                    ["OrderID"] = "OrderID",
                    ["IsAutoCharge"] = "IsAutoCharge",
                    ["FavMoney"] = "FavMoney",
                    ["FavTime"] = "FavTime",
                    ["CurrFavMoney"] = "CurrFavMoney",
                    ["Overtime"] = "Overtime"
                };

                foreach (var mapping in additionalChargeFields)
                {
                    if (charge.TryGetProperty(mapping.Key, out var property))
                    {
                        var value = ExtractJsonValue(property);
                        if (value != null)
                        {
                            result[mapping.Value] = value;
                        }
                    }
                }
            }
        }

        /// <summary>
        /// 提取其他有用字段
        /// </summary>
        private static void ExtractOtherFields(JsonElement root, Dictionary<string, object> result, ILogger? logger)
        {
            var topLevelFields = new[] { "log_original_timestamp", "log_event_type", "timestamp" };
            
            foreach (var fieldName in topLevelFields)
            {
                if (root.TryGetProperty(fieldName, out var property))
                {
                    var value = ExtractJsonValue(property);
                    if (value != null)
                    {
                        result[fieldName] = value;
                    }
                }
            }

            if (TryGetNestedElement(root, "response_payload", out var responsePayload))
            {
                var statusFields = new[] { "status", "code", "msg" };
                
                foreach (var fieldName in statusFields)
                {
                    if (responsePayload.TryGetProperty(fieldName, out var property))
                    {
                        var value = ExtractJsonValue(property);
                        if (value != null)
                        {
                            result[$"response_{fieldName}"] = value;
                        }
                    }
                }
            }
        }

        /// <summary>
        /// 从ParkTime文本解析停车时长（分钟）
        /// </summary>
        private static int ParseDurationFromParkTime(string parkTimeText)
        {
            if (string.IsNullOrWhiteSpace(parkTimeText))
                return 0;

            try
            {
                var totalMinutes = 0;

                // 匹配小时：支持 "2小时" 或 "2时"
                var hourMatch = Regex.Match(parkTimeText, @"(\d+)[小时时]");
                if (hourMatch.Success)
                {
                    totalMinutes += int.Parse(hourMatch.Groups[1].Value) * 60;
                }

                // 匹配分钟：支持 "30分钟" 或 "30分"
                var minuteMatch = Regex.Match(parkTimeText, @"(\d+)分");
                if (minuteMatch.Success)
                {
                    totalMinutes += int.Parse(minuteMatch.Groups[1].Value);
                }

                return totalMinutes;
            }
            catch (Exception)
            {
                return 0;
            }
        }

        /// <summary>
        /// 尝试获取嵌套JSON元素
        /// </summary>
        private static bool TryGetNestedElement(JsonElement root, string path, out JsonElement element)
        {
            element = root;
            var parts = path.Split('.');

            foreach (var part in parts)
            {
                if (element.TryGetProperty(part, out var nextElement))
                {
                    element = nextElement;
                }
                else
                {
                    element = default;
                    return false;
                }
            }

            return true;
        }

        /// <summary>
        /// 提取JSON值并转换为合适的.NET类型
        /// </summary>
        private static object? ExtractJsonValue(JsonElement element)
        {
            return element.ValueKind switch
            {
                JsonValueKind.String => element.GetString(),
                JsonValueKind.Number => element.TryGetInt64(out var longValue) ? longValue : element.GetDouble(),
                JsonValueKind.True => true,
                JsonValueKind.False => false,
                JsonValueKind.Null => null,
                _ => element.GetRawText()
            };
        }
    }
}
