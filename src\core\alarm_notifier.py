#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
告警通知模块

整合详情信息，构建告警消息并通过MQTT发送。
"""

import logging
import threading
from datetime import datetime, timezone
from typing import Dict, Any, List, Optional


logger = logging.getLogger(__name__)


class AlarmNotifier:
    """
    告警通知器
    
    负责收集详情信息、构建告警消息并发送通知。
    """
    
    def __init__(self, config: Dict[str, Any], mqtt_client):
        """
        初始化告警通知器
        
        Args:
            config: 配置参数
            mqtt_client: MQTT客户端实例
        """
        self.alarm_config = config
        self.mqtt_client = mqtt_client
        
        # 基本配置
        self.comm_id = config.get('COMM_ID')
        self.position_id = config.get('POSITION_ID')
        self.event_id = config.get('EVENT_ID')
        self.position_name = config.get('EP_POSITION_NAME')

        
        # 详情信息配置
        self.detail_info_source_type = config.get('EP_PV_DETAIL_INFO_SOURCE_TYPE', 'MQTT')
        self.detail_info_source = config.get('EP_PV_DETAIL_INFO_SOURCE', '')
        self.detail_info_fields = config.get('EP_PV_DETAIL_INFO_FIELDS', [])
        
        # 告警发送主题
        self.alarm_topic = f"{self.comm_id}/{self.position_id}/event"
        
        # 详情信息存储
        self.detail_info_cache: Dict[str, Any] = {}
        self.detail_info_lock = threading.Lock()
        
        logger.info(f"告警通知器初始化: 主题={self.alarm_topic}")
        logger.info(f"详情信息源配置: {self.detail_info_source_type} - {self.detail_info_source}")
        logger.info(f"详情字段数量: {len(self.detail_info_fields)}")
        
        # 注意：详情信息主题订阅现在由MultiEventProcessor统一管理
    
    def _handle_detail_info(self, topic: str, message: Dict[str, Any]):
        """
        处理详情信息消息
        
        Args:
            topic: MQTT主题
            message: 详情信息消息
        """
        try:
            str_msg_type = ""
            if message.get("log_event_type", "").find("进场查询系统返回结果") != -1: # 寻找字符串的结果不为-1，则表示找到了
                pass
            elif message.get("log_event_type", "").find("查询返回场内剩余车位和场内车辆数量") != -1:
                pass
            elif message.get("log_event_type", "").find("进场开闸控制指令") != -1:
                pass
            elif message.get("log_event_type", "").find("车辆入场成功") != -1:
                pass
            elif message.get("log_event_type", "").find("车牌识别") != -1:
                str_msg_type = "车牌识别"
                logger.debug(f"收到车牌识别消息: 车牌号={message['log_car_no']}")
            elif message.get("log_event_type", "").find("车辆进场") != -1:
                pass
            elif message.get("log_event_type", "").find("车辆出场") != -1:
                pass
            else:
                logger.debug(f"收到详情信息: {message}")
            
            with self.detail_info_lock:
                # 检查哪些字段实际发生了变更
                updated_fields = []
                for key, new_value in message.items():
                    old_value = self.detail_info_cache.get(key)
                    if old_value != new_value:
                        updated_fields.append(f"{key}: {old_value} -> {new_value}")
                
                # 更新详情信息缓存
                self.detail_info_cache.update(message)
                
                # 只有确实有字段更新时才记录日志
                if updated_fields:
                    logger.debug(f"详情信息已更新: {', '.join(updated_fields)}")
            
        except Exception as e:
            logger.error(f"处理详情信息失败: {e}", exc_info=True)
    
    def send_alarm(self, image_urls: List[str], ai_result: Optional[Dict[str, Any]] = None, event_config: Optional[Dict[str, Any]] = None) -> bool:
        """
        发送告警通知
        
        Args:
            image_urls: 图片URL列表
            ai_result: AI分析结果（可选）
            event_config: 事件特有配置（可选，用于覆盖默认配置）
            
        Returns:
            bool: 发送是否成功
        """
        try:
            logger.info(f"开始发送告警通知: 图片数量={len(image_urls)}")
            
            # 获取事件特有配置
            qos = 1
            if event_config:
                qos = event_config.get('ALARM_QOS', 1)
            
            # 构建事件特定的告警主题
            alarm_topic = self._build_alarm_topic(event_config)
            logger.info(f"构建的告警主题: {alarm_topic}")
            
            # 构建告警消息
            alarm_message = self._build_alarm_message(image_urls, ai_result, event_config)
            
            # 发送MQTT消息
            success = self.mqtt_client.publish(
                alarm_topic,
                alarm_message,
                qos=qos
            )
            
            if success:
                logger.info(f"告警通知发送成功: 主题={alarm_topic}")
                logger.debug(f"告警内容: {alarm_message}")
            else:
                logger.error(f"告警通知发送失败: 主题={alarm_topic}")
            
            return success
            
        except Exception as e:
            logger.error(f"发送告警通知异常: {e}", exc_info=True)
            return False
    
    def _build_alarm_message(self, image_urls: List[str], ai_result: Optional[Dict[str, Any]] = None, event_config: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        构建告警消息
        
        Args:
            image_urls: 图片URL列表
            ai_result: AI分析结果
            
        Returns:
            Dict[str, Any]: 告警消息
        """
        # 构建详情信息
        detail_data = self._build_detail_data(ai_result, event_config)
        
        # 获取事件ID（优先使用事件特定配置）
        event_id = self.event_id
        if event_config and 'EVENT_ID' in event_config:
            event_id = event_config['EVENT_ID']
            logger.debug(f"使用事件特定配置的EVENT_ID: {event_id}")
        else:
            logger.debug(f"使用默认EVENT_ID: {event_id}, event_config有EVENT_ID: {event_config and 'EVENT_ID' in event_config if event_config else False}")
        
        # 构建告警消息
        alarm_message = {
            'eventid': event_id,
            'comm_id': self.comm_id,
            'positionid': self.position_id,
            'data': detail_data,
            'urls': image_urls
        }
        
        logger.debug(f"构建告警消息完成: {alarm_message}")
        return alarm_message
    
    def _build_alarm_topic(self, event_config: Optional[Dict[str, Any]] = None) -> str:
        """
        构建事件特定的告警主题
        
        Args:
            event_config: 事件特有配置（可选）
            
        Returns:
            str: 告警主题
        """
        logger.info(f"_build_alarm_topic: event_config存在={event_config is not None}")
        
        # 优先使用事件特定的告警主题配置
        if event_config and 'EP_PV_ALARM_TOPIC' in event_config:
            alarm_topic = event_config['EP_PV_ALARM_TOPIC']
            logger.debug(f"使用事件特定告警主题: {alarm_topic}")
        else:
            # 使用默认构建方式
            comm_id = self.comm_id
            position_id = self.position_id
            
            if event_config:
                comm_id = event_config.get('COMM_ID', comm_id)
                position_id = event_config.get('POSITION_ID', position_id)
            
            alarm_topic = f"{comm_id}/{position_id}/event"
            logger.debug(f"使用默认构建告警主题: {alarm_topic}")
        
        logger.info(f"最终告警主题: {alarm_topic}")
        return alarm_topic
    
    def _build_detail_data(self, ai_result: Optional[Dict[str, Any]] = None, event_config: Optional[Dict[str, Any]] = None) -> str:
        """
        构建详情数据字符串
        
        Args:
            ai_result: AI分析结果
            
        Returns:
            str: 格式化的详情数据
        """
        detail_lines = []
        
        # 从AI结果中提取值为true的字段名（用于名称字段拼接）
        true_field_names = self._extract_true_fields_from_ai_result(ai_result)
        logger.debug(f"AI结果中值为true的字段: {true_field_names}")
        
        # 获取详情字段配置（优先使用事件特定配置）
        detail_info_fields = self.detail_info_fields
        if event_config and 'EP_PV_DETAIL_INFO_FIELDS' in event_config:
            detail_info_fields = event_config['EP_PV_DETAIL_INFO_FIELDS']
            logger.info(f"使用事件特定配置的详情字段: {len(detail_info_fields)}个字段")
            logger.info(f"详情字段配置: {detail_info_fields}")
        else:
            logger.info(f"使用默认详情字段: {len(detail_info_fields)}个字段")
            logger.info(f"默认详情字段配置: {detail_info_fields}")
        
        # 处理配置的详情字段
        with self.detail_info_lock:
            for field_config in detail_info_fields:
                field_name = field_config.get('field_name', '')
                field_keyword = field_config.get('field_keyword', '')
                field_default = field_config.get('field_default', '')
                field_format = field_config.get('field_format', '')  # 新增：字段格式化模板
                
                if not field_name:
                    continue
                
                # 获取字段值（支持格式化模板）
                if field_format:
                    field_value = self._format_field_value(field_format, field_keyword)
                else:
                    field_value = self._get_field_value(field_keyword, field_default)
                
                if field_value:
                    # 检查是否为名称字段，如果是且有AI结果中的true字段，则在字段值后追加AI检测信息
                    enhanced_field_value = self._enhance_name_field_value_with_true_values(field_name, field_value, true_field_names)
                    detail_lines.append(f"[{field_name}]: {enhanced_field_value}")
                    logger.debug(f"详情字段 '{field_name}': {enhanced_field_value} (关键字={field_keyword}, 默认值={field_default}, 格式={field_format})")
                else:
                    logger.warning(f"字段值为空: {field_name} (关键字={field_keyword}, 默认值={field_default}, 格式={field_format})")
        
        # 添加本地格式化时间(REQ: 最后加上\r\n[时间]:YYYY-MM-DD HH:MM:SS, 此时间是发送消息时的时间)
        alarm_send_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        detail_lines.append(f"[时间]: {alarm_send_time}")
        
        # 拼接所有行
        try:
            detail_data = '\r\n'.join(detail_lines)
        except Exception as e:
            logger.error(f"构建详情数据失败: {e}", exc_info=True)
            detail_data = ''
        
        logger.info(f"构建详情数据完成: {detail_data}")
        return detail_data
    
    def _get_field_value(self, field_keyword: str, field_default: str) -> str:
        """
        获取字段值
        
        Args:
            field_keyword: 字段关键字
            field_default: 默认值
            
        Returns:
            str: 字段值
        """
        # 如果没有关键字，直接返回默认值
        if not field_keyword:
            logger.debug(f"字段无关键字，使用默认值: {field_default}")
            return field_default
        
        # 从详情信息缓存中查找
        logger.debug(f"查找关键字 '{field_keyword}' 在缓存中: {list(self.detail_info_cache.keys())}")
        if field_keyword in self.detail_info_cache:
            field_cache_value = self.detail_info_cache[field_keyword]
            if field_cache_value is not None:
                logger.debug(f"从缓存中找到关键字 '{field_keyword}': {field_cache_value}")
                return str(field_cache_value)
        
        # 如果没有找到，返回默认值
        logger.debug(f"关键字 '{field_keyword}' 未找到，使用默认值: {field_default}")
        return field_default
    
    def _format_field_value(self, field_format: str, field_keywords: str) -> str:
        """
        使用格式化模板生成字段值
        
        Args:
            field_format: 格式化模板，如 "[{CardType}][{log_car_no}][{log_user_name}][{log_end_time}]"
            field_keywords: 字段关键字列表，逗号分隔
            
        Returns:
            str: 格式化后的字段值
        """
        try:
            # 解析字段关键字
            keywords = [k.strip() for k in field_keywords.split(',') if k.strip()]
            
            # 构建格式化参数字典
            format_params = {}
            for keyword in keywords:
                cache_value = self.detail_info_cache.get(keyword, '')
                format_params[keyword] = str(cache_value) if cache_value is not None else ''
                logger.debug(f"格式化参数 '{keyword}': '{format_params[keyword]}'")
            
            # 添加特殊字段：持续时间
            if 'duration' in field_format:
                # 这里可以从事件上下文计算持续时间，暂时使用占位符
                format_params['duration'] = self.detail_info_cache.get('duration', '0.0')
            
            # 应用格式化模板
            formatted_value = field_format.format(**format_params)
            logger.debug(f"格式化结果: '{formatted_value}'")
            
            return formatted_value
            
        except Exception as e:
            logger.error(f"字段格式化失败: {e}")
            logger.error(f"格式模板: '{field_format}', 关键字: '{field_keywords}'")
            return field_format  # 失败时返回原模板
    
    def clear_detail_info_cache(self):
        """清空详情信息缓存"""
        with self.detail_info_lock:
            self.detail_info_cache.clear()
            logger.debug("详情信息缓存已清空")
    
    def get_detail_info_cache(self) -> Dict[str, Any]:
        """获取详情信息缓存"""
        with self.detail_info_lock:
            return self.detail_info_cache.copy()
    
    def update_detail_info(self, info: Dict[str, Any]):
        """
        更新详情信息
        
        Args:
            info: 详情信息字典
        """
        with self.detail_info_lock:
            # 检查哪些字段实际发生了变更
            updated_fields = []
            for key, new_value in info.items():
                old_value = self.detail_info_cache.get(key)
                if old_value != new_value:
                    updated_fields.append(f"{key}: {old_value} -> {new_value}")
            
            # 更新详情信息缓存
            self.detail_info_cache.update(info)
            
            # 只有确实有字段更新时才记录日志
            if updated_fields:
                logger.debug(f"详情信息已更新: {', '.join(updated_fields)}")
    
    def get_alarm_topic(self) -> str:
        """获取告警主题"""
        return self.alarm_topic
    
    def _extract_true_fields_from_ai_result(self, ai_result: Optional[Dict[str, Any]]) -> List[str]:
        """
        从AI分析结果中提取值为true的字段名
        
        Args:
            ai_result: AI分析结果字典
            
        Returns:
            List[str]: 值为true的字段名列表
        """
        true_fields = []
        
        if not ai_result:
            logger.debug("AI结果为空，无法提取true字段")
            return true_fields
        
        try:
            # 检查ai_result中的ai_result字段（嵌套结构）
            actual_ai_result = ai_result.get('ai_result', {})
            if not actual_ai_result:
                logger.debug("AI结果中无ai_result字段，检查顶级字段")
                actual_ai_result = ai_result
            
            logger.debug(f"检查AI结果字段: {actual_ai_result}")
            
            # 遍历AI结果中的所有字段
            for field_name, field_value in actual_ai_result.items():
                if field_name in ['confidence', 'timestamp', 'raw_response', 'request_detail']:
                    # 跳过元数据字段
                    continue
                
                # 检查值是否为true（支持多种true表示）
                if self._is_true_value(field_value):
                    true_fields.append(field_name)
                    logger.info(f"AI结果中发现true字段: '{field_name}' = {field_value}")
            
            logger.info(f"从AI结果中提取到 {len(true_fields)} 个true字段: {true_fields}")
            
        except Exception as e:
            logger.error(f"提取AI结果true字段时出错: {e}", exc_info=True)
        
        return true_fields
    
    def _is_true_value(self, value: Any) -> bool:
        """
        判断值是否表示true
        
        Args:
            value: 待检查的值
            
        Returns:
            bool: 是否为true值
        """
        if value is True:
            return True
        
        if isinstance(value, str):
            # 字符串形式的true值检查
            value_lower = value.lower().strip()
            if value_lower in ['true', '是', 'yes', '1', '存在', '有', '检测到']:
                return True
        
        if isinstance(value, (int, float)):
            # 数值形式的true值检查（非零值视为true）
            return value != 0
        
        return False
    
    def _enhance_name_field_value_with_true_values(self, field_name: str, field_value: str, true_field_names: List[str]) -> str:
        """
        增强名称字段值，在字段值后追加AI结果中值为true的字段名
        
        Args:
            field_name: 字段名
            field_value: 原始字段值
            true_field_names: AI结果中值为true的字段名列表
            
        Returns:
            str: 增强后的字段值
        """
        # 检查是否为名称字段（通过字段名关键词判断）
        name_keywords = ['名称', 'name', '事件名称', '告警名称', '事件描述']
        is_name_field = any(keyword in field_name.lower() for keyword in name_keywords)
        
        if not is_name_field or not true_field_names:
            # 不是名称字段或无true字段，返回原字段值
            return field_value
        
        # 将AI检测到的字段作为补充信息追加到字段值后面
        true_fields_str = '('.join(true_field_names) + ')' if true_field_names else ''
        enhanced_value = f"{field_value}({', '.join(true_field_names)})"
        
        logger.info(f"增强名称字段值: '{field_value}' -> '{enhanced_value}' (AI检测字段: {true_field_names})")
        
        return enhanced_value
