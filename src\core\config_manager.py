#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置管理模块 - 版本

从环境变量读取多事件配置参数，支持全局共享参数和per-event独有参数。
"""

import os
import json
import logging
import threading
import time
from typing import Dict, Any, List, Union, Optional
from dotenv import load_dotenv


logger = logging.getLogger(__name__)


class ConfigValidationError(Exception):
    """配置验证错误"""
    pass


class ConfigManager:
    """
    配置管理器 - 版本
    
    V3统一配置管理：
    - 从环境变量加载全局共享参数
    - 从环境变量加载per-event独有参数（EVENT_ID_前缀）
    - 从环境变量加载AI提示词（EVENT_ID_{event_id}_EP_AI_PROMPT）
    """
    
    # 必需的全局环境变量
    REQUIRED_GLOBAL_VARS = [
        'COMM_ID',
        'POSITION_ID',
        'EVENT_IDS',  # 新增：事件ID列表
        'MQTT_BROKER_HOST',
        'EP_POSITION_NAME',
        'FTP_HOST',
        'FTP_USERNAME',
        'FTP_PASSWORD',
        'FTP_REMOTE_DIR',
    ]
    
    # V3新增配置参数（严格按照需求规格）
    V3_CONFIG_VARS = [
        'EP_AI_ANALYSIS_DELAY_SEC',               # AI分析延迟启动时间（秒）
        'EP_AI_RESULT_TIMEOUT',                   # AI结果超时时间（秒）
        'EP_PV_BIND_IPC_IMAGE_DIR',              # V3统一图片目录配置
        'EP_PV_IMAGE_CROP_COORDINATES',          # V3统一图片裁剪坐标
    ]
    
    # 必需的per-event环境变量（将通过EVENT_ID_前缀查找）
    REQUIRED_PER_EVENT_VARS = [
        'EP_START_DEVICE_EVENT_TOPIC',
        'EP_START_FIELD_FROM_DEVICE_EVENT', 
        'EP_POSITION_START_VALUE_FROM_DEVICE_EVENT',
        'EP_PV_HOLDING_TIMEOUT'
        # 移除EP_PV_BIND_IPC_IMAGE_DIR（现为全局配置）
        # 移除EP_EV_PRIORITY（不再需要优先级）
    ]
    
    # 默认值配置
    DEFAULT_VALUES = {
        'MQTT_BROKER_PORT': 1883,
        'MQTT_USERNAME': '',
        'MQTT_PASSWORD': '',
        'LOG_LEVEL': 'INFO',
        'EP_PV_AI_RESULT_WAIT_TIMEOUT': 60,
        # V3配置参数默认值
        'EP_AI_ANALYSIS_DELAY_SEC': 5,
        'EP_AI_RESULT_TIMEOUT': 60,
        # 统一的全局图片配置
        'EP_PV_BIND_IPC_IMAGE_DIR': '',
        'EP_PV_IMAGE_CROP_COORDINATES': '[0, 0, 0, 0]',  # 默认不裁剪
        'EP_PV_DETAIL_INFO_SOURCE_TYPE': 'MQTT',
        'EP_PV_DETAIL_INFO_SOURCE': '',
        'EP_PV_DETAIL_INFO_FIELDS': '[]',
        'FTP_PORT': 21,
        'FTP_IS_ENABLED': True,
        'FTP_URL_PREFIX': '',
        # Events.ini刷新配置
        'EP_EVENTS_INI_RELOAD_DURATION': 5,
        # V3配置默认值（按需求规格）
        'EP_AI_ANALYSIS_DELAY_SEC': 6,
    }
    
    def __init__(self, env_path: str, cli_args=None):
        """初始化配置管理器（V3统一配置架构）"""
        if not env_path:
            raise ConfigValidationError("必须通过--env参数指定.env文件路径")
        self.env_path = env_path
        self.cli_args = cli_args
        self.global_config: Dict[str, Any] = {}
        self.events_config: Dict[str, Dict[str, Any]] = {}
        self.ai_prompts: Dict[str, str] = {}
        
        logger.info("配置管理器初始化（V3统一配置架构）")
    
    def _get_cli_override(self, var_name: str):
        """获取命令行参数对配置项的覆盖值"""
        if not self.cli_args:
            return None
        
        # 命令行参数到配置项的映射
        cli_mappings = {
            'LOG_LEVEL': 'DEBUG' if getattr(self.cli_args, 'debug', False) else None,
            # 可以在这里添加更多命令行参数到配置的映射
        }
        
        return cli_mappings.get(var_name)
    
    def load_config(self) -> Dict[str, Dict[str, Any]]:
        """
        加载多事件配置
        
        Returns:
            Dict[str, Dict[str, Any]]: {event_id: config_dict}
        
        Raises:
            ConfigValidationError: 配置验证失败
        """
        logger.info("开始加载多事件配置...")
        
        # 1. 加载.env文件
        self._load_dotenv_file()
        
        # 2. 加载全局配置
        self._load_global_config()
        
        # 3. 加载事件列表
        event_ids = self._load_event_ids()
        
        # 4. 加载per-event配置
        self._load_per_event_configs(event_ids)
        
        # 5. 从环境变量加载AI prompt映射
        self._load_ai_prompts_from_env(event_ids)
        
        # 6. 验证配置
        self._validate_configs()
        
        logger.info(f"配置加载完成，共加载了{len(event_ids)}个事件配置")
        self._log_config_summary()
        
        return self.events_config
    
    def get_global_config(self) -> Dict[str, Any]:
        """获取全局配置"""
        return self.global_config.copy()
    
    def _load_dotenv_file(self):
        """加载.env文件（仅使用传入路径）"""
        logger.debug(f"准备加载.env文件: {self.env_path}")
        logger.debug(f"加载前环境变量快照: COMM_ID={os.environ.get('COMM_ID')} ({type(os.environ.get('COMM_ID'))}), POSITION_ID={os.environ.get('POSITION_ID')} ({type(os.environ.get('POSITION_ID'))}), EVENT_IDS={os.environ.get('EVENT_IDS')} ({type(os.environ.get('EVENT_IDS'))})")
        try:
            if not os.path.exists(self.env_path):
                logger.error(f"未找到.env文件: {self.env_path}")
                raise ConfigValidationError(f"未找到.env文件: {self.env_path}")
            if not os.access(self.env_path, os.R_OK):
                logger.error(f".env文件不可读: {self.env_path}")
                raise ConfigValidationError(f".env文件不可读: {self.env_path}")
            result = load_dotenv(self.env_path, override=True)
            logger.info(f"已加载.env文件: {self.env_path}, load_dotenv返回: {result}")
        except Exception as e:
            logger.warning(f"加载.env文件时发生异常: {e}")
            raise ConfigValidationError(f"加载.env文件失败: {e}")
        logger.debug(f"加载后环境变量快照: COMM_ID={os.environ.get('COMM_ID')} ({type(os.environ.get('COMM_ID'))}), POSITION_ID={os.environ.get('POSITION_ID')} ({type(os.environ.get('POSITION_ID'))}), EVENT_IDS={os.environ.get('EVENT_IDS')} ({type(os.environ.get('EVENT_IDS'))})")
    
    def _load_global_config(self):
        """加载全局配置"""
        logger.debug("开始加载全局配置...")
        # 加载必需的全局变量
        for var_name in self.REQUIRED_GLOBAL_VARS:
            env_raw = os.environ.get(var_name)
            getenv_val = os.getenv(var_name)
            logger.debug(f"读取变量: {var_name}, os.environ: {env_raw} ({type(env_raw)}), os.getenv: {getenv_val} ({type(getenv_val)})")
            try:
                strip_val = getenv_val.strip() if isinstance(getenv_val, str) else getenv_val
                logger.debug(f"变量: {var_name}, strip前: {getenv_val}, strip后: {strip_val}")
            except Exception as e:
                logger.error(f"strip处理异常: {var_name}, 值: {getenv_val}, 错误: {e}")
                strip_val = getenv_val
            value = getenv_val
            if value is None:
                error_msg = f"缺少必需的全局环境变量: {var_name} (os.environ: {env_raw}, os.getenv: {getenv_val})"
                logger.error(error_msg)
                logger.error(f"当前全局配置快照: {self.global_config}")
                raise ConfigValidationError(error_msg)
            if not (isinstance(value, str) and value.strip()):
                error_msg = f"全局环境变量 {var_name} 不能为空 (os.environ: {env_raw}, os.getenv: {getenv_val})"
                logger.error(error_msg)
                logger.error(f"当前全局配置快照: {self.global_config}")
                raise ConfigValidationError(error_msg)
            self.global_config[var_name] = value
            logger.debug(f"加载全局变量: {var_name}={value}")
        # 加载可选的全局变量（按优先级：命令行参数 > 环境变量（非空） > 默认值）
        for var_name, default_value in self.DEFAULT_VALUES.items():
            # 1. 检查命令行参数覆盖
            cli_value = self._get_cli_override(var_name)
            # 2. 检查环境变量（必须非空）
            env_value = os.getenv(var_name)
            env_value = env_value if env_value and env_value.strip() else None
            
            # 按优先级选择配置值
            if cli_value is not None:
                final_value = cli_value
                source = "命令行参数"
            elif env_value is not None:
                final_value = env_value
                source = "环境变量"
            else:
                final_value = default_value
                source = "默认值"
            
            # 类型转换
            if isinstance(default_value, bool) and isinstance(final_value, str):
                self.global_config[var_name] = final_value.lower() in ('true', '1', 'yes', 'on')
            elif isinstance(default_value, int) and isinstance(final_value, str):
                try:
                    self.global_config[var_name] = int(final_value)
                except ValueError:
                    logger.warning(f"无法转换为整数: {var_name}={final_value}，使用默认值: {default_value}")
                    self.global_config[var_name] = default_value
                    source = "默认值（转换失败）"
            elif isinstance(default_value, float) and isinstance(final_value, str):
                try:
                    self.global_config[var_name] = float(final_value)
                except ValueError:
                    logger.warning(f"无法转换为浮点数: {var_name}={final_value}，使用默认值: {default_value}")
                    self.global_config[var_name] = default_value
                    source = "默认值（转换失败）"
            else:
                self.global_config[var_name] = final_value
            
            logger.debug(f"配置加载: {var_name}={self.global_config[var_name]} (来源: {source})")
        
        # V3配置参数全局验证
        self._validate_v3_global_config()
    
    def _validate_v3_global_config(self):
        """验证V3全局配置参数"""
        try:
            # 验证AI分析延迟时间
            ai_delay = self.global_config.get('EP_AI_ANALYSIS_DELAY_SEC')
            if ai_delay is not None:
                if not isinstance(ai_delay, (int, float)) or ai_delay < 0:
                    raise ConfigValidationError(f"EP_AI_ANALYSIS_DELAY_SEC必须是非负数: {ai_delay}")
                if ai_delay > 300:  # 最大5分钟
                    logger.warning(f"EP_AI_ANALYSIS_DELAY_SEC设置过大: {ai_delay}秒，建议不超过300秒")
            
            # 验证AI结果等待超时
            ai_timeout = self.global_config.get('EP_PV_AI_RESULT_WAIT_TIMEOUT')
            if ai_timeout is not None:
                if not isinstance(ai_timeout, (int, float)) or ai_timeout <= 0:
                    raise ConfigValidationError(f"EP_PV_AI_RESULT_WAIT_TIMEOUT必须大于0: {ai_timeout}")
                if ai_timeout > 600:  # 最大10分钟
                    logger.warning(f"EP_PV_AI_RESULT_WAIT_TIMEOUT设置过大: {ai_timeout}秒，建议不超过600秒")
            
            # 验证图片裁剪坐标格式
            crop_coords = self.global_config.get('EP_PV_IMAGE_CROP_COORDINATES')
            if crop_coords and isinstance(crop_coords, str):
                try:
                    coords = json.loads(crop_coords)
                    if not isinstance(coords, list) or len(coords) != 4:
                        raise ConfigValidationError(f"EP_PV_IMAGE_CROP_COORDINATES必须是包含4个元素的数组: {crop_coords}")
                    if not all(isinstance(x, (int, float)) and x >= 0 for x in coords):
                        raise ConfigValidationError(f"EP_PV_IMAGE_CROP_COORDINATES所有坐标必须是非负数: {crop_coords}")
                except json.JSONDecodeError as e:
                    raise ConfigValidationError(f"EP_PV_IMAGE_CROP_COORDINATES JSON格式错误: {crop_coords}, 错误: {e}")
            
            logger.debug("V3全局配置验证通过")
            
        except Exception as e:
            logger.error(f"V3全局配置验证失败: {e}")
            raise
    
    def _load_event_ids(self) -> List[str]:
        """加载事件ID列表"""
        event_ids_str = self.global_config.get('EVENT_IDS', '')
        if not event_ids_str:
            raise ConfigValidationError("EVENT_IDS不能为空")
        
        try:
            event_ids = json.loads(event_ids_str)
            if not isinstance(event_ids, list) or len(event_ids) == 0:
                raise ConfigValidationError("EVENT_IDS必须是非空的JSON数组")
            
            logger.info(f"加载事件ID列表: {event_ids}")
            return event_ids
            
        except json.JSONDecodeError as e:
            raise ConfigValidationError(f"EVENT_IDS JSON解析失败: {e}")
    
    def _load_per_event_configs(self, event_ids: List[str]):
        """加载per-event配置"""
        logger.debug("开始加载per-event配置...")
        
        for event_id in event_ids:
            # 只继承事件级别需要的全局配置
            event_config = {
                'EVENT_ID': event_id,
                # MQTT配置
                'MQTT_BROKER_HOST': self.global_config.get('MQTT_BROKER_HOST'),
                'MQTT_BROKER_PORT': self.global_config.get('MQTT_BROKER_PORT'),
                'MQTT_USERNAME': self.global_config.get('MQTT_USERNAME'),
                'MQTT_PASSWORD': self.global_config.get('MQTT_PASSWORD'),
                # 基础配置
                'COMM_ID': self.global_config.get('COMM_ID'),
                'POSITION_ID': self.global_config.get('POSITION_ID'),
                'EP_POSITION_NAME': self.global_config.get('EP_POSITION_NAME'),
                # FTP配置
                'FTP_HOST': self.global_config.get('FTP_HOST'),
                'FTP_PORT': self.global_config.get('FTP_PORT'),
                'FTP_USERNAME': self.global_config.get('FTP_USERNAME'),
                'FTP_PASSWORD': self.global_config.get('FTP_PASSWORD'),
                'FTP_REMOTE_DIR': self.global_config.get('FTP_REMOTE_DIR'),
                'FTP_IS_ENABLED': self.global_config.get('FTP_IS_ENABLED'),
                'FTP_URL_PREFIX': self.global_config.get('FTP_URL_PREFIX'),
                # V3统一时间配置
                'EP_AI_ANALYSIS_DELAY_SEC': self.global_config.get('EP_AI_ANALYSIS_DELAY_SEC'),
                # 不包含图片相关配置，这些现在是纯全局的
            }
            
            # 加载该事件的专有配置
            for var_name in self.REQUIRED_PER_EVENT_VARS:
                env_var_name = f"EVENT_ID_{event_id}_{var_name}"
                value = os.getenv(env_var_name)
                
                if value is None:
                    error_msg = f"缺少事件 {event_id} 的必需环境变量: {env_var_name}"
                    logger.error(error_msg)
                    raise ConfigValidationError(error_msg)
                
                if not value.strip():
                    error_msg = f"事件 {event_id} 的环境变量 {env_var_name} 不能为空"
                    logger.error(error_msg)
                    raise ConfigValidationError(error_msg)
                
                # 类型转换
                if var_name == 'EP_PV_HOLDING_TIMEOUT':
                    try:
                        event_config[var_name] = int(value)
                        if event_config[var_name] <= 0:
                            raise ValueError("必须是正整数")
                    except ValueError:
                        error_msg = f"事件 {event_id} 的 {env_var_name} 必须是正整数"
                        logger.error(error_msg)
                        raise ConfigValidationError(error_msg)
                else:
                    event_config[var_name] = value
                
                logger.debug(f"加载事件 {event_id} 配置: {var_name}={value}")
            
            # 加载可选的per-event配置
            optional_configs = [
                # 告警配置
                'EP_PV_ALARM_TOPIC',
                # 详情信息配置
                'EP_PV_DETAIL_INFO_TOPIC',
                'EP_PV_DETAIL_INFO_FIELDS',
                # V3业务逻辑配置
                'EP_BusinessLogic_JUDGE_SOURCE',
                'EP_BusinessLogic_JUDGE_FIELD',
                'EP_BusinessLogic_JUDGE_LOGIC',
                'EP_BusinessLogic_JUDGE_VALUE',
                # V3排除匹配配置
                'EP_MQTT_EXCLUDE_INFO_SOURCE',
                'EP_MQTT_EXCLUDE_INFO_FIELD',
                'EP_MQTT_EXCLUDE_LOGIC',
                # AI提示词配置（新增）
                'EP_AI_PROMPT'
            ]
            for var_name in optional_configs:
                env_var_name = f"EVENT_ID_{event_id}_{var_name}"
                value = os.getenv(env_var_name)
                if value is not None:
                    # 处理字符串"None"转换为Python None值（用于禁用排除匹配）
                    if value.lower() == 'none' and var_name.startswith('EP_MQTT_EXCLUDE_'):
                        event_config[var_name] = None
                        logger.debug(f"事件 {event_id} 排除配置 {var_name} 设置为None（禁用）")
                    else:
                        event_config[var_name] = value
                else:
                    # 如果没有找到per-event配置，优先使用全局配置，然后使用默认值
                    if var_name in self.global_config and self.global_config[var_name]:
                        event_config[var_name] = self.global_config[var_name]
                        logger.debug(f"事件 {event_id} 使用全局配置: {var_name}={self.global_config[var_name]}")
                    elif var_name in self.DEFAULT_VALUES:
                        event_config[var_name] = self.DEFAULT_VALUES[var_name]
                        logger.debug(f"事件 {event_id} 使用默认值: {var_name}={self.DEFAULT_VALUES[var_name]}")
            
            # 处理特殊格式的配置
            self._process_special_configs_for_event(event_config)
            
            self.events_config[event_id] = event_config
            logger.info(f"事件 {event_id} 配置加载完成")
    
    def _process_special_configs_for_event(self, event_config: Dict[str, Any]):
        """处理单个事件的特殊格式配置"""
        # 处理JSON格式的配置
        json_configs = [
            'EP_START_DEVICE_EVENT_TOPIC',
            'EP_POSITION_START_VALUE_FROM_DEVICE_EVENT',
            'EP_PV_DETAIL_INFO_FIELDS',
        ]
        
        for config_name in json_configs:
            if config_name in event_config:
                value = event_config[config_name]
                # 跳过None值（用于禁用配置）
                if value is None:
                    logger.debug(f"跳过None值JSON解析: {config_name}")
                    continue
                try:
                    event_config[config_name] = json.loads(value)
                except json.JSONDecodeError as e:
                    error_msg = f"事件配置JSON解析失败: {config_name}={value}, 错误: {e}"
                    logger.error(error_msg)
                    raise ConfigValidationError(error_msg)
    
    def _load_ai_prompts_from_env(self, event_ids: List[str]):
        """从环境变量加载AI prompt映射（V3统一配置）"""
        logger.debug("从环境变量加载AI提示词配置")
        
        for event_id in event_ids:
            env_var_name = f"EVENT_ID_{event_id}_EP_AI_PROMPT"
            prompt_value = os.getenv(env_var_name)
            
            if prompt_value is None:
                logger.warning(f"事件 {event_id} 未配置AI提示词: {env_var_name}")
                logger.warning(f"如果这是业务逻辑事件，请设置: {env_var_name}=None")
                self.ai_prompts[event_id] = ""
                continue
                
            # 处理None值（业务逻辑事件）
            if prompt_value.lower() == 'none':
                self.ai_prompts[event_id] = ""
                logger.debug(f"事件 {event_id} 配置为业务逻辑事件（AI提示词=None）")
            else:
                self.ai_prompts[event_id] = prompt_value
                logger.debug(f"事件 {event_id} AI提示词: {prompt_value[:50]}...")
        
        logger.info(f"成功从环境变量加载 {len(self.ai_prompts)} 个AI prompt配置")
    
    # 移除events.ini文件相关方法（V3统一配置架构）
    
    # events.ini动态刷新功能已移除（V3统一配置架构）
    
    
    
    # 动态刷新功能已移除（V3统一配置架构）
    
    def _validate_configs(self):
        """验证所有事件配置"""
        logger.debug("验证多事件配置...")
        
        for event_id, config in self.events_config.items():
            try:
                self._validate_single_event_config(event_id, config)
            except ConfigValidationError as e:
                raise ConfigValidationError(f"事件 {event_id} 配置验证失败: {e}")
    
    def _validate_single_event_config(self, event_id: str, config: Dict[str, Any]):
        """验证单个事件配置"""
        # 验证端口号
        for port_config in ['MQTT_BROKER_PORT', 'FTP_PORT']:
            port = config.get(port_config)
            if port is not None and not (1 <= port <= 65535):
                raise ConfigValidationError(f"端口号无效: {port_config}={port}")
        
        # 验证超时时间
        timeout_configs = ['EP_PV_HOLDING_TIMEOUT']
        for timeout_config in timeout_configs:
            timeout = config.get(timeout_config)
            if timeout is not None and timeout <= 0:
                raise ConfigValidationError(f"超时时间必须大于0: {timeout_config}={timeout}")
        
        # V3配置参数验证
        ai_delay = config.get('EP_AI_ANALYSIS_DELAY_SEC')
        if ai_delay is not None and (not isinstance(ai_delay, (int, float)) or ai_delay < 0):
            raise ConfigValidationError(f"AI分析延迟时间必须是非负数: EP_AI_ANALYSIS_DELAY_SEC={ai_delay}")
        
        # 验证AI结果等待超时
        ai_timeout = config.get('EP_PV_AI_RESULT_WAIT_TIMEOUT')
        if ai_timeout is not None and (not isinstance(ai_timeout, (int, float)) or ai_timeout <= 0):
            raise ConfigValidationError(f"AI结果等待超时必须大于0: EP_PV_AI_RESULT_WAIT_TIMEOUT={ai_timeout}")
        
        # 验证设备事件主题列表
        topics = config.get('EP_START_DEVICE_EVENT_TOPIC', [])
        if not isinstance(topics, list) or len(topics) == 0:
            raise ConfigValidationError("设备事件主题列表不能为空")
        
        # 验证触发值配置
        start_values = config.get('EP_POSITION_START_VALUE_FROM_DEVICE_EVENT', {})
        if not isinstance(start_values, dict) or 'true' not in start_values or 'false' not in start_values:
            raise ConfigValidationError("触发值配置必须包含'true'和'false'键")
        
        # 移除事件优先级验证（不再需要EP_EV_PRIORITY）
    
    def _log_config_summary(self):
        """记录配置摘要"""
        logger.info("=== 多事件配置摘要 ===")
        logger.info(f"小区编号: {self.global_config.get('COMM_ID')}")
        logger.info(f"点位ID: {self.global_config.get('POSITION_ID')}")
        logger.info(f"点位名称: {self.global_config.get('EP_POSITION_NAME')}")
        logger.info(f"MQTT服务器: {self.global_config.get('MQTT_BROKER_HOST')}:{self.global_config.get('MQTT_BROKER_PORT')}")
        logger.info(f"FTP服务器: {self.global_config.get('FTP_HOST')}:{self.global_config.get('FTP_PORT')}")
        
        # 显示统一的图片配置
        logger.info(f"统一图片目录: {self.global_config.get('EP_PV_BIND_IPC_IMAGE_DIR')}")
        logger.info(f"统一裁剪坐标: {self.global_config.get('EP_PV_IMAGE_CROP_COORDINATES')}")
        logger.info(f"事件数量: {len(self.events_config)}")
        
        for event_id, config in self.events_config.items():
            logger.info(f"事件 {event_id}:")
            logger.info(f"滞留超时: {config.get('EP_PV_HOLDING_TIMEOUT')}秒")
            logger.info(f"设备主题数: {len(config.get('EP_START_DEVICE_EVENT_TOPIC', []))}")
            if event_id in self.ai_prompts:
                logger.info(f"AI Prompt: {self.ai_prompts[event_id][:50]}...")
        
        logger.info("======================")
    
    def get_ai_prompt(self, event_id: str) -> str:
        """获取事件的AI prompt（V3统一配置）"""
        if event_id not in self.ai_prompts:
            # 事件ID不存在，检查大小写问题
            logger.warning(f"事件 {event_id} 没有配置AI prompt")
            logger.warning(f"请在 .env 文件中添加以下配置:")
            logger.warning(f"  EVENT_ID_{event_id}_EP_AI_PROMPT=您的AI分析提示词")
            logger.warning(f"  或者对于业务逻辑事件: EVENT_ID_{event_id}_EP_AI_PROMPT=None")
            logger.warning(f"当前已配置的事件ID: {list(self.ai_prompts.keys())}")
            
            # 检查大小写不匹配
            for config_key in self.ai_prompts.keys():
                if config_key.upper() == event_id.upper() and config_key != event_id:
                    logger.warning(f"检测到大小写不匹配:")
                    logger.warning(f"  配置中: '{config_key}' (十六进制: {config_key.encode('utf-8').hex()})")
                    logger.warning(f"  请求的键: '{event_id}' (十六进制: {event_id.encode('utf-8').hex()})")
                    break
            return ""
        
        prompt = self.ai_prompts[event_id]
        
        # 处理 None 配置（业务逻辑事件）
        if prompt is None or (isinstance(prompt, str) and prompt.strip().lower() == 'none'):
            logger.debug(f"事件 {event_id} 配置为业务逻辑事件（AI提示词=None）")
            return ""
        
        # 处理空字符串（向后兼容）
        if not prompt or not prompt.strip():
            logger.debug(f"事件 {event_id} 配置了空的AI prompt（建议改为None）")
            return ""
            
        return prompt.strip()
    
    def get_prompt_for_event(self, event_id: str) -> str:
        """获取事件的prompt（V3兼容方法）"""
        return self.get_ai_prompt(event_id)
    
    def get_event_config(self, event_id: str) -> Dict[str, Any]:
        """获取单个事件的配置"""
        return self.events_config.get(event_id, {})
    
    def get_all_event_ids(self) -> List[str]:
        """获取所有事件ID"""
        return list(self.events_config.keys())
    
    # 为兼容性添加的方法（main.py中的validate_config_values函数需要）
    def get_comm_id(self) -> str:
        """获取小区编号"""
        return self.global_config.get('COMM_ID', '')
    
    def get_position_id(self) -> str:
        """获取点位ID"""
        return self.global_config.get('POSITION_ID', '')
    
    def get_event_ids(self) -> List[str]:
        """获取事件ID列表"""
        event_ids_str = self.global_config.get('EVENT_IDS', '')
        if not event_ids_str:
            return []
        try:
            event_ids = json.loads(event_ids_str)
            return event_ids if isinstance(event_ids, list) else []
        except json.JSONDecodeError:
            return []
    
    def get_mqtt_config(self) -> Dict[str, Any]:
        """获取MQTT配置"""
        return {
            'host': self.global_config.get('MQTT_BROKER_HOST', ''),
            'port': self.global_config.get('MQTT_BROKER_PORT', 1883),
            'username': self.global_config.get('MQTT_USERNAME', ''),
            'password': self.global_config.get('MQTT_PASSWORD', '')
        }
    
    def stop_reload(self):
        """停止配置重载（V3统一配置架构中为空实现）"""
        # V3架构中已移除动态配置重载功能，此方法为兼容性保留
        logger.debug("ConfigManager.stop_reload() 调用完成（V3架构中无需操作）")