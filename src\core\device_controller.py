#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MQTT业务绑定设备控制系统

根据MQTT消息中的业务条件，触发设备控制操作。
支持监听指定主题，检查特定字段值，并在条件满足时发送控制指令。
"""

import json
import logging
import time
import threading
from typing import Dict, List, Any, Optional
from src.core.mqtt_client import MQTTClient

logger = logging.getLogger(__name__)


class DeviceController:
    """
    设备控制器
    
    监听MQTT业务消息，根据配置的条件触发设备控制操作。
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化设备控制器
        
        Args:
            config: 配置参数，包含MQTT配置和设备控制参数
        """
        self.config = config
        self.mqtt_client: Optional[MQTTClient] = None
        self.running = False
        self.monitor_thread: Optional[threading.Thread] = None
        
        # 解析设备控制配置
        self._parse_device_config()
        
        logger.info(f"设备控制器初始化完成，业务主题: {self.business_topics}")
        logger.info(f"触发关键字: {self.business_keyword}={self.trigger_value}")
        logger.info(f"控制主题: {self.control_topics}")
    
    def _parse_device_config(self):
        """解析设备控制配置参数"""
        # 业务绑定配置
        self.business_topics = self.config.get('DEVICE_BIND_BUSINESS_TOPIC', [])
        if isinstance(self.business_topics, str):
            self.business_topics = [self.business_topics]
        
        self.business_keyword = self.config.get('DEVICE_BIND_BUSINESS_KEYWORD', '')
        self.trigger_value = self.config.get('DEVICE_BIND_BUSINESS_TRUE_KEYWORD_VALUE', '')
        
        # 设备控制配置
        self.control_topics = self.config.get('DEVICE_CONTROL_TOPIC', [])
        if isinstance(self.control_topics, str):
            self.control_topics = [self.control_topics]
        
        self.control_payload = self.config.get('DEVICE_CONTROL_PAYLOAD', {})
        
        # 验证必需配置
        if not self.business_topics:
            raise ValueError("DEVICE_BIND_BUSINESS_TOPIC 未配置")
        if not self.business_keyword:
            raise ValueError("DEVICE_BIND_BUSINESS_KEYWORD 未配置")
        if not self.trigger_value:
            raise ValueError("DEVICE_BIND_BUSINESS_TRUE_KEYWORD_VALUE 未配置")
        if not self.control_topics:
            raise ValueError("DEVICE_CONTROL_TOPIC 未配置")
        if not self.control_payload:
            raise ValueError("DEVICE_CONTROL_PAYLOAD 未配置")
    
    def _create_mqtt_client(self) -> MQTTClient:
        """创建MQTT客户端"""
        # 为设备控制器创建独立的MQTT客户端配置
        mqtt_config = {
            'MQTT_BROKER_HOST': self.config.get('MQTT_BROKER_HOST'),
            'MQTT_BROKER_PORT': self.config.get('MQTT_BROKER_PORT', 1883),
            'MQTT_USERNAME': self.config.get('MQTT_USERNAME', ''),
            'MQTT_PASSWORD': self.config.get('MQTT_PASSWORD', ''),
            'COMM_ID': self.config.get('COMM_ID', 'DeviceController'),
            'POSITION_ID': self.config.get('POSITION_ID', 'DC'),
            'EVENT_ID': 'DEVICE_CTRL'
        }
        
        return MQTTClient(mqtt_config)
    
    def _on_business_message(self, topic: str, message: Dict[str, Any]):
        """
        处理业务消息
        
        Args:
            topic: 消息主题
            message: 消息内容
        """
        try:
            logger.info(f"收到业务消息: {topic}")
            logger.debug(f"消息内容: {json.dumps(message, ensure_ascii=False)}")
            
            # 检查是否包含业务关键字段
            if self.business_keyword not in message:
                logger.debug(f"消息中未找到关键字段 '{self.business_keyword}'，跳过处理")
                return
            
            field_value = message[self.business_keyword]
            logger.info(f"检查字段 '{self.business_keyword}' 值: {field_value}")
            
            # 检查字段值是否匹配触发条件
            if str(field_value) == str(self.trigger_value):
                logger.info(f"触发条件满足: {self.business_keyword}={field_value}")
                self._trigger_device_control(topic, message)
            else:
                logger.debug(f"触发条件不满足: 期望值={self.trigger_value}, 实际值={field_value}")
                
        except Exception as e:
            logger.error(f"处理业务消息时发生异常: {e}", exc_info=True)
    
    def _trigger_device_control(self, trigger_topic: str, trigger_message: Dict[str, Any]):
        """
        触发设备控制操作
        
        Args:
            trigger_topic: 触发消息的主题
            trigger_message: 触发消息的内容
        """
        try:
            # 生成HH:MM:SS.fff格式的时间戳
            from datetime import datetime
            timestamp = datetime.now().strftime("%H:%M:%S.%f")[:-3]
            
            logger.info(f"[{timestamp}] 开始执行设备控制操作")
            logger.info(f"[{timestamp}] 触发源: {trigger_topic}")
            
            # 向所有配置的控制主题发送控制消息
            success_count = 0
            for control_topic in self.control_topics:
                if self._send_control_message(control_topic):
                    success_count += 1
            
            # 记录执行结果
            if success_count == len(self.control_topics):
                logger.info(f"[{timestamp}] 设备控制操作全部成功 ({success_count}/{len(self.control_topics)})")
            elif success_count > 0:
                logger.warning(f"[{timestamp}] 设备控制操作部分成功 ({success_count}/{len(self.control_topics)})")
            else:
                logger.error(f"[{timestamp}] 设备控制操作全部失败 ({success_count}/{len(self.control_topics)})")
                
        except Exception as e:
            logger.error(f"执行设备控制操作时发生异常: {e}", exc_info=True)
    
    def _send_control_message(self, control_topic: str) -> bool:
        """
        发送控制消息到指定主题
        
        Args:
            control_topic: 控制主题
            
        Returns:
            bool: 发送是否成功
        """
        try:
            # 生成HH:MM:SS.fff格式的时间戳
            from datetime import datetime
            timestamp = datetime.now().strftime("%H:%M:%S.%f")[:-3]
            
            logger.info(f"[{timestamp}] 发送控制消息到主题: {control_topic}")
            logger.debug(f"[{timestamp}] 控制消息内容: {json.dumps(self.control_payload, ensure_ascii=False)}")
            
            success = self.mqtt_client.publish(
                topic=control_topic,
                message=self.control_payload,
                qos=1  # 使用QoS 1确保消息送达
            )
            
            if success:
                logger.info(f"[{timestamp}] 控制消息发送成功: {control_topic}")
            else:
                logger.error(f"[{timestamp}] 控制消息发送失败: {control_topic}")
            
            return success
            
        except Exception as e:
            logger.error(f"发送控制消息异常: {e}", exc_info=True)
            return False
    
    def start(self) -> bool:
        """
        启动设备控制器
        
        Returns:
            bool: 启动是否成功
        """
        try:
            logger.info("正在启动设备控制器...")
            
            # 创建MQTT客户端
            self.mqtt_client = self._create_mqtt_client()
            
            # 连接MQTT服务器
            if not self.mqtt_client.connect():
                logger.error("MQTT连接失败，无法启动设备控制器")
                return False
            
            # 订阅业务主题
            for topic in self.business_topics:
                logger.info(f"订阅业务主题: {topic}")
                self.mqtt_client.subscribe(topic, self._on_business_message, qos=1)
            
            self.running = True
            logger.info("设备控制器启动成功")
            return True
            
        except Exception as e:
            logger.error(f"启动设备控制器时发生异常: {e}", exc_info=True)
            return False
    
    def stop(self):
        """停止设备控制器"""
        try:
            logger.info("正在停止设备控制器...")
            
            self.running = False
            
            # 断开MQTT连接
            if self.mqtt_client:
                self.mqtt_client.disconnect()
                self.mqtt_client = None
            
            logger.info("设备控制器已停止")
            
        except Exception as e:
            logger.error(f"停止设备控制器时发生异常: {e}", exc_info=True)
    
    def is_running(self) -> bool:
        """检查设备控制器是否正在运行"""
        return self.running and (self.mqtt_client is not None) and self.mqtt_client.is_connected()
    
    def get_status(self) -> Dict[str, Any]:
        """
        获取设备控制器状态信息
        
        Returns:
            Dict: 状态信息
        """
        return {
            'running': self.running,
            'mqtt_connected': self.mqtt_client.is_connected() if self.mqtt_client else False,
            'business_topics': self.business_topics,
            'control_topics': self.control_topics,
            'business_keyword': self.business_keyword,
            'trigger_value': self.trigger_value
        }