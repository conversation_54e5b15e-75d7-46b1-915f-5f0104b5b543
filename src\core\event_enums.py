#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
事件状态和阶段枚举定义

定义事件状态和阶段枚举，避免循环导入问题。
"""

from enum import Enum


class EventState(Enum):
    """事件状态枚举"""
    IDLE = "idle"                           # 空闲状态
    COLLECTING = "collecting"               # 信息收集期
    WAITING_AI_RESULT = "waiting_ai_result" # 等待AI结果
    ALARM_READY = "alarm_ready"             # 告警就绪
    EXCLUDED = "excluded"                   # 已被排除