#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
图片获取和处理模块

从IPC目录获取图片，支持时间戳匹配和重试机制。
"""

import os
import re
import logging
import tempfile
from datetime import datetime
from typing import Optional, Tuple, List, Dict, Any

# 导入项目内部的工具模块
try:
    from ..utils.image_processor import crop_image
    from ..utils.image_ftp_uploader import file_upload_ftp_to_cdn
    from ..utils.timestamp_formatter import format_trigger_timestamp_for_log, format_image_timestamp_for_log
except ImportError:
    # 备用导入路径（用于测试环境）
    import sys
    import os
    utils_path = os.path.join(os.path.dirname(__file__), '..', 'utils')
    if utils_path not in sys.path:
        sys.path.insert(0, utils_path)
    from image_processor import crop_image
    from image_ftp_uploader import file_upload_ftp_to_cdn
    from timestamp_formatter import format_trigger_timestamp_for_log, format_image_timestamp_for_log


logger = logging.getLogger(__name__)


class ImageHandler:
    """
    图片处理器
    
    负责从IPC目录获取图片、裁剪处理、FTP上传等功能。
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化图片处理器

        Args:
            config: 配置参数
        """
        self.image_handler_config = config
        self.ipc_image_dir = config.get('EP_PV_BIND_IPC_IMAGE_DIR')
        self.ftp_enabled = config.get('FTP_IS_ENABLED', True)
        
        # 处理图片裁剪坐标配置
        crop_coords_raw = config.get('EP_PV_IMAGE_CROP_COORDINATES', '[0, 0, 0, 0]')
        self.crop_coords = self._parse_crop_coordinates(crop_coords_raw)

        # 严格的配置验证
        self._validate_configuration()

        logger.info(f"图片处理器初始化: IPC目录={self.ipc_image_dir}")
        logger.info(f"裁剪坐标={self.crop_coords}, FTP上传={'启用' if self.ftp_enabled else '禁用'}")
        logger.info("查找逻辑: 从AI监测开始时间往前查找到触发事件时间点，一次性.")
    
    def _parse_crop_coordinates(self, coords_data):
        """解析图片裁剪坐标配置"""
        try:
            # 如果已经是列表或元组格式，直接使用
            if isinstance(coords_data, (list, tuple)):
                coords = list(coords_data)
            else:
                # 如果是字符串格式，进行JSON解析
                import json
                coords_str = str(coords_data)
                if coords_str.strip().startswith('[') and coords_str.strip().endswith(']'):
                    coords = json.loads(coords_str)
                else:
                    # 支持逗号分隔格式
                    coords = [int(x.strip()) for x in coords_str.split(',')]
            
            # 验证坐标数量
            if len(coords) != 4:
                raise ValueError("坐标必须包含4个值")
                
            # 转换为元组并返回
            return tuple(int(coord) for coord in coords)
            
        except (ValueError, json.JSONDecodeError, TypeError) as e:
            logger.error(f"图片裁剪坐标解析失败: {coords_data}, 错误: {e}")
            logger.info("使用默认坐标 (0, 0, 0, 0)")
            return (0, 0, 0, 0)
    
    def _validate_configuration(self):
        """验证配置参数的有效性"""
        # 验证EP_PV_BIND_IPC_IMAGE_DIR
        if not self.ipc_image_dir:
            raise ValueError("配置错误: EP_PV_BIND_IPC_IMAGE_DIR 参数缺失")

        if not os.path.exists(self.ipc_image_dir):
            # Windows和Linux兼容的目录创建
            try:
                os.makedirs(self.ipc_image_dir, exist_ok=True)
                logger.info(f"目录已创建: {self.ipc_image_dir}")
            except OSError as e:
                raise RuntimeError(f"创建IPC目录失败: {self.ipc_image_dir}, 错误: {e}")
            
        if not os.access(self.ipc_image_dir, os.R_OK):
            raise RuntimeError(f"配置错误: IPC根目录不可读: {self.ipc_image_dir}")

        # 验证EP_PV_IMAGE_CROP_COORDINATES
        if not isinstance(self.crop_coords, (tuple, list)) or len(self.crop_coords) != 4:
            raise ValueError(f"配置错误: EP_PV_IMAGE_CROP_COORDINATES 必须为4个数字的元组或列表，当前值: {self.crop_coords}")
        
        if not all(isinstance(coord, (int, float)) and coord >= 0 for coord in self.crop_coords):
            raise ValueError(f"配置错误: EP_PV_IMAGE_CROP_COORDINATES 所有坐标必须为非负数，当前值: {self.crop_coords}")

        logger.info("配置参数验证通过\t\t\t\t")
        logger.info("按照IPC设备的日期目录结构进行图片扫描\t\t")
    
    def find_and_process_image(self, trigger_timestamp: int, event_config: Optional[Dict[str, Any]] = None) -> Optional[Tuple[str, str, int, Optional[str]]]:
        """
        查找并处理图片

        Args:
            trigger_timestamp: 触发时间戳（毫秒）
            event_config: 事件特有配置（可选，用于覆盖默认配置）

        Returns:
            Optional[Tuple[str, str, int, Optional[str]]]: (原始图片路径, 处理后图片路径, 图片时间戳, CDN_URL) 或 None
        """
        logger.info(f"开始查找和处理图片，{format_trigger_timestamp_for_log(trigger_timestamp)}")

        # 查找图片
        image_path, image_timestamp = self._find_closest_image(trigger_timestamp)
        if not image_path or image_timestamp is None:
            logger.error("未找到匹配的图片")
            return None

        logger.info(f"找到图片: {image_path}, {format_image_timestamp_for_log(image_timestamp)}")

        # 从文件名提取原始17位时间戳字符串（符合FR-008要求）
        original_timestamp_str = self._extract_original_timestamp_string(image_path)
        if not original_timestamp_str:
            logger.error("无法从文件名提取原始17位时间戳字符串")
            return None

        # 处理图片 - 直接传递17位时间戳字符串（不再使用captured_datetime()格式）
        processed_path, cdn_url = self._process_image(image_path, original_timestamp_str, event_config)
        if not processed_path:
            logger.error("图片处理失败")
            return None

        logger.info(f"图片处理完成,CDN URL: {cdn_url}")

        return image_path, processed_path, image_timestamp, cdn_url
    
    def _find_closest_image(self, trigger_timestamp: int) -> Tuple[Optional[str], Optional[int]]:
        """
        查找最接近触发时间的图片

        新的查找逻辑：从AI监测开始时间往前查找到触发事件时间点，不进行重试
        按照IPC设备的日期目录结构进行精确扫描

        Args:
            trigger_timestamp: 触发时间戳（毫秒）

        Returns:
            Tuple[Optional[str], Optional[int]]: (图片路径, 图片时间戳) 或 (None, None)
        """
        logger.debug(f"开始图片查找 ({format_trigger_timestamp_for_log(trigger_timestamp)})")

        try:
            # 检查IPC根目录是否存在
            if not self.ipc_image_dir or not os.path.exists(self.ipc_image_dir):
                logger.warning(f"IPC根目录不存在: {self.ipc_image_dir}")
                return None, None

            # 根据触发时间戳计算目标日期目录
            target_date_dir = self._calculate_target_date_directory(trigger_timestamp)
            if not target_date_dir:
                logger.error("无法计算目标日期目录")
                return None, None

            # 构建完整的目标目录路径
            target_dir_path = os.path.join(self.ipc_image_dir, target_date_dir)
            logger.info(f"目标扫描目录: {target_dir_path}")

            # 检查目标日期目录是否存在
            if not os.path.exists(target_dir_path):
                logger.error(f"目标日期目录不存在: {target_dir_path} (计算日期: {target_date_dir})")
                return None, None

            # 获取目标日期目录中的所有图片文件
            image_files = self._get_image_files_from_date_directory(target_dir_path)
            if not image_files:
                logger.info(f"目标日期目录中没有符合格式的图片文件: {target_dir_path}")
                return None, None

            # 查找时间戳最接近的图片
            closest_image = self._find_closest_by_timestamp(image_files, trigger_timestamp)
            if closest_image:
                logger.info("成功找到匹配图片")
                return closest_image

            logger.info("在指定时间范围内未找到匹配的图片")
            return None, None

        except Exception as e:
            logger.error(f"图片查找时发生异常: {e}", exc_info=True)
            return None, None
    
    def _calculate_target_date_directory(self, trigger_timestamp: int) -> Optional[str]:
        """
        根据触发时间戳计算目标日期目录名称

        Args:
            trigger_timestamp: 触发时间戳（毫秒）

        Returns:
            Optional[str]: 目标日期目录名称（格式：YYYY_MM_DD-YYYY_MM_DD）或 None
        """
        try:
            # 验证输入参数
            if not isinstance(trigger_timestamp, (int, float)):
                logger.error(f"时间戳类型错误: {type(trigger_timestamp)}, 值: {trigger_timestamp}")
                return None

            if trigger_timestamp <= 0:
                logger.error(f"时间戳值无效: {trigger_timestamp}")
                return None

            # 将毫秒时间戳转换为秒级时间戳
            timestamp_seconds = trigger_timestamp / 1000.0

            # 转换为datetime对象
            dt = datetime.fromtimestamp(timestamp_seconds)

            # 格式化为 YYYY_MM_DD 格式
            date_str = dt.strftime('%Y_%m_%d')

            # 构建目录名称：YYYY_MM_DD-YYYY_MM_DD（同一天）
            target_date_dir = f"{date_str}-{date_str}"

            logger.debug(f"计算目标日期目录: 时间戳={trigger_timestamp} -> 日期={date_str} -> 目录={target_date_dir}")

            return target_date_dir

        except (ValueError, OSError, OverflowError, TypeError) as e:
            logger.error(f"时间戳转换失败: {trigger_timestamp}, 错误: {e}")
            return None

    def _is_valid_image_filename(self, filename: str) -> bool:
        """
        检查文件名是否包含可识别的时间戳格式

        Args:
            filename: 文件名

        Returns:
            bool: 是否符合格式要求
        """
        # 快速匹配时间戳格式
        pattern = r'.*_(\d{17})_.*|.*_(\d{14})_.*|.*_(\d{13})_.*|.*_(\d{10})_.*'
        is_valid = bool(re.search(pattern, filename))
        
        if not is_valid:
            logger.debug(f"文件名不符合时间戳格式要求: {filename} (需要格式: *_数字17位_* 或 *_数字14位_* 或 *_数字13位_* 或 *_数字10位_*)")
        
        return is_valid
    
    def _get_image_files_from_date_directory(self, date_dir_path: str) -> List[str]:
        """
        从指定的日期目录中获取图片文件

        Args:
            date_dir_path: 日期目录的完整路径

        Returns:
            List[str]: 图片文件路径列表
        """
        # NFR-011: 输入参数验证
        if not isinstance(date_dir_path, str):
            error_msg = f"目录路径必须为字符串类型，当前类型: {type(date_dir_path)}"
            logger.error(error_msg)
            return []

        if not date_dir_path.strip():
            error_msg = "目录路径不能为空"
            logger.error(error_msg)
            return []

        # NFR-012: 记录上下文信息
        logger.debug(f"开始扫描日期目录: {date_dir_path}")

        try:
            # NFR-010: 文件I/O操作异常处理 - 目录存在性检查
            try:
                exists = os.path.exists(date_dir_path)
            except (OSError, IOError) as e:
                error_msg = f"检查目录存在性失败: {date_dir_path}"
                logger.error(f"{error_msg}, 错误类型: {type(e).__name__}, 错误信息: {e}")
                return []

            if not exists:
                logger.warning(f"日期目录不存在: {date_dir_path}")
                return []

            # NFR-010: 文件I/O操作异常处理 - 目录访问权限检查
            try:
                readable = os.access(date_dir_path, os.R_OK)
            except (OSError, IOError) as e:
                error_msg = f"检查目录访问权限失败: {date_dir_path}"
                logger.error(f"{error_msg}, 错误类型: {type(e).__name__}, 错误信息: {e}")
                return []

            if not readable:
                logger.warning(f"日期目录不可读: {date_dir_path}")
                return []

            # NFR-010: 文件I/O操作异常处理 - 目录列表获取
            try:
                files = os.listdir(date_dir_path)
            except (OSError, IOError, PermissionError) as e:
                error_msg = f"读取目录内容失败: {date_dir_path}"
                logger.error(f"{error_msg}, 错误类型: {type(e).__name__}, 错误信息: {e}")
                return []
            except UnicodeDecodeError as e:
                error_msg = f"目录路径编码错误: {date_dir_path}"
                logger.error(f"{error_msg}, 错误类型: {type(e).__name__}, 错误信息: {e}")
                return []
            
            if not files:
                logger.info(f"日期目录为空: {date_dir_path}")
                return []

            image_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif'}
            image_files = []
            filtered_count = 0
            total_files = 0
            error_files = 0

            for file in files:
                try:
                    # NFR-010: 路径操作异常处理
                    try:
                        full_path = os.path.join(date_dir_path, file)
                    except (TypeError, ValueError) as e:
                        logger.warning(f"路径拼接失败，文件名: {file}, 错误: {e}")
                        error_files += 1
                        continue

                    # NFR-010: 文件I/O操作异常处理 - 文件类型检查
                    try:
                        is_file = os.path.isfile(full_path)
                    except (OSError, IOError) as e:
                        logger.warning(f"检查文件类型失败: {full_path}, 错误: {e}")
                        error_files += 1
                        continue

                    # 仅处理文件，跳过子目录
                    if not is_file:
                        continue

                    total_files += 1

                    # 检查文件扩展名
                    if any(file.lower().endswith(ext) for ext in image_extensions):
                        # 添加文件名格式验证
                        if self._is_valid_image_filename(file):
                            image_files.append(full_path)
                        else:
                            filtered_count += 1

                except Exception as e:
                    # NFR-011: 单个文件处理异常不影响整体流程
                    logger.warning(f"处理文件时发生异常: {file}, 错误类型: {type(e).__name__}, 错误信息: {e}")
                    error_files += 1
                    continue

            logger.info(f"日期目录扫描完成: {date_dir_path}, 有效图片: {len(image_files)}个")
            
            if len(image_files) == 0 and total_files > 0:
                logger.warning(f"目标日期目录中没有符合格式的图片文件: {date_dir_path}")
                logger.warning(f"过滤原因: 文件名不包含所需的时间戳格式 (*_数字17位_* 或 *_数字14位_* 或 *_数字13位_* 或 *_数字10位_*)")
                if filtered_count > 0:
                    logger.warning(f"共有 {filtered_count} 个图片文件因格式不符被过滤")
            elif len(image_files) == 0:
                logger.info(f"目标日期目录中没有图片文件: {date_dir_path}")

            return image_files

        except Exception as e:
            # NFR-011: 安全网机制 - 通用异常捕获
            error_msg = f"扫描日期目录时发生未预期异常: {date_dir_path}"
            logger.error(f"{error_msg}, 错误类型: {type(e).__name__}, 错误信息: {e}", exc_info=True)
            return []

    def _get_image_files(self) -> List[str]:
        """
        获取IPC目录中的图片文件（已废弃，保留用于向后兼容）

        注意：此方法已被 _get_image_files_from_date_directory 替代
        根据FR-010规范，不应直接扫描根目录
        """
        logger.warning("使用了已废弃的根目录扫描方法，建议使用基于日期目录的扫描")

        try:
            files = os.listdir(self.ipc_image_dir)
            image_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif'}

            image_files = []
            filtered_count = 0

            for file in files:
                if any(file.lower().endswith(ext) for ext in image_extensions):
                    if self.ipc_image_dir:  # 类型检查
                        full_path = os.path.join(self.ipc_image_dir, file)
                        if os.path.isfile(full_path):
                            # 添加文件名格式验证
                            if self._is_valid_image_filename(file):
                                image_files.append(full_path)
                            else:
                                filtered_count += 1
                                logger.debug(f"过滤不符合格式的文件: {file}")

            logger.debug(f"找到 {len(image_files)} 个符合格式的图片文件，过滤了 {filtered_count} 个文件")
            return image_files

        except Exception as e:
            logger.error(f"读取IPC目录失败: {e}")
            return []
    
    def _find_closest_by_timestamp(self, image_files: List[str], trigger_timestamp: int) -> Optional[Tuple[str, int]]:
        """
        查找触发信号时间戳之后的第一张图片

        查找逻辑：选择时间戳大于等于触发信号时间戳的第一张图片（时间戳最小的）

        Args:
            image_files: 图片文件列表
            trigger_timestamp: 触发时间戳（毫秒）

        Returns:
            Optional[Tuple[str, int]]: (图片路径, 图片时间戳) 或 None
        """
        # 查找触发信号时间戳之后的第一张图片
        current_timestamp = int(datetime.now().timestamp() * 1000)  # 当前时间戳（毫秒）
        str_formatted_trigger_time = format_image_timestamp_for_log(trigger_timestamp)  # 触发事件时间点
        str_formatted_current_time = format_image_timestamp_for_log(current_timestamp)  # AI监测开始时间
        
        # 设置图片查找时间窗口：触发时间前2秒到后10秒
        before_window = 2000  # 触发前2秒
        after_window = 10000  # 触发后10秒
        int_window_start = trigger_timestamp - before_window
        int_window_end = trigger_timestamp + after_window

        logger.debug(f"触发时间: {str_formatted_trigger_time}, 当前时间: {str_formatted_current_time}")
        logger.debug(f"查找策略: 触发时间前{before_window}ms到后{after_window}ms的时间窗口内查找最佳图片")
        logger.debug(f"开始处理 {len(image_files)} 个文件")
        
        candidates = []
        valid_files = 0
        invalid_files = 0
        out_of_window_files = 0
        
        for image_path in image_files:
            filename = os.path.basename(image_path)
            # 从文件名提取时间戳
            image_timestamp = self._extract_timestamp_from_image(image_path)
            
            if image_timestamp is None:
                invalid_files += 1
                logger.debug(f"时间戳提取失败: {filename}")
                continue
            
            valid_files += 1
            
            # 选择时间窗口内的图片
            if int_window_start <= image_timestamp <= int_window_end:
                candidates.append((image_path, image_timestamp))
            else:
                out_of_window_files += 1

        if not candidates:
            logger.info("未找到时间窗口内的候选图片")
            return None

        # 选择最接近触发时间的图片（优先选择触发后的，如果没有则选择触发前最近的）
        candidates.sort(key=lambda x: (
            0 if x[1] >= trigger_timestamp else 1,  # 触发后的图片优先
            abs(x[1] - trigger_timestamp)  # 然后按时间距离排序
        ))
        best_match = candidates[0]

        time_diff = best_match[1] - trigger_timestamp
        position = "后" if time_diff >= 0 else "前"
        logger.info(f"选择最佳匹配图片: {os.path.basename(best_match[0])}, 触发{position}时间差: {abs(time_diff)}ms")
        return best_match[0], best_match[1]
    
    def _extract_timestamp_from_image(self, image_path: str) -> Optional[int]:
        """
        从图片文件提取时间戳
        
        Args:
            image_path: 图片文件路径
            
        Returns:
            Optional[int]: 时间戳（毫秒）或 None
        """
        filename = os.path.basename(image_path)
        
        # 尝试从文件名提取时间戳
        # 支持格式: IPC-LFY-OUT01_G31870503_20250621153744628_TIMING.jpg
        patterns = [
            r'_(\d{17})_',  # 17位时间戳
            r'_(\d{14})_',  # 14位时间戳
            r'_(\d{13})_',  # 13位时间戳（毫秒）
            r'_(\d{10})_',  # 10位时间戳（秒）
        ]
        
        for pattern in patterns:
            match = re.search(pattern, filename)
            if match:
                timestamp_str = match.group(1)
                try:
                    timestamp = int(timestamp_str)
                    
                    # 根据长度判断时间戳类型并转换为毫秒
                    if len(timestamp_str) == 10:  # 秒级时间戳
                        timestamp = timestamp * 1000
                    elif len(timestamp_str) == 14:  # 日期时间格式
                        # 20250621153744 -> datetime -> timestamp
                        dt_str = timestamp_str
                        dt = datetime.strptime(dt_str, '%Y%m%d%H%M%S')
                        # 按本地时间处理，不进行时区转换
                        timestamp = int(dt.timestamp() * 1000)
                    elif len(timestamp_str) == 17:  # 带毫秒的日期时间格式
                        # 20250621153744628 -> datetime -> timestamp
                        dt_str = timestamp_str[:14]
                        ms = int(timestamp_str[14:])
                        dt = datetime.strptime(dt_str, '%Y%m%d%H%M%S')
                        # 按本地时间处理，不进行时区转换
                        timestamp = int(dt.timestamp() * 1000) + ms
                    
                    return timestamp
                    
                except ValueError as e:
                    logger.debug(f"时间戳转换失败: {timestamp_str}, 错误: {e}")
                    continue
        
        # 如果文件名中没有时间戳，返回None（不允许使用文件修改时间）
        logger.warning(f"文件名格式不符合要求，无法提取时间戳: {filename}")
        return None
    
    def _extract_original_timestamp_string(self, image_path: str) -> Optional[str]:
        """
        从图片文件名提取原始17位时间戳字符串
        
        按照FR-007要求，准确提取17位抓拍时间戳字符串
        
        Args:
            image_path: 图片文件路径
            
        Returns:
            Optional[str]: 原始17位时间戳字符串或None
        """
        filename = os.path.basename(image_path)
        
        try:
            # 按照FR-007要求，仅支持17位时间戳格式
            pattern = r'_(\d{17})_'
            match = re.search(pattern, filename)
            
            if match:
                timestamp_str = match.group(1)
                
                # 验证格式：前14位应该是有效日期时间，后3位是毫秒
                if len(timestamp_str) == 17 and timestamp_str.startswith('20'):
                    # 验证前14位是否为有效日期时间格式
                    dt_part = timestamp_str[:14]
                    ms_part = timestamp_str[14:]
                    
                    try:
                        # 验证日期时间部分
                        datetime.strptime(dt_part, '%Y%m%d%H%M%S')
                        
                        # 验证毫秒部分
                        ms_value = int(ms_part)
                        if 0 <= ms_value <= 999:
                            logger.debug(f"提取原始17位时间戳: {filename} -> {timestamp_str}")
                            return timestamp_str
                        else:
                            logger.warning(f"毫秒部分超出范围: {ms_part}")
                    except ValueError as e:
                        logger.warning(f"日期时间部分格式错误: {dt_part}, 错误: {e}")
                
            logger.warning(f"文件名不符合17位时间戳格式要求: {filename}")
            return None
            
        except Exception as e:
            logger.error(f"提取原始时间戳字符串时发生异常: {e}", exc_info=True)
            return None
    
    def _process_image(self, image_path: str, captured_datetime: str, event_config: Optional[Dict[str, Any]] = None) -> Tuple[Optional[str], Optional[str]]:
        """
        处理图片：裁剪并上传到FTP

        Args:
            image_path: 图片文件路径
            captured_datetime: 原始17位时间戳字符串
            event_config: 事件特有配置（可选，用于覆盖默认配置）

        Returns:
            Tuple[Optional[str], Optional[str]]: (处理后的图片路径, CDN URL)
        """
        try:
            logger.info("开始处理图片")
            
            # 验证17位时间戳格式
            if not captured_datetime or len(captured_datetime) != 17:
                raise ValueError(f"时间戳长度错误，期望17位，实际{len(captured_datetime)}位: {captured_datetime}")
            
            if not captured_datetime.isdigit():
                raise ValueError(f"时间戳必须为纯数字: {captured_datetime}")
            
            # 获取事件特有配置，如果没有则使用默认配置
            crop_coords = self.crop_coords
            ftp_enabled = self.ftp_enabled
            
            if event_config:
                # 不再从事件配置获取图片裁剪坐标，统一使用全局配置
                ftp_enabled = event_config.get('FTP_IS_ENABLED', self.ftp_enabled)
            
            # 创建临时目录
            temp_dir = tempfile.mkdtemp(prefix="event_processor_")
            
            logger.info(f"开始裁剪图片.裁剪坐标: {crop_coords}")

            # 执行裁剪 - 输出文件名将自动添加-CROPPED后缀
            cropped_path = crop_image(image_path, temp_dir, crop_coords)

            if cropped_path:
                logger.info(f"图片裁剪成功: {os.path.basename(cropped_path)}")

                # 上传到FTP并获取CDN URL
                cdn_url = None
                if ftp_enabled:
                    # 计算日期目录（由调用者提供给FTP模块）
                    date_dir = self._calculate_date_directory_from_timestamp(captured_datetime)
                    cdn_url = self._upload_to_ftp(cropped_path, date_dir, event_config)

                return cropped_path, cdn_url
            else:
                logger.error("图片裁剪失败")
                return None, None
        except ValueError as e:
            logger.error(f"时间戳格式错误: {e}")
            logger.error(f"输入参数: captured_datetime={captured_datetime}")
            logger.error("解决建议: 请检查文件名是否包含正确的17位时间戳格式(YYYYMMDDHHMMSSFFF)")
            return None, None
        except OSError as e:
            logger.error(f"系统调用错误: {e}")
            logger.error(f"错误代码: {e.errno if hasattr(e, 'errno') else 'N/A'}")
            logger.error("解决建议: 请检查系统时间设置和文件系统权限")
            return None, None
        except PermissionError as e:
            logger.error(f"权限错误: {e}")
            logger.error(f"影响路径: {image_path}")
            logger.error("解决建议: 请检查文件和目录的访问权限")
            return None, None
        except FileNotFoundError as e:
            logger.error(f"文件未找到: {e}")
            logger.error(f"影响路径: {image_path}")
            logger.error("解决建议: 请确认文件路径正确且文件存在")
            return None, None
        except Exception as e:
            logger.error(f"图片处理异常: {e}")
            logger.error(f"异常类型: {type(e).__name__}")
            logger.error(f"输入参数: image_path={image_path}, captured_datetime={captured_datetime}")
            logger.error("解决建议: 请检查图片文件完整性和系统资源")
            logger.debug("详细异常信息", exc_info=True)
            return None, None

    def _calculate_date_directory_from_timestamp(self, timestamp_str: str) -> str:
        """
        从17位时间戳计算日期目录
        
        Args:
            timestamp_str: 17位时间戳字符串
            
        Returns:
            str: 日期目录字符串，格式：YYYYMM
        """
        try:
            # 提取年月：YYYYMM
            year = timestamp_str[:4]
            month = timestamp_str[4:6]
            return f"{year}{month}"
        except Exception as e:
            logger.warning(f"计算日期目录失败: {e}, 使用当前日期")
            return datetime.now().strftime('%Y%m')
    
    def _upload_to_ftp(self, image_path: str, date_directory: str = None, event_config: Optional[Dict[str, Any]] = None) -> Optional[str]:
        """
        上传图片到FTP并返回CDN URL

        Args:
            image_path: 图片路径
            date_directory: 日期子目录

        Returns:
            Optional[str]: CDN URL，上传失败时返回None
        """
        # NFR-011: 输入参数验证
        if not isinstance(image_path, str):
            error_msg = f"图片路径必须为字符串类型，当前类型: {type(image_path)}"
            logger.error(error_msg)
            return None

        if not image_path.strip():
            error_msg = "图片路径不能为空"
            logger.error(error_msg)
            return None

        # NFR-012: 记录上下文信息
        logger.info(f"开始FTP上传: {os.path.basename(image_path)}")

        try:
            # NFR-010: 文件I/O操作异常处理 - 检查文件存在性
            try:
                if not os.path.exists(image_path):
                    error_msg = f"上传文件不存在: {image_path}"
                    logger.error(error_msg)
                    return None
            except (OSError, IOError) as e:
                error_msg = f"检查上传文件存在性失败: {image_path}"
                logger.error(f"{error_msg}, 错误类型: {type(e).__name__}, 错误信息: {e}")
                return None

            # NFR-010: 配置参数处理异常处理
            try:
                ftp_config = {
                    'ftp_host': self.image_handler_config.get('FTP_HOST'),
                    'ftp_port': self.image_handler_config.get('FTP_PORT'),
                    'ftp_username': self.image_handler_config.get('FTP_USERNAME'),
                    'ftp_password': self.image_handler_config.get('FTP_PASSWORD'),
                    'ftp_remote_dir': self.image_handler_config.get('FTP_REMOTE_DIR'),
                    'ftp_base_url': self.image_handler_config.get('FTP_URL_PREFIX', '')
                }

                # 验证必需的FTP配置
                required_configs = ['ftp_host', 'ftp_username', 'ftp_password']
                for config_key in required_configs:
                    if not ftp_config.get(config_key):
                        error_msg = f"FTP配置缺失: {config_key}"
                        logger.error(error_msg)
                        return None

            except (AttributeError, TypeError) as e:
                error_msg = "获取FTP配置失败"
                logger.error(f"{error_msg}, 错误类型: {type(e).__name__}, 错误信息: {e}")
                return None

            # NFR-010: 网络操作异常处理 - FTP上传
            # 获取position_id配置参数
            position_id = self.image_handler_config.get('POSITION_ID')
            if not position_id:
                logger.error("配置缺失: POSITION_ID")
                return None
            
            try:
                cdn_url = file_upload_ftp_to_cdn(image_path, ftp_config, date_directory, position_id)
            except ConnectionError as e:
                error_msg = f"FTP连接失败: {ftp_config.get('ftp_host', 'unknown')}"
                logger.error(f"{error_msg}, 错误类型: {type(e).__name__}, 错误信息: {e}")
                logger.error("解决建议: 请检查网络连接和FTP服务器状态")
                return None
            except TimeoutError as e:
                error_msg = f"FTP上传超时: {image_path}"
                logger.error(f"{error_msg}, 错误类型: {type(e).__name__}, 错误信息: {e}")
                logger.error("解决建议: 请检查网络速度和文件大小")
                return None
            except PermissionError as e:
                error_msg = f"FTP权限错误: {ftp_config.get('ftp_remote_dir', 'unknown')}"
                logger.error(f"{error_msg}, 错误类型: {type(e).__name__}, 错误信息: {e}")
                logger.error("解决建议: 请检查FTP用户权限和目录访问权限")
                return None
            except FileNotFoundError as e:
                error_msg = f"FTP上传文件未找到: {image_path}"
                logger.error(f"{error_msg}, 错误类型: {type(e).__name__}, 错误信息: {e}")
                return None

            if cdn_url:
                logger.info(f"FTP上传成功: {cdn_url}")
                return cdn_url
            else:
                # NFR-013: 优雅降级 - FTP上传失败不影响核心流程
                logger.warning("FTP上传失败，返回None，但不影响核心业务流程")
                return None

        except Exception as e:
            # NFR-011: 安全网机制 - 通用异常捕获
            # NFR-013: 优雅降级 - 非关键功能失败不中断服务
            error_msg = f"FTP上传时发生未预期异常: {image_path}"
            logger.warning(f"{error_msg}, 错误类型: {type(e).__name__}, 错误信息: {e}", exc_info=True)
            logger.info("FTP上传失败，但核心业务流程将继续执行")
            return None
    
    def cleanup_temp_files(self, file_path: str):
        """
        清理临时文件

        Args:
            file_path: 要清理的文件路径
        """
        # NFR-011: 输入参数验证
        if not isinstance(file_path, str):
            logger.warning(f"清理文件路径必须为字符串类型，当前类型: {type(file_path)}")
            return

        if not file_path.strip():
            logger.warning("清理文件路径不能为空")
            return

        # NFR-012: 记录上下文信息
        logger.debug(f"开始清理临时文件: {file_path}")

        try:
            # NFR-010: 文件I/O操作异常处理 - 检查文件存在性
            try:
                file_exists = os.path.exists(file_path)
            except (OSError, IOError) as e:
                logger.warning(f"检查文件存在性失败: {file_path}, 错误类型: {type(e).__name__}, 错误信息: {e}")
                return

            if file_exists:
                # NFR-010: 文件I/O操作异常处理 - 删除文件
                try:
                    os.unlink(file_path)
                    logger.debug(f"成功删除临时文件: {file_path}")
                except (OSError, IOError, PermissionError) as e:
                    logger.warning(f"删除临时文件失败: {file_path}, 错误类型: {type(e).__name__}, 错误信息: {e}")
                    return
                except FileNotFoundError:
                    # 文件已被其他进程删除，这是正常情况
                    logger.debug(f"临时文件已不存在（可能被其他进程删除）: {file_path}")

                # NFR-013: 资源清理 - 删除空的临时目录
                try:
                    temp_dir = os.path.dirname(file_path)
                    if temp_dir and os.path.exists(temp_dir):
                        try:
                            # 检查目录是否为空
                            dir_contents = os.listdir(temp_dir)
                            if not dir_contents:
                                os.rmdir(temp_dir)
                                logger.debug(f"成功删除空临时目录: {temp_dir}")
                            else:
                                logger.debug(f"临时目录非空，保留: {temp_dir} (包含 {len(dir_contents)} 个文件)")
                        except (OSError, IOError, PermissionError) as e:
                            logger.debug(f"删除临时目录失败: {temp_dir}, 错误类型: {type(e).__name__}, 错误信息: {e}")
                except (TypeError, ValueError) as e:
                    logger.warning(f"获取临时目录路径失败: {file_path}, 错误类型: {type(e).__name__}, 错误信息: {e}")
            else:
                logger.debug(f"临时文件不存在，无需清理: {file_path}")

        except Exception as e:
            # NFR-011: 安全网机制 - 通用异常捕获
            # NFR-013: 资源清理失败不影响主流程
            logger.warning(f"清理临时文件时发生未预期异常: {file_path}, 错误类型: {type(e).__name__}, 错误信息: {e}")
            logger.info("临时文件清理失败，但不影响主业务流程", exc_info=True)
