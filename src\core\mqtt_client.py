#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MQTT客户端模块

提供MQTT连接、订阅、发布功能，包含重连机制和错误处理。
"""

import json
import logging
import threading
import time
from typing import Dict, List, Callable, Optional, Any
import paho.mqtt.client as mqtt
import random
import string

logger = logging.getLogger(__name__)

class MQTTClient:
    """
    MQTT客户端封装类
    
    提供连接、订阅、发布功能，支持自动重连和错误处理。
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化MQTT客户端
        
        Args:
            config: MQTT配置参数
        """
        self.mqtt_config = config
        self.client = None
        self.connected = False
        self.reconnect_thread = None
        self.stop_reconnect = False
        self.message_handlers: Dict[str, List[Callable]] = {}
        
        # MQTT配置参数
        self.broker_host = config.get('MQTT_BROKER_HOST')
        self.broker_port = config.get('MQTT_BROKER_PORT', 1883)
        self.username = config.get('MQTT_USERNAME', '')
        self.password = config.get('MQTT_PASSWORD', '')
        str_randon = ''.join(random.choices(string.ascii_letters + string.digits, k=8))  # 8位随机字符
        self.client_id = f"Event_Processor_{config.get('COMM_ID')}_{config.get('POSITION_ID')}_{config.get('EVENT_ID')}_{str_randon}"
        
        # 重连配置
        self.reconnect_delay = 5  # 重连延迟（秒）
        self.max_reconnect_attempts = 0  # 0表示无限重试
        
        logger.info(f"MQTT客户端初始化: {self.broker_host}:{self.broker_port}, 客户端ID: {self.client_id}")
    
    def _on_connect(self, client, userdata, flags, rc):
        """连接回调"""
        if rc == 0:
            self.connected = True
            logger.info("MQTT连接成功")
            
            # 重新订阅所有主题，添加重试限制
            for topic in self.message_handlers.keys():
                self._subscribe_topic_with_retry(topic)
                
        else:
            self.connected = False
            logger.error(f"MQTT连接失败，错误代码: {rc}")
    
    def _on_disconnect(self, client, userdata, rc):
        """断开连接回调"""
        self.connected = False
        if rc != 0:
            # 提供更详细的错误代码说明
            error_descriptions = {
                1: "协议版本不正确",
                2: "客户端ID无效", 
                3: "服务器不可用",
                4: "用户名或密码错误",
                5: "未授权",
                7: "网络错误或连接丢失"
            }
            error_desc = error_descriptions.get(rc, "未知错误")
            logger.warning(f"MQTT意外断开连接，错误代码: {rc} ({error_desc})")
            
            # 避免在停止重连时启动新的重连
            if not self.stop_reconnect:
                self._start_reconnect()
        else:
            logger.info("MQTT正常断开连接")
    
    def _on_message(self, client, userdata, msg):
        """消息接收回调"""
        try:
            topic = msg.topic
            payload = msg.payload.decode('utf-8')
            
            logger.debug(f"收到MQTT消息{topic}")
            
            # 尝试解析JSON
            try:
                message_data = json.loads(payload)
            except json.JSONDecodeError:
                logger.warning(f"无法解析JSON消息: {payload[:100]}...")
                return
            
            # 调用注册的处理器
            handlers = self.message_handlers.get(topic, [])
            for handler in handlers:
                try:
                    handler(topic, message_data)
                except Exception as e:
                    logger.error(f"消息处理器执行失败: {e}", exc_info=True)
                    
        except Exception as e:
            logger.error(f"消息处理失败: {e}", exc_info=True)
    
    def _on_subscribe(self, client, userdata, mid, granted_qos):
        """订阅回调"""
        logger.debug(f"订阅成功: mid={mid}, qos={granted_qos}")
    
    def _on_publish(self, client, userdata, mid):
        """发布回调"""
        logger.debug(f"消息发布成功: mid={mid}")
    
    def _start_reconnect(self):
        """启动重连线程"""
        if self.reconnect_thread and self.reconnect_thread.is_alive():
            return
            
        self.stop_reconnect = False
        self.reconnect_thread = threading.Thread(target=self._reconnect_loop, daemon=True)
        self.reconnect_thread.start()
    
    def _reconnect_loop(self):
        """重连循环"""
        attempt = 0
        while not self.stop_reconnect and not self.connected:
            attempt += 1
            
            if self.max_reconnect_attempts > 0 and attempt > self.max_reconnect_attempts:
                logger.error(f"达到最大重连次数 {self.max_reconnect_attempts}，停止重连")
                break
            
            logger.info(f"尝试重连MQTT (第{attempt}次)...")
            
            try:
                # 安全地断开现有连接
                if self.client:
                    try:
                        self.client.disconnect()
                    except Exception as e:
                        logger.debug(f"断开现有连接时异常: {e}")
                    try:
                        self.client.loop_stop()
                    except Exception as e:
                        logger.debug(f"停止现有循环时异常: {e}")
                
                # 重新创建客户端
                self._create_client()
                
                # 尝试连接
                result = self.client.connect(self.broker_host, self.broker_port, 60)
                if result != 0:
                    logger.warning(f"连接返回错误代码: {result}")
                    continue
                    
                self.client.loop_start()
                
                # 等待连接结果
                time.sleep(2)
                
                if self.connected:
                    logger.info("MQTT重连成功")
                    break
                    
            except ConnectionRefusedError as e:
                logger.error(f"重连被拒绝: {e}")
            except OSError as e:
                logger.error(f"重连网络错误: {e}")
            except Exception as e:
                logger.error(f"重连失败: {e}", exc_info=True)
            
            # 等待后重试
            if not self.stop_reconnect:
                time.sleep(self.reconnect_delay)
    
    def _create_client(self):
        """创建MQTT客户端"""
        self.client = mqtt.Client(client_id=self.client_id, clean_session=True)
        
        # 设置回调
        self.client.on_connect = self._on_connect
        self.client.on_disconnect = self._on_disconnect
        self.client.on_message = self._on_message
        self.client.on_subscribe = self._on_subscribe
        self.client.on_publish = self._on_publish
        
        # 设置认证
        if self.username and self.password:
            self.client.username_pw_set(self.username, self.password)
            logger.debug("设置MQTT认证信息")
    
    def connect(self) -> bool:
        """
        连接到MQTT服务器

        Returns:
            bool: 连接是否成功
        """
        # NFR-012: 记录上下文信息
        logger.info(f"开始连接MQTT服务器: {self.broker_host}:{self.broker_port}")

        try:
            # NFR-010: 配置参数处理异常处理
            if not self.broker_host:
                error_msg = "MQTT服务器地址未配置"
                logger.error(error_msg)
                logger.error("解决建议: 请检查MQTT_BROKER_HOST配置参数")
                return False

            if not isinstance(self.broker_port, int) or not (1 <= self.broker_port <= 65535):
                error_msg = f"MQTT端口配置无效: {self.broker_port}"
                logger.error(error_msg)
                logger.error("解决建议: 请检查MQTT_BROKER_PORT配置参数，应为1-65535之间的整数")
                return False

            # NFR-010: 外部依赖调用异常处理 - 创建客户端
            try:
                self._create_client()
            except Exception as e:
                error_msg = "创建MQTT客户端失败"
                logger.error(f"{error_msg}, 错误类型: {type(e).__name__}, 错误信息: {e}")
                logger.error("解决建议: 请检查MQTT客户端库是否正确安装")
                return False

            # NFR-010: 网络操作异常处理 - MQTT连接
            try:
                result = self.client.connect(self.broker_host, self.broker_port, 60)
            except ConnectionRefusedError as e:
                error_msg = f"MQTT服务器拒绝连接: {self.broker_host}:{self.broker_port}"
                logger.error(f"{error_msg}, 错误类型: {type(e).__name__}, 错误信息: {e}")
                logger.error("解决建议: 请检查MQTT服务器是否运行，端口是否正确，防火墙设置")
                return False
            except TimeoutError as e:
                error_msg = f"MQTT连接超时: {self.broker_host}:{self.broker_port}"
                logger.error(f"{error_msg}, 错误类型: {type(e).__name__}, 错误信息: {e}")
                logger.error("解决建议: 请检查网络连接和MQTT服务器响应时间")
                return False
            except OSError as e:
                error_msg = f"MQTT网络错误: {self.broker_host}:{self.broker_port}"
                logger.error(f"{error_msg}, 错误类型: {type(e).__name__}, 错误信息: {e}")
                logger.error("解决建议: 请检查网络连接、DNS解析和主机可达性")
                return False

            if result != 0:
                error_msg = f"MQTT连接失败，错误代码: {result}"
                logger.error(error_msg)
                # 提供具体的错误代码解释
                error_descriptions = {
                    1: "协议版本不正确",
                    2: "客户端ID无效",
                    3: "服务器不可用",
                    4: "用户名或密码错误",
                    5: "未授权"
                }
                if result in error_descriptions:
                    logger.error(f"错误说明: {error_descriptions[result]}")
                logger.error("解决建议: 请检查MQTT服务器配置和认证信息")
                return False

            # NFR-010: 网络操作异常处理 - 启动网络循环
            try:
                self.client.loop_start()
            except Exception as e:
                error_msg = "启动MQTT网络循环失败"
                logger.error(f"{error_msg}, 错误类型: {type(e).__name__}, 错误信息: {e}")
                return False

            # NFR-013: 超时控制 - 等待连接完成
            timeout = 10
            connection_start_time = time.time()
            while not self.connected and (time.time() - connection_start_time) < timeout:
                try:
                    time.sleep(0.1)
                except KeyboardInterrupt:
                    logger.warning("用户中断MQTT连接等待")
                    return False

            if self.connected:
                return True
            else:
                error_msg = f"MQTT连接超时（{timeout}秒）"
                logger.error(error_msg)
                logger.error("解决建议: 请检查网络延迟和MQTT服务器响应时间")
                return False

        except Exception as e:
            # NFR-011: 安全网机制 - 通用异常捕获
            error_msg = f"MQTT连接时发生未预期异常: {self.broker_host}:{self.broker_port}"
            logger.error(f"{error_msg}, 错误类型: {type(e).__name__}, 错误信息: {e}", exc_info=True)
            logger.error("解决建议: 请检查系统资源和MQTT客户端库版本")
            return False
    
    def disconnect(self):
        """断开MQTT连接"""
        logger.info("断开MQTT连接")

        try:
            # 停止重连机制
            self.stop_reconnect = True
            
            # 等待重连线程结束
            if self.reconnect_thread and self.reconnect_thread.is_alive():
                logger.debug("等待重连线程结束...")
                self.reconnect_thread.join(timeout=2.0)
                if self.reconnect_thread.is_alive():
                    logger.warning("重连线程未能在超时时间内结束")

            if self.client:
                try:
                    # 断开连接
                    logger.info("正在断开MQTT客户端连接...")
                    self.client.disconnect()
                    logger.info("MQTT客户端连接断开完成")
                except AttributeError as e:
                    logger.debug(f"断开连接时客户端对象异常: {e}")
                except Exception as e:
                    logger.warning(f"断开MQTT客户端连接时发生异常: {e}")

                try:
                    # 停止网络循环
                    logger.debug("正在停止MQTT网络循环...")
                    self.client.loop_stop()
                    logger.debug("MQTT网络循环停止完成")
                except AttributeError as e:
                    logger.debug(f"停止网络循环时客户端对象异常: {e}")
                except Exception as e:
                    logger.warning(f"停止MQTT网络循环时发生异常: {e}")

            # 重置连接状态和客户端
            self.connected = False
            self.client = None
            logger.info("MQTT连接已断开")

        except Exception as e:
            logger.error(f"断开MQTT连接过程中发生未预期异常: {e}", exc_info=True)
            # 即使发生异常，也要确保连接状态被重置
            self.connected = False
            self.client = None
    
    def subscribe(self, topic: str, message_handler: Callable[[str, Dict], None], qos: int = 0):
        """
        订阅MQTT主题
        
        Args:
            topic: 主题名称
            message_handler: 消息处理函数
            qos: QoS级别
        """
        
        # 注册消息处理器
        if topic not in self.message_handlers:
            self.message_handlers[topic] = []
        self.message_handlers[topic].append(message_handler)
        
        # 如果已连接，立即订阅
        if self.connected:
            self._subscribe_topic(topic, qos)
    
    def _subscribe_topic(self, topic: str, qos: int = 0):
        """执行主题订阅"""
        if self.client and self.connected:
            result, mid = self.client.subscribe(topic, qos)
            if result == 0:
                logger.debug(f"订阅主题成功: {topic}")
            else:
                logger.error(f"订阅主题失败: {topic}, 错误代码: {result}")
    
    def _subscribe_topic_with_retry(self, topic: str, qos: int = 0, max_retries: int = 3):
        """带重试机制的主题订阅"""
        if not self.client or not self.connected:
            return
            
        for attempt in range(max_retries):
            try:
                result, mid = self.client.subscribe(topic, qos)
                if result == 0:
                    logger.debug(f"订阅主题成功: {topic}")
                    return
                else:
                    logger.warning(f"订阅主题失败: {topic}, 错误代码: {result}, 尝试次数: {attempt + 1}/{max_retries}")
                    if attempt < max_retries - 1:
                        time.sleep(0.5)  # 短暂等待后重试
                    else:
                        logger.error(f"订阅主题最终失败: {topic}, 已达到最大重试次数")
            except Exception as e:
                logger.error(f"订阅主题异常: {topic}, 错误: {e}, 尝试次数: {attempt + 1}/{max_retries}")
                if attempt < max_retries - 1:
                    time.sleep(0.5)
                else:
                    logger.error(f"订阅主题最终异常失败: {topic}")
    
    def publish(self, topic: str, message: Dict[str, Any], qos: int = 0, retain: bool = False) -> bool:
        """
        发布MQTT消息
        
        Args:
            topic: 主题名称
            message: 消息内容（字典）
            qos: QoS级别
            retain: 是否保留消息
            
        Returns:
            bool: 发布是否成功
        """
        if not self.connected:
            logger.error("MQTT未连接，无法发布消息")
            return False
        
        try:
            payload = json.dumps(message, ensure_ascii=False)
            logger.info(f"发布MQTT消息: 主题={topic}")
            logger.debug(f"消息内容: {payload}")
            
            result = self.client.publish(topic, payload, qos, retain)
            
            if result.rc == 0:
                logger.debug("消息发布成功")
                return True
            else:
                logger.error(f"消息发布失败，错误代码: {result.rc}")
                return False
                
        except Exception as e:
            logger.error(f"发布消息异常: {e}", exc_info=True)
            return False
    
    def is_connected(self) -> bool:
        """检查连接状态"""
        return self.connected
