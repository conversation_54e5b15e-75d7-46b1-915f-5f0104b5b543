#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MQTT主题路由器

实现统一的MQTT客户端实例和智能主题路由机制，
支持多事件并发处理，避免主题冲突和处理器混乱。
"""

import logging
from typing import Dict, List, Callable, Any, Set
from collections import defaultdict

logger = logging.getLogger(__name__)


class MQTTTopicRouter:
    """
    MQTT主题路由器
    
    负责管理MQTT主题订阅和消息路由，确保每个主题的消息
    能够正确路由到对应的事件处理器。
    """
    
    def __init__(self, mqtt_client):
        """
        初始化MQTT主题路由器
        
        Args:
            mqtt_client: MQTT客户端实例
        """
        self.mqtt_client = mqtt_client
        
        # 主题路由映射表 - 一个主题可以对应多个处理器
        self.topic_handlers: Dict[str, List[Dict[str, Any]]] = defaultdict(list)
        
        # 已订阅的主题集合 - 避免重复订阅
        self.subscribed_topics: Set[str] = set()
        
        logger.info("MQTT主题路由器初始化完成")
    
    def register_device_topic(self, event_id: str, topic: str, handler: Callable[[str, str, Dict[str, Any]], None]):
        """
        注册设备事件主题
        
        Args:
            event_id: 事件ID
            topic: MQTT主题
            handler: 处理函数，签名为 handler(event_id, topic, message)
        """
        handler_info = {
            'event_id': event_id,
            'type': 'device',
            'handler': handler
        }
        
        self.topic_handlers[topic].append(handler_info)
        logger.info(f"注册设备主题: {topic} -> 事件 {event_id}")
        
        # 如果主题尚未订阅，进行订阅
        self._ensure_topic_subscribed(topic, qos=0)
    
    def register_exclusion_topic(self, event_id: str, topic: str, handler: Callable[[str, str, Dict[str, Any]], None]):
        """
        注册排除信息主题
        
        Args:
            event_id: 事件ID
            topic: MQTT主题
            handler: 处理函数，签名为 handler(event_id, topic, message)
        """
        handler_info = {
            'event_id': event_id,
            'type': 'exclusion',
            'handler': handler
        }
        
        self.topic_handlers[topic].append(handler_info)
        logger.info(f"注册排除信息主题: {topic} -> 事件 {event_id}")
        
        # 如果主题尚未订阅，进行订阅
        self._ensure_topic_subscribed(topic, qos=0)
    
    def register_detail_topic(self, event_id: str, topic: str, handler: Callable[[str, str, Dict[str, Any]], None]):
        """
        注册详情信息主题
        
        Args:
            event_id: 事件ID
            topic: MQTT主题
            handler: 处理函数，签名为 handler(event_id, topic, message)
        """
        handler_info = {
            'event_id': event_id,
            'type': 'detail',
            'handler': handler
        }
        
        self.topic_handlers[topic].append(handler_info)
        logger.info(f"注册详情信息主题: {topic} -> 事件 {event_id}")
        
        # 如果主题尚未订阅，进行订阅
        self._ensure_topic_subscribed(topic, qos=1)
    
    def _ensure_topic_subscribed(self, topic: str, qos: int = 0):
        """
        确保主题已订阅
        
        Args:
            topic: MQTT主题
            qos: QoS级别
        """
        if topic not in self.subscribed_topics:
            # 订阅主题，使用统一的路由处理器
            self.mqtt_client.subscribe(topic, self._route_message, qos=qos)
            self.subscribed_topics.add(topic)
            logger.info(f"订阅MQTT主题: {topic} (QoS={qos})")
        else:
            logger.debug(f"主题 {topic} 已订阅，跳过重复订阅")
    
    def _route_message(self, topic: str, message: Dict[str, Any]):
        """
        路由MQTT消息到对应的处理器
        
        Args:
            topic: MQTT主题
            message: 消息内容
        """
        try:
            # 查找该主题的所有处理器
            handlers = self.topic_handlers.get(topic, [])
            
            if not handlers:
                logger.warning(f"未找到主题 {topic} 的处理器")
                return
            
            logger.debug(f"主题 {topic} 找到 {len(handlers)} 个处理器")
            
            # 调用所有注册的处理器
            for handler_info in handlers:
                try:
                    event_id = handler_info['event_id']
                    handler_type = handler_info['type']
                    handler_func = handler_info['handler']
                    
                    logger.debug(f"路由消息: {topic} -> 事件 {event_id} ({handler_type})")
                    
                    # 调用处理器
                    handler_func(event_id, topic, message)
                    
                except Exception as e:
                    logger.error(f"处理器执行失败: 事件 {handler_info.get('event_id', '未知')}, "
                               f"类型 {handler_info.get('type', '未知')}, 错误: {e}", exc_info=True)
                    
        except Exception as e:
            logger.error(f"消息路由失败: 主题 {topic}, 错误: {e}", exc_info=True)
    
    def unregister_topic(self, event_id: str, topic: str):
        """
        取消注册主题（用于事件清理）
        
        Args:
            event_id: 事件ID
            topic: MQTT主题
        """
        if topic in self.topic_handlers:
            # 移除该事件的所有处理器
            self.topic_handlers[topic] = [
                handler for handler in self.topic_handlers[topic]
                if handler['event_id'] != event_id
            ]
            
            # 如果主题没有任何处理器了，可以考虑取消订阅
            if not self.topic_handlers[topic]:
                del self.topic_handlers[topic]
                # 注意：这里不立即取消订阅，因为可能有其他组件在使用
                logger.info(f"主题 {topic} 已无处理器")
            
            logger.info(f"取消注册: 事件 {event_id} 的主题 {topic}")
    
    def get_topic_summary(self) -> Dict[str, Any]:
        """
        获取主题注册摘要
        
        Returns:
            Dict[str, Any]: 主题摘要信息
        """
        summary = {
            'total_topics': len(self.topic_handlers),
            'subscribed_topics': len(self.subscribed_topics),
            'topic_details': {}
        }
        
        for topic, handlers in self.topic_handlers.items():
            handler_summary = []
            for handler in handlers:
                handler_summary.append({
                    'event_id': handler['event_id'],
                    'type': handler['type']
                })
            summary['topic_details'][topic] = handler_summary
        
        return summary
    
    def log_topic_summary(self):
        """记录主题注册摘要"""
        summary = self.get_topic_summary()
        logger.info("=== MQTT主题路由摘要 ===")
        logger.info(f"总主题数: {summary['total_topics']}")
        logger.info(f"已订阅主题数: {summary['subscribed_topics']}")
        
        for topic, handlers in summary['topic_details'].items():
            handler_desc = ", ".join([f"{h['event_id']}({h['type']})" for h in handlers])
            logger.info(f"{topic}: {handler_desc}")
        
        logger.info("========================")