#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
多事件处理核心逻辑

实现多事件监听、事件上下文切换、状态管理等核心业务逻辑。
支持同时监听多个事件，但任一时刻只有一个事件处于活跃状态。
"""

import logging
import threading
import time
import json
from enum import Enum
from typing import Dict, Any, Optional, List
from datetime import datetime, timezone
from queue import Queue, Empty

from .config_manager import ConfigManager
from .mqtt_client import MQTTClient
from .image_handler import ImageHandler
from .alarm_notifier import AlarmNotifier
from .event_enums import EventState
from ..utils.timestamp_formatter import format_timestamp_for_log
# V3架构：移除复杂的V2组件，使用简化的处理逻辑


logger = logging.getLogger(__name__)


class EventContext:
    """事件上下文类，管理单个事件的状态和数据"""
    
    def __init__(self, event_id: str, config: Dict[str, Any]):
        self.event_id = event_id
        self.config = config
        self.state = EventState.IDLE
        self.state_lock = threading.Lock()
        
        # 事件状态数据
        self.current_event: Optional[Dict[str, Any]] = None
        self.timeout_timer: Optional[threading.Timer] = None
        self.ai_control_timer: Optional[threading.Timer] = None
        self.ai_request_id: Optional[str] = None
        
        # V3简化状态数据
        self.excluded: bool = False
        self.ai_result: Optional[Dict[str, Any]] = None
        
        # 配置参数
        self.device_topics = config.get('EP_START_DEVICE_EVENT_TOPIC', [])
        self.device_field = config.get('EP_START_FIELD_FROM_DEVICE_EVENT')
        self.trigger_values = config.get('EP_POSITION_START_VALUE_FROM_DEVICE_EVENT', {})
        self.holding_timeout = config.get('EP_PV_HOLDING_TIMEOUT', 15)
        # 使用统一的EP_AI_ANALYSIS_DELAY_SEC作为AI分析延迟
        self.ai_analysis_delay = config.get('EP_AI_ANALYSIS_DELAY_SEC', 6)
        
        # V3简化配置参数
        self.timeout = self.holding_timeout
        
        logger.info(f"事件上下文 {event_id} 初始化完成")
        logger.info(f"监听设备: {self.device_topics}")
        logger.info(f"触发字段: {self.device_field}")
        logger.info(f"滞留超时: {self.holding_timeout}秒")
        # 图片目录现为全局配置，不在事件上下文中存储
    
    def cancel_timers(self):
        """取消所有定时器"""
        if self.timeout_timer:
            self.timeout_timer.cancel()
            self.timeout_timer = None
        if self.ai_control_timer:
            self.ai_control_timer.cancel()
            self.ai_control_timer = None
    
    def reset_state(self):
        """重置状态到空闲"""
        with self.state_lock:
            self.cancel_timers()
            self.state = EventState.IDLE
            self.current_event = None
            self.ai_request_id = None
            # V3状态重置
            self.excluded = False
            self.ai_result = None
    
    def get_status(self) -> Dict[str, Any]:
        """获取当前状态"""
        with self.state_lock:
            status = {
                'event_id': self.event_id,
                'state': self.state.value,
                'current_event': self.current_event.copy() if self.current_event else None,
                # V3状态信息
                'excluded': self.excluded,
                'has_ai_result': self.ai_result is not None,
                'ai_request_id': self.ai_request_id
            }
            
            if self.current_event:
                status['event_duration'] = time.time() - self.current_event['start_time']
            
            return status


class MultiEventProcessor:
    """
    多事件处理器
    
    管理多个事件上下文，实现事件监听、状态切换、AI分析等功能。
    确保同一时刻只有一个事件处于活跃状态。
    """
    
    def __init__(self, config_manager: ConfigManager, events_config: Dict[str, Dict[str, Any]]):
        """
        初始化多事件处理器
        
        Args:
            config_manager: 配置管理器
            events_config: 事件配置字典 {event_id: config}
        """
        self.config_manager = config_manager
        self.events_config = events_config
        self.event_contexts: Dict[str, EventContext] = {}
        # 移除active_event_id限制，支持多事件并发处理
        self.global_lock = threading.Lock()
        self.shutdown_flag = False
        
        # 图片处理结果缓存（避免重复处理）
        self.image_processing_cache = {}  # {(timestamp, image_dir): (original_path, processed_path, image_timestamp, cdn_url)}
        self.image_processing_in_progress = set()  # 记录正在处理的缓存键，避免重复处理
        self.image_cache_lock = threading.Lock()
        
        # 创建事件上下文
        for event_id, config in events_config.items():
            context = EventContext(event_id, config)
            # 设置父处理器引用，用于排除消息处理
            context._parent_processor = self
            self.event_contexts[event_id] = context
        
        # 获取全局配置用于共享模块
        global_config = config_manager.get_global_config()
        
        # 使用全局配置初始化共享模块
        self.mqtt_client = MQTTClient(global_config)
        self.image_handler = ImageHandler(global_config)
        self.alarm_notifier = AlarmNotifier(global_config, self.mqtt_client)
        
        # 简化架构：移除复杂的V3组件，避免外部循环阻塞
        logger.info("初始化简化的事件处理组件...")
        
        # V3架构：完全移除复杂组件，使用直接的事件处理逻辑
        
        logger.info(f"组件初始化完成")
        
        logger.info(f"多事件处理器初始化完成，管理 {len(self.event_contexts)} 个事件")
    
    def start(self) -> bool:
        """
        启动多事件处理器
        
        Returns:
            bool: 启动是否成功
        """
        try:
            logger.info("启动多事件处理器...")
            
            # 连接MQTT
            if not self.mqtt_client.connect():
                logger.error("MQTT连接失败")
                return False
            
            # 直接注册MQTT主题处理器
            self._setup_mqtt_subscriptions()
            
            # 记录主题订阅摘要
            self._log_subscription_summary()
            
            return True
            
        except Exception as e:
            logger.error(f"启动多事件处理器失败: {e}", exc_info=True)
            return False
    
    def _setup_mqtt_subscriptions(self):
        """
        直接设置MQTT主题订阅，简化消息处理流程
        """
        subscribed_topics = set()
        
        # 订阅所有设备事件主题
        for event_id, context in self.event_contexts.items():
            for topic in context.device_topics:
                if topic not in subscribed_topics:
                    self.mqtt_client.subscribe(topic, self._handle_mqtt_message, qos=0)
                    subscribed_topics.add(topic)
                    logger.info(f"订阅设备事件主题: {topic}")
        
        # V3架构：排除匹配信息通过详情信息主题获取，无需单独订阅
        
        # 订阅AI结果主题
        ai_result_topic = f"ai/{self.config_manager.get_comm_id()}/{self.config_manager.get_position_id()}/result"
        if ai_result_topic not in subscribed_topics:
            self.mqtt_client.subscribe(ai_result_topic, self._handle_mqtt_message, qos=1)
            subscribed_topics.add(ai_result_topic)
            logger.info(f"订阅AI结果主题: {ai_result_topic}")
        
        # 订阅所有详情信息主题
        for event_id, context in self.event_contexts.items():
            detail_info_source_type = context.config.get('EP_PV_DETAIL_INFO_SOURCE_TYPE', '')
            detail_info_source = context.config.get('EP_PV_DETAIL_INFO_SOURCE', '')
            
            if not detail_info_source and context.config.get('EP_PV_DETAIL_INFO_TOPIC'):
                detail_info_source = context.config.get('EP_PV_DETAIL_INFO_TOPIC')
                detail_info_source_type = 'MQTT'
            
            if detail_info_source_type.upper() == 'MQTT' and detail_info_source:
                if detail_info_source not in subscribed_topics:
                    self.mqtt_client.subscribe(detail_info_source, self._handle_mqtt_message, qos=1)
                    subscribed_topics.add(detail_info_source)
                    logger.info(f"订阅详情信息主题: {detail_info_source}")
        
        self.subscribed_topics = subscribed_topics
        logger.info(f"MQTT主题订阅完成，共订阅 {len(subscribed_topics)} 个主题")
        
        # 构建主题映射表，优化消息路由性能
        self._build_topic_mappings()
    
    def _handle_mqtt_message(self, topic: str, message: Dict[str, Any]):
        """
        统一的MQTT消息处理入口，异步处理避免阻塞MQTT线程
        
        Args:
            topic: MQTT主题
            message: 消息内容
        """
        # 立即启动异步处理线程，避免阻塞MQTT消息接收
        threading.Thread(
            target=self._process_mqtt_message_async,
            args=[topic, message],
            daemon=True
        ).start()
    
    def _process_mqtt_message_async(self, topic: str, message: Dict[str, Any]):
        """
        异步处理MQTT消息，避免阻塞MQTT接收线程
        
        Args:
            topic: MQTT主题
            message: 消息内容
        """
        try:
            # 预构建主题映射表，避免每次消息都循环查找
            if not hasattr(self, '_topic_mappings'):
                self._build_topic_mappings()
            
            # 快速查找对应的处理器
            handlers = self._topic_mappings.get(topic, [])
            
            if not handlers:
                logger.debug(f"未知主题消息: {topic}")
                return
            
            # 并行处理所有匹配的处理器
            for handler_info in handlers:
                try:
                    handler_type = handler_info['type']
                    event_id = handler_info.get('event_id')
                    
                    if handler_type == 'device':
                        self._handle_device_event(topic, message)
                    elif handler_type == 'ai_result':
                        self._handle_ai_result_message(topic, message)
                    # V3架构：移除独立的exclusion处理
                    elif handler_type == 'detail':
                        self._handle_detail_info_event(event_id, topic, message)
                        
                except Exception as e:
                    logger.error(f"处理器执行失败: {handler_info}, 错误={e}", exc_info=True)
                
        except Exception as e:
            logger.error(f"异步处理MQTT消息失败: 主题={topic}, 错误={e}", exc_info=True)
    
    def _build_topic_mappings(self):
        """构建主题到处理器的映射表，提高消息路由效率"""
        self._topic_mappings = {}
        
        # 构建设备事件主题映射（避免重复处理）
        device_topics_added = set()
        for event_id, context in self.event_contexts.items():
            for topic in context.device_topics:
                if topic not in device_topics_added:
                    if topic not in self._topic_mappings:
                        self._topic_mappings[topic] = []
                    self._topic_mappings[topic].append({
                        'type': 'device',
                        'event_id': None  # 设备事件处理所有相关事件，不需要特定event_id
                    })
                    device_topics_added.add(topic)
        
        # V3架构：恢复排除信息主题映射（通过详情信息处理器处理）
        for event_id, context in self.event_contexts.items():
            exclude_source = context.config.get('EP_MQTT_EXCLUDE_INFO_SOURCE', '').strip()
            if exclude_source:
                if exclude_source not in self._topic_mappings:
                    self._topic_mappings[exclude_source] = []
                # 排除消息通过详情信息处理器处理，更新缓存后供排除检查使用
                self._topic_mappings[exclude_source].append({
                    'type': 'detail',
                    'event_id': event_id
                })
        
        # 构建AI结果主题映射
        ai_result_topic = f"ai/{self.config_manager.get_comm_id()}/{self.config_manager.get_position_id()}/result"
        if ai_result_topic not in self._topic_mappings:
            self._topic_mappings[ai_result_topic] = []
        self._topic_mappings[ai_result_topic].append({
            'type': 'ai_result',
            'event_id': None  # AI结果处理所有事件
        })
        
        # 构建详情信息主题映射
        for event_id, context in self.event_contexts.items():
            detail_info_source = (context.config.get('EP_PV_DETAIL_INFO_SOURCE') or 
                                context.config.get('EP_PV_DETAIL_INFO_TOPIC'))
            if detail_info_source:
                if detail_info_source not in self._topic_mappings:
                    self._topic_mappings[detail_info_source] = []
                self._topic_mappings[detail_info_source].append({
                    'type': 'detail',
                    'event_id': event_id
                })
        
        logger.info(f"构建主题映射表完成，共 {len(self._topic_mappings)} 个主题映射")
    
    # V3架构：移除复杂的排除配置构建，直接在事件处理中检查
    
    # V2架构方法 - 已废弃，V3使用_check_event_exclusion
    def _check_exclusion_simple(self, event_id: str, topic: str, message: Dict[str, Any]) -> bool:
        """V2架构方法 - 已废弃，V3使用_check_event_exclusion"""
        logger.debug(f"V2方法_check_exclusion_simple已废弃，V3使用_check_event_exclusion")
        return False
    
    def _log_subscription_summary(self):
        """记录订阅摘要"""
        logger.info("=== MQTT主题订阅摘要 ===")
        logger.info(f"总订阅主题数: {len(self.subscribed_topics)}")
        for topic in sorted(self.subscribed_topics):
            logger.info(f"订阅主题: {topic}")
        logger.info("========================\t")
    
    def stop(self):
        """停止多事件处理器"""
        logger.info("停止多事件处理器...")
        
        # 设置停止标志
        self.shutdown_flag = True
        
        # 取消所有事件的定时器
        for context in self.event_contexts.values():
            context.cancel_timers()
        
        # AI请求取消功能已移除（现使用外部AI服务）
        
        # 停止配置刷新
        if hasattr(self, 'config_manager'):
            self.config_manager.stop_reload()
        
        # 短暂等待让正在进行的操作检查标志
        time.sleep(0.1)
        
        logger.info("多事件处理器已停止")
    
    def run_main_loop(self):
        """
        运行主循环 - MQTT消息处理主线程
        
        简化版主循环，只负责MQTT连接监控
        """
        logger.info("启动MQTT主循环...\t\t")
        
        try:
            while not self.shutdown_flag:
                # 检查MQTT连接状态
                if not self.mqtt_client.is_connected():
                    logger.warning("MQTT连接断开，尝试重连...")
                    if not self.mqtt_client.connect():
                        logger.error("MQTT重连失败，等待重试...")
                        time.sleep(5)
                        continue
                
                # 短暂休眠，让出CPU给MQTT消息处理
                time.sleep(0.1)  # 100ms
                
        except KeyboardInterrupt:
            logger.info("收到中断信号，停止主循环")
        except Exception as e:
            logger.error(f"主循环异常: {e}", exc_info=True)
        finally:
            logger.info("MQTT主循环已停止")
    
    
    
    # V2架构方法 - 已废弃，V3使用_check_event_exclusion
    def _check_exclusion_matching_for_event(self, event_id: str, topic: str, message: Dict[str, Any]) -> bool:
        """V2架构方法 - 已废弃，V3使用_check_event_exclusion"""
        logger.debug(f"V2方法_check_exclusion_matching_for_event已废弃，V3使用_check_event_exclusion")
        return False
    
    def _handle_device_event(self, topic: str, message: Dict[str, Any]):
        """
        处理设备事件，根据主题确定对应的事件ID
        
        Args:
            topic: MQTT主题
            message: 设备事件消息
        """
        try:
            logger.debug(f"收到设备事件{topic}:{message}")
            
            # V3架构：移除废弃的全局排除匹配检查，每个事件独立处理排除逻辑
            
            # 查找所有匹配主题的事件上下文
            matched_events = []
            for event_id, context in self.event_contexts.items():
                if topic in context.device_topics:
                    matched_events.append((event_id, context))
                    logger.debug(f"主题匹配: {event_id} 监听 {topic}")
            
            if not matched_events:
                logger.debug(f"未找到匹配的事件上下文，主题: {topic}")
                return
            
            # 为每个匹配事件独立处理
            for target_event_id, target_context in matched_events:
                try:
                    # 验证消息格式
                    if target_context.device_field not in message: # 如果消息中没有字段，则跳过
                        continue
                    
                    field_data = message[target_context.device_field]
                    if not isinstance(field_data, dict): # 如果字段不是字典，则跳过
                        continue
                    
                    field_value = field_data.get('value')
                    field_timestamp = field_data.get('timestamp')
                    
                    if field_value is None or field_timestamp is None:
                        logger.warning(f"事件{target_event_id}: 字段数据不完整: value={field_value}, timestamp={field_timestamp}")
                        continue
                    
                    # 转换时间戳为毫秒
                    if len(str(field_timestamp)) == 10:  # 秒级时间戳
                        field_timestamp = field_timestamp * 1000
                    
                    # 判断事件类型
                    trigger_value = target_context.trigger_values.get('true')
                    release_value = target_context.trigger_values.get('false')
                    
                    if field_value == trigger_value:
                        # 应用信号条件验证
                        if self._validate_signal_conditions(target_event_id, topic, message, field_timestamp):
                            self._handle_trigger_event(target_event_id, target_context, field_timestamp)
                            logger.info(f"事件 {target_event_id} 触发")
                        else:
                            logger.debug(f"事件 {target_event_id} 触发信号验证失败，忽略触发")
                    elif field_value == release_value:
                        # 应用信号条件验证
                        if self._validate_signal_conditions(target_event_id, topic, message, field_timestamp):
                            self._handle_release_event(target_event_id, target_context, field_timestamp)
                            logger.info(f"事件 {target_event_id} 释放")
                        else:
                            logger.debug(f"事件 {target_event_id} 解除信号验证失败，忽略解除")
                    else:
                        logger.debug(f"事件 {target_event_id} 未知的字段值: {field_value}")
                        
                except Exception as e:
                    logger.error(f"事件{target_event_id}处理失败: {e}", exc_info=True)
                    # 继续处理其他事件
                
        except Exception as e:
            logger.error(f"处理设备事件失败: {e}", exc_info=True)
    
    def _validate_signal_conditions(self, event_id: str, topic: str, message: Dict[str, Any], field_timestamp: int) -> bool:
        """
        验证信号条件（触发/解除信号通用验证）
        
        验证逻辑：
        1. 消息来源验证：确认信号来自正确的MQTT主题
        2. 关键字段验证：检查消息结构完整性
        3. 时间戳验证：验证时间戳在当前时间±5秒内
        
        Args:
            event_id: 事件ID
            topic: MQTT主题
            message: 完整的MQTT消息
            field_timestamp: 字段时间戳（毫秒）
            
        Returns:
            bool: 验证是否通过
        """
        try:
            # 1. 消息来源验证（已通过topic匹配验证，这里记录日志）
            logger.debug(f"事件 {event_id} 信号来源验证通过: {topic}")
            
            # 2. 关键字段验证：检查消息结构完整性
            if not isinstance(message, dict):
                logger.warning(f"事件 {event_id} 信号验证失败: 消息不是有效的JSON对象")
                return False
            
            # 检查是否有基本的消息结构
            if len(message) == 0:
                logger.warning(f"事件 {event_id} 信号验证失败: 消息为空")
                return False
            
            # 3. 时间戳验证：±5秒内
            current_timestamp = int(time.time())
            signal_timestamp = field_timestamp // 1000  # 转换为秒
            time_diff = abs(signal_timestamp - current_timestamp)
            
            if time_diff > 5:  # 硬编码5秒容差
                logger.warning(f"事件 {event_id} 信号验证失败: 时间戳超出允许范围")
                logger.warning(f"  信号时间戳: {signal_timestamp} ({format_timestamp_for_log(field_timestamp)})")
                logger.warning(f"  当前时间戳: {current_timestamp} ({format_timestamp_for_log(current_timestamp * 1000)})")
                logger.warning(f"  时间差: {time_diff}秒 (允许范围: ±5秒)")
                return False
            
            logger.debug(f"事件 {event_id} 信号验证通过: 时间差 {time_diff}秒 (允许范围: ±5秒)")
            return True
            
        except Exception as e:
            logger.error(f"事件 {event_id} 信号验证异常: {e}", exc_info=True)
            return False
    
    # V2架构方法 - 已废弃，V3使用_check_event_exclusion
    def _handle_exclusion_message(self, event_id: str, topic: str, message: Dict[str, Any]):
        """V2架构方法 - 已废弃，V3使用_check_event_exclusion"""
        logger.debug(f"V2方法_handle_exclusion_message已废弃，V3使用_check_event_exclusion")
    
    def _handle_detail_info_event(self, event_id: str, topic: str, message: Dict[str, Any]):
        """
        处理详情信息事件
        
        Args:
            event_id: 事件ID
            topic: MQTT主题
            message: 详情信息消息
        """
        try:
            # 兼容性处理：获取详情信息摘要
            try:
                message_summary = self._format_exclusion_message_summary(message)
                logger.debug(f"事件 {event_id} 收到详情信息 {topic}: {message_summary}")
            except AttributeError:
                # 简化版本
                event_type = message.get('log_event_type', '')
                car_no = message.get('log_car_no', '')
                summary_parts = []
                if event_type:
                    summary_parts.append(f"事件类型={event_type}")
                if car_no:
                    summary_parts.append(f"车牌={car_no}")
                message_summary = ", ".join(summary_parts) if summary_parts else f"字段数量={len(message)}"
                logger.debug(f"事件 {event_id} 收到详情信息 {topic}: {message_summary}")
            
            # 获取事件上下文
            context = self.event_contexts.get(event_id)
            if not context:
                logger.debug(f"事件 {event_id} 上下文不存在，跳过详情信息处理")
                return
            
            # 更新AlarmNotifier的详情信息缓存
            self.alarm_notifier.update_detail_info(message)
            logger.debug(f"事件 {event_id} 详情信息已更新到告警通知器缓存")
            
            # V3架构：详情信息更新后，检查是否触发排除条件
            if context.state == EventState.COLLECTING:
                if self._check_event_exclusion(event_id, context):
                    with context.state_lock:
                        context.state = EventState.EXCLUDED
                        context.excluded = True
                        logger.info(f"事件 {event_id} 满足排除条件，状态变更为EXCLUDED")
                        # 取消相关定时器
                        context.cancel_timers()
            
        except Exception as e:
            logger.error(f"处理事件 {event_id} 的详情信息失败: {e}", exc_info=True)
    
    # V2架构方法 - 已废弃，V3使用简化的消息处理
    def _format_exclusion_message_summary(self, message: Dict[str, Any]) -> str:
        """V2架构方法 - 已废弃，V3使用简化的消息处理"""
        logger.debug(f"V2方法_format_exclusion_message_summary已废弃")
        return str(message)[:50]
    
    def _send_ai_control_message(self, event_id: str, context: EventContext):
        """
        发送AI控制消息
        
        Args:
            event_id: 事件ID
            context: 事件上下文
        """
        try:
            import uuid
            import json
            
            # 生成请求ID
            request_id = str(uuid.uuid4())
            context.ai_request_id = request_id
            
            # 获取图片路径
            if not context.current_event:
                logger.error(f"事件 {event_id} 无当前事件，无法发送AI控制消息")
                return
            
            image_path = context.current_event.get('processed_image_path', '')
            if not image_path:
                logger.error(f"事件 {event_id} 无处理后图片路径，无法发送AI控制消息")
                return
            
            # 获取AI提示词
            prompt = self.config_manager.get_ai_prompt(event_id)
            if not prompt:
                logger.warning(f"事件 {event_id} 无AI提示词，跳过AI分析")
                return
            
            # 构建AI控制消息
            ai_control_topic = f"ai/{self.config_manager.get_comm_id()}/{self.config_manager.get_position_id()}/control"
            ai_control_payload = {
                "image_path": image_path,
                "prompt": prompt,
                "timestamp": context.current_event.get('timestamp_str', ''),
                "request_id": request_id,
                "event_id": event_id
            }
            
            # 发送MQTT消息
            if self.mqtt_client:
                self.mqtt_client.publish(ai_control_topic, json.dumps(ai_control_payload, ensure_ascii=False))
                logger.info(f"事件 {event_id} AI控制消息已发送: request_id={request_id}")
                
                # 更新状态
                with context.state_lock:
                    context.state = EventState.WAITING_AI_RESULT
            else:
                logger.error(f"事件 {event_id} MQTT客户端不可用")
                
        except Exception as e:
            logger.error(f"事件 {event_id} 发送AI控制消息失败: {e}", exc_info=True)
    
    def _handle_ai_result_message(self, topic: str, message: Dict[str, Any]):
        """
        处理AI结果消息
        
        Args:
            topic: MQTT主题
            message: AI结果消息
        """
        try:
            request_id = message.get('request_id', '')
            ai_result = message.get('result', {})
            
            if not request_id:
                logger.warning(f"AI结果消息缺少request_id: {message}")
                return
            
            # 查找对应的事件
            target_event_id = None
            target_context = None
            
            for event_id, context in self.event_contexts.items():
                if context.ai_request_id == request_id:
                    target_event_id = event_id
                    target_context = context
                    break
            
            if not target_event_id or not target_context:
                logger.warning(f"未找到匹配的事件，request_id={request_id}")
                return
            
            logger.info(f"事件 {target_event_id} 收到AI分析结果: request_id={request_id}")
            
            # 更新事件状态
            with target_context.state_lock:
                target_context.ai_result = ai_result
                target_context.state = EventState.ALARM_READY
            
            # 处理AI结果或业务逻辑
            self._process_ai_or_business_logic_result(target_event_id, target_context, ai_result)
            
        except Exception as e:
            logger.error(f"处理AI结果消息失败: {e}", exc_info=True)
    
    def _process_ai_or_business_logic_result(self, event_id: str, context: EventContext, ai_result: Dict[str, Any]):
        """
        处理AI结果或业务逻辑结果
        
        Args:
            event_id: 事件ID
            context: 事件上下文
            ai_result: AI分析结果
        """
        try:
            # 检查是否需要进行业务逻辑判断
            judge_source = context.config.get('EP_BusinessLogic_JUDGE_SOURCE', '')
            if judge_source:
                # 有业务逻辑配置，等待业务逻辑结果
                logger.info(f"事件 {event_id} 等待业务逻辑判断结果")
                # 这里可以添加业务逻辑处理
            else:
                # 纯AI模式，直接处理结果
                self._handle_final_result(event_id, context, ai_result)
                
        except Exception as e:
            logger.error(f"事件 {event_id} 处理AI/业务逻辑结果失败: {e}", exc_info=True)
    
    def _handle_final_result(self, event_id: str, context: EventContext, result: Dict[str, Any]):
        """
        处理最终结果并发送告警
        
        Args:
            event_id: 事件ID
            context: 事件上下文
            result: 最终结果
        """
        try:
            # 这里应该根据结果判断是否需要告警
            should_alarm = self._should_send_alarm(result)
            
            if should_alarm:
                logger.info(f"事件 {event_id} 触发告警")
                self._send_alarm_notification(event_id, context)
            else:
                logger.info(f"事件 {event_id} 未触发告警")
            
            # 重置事件状态
            context.reset_state()
            
        except Exception as e:
            logger.error(f"事件 {event_id} 处理最终结果失败: {e}", exc_info=True)
    
    def _should_send_alarm(self, result: Dict[str, Any]) -> bool:
        """
        判断是否应该发送告警
        
        Args:
            result: 分析结果
            
        Returns:
            bool: 是否应该发送告警
        """
        # 简单实现：如果结果中有任何True值，则发送告警
        if isinstance(result, dict):
            return any(value is True for value in result.values())
        return False
    
    # V2架构方法 - 已废弃，V3使用简化的字段处理
    def _get_exclusion_key_fields(self, message: Dict[str, Any]) -> str:
        """V2架构方法 - 已废弃，V3使用简化的字段处理"""
        logger.debug(f"V2方法_get_exclusion_key_fields已废弃")
        return "无关键字段"
    
    def _handle_trigger_event(self, event_id: str, context: EventContext, timestamp: int):
        """
        处理触发事件，实现事件上下文切换
        
        Args:
            event_id: 事件ID
            context: 事件上下文
            timestamp: 事件时间戳（毫秒）
        """
        with self.global_lock:
            logger.info(f"事件 {event_id} 收到触发事件: {format_timestamp_for_log(timestamp)}")
            
            # 检查是否被排除
            if context.excluded:
                logger.debug(f"事件 {event_id} 已被排除匹配规则过滤，忽略触发")
                return
            
            # 每个事件独立运行，互不干扰
            logger.info(f"事件 {event_id} 开始处理")
            
            with context.state_lock:
                # 检查当前事件状态，只有IDLE状态才接受新的触发
                if context.state != EventState.IDLE:
                    logger.debug(f"事件 {event_id} 当前状态为 {context.state.name}，忽略新的触发信号")
                    return
                
                # 取消之前的定时器
                context.cancel_timers()
                
                # 设置为信息收集状态
                context.state = EventState.COLLECTING
                context.current_event = {
                    'trigger_timestamp': timestamp,
                    'start_time': time.time(),
                    'timestamp_str': format_timestamp_for_log(timestamp)
                }
                
                logger.info(f"事件 {event_id} 进入信息收集期 (COLLECTING)")
                
                # 启动AI分析延迟定时器
                try:
                    ai_delay = context.ai_analysis_delay
                    if ai_delay > 0:
                        context.ai_control_timer = threading.Timer(
                            ai_delay, 
                            self._process_image_and_send_ai_control, 
                            args=[event_id, context]
                        )
                        context.ai_control_timer.start()
                        logger.info(f"事件 {event_id} AI分析将在 {ai_delay} 秒后开始")
                    else:
                        # 立即开始AI分析
                        threading.Thread(
                            target=self._process_image_and_send_ai_control, 
                            args=[event_id, context], 
                            daemon=True
                        ).start()
                        logger.info(f"事件 {event_id} 立即开始AI分析")
                except Exception as e:
                    logger.error(f"事件 {event_id} 启动AI分析定时器失败: {e}", exc_info=True)
                
                # 启动超时定时器
                try:
                    context.timeout_timer = threading.Timer(
                        context.holding_timeout, 
                        self._handle_timeout, 
                        args=[event_id, context]
                    )
                    context.timeout_timer.start()
                    logger.info(f"事件 {event_id} 滞留超时设置为 {context.holding_timeout} 秒")
                except Exception as e:
                    logger.error(f"事件 {event_id} 启动超时定时器失败: {e}", exc_info=True)
    
    def _process_image_and_send_ai_control(self, event_id: str, context: EventContext):
        """
        处理图片并发送AI控制消息
        
        Args:
            event_id: 事件ID
            context: 事件上下文
        """
        try:
            # 检查事件是否已被排除
            if context.excluded or context.state == EventState.EXCLUDED:
                logger.info(f"事件 {event_id} 已被排除，停止AI分析")
                return
            
            # 获取并处理最新图片
            timestamp = context.current_event.get('trigger_timestamp', int(time.time() * 1000))
            
            # 调用图片处理
            image_result = self._get_or_process_image(timestamp, context.config)
            if not image_result:
                logger.error(f"事件 {event_id} 获取图片失败，无法进行AI分析")
                return
            
            # 更新事件信息
            original_path, processed_path, image_timestamp, cdn_url = image_result
            context.current_event.update({
                'original_image_path': original_path,
                'processed_image_path': processed_path,
                'image_timestamp': image_timestamp,
                'cdn_url': cdn_url
            })
            
            # 发送AI控制消息
            self._send_ai_control_message(event_id, context)
            
        except Exception as e:
            logger.error(f"事件 {event_id} 处理图片并发送AI控制失败: {e}", exc_info=True)
    
    def _handle_release_event(self, event_id: str, context: EventContext, timestamp: int):
        """
        处理解除事件
        
        Args:
            event_id: 事件ID
            context: 事件上下文
            timestamp: 事件时间戳（毫秒）
        """
        with context.state_lock:
            if context.state == EventState.IDLE:
                logger.debug(f"事件 {event_id} 收到解除事件，但当前处于空闲状态")
                return
            
            # 检查是否已经在告警准备阶段
            if context.state == EventState.ALARM_READY:
                logger.warning(f"事件 {event_id} 已进入告警准备阶段，忽略解除信号")
                return
            
            # 检查是否已经达到滞留超时时间
            if context.current_event:
                trigger_time = context.current_event.get('start_time', time.time())
                elapsed_time = time.time() - trigger_time
                holding_timeout = context.holding_timeout
                
                if elapsed_time >= holding_timeout:
                    logger.warning(f"事件 {event_id} 已达到滞留超时时间({holding_timeout}秒)，忽略解除信号")
                    return
            
            logger.info(f"事件 {event_id} 收到解除事件: {format_timestamp_for_log(timestamp)}，重置状态")
            
            # 重置事件状态
            context.reset_state()
            logger.info(f"事件 {event_id} 已重置，等待下一次触发")
    
    def _start_ai_detection(self, event_id: str, context: EventContext):
        """开始AI检测"""
        with context.state_lock:
            # V3状态检查：允许在收集期间开始AI检测
            if context.state != EventState.COLLECTING or not context.current_event:
                logger.debug(f"事件 {event_id} 状态已改变（{context.state.name}），跳过AI检测")
                return
            
            logger.info(f"事件 {event_id} 开始AI检测流程（当前状态：{context.state.name}）")
            context.state = EventState.WAITING_AI_RESULT
        
        # 在新线程中处理图片
        threading.Thread(
            target=self._process_image_for_ai, 
            args=[event_id, context], 
            daemon=True
        ).start()
    
    def _process_image_for_ai(self, event_id: str, context: EventContext):
        """统一的事件处理流程：图片处理 → 排除检查 → 条件判断 → 告警"""
        try:
            logger.info(f"事件 {event_id} 开始统一处理流程")
            
            if not context.current_event:
                logger.error(f"事件 {event_id} 当前事件为空，无法处理")
                self._abort_event(event_id)
                return
            
            # 阶段1: 图片处理（所有事件都需要）
            success = self._process_event_image(event_id, context)
            if not success:
                logger.error(f"事件 {event_id} 图片处理失败，中止事件")
                self._abort_event(event_id)
                return
            
            # 阶段2: 排除匹配检查（所有事件都支持）
            if self._check_event_exclusion(event_id, context):
                logger.info(f"事件 {event_id} 被排除匹配规则排除，结束处理")
                with context.state_lock:
                    context.state = EventState.EXCLUDED
                return
            
            # 阶段3: 条件判断和分析（根据配置决定执行AI分析和/或业务逻辑）
            self._perform_event_analysis(event_id, context)
                
        except Exception as e:
            logger.error(f"事件 {event_id} 统一处理流程异常: {e}", exc_info=True)
            self._abort_event(event_id)
    
    def _handle_ai_result(self, event_id: str, context: EventContext, success: bool, result: Dict[str, Any]):
        """
        处理AI分析结果
        
        Args:
            event_id: 事件ID
            context: 事件上下文
            success: AI分析是否成功
            result: AI分析结果
        """
        try:
            logger.debug(f"事件 {event_id} 开始处理AI结果: success={success}")

            should_abort = False
            should_send_alarm = False

            with context.state_lock:
                if context.state != EventState.WAITING_AI_RESULT:
                    logger.debug(f"事件 {event_id} 状态已改变，忽略AI结果")
                    return

                if not success:
                    logger.warning(f"事件 {event_id} AI分析失败: {result.get('error', '未知错误')}")
                    should_abort = True
                elif not context.current_event:
                    logger.error(f"事件 {event_id} 当前事件为空，无法设置AI结果")
                    should_abort = True
                else:
                    # 检查AI结果中是否有true字段
                    true_field_count = self._count_true_fields_in_ai_result(result)
                    logger.info(f"事件 {event_id} AI分析结果包含 {true_field_count} 个true字段")
                    
                    if true_field_count == 0:
                        # 0个true字段，返回IDLE状态
                        logger.info(f"事件 {event_id} AI分析未检测到任何异常，返回空闲状态")
                        context.state = EventState.IDLE
                        context.current_event = None
                        context.excluded = False
                        return
                    else:
                        # 有true字段，准备告警消息
                        logger.info(f"事件 {event_id} 收到肯定的AI分析结果，准备告警消息")
                        context.current_event['ai_result'] = result
                        context.state = EventState.ALARM_READY
                        # 预先准备告警消息，但不立即发送
                        self._prepare_alarm_message(event_id, context)

            # 在锁外执行操作
            if should_abort:
                self._abort_event(event_id)

        except Exception as e:
            logger.error(f"事件 {event_id} 处理AI结果异常: {e}", exc_info=True)
            self._abort_event(event_id)
    
    def _count_true_fields_in_ai_result(self, result: Dict[str, Any]) -> int:
        """
        计算AI结果中true字段的数量（支持统一格式）
        
        Args:
            result: AI分析结果
            
        Returns:
            int: true字段数量
        """
        true_count = 0
        
        try:
            # 获取实际的AI结果数据
            ai_result = result.get('ai_result', {})
            if not ai_result:
                ai_result = result
            
            logger.debug(f"检查AI结果中的true字段: {ai_result}")
            
            # 统计所有为true的字段
            for field_name, field_value in ai_result.items():
                # 跳过系统字段
                if field_name in ['confidence', 'timestamp', 'raw_response', 'request_detail', 
                                'detected_events', 'analysis_details']:
                    continue
                
                # 检查字段值是否为true
                if self._is_field_value_true(field_value):
                    true_count += 1
                    logger.debug(f"发现true字段: '{field_name}' = {field_value}")
            
            logger.info(f"AI结果统计：总共 {true_count} 个true字段")
            
        except Exception as e:
            logger.error(f"统计AI结果true字段时出错: {e}", exc_info=True)
        
        return true_count
    
    def _is_field_value_true(self, value: Any) -> bool:
        """
        判断字段值是否为true（支持多种格式）
        
        Args:
            value: 字段值
            
        Returns:
            bool: 是否为true
        """
        if value is True:
            return True
        
        if isinstance(value, str):
            value_lower = value.lower().strip()
            if value_lower in ['true', '是', 'yes', '1', '存在', '有', '检测到']:
                return True
        
        if isinstance(value, (int, float)):
            return value != 0
        
        return False
    
    def _process_event_image(self, event_id: str, context: EventContext) -> bool:
        """处理事件图片：查找、裁剪、上传（统一的图片处理逻辑）"""
        try:
            trigger_timestamp = context.current_event['trigger_timestamp']
            logger.info(f"事件 {event_id} 开始图片处理，触发时间戳: {trigger_timestamp}")
            
            # 查找并处理图片（使用缓存避免重复处理）
            image_process_result = self._get_or_process_image(
                trigger_timestamp, 
                context.config  # 传递事件特有配置
            )
            if not image_process_result:
                logger.warning(f"事件 {event_id} 图片处理失败")
                return False

            original_path, processed_path, image_timestamp, cdn_url = image_process_result
            logger.info(f"事件 {event_id} 图片处理成功: {original_path} -> CDN: {cdn_url}")

            # 更新事件信息
            with context.state_lock:
                if context.state != EventState.WAITING_AI_RESULT:
                    logger.debug(f"事件 {event_id} 状态已改变，停止图片处理")
                    return False

                context.current_event.update({
                    'original_image_path': original_path,
                    'processed_image_path': processed_path,
                    'image_timestamp': image_timestamp,
                    'cdn_url': cdn_url
                })
            
            return True
            
        except Exception as e:
            logger.error(f"事件 {event_id} 图片处理异常: {e}", exc_info=True)
            return False
    
    def _check_event_exclusion(self, event_id: str, context: EventContext) -> bool:
        """检查事件是否应被排除（使用统一的MQTT简化格式）"""
        try:
            # 检查是否配置了MQTT排除规则
            exclude_source = context.config.get('EP_MQTT_EXCLUDE_INFO_SOURCE', '').strip()
            exclude_field = context.config.get('EP_MQTT_EXCLUDE_INFO_FIELD', '').strip()
            exclude_logic = context.config.get('EP_MQTT_EXCLUDE_LOGIC', 'exists').strip()
            
            if not exclude_source or not exclude_field:
                logger.debug(f"事件 {event_id} 未配置 MQTT 排除规则")
                return False
            
            # 获取详情信息缓存
            detail_info = self.alarm_notifier.get_detail_info_cache()
            if not detail_info:
                logger.debug(f"事件 {event_id} 详情信息为空，无法进行排除检查")
                return False
            
            # 执行简化的排除检查
            return self._evaluate_mqtt_exclusion(event_id, detail_info, exclude_field, exclude_logic)
            
        except Exception as e:
            logger.error(f"事件 {event_id} MQTT排除检查异常: {e}", exc_info=True)
            return False
    
    def _evaluate_mqtt_exclusion(self, event_id: str, detail_info: dict, exclude_field: str, exclude_logic: str) -> bool:
        """评估MQTT排除条件（简化版本）"""
        try:
            # 检查字段是否存在于详情信息中
            if exclude_field not in detail_info:
                logger.debug(f"事件 {event_id} 排除字段 '{exclude_field}' 不存在于详情信息中")
                return False
            
            # 简化逻辑：只要字段存在就认为应该排除
            field_value = detail_info.get(exclude_field)
            
            # 对于 exists 逻辑，只要字段存在就排除
            if exclude_logic.lower() == 'exists':
                result = True
                logger.info(f"事件 {event_id} MQTT排除检查: 字段 '{exclude_field}' 存在，值='{field_value}' → 排除")
                return result
            
            # 其他逻辑类型暂不支持，但可以扩展
            logger.debug(f"事件 {event_id} 不支持的排除逻辑: {exclude_logic}，默认不排除")
            return False
            
        except Exception as e:
            logger.error(f"事件 {event_id} MQTT排除条件评估异常: {e}", exc_info=True)
            return False
    
    def _perform_event_analysis(self, event_id: str, context: EventContext):
        """执行事件分析：AI分析和/或业务逻辑判断（支持混合模式）"""
        try:
            # 获取AI提示词和业务逻辑配置
            ai_prompt = self.config_manager.get_ai_prompt(event_id)
            has_ai_analysis = ai_prompt and ai_prompt.strip()
            has_business_logic = bool(context.config.get('EP_BusinessLogic_JUDGE_FIELD'))
            
            logger.info(f"事件 {event_id} 分析配置: AI分析={has_ai_analysis}, 业务逻辑={has_business_logic}")
            
            if has_ai_analysis:
                # 执行AI分析
                self._execute_ai_analysis(event_id, context, ai_prompt)
            elif has_business_logic:
                # 仅业务逻辑判断
                self._execute_business_logic_analysis(event_id, context)
            else:
                # 既无AI又无业务逻辑，按超时处理
                logger.warning(f"事件 {event_id} 未配置AI分析或业务逻辑，按超时处理")
                self._handle_timeout(event_id, context)
                
        except Exception as e:
            logger.error(f"事件 {event_id} 分析执行异常: {e}", exc_info=True)
            self._abort_event(event_id)
    
    def _execute_ai_analysis(self, event_id: str, context: EventContext, ai_prompt: str):
        """执行AI分析"""
        try:
            with context.state_lock:
                context.state = EventState.WAITING_AI_RESULT
            
            processed_path = context.current_event.get('processed_image_path')
            if not processed_path:
                logger.error(f"事件 {event_id} 缺少处理后的图片路径")
                self._handle_timeout(event_id, context)
                return
            
            # 发送AI控制消息到外部AI服务
            self._send_ai_control_message(event_id, context)
            logger.info(f"事件 {event_id} AI控制消息已发送，等待AI分析结果")
                
        except Exception as e:
            logger.error(f"事件 {event_id} AI分析执行异常: {e}", exc_info=True)
            self._handle_timeout(event_id, context)
    
    def _execute_business_logic_analysis(self, event_id: str, context: EventContext):
        """执行业务逻辑分析"""
        try:
            # 获取业务逻辑配置
            judge_field = context.config.get('EP_BusinessLogic_JUDGE_FIELD')
            judge_condition = context.config.get('EP_BusinessLogic_JUDGE_LOGIC', 'contains')
            judge_value = context.config.get('EP_BusinessLogic_JUDGE_VALUE')
            
            if not judge_field or not judge_value:
                logger.error(f"事件 {event_id} 业务逻辑配置不完整")
                self._handle_timeout(event_id, context)
                return
            
            # 执行业务逻辑判断
            if self._evaluate_business_logic(event_id, context, judge_field, judge_condition, judge_value):
                # 业务逻辑条件满足，准备告警
                logger.info(f"事件 {event_id} 业务逻辑条件满足，准备告警")
                with context.state_lock:
                    context.state = EventState.ALARM_READY
                    context.current_event['ai_result'] = {
                        'business_logic_result': True,
                        'judge_field': judge_field,
                        'judge_value': judge_value,
                        'confidence': 1.0
                    }
                    self._prepare_alarm_message(event_id, context)
            else:
                # 业务逻辑条件不满足，返回IDLE
                logger.info(f"事件 {event_id} 业务逻辑条件不满足，返回IDLE状态")
                with context.state_lock:
                    context.state = EventState.IDLE
                    context.current_event = None
                    
        except Exception as e:
            logger.error(f"事件 {event_id} 业务逻辑分析异常: {e}", exc_info=True)
            self._handle_timeout(event_id, context)
    
    def _handle_unified_ai_result(self, event_id: str, context: EventContext, success: bool, result: Dict[str, Any]):
        """统一的AI结果处理（支持后续业务逻辑判断）"""
        try:
            logger.info(f"事件 {event_id} 收到AI分析结果: success={success}")
            
            # 先处理AI结果
            ai_detected = False
            if success and result:
                # AI分析成功，检查是否检测到异常
                true_count = self._count_true_fields_in_ai_result(result)
                ai_detected = true_count > 0
                logger.info(f"事件 {event_id} AI检测结果: 检测到 {true_count} 个异常字段")
                
                # 存储AI结果
                with context.state_lock:
                    context.current_event['ai_result'] = result
            else:
                # AI分析失败，按异常处理
                logger.warning(f"事件 {event_id} AI分析失败，按异常处理")
                ai_detected = True  # AI失败时保守处理
                with context.state_lock:
                    context.current_event['ai_result'] = {'ai_analysis_failed': True}
            
            # 检查是否还需要业务逻辑判断
            has_business_logic = bool(context.config.get('EP_BusinessLogic_JUDGE_FIELD'))
            business_satisfied = True  # 默认业务逻辑满足
            
            if has_business_logic:
                # 执行额外的业务逻辑判断
                judge_field = context.config.get('EP_BusinessLogic_JUDGE_FIELD')
                judge_condition = context.config.get('EP_BusinessLogic_JUDGE_LOGIC', 'contains')
                judge_value = context.config.get('EP_BusinessLogic_JUDGE_VALUE')
                
                business_satisfied = self._evaluate_business_logic(event_id, context, judge_field, judge_condition, judge_value)
                logger.info(f"事件 {event_id} 业务逻辑判断结果: {business_satisfied}")
            
            # 综合判断是否发送告警
            should_alarm = ai_detected and business_satisfied
            logger.info(f"事件 {event_id} 最终判断: AI检测={ai_detected}, 业务逻辑={business_satisfied}, 发送告警={should_alarm}")
            
            if should_alarm:
                # 准备发送告警
                with context.state_lock:
                    context.state = EventState.ALARM_READY
                    self._prepare_alarm_message(event_id, context)
            else:
                # 不满足告警条件，返回IDLE
                logger.info(f"事件 {event_id} 不满足告警条件，返回IDLE状态")
                with context.state_lock:
                    context.state = EventState.IDLE
                    context.current_event = None
                    
        except Exception as e:
            logger.error(f"事件 {event_id} 统一AI结果处理异常: {e}", exc_info=True)
            self._handle_timeout(event_id, context)
    
    def _handle_business_logic_event(self, event_id: str, context: EventContext):
        """
        处理业务逻辑事件（非AI事件）
        
        Args:
            event_id: 事件ID
            context: 事件上下文
        """
        try:
            logger.info(f"业务逻辑事件 {event_id} 开始处理")
            
            # 获取业务逻辑判断配置
            judge_field = context.config.get('EP_BusinessLogic_JUDGE_FIELD')
            judge_condition = context.config.get('EP_BusinessLogic_JUDGE_LOGIC', 'contains')
            judge_value = context.config.get('EP_BusinessLogic_JUDGE_VALUE')
            
            if not judge_field or not judge_value:
                logger.error(f"事件 {event_id} 业务逻辑判断配置不完整")
                self._abort_event(event_id)
                return
            
            # 业务逻辑事件也需要处理图片，确保告警消息包含图片URL
            trigger_timestamp = context.current_event['trigger_timestamp']
            logger.info(f"业务逻辑事件 {event_id} 开始图片处理，触发时间戳: {trigger_timestamp}")
            
            # 查找并处理图片（使用缓存避免重复处理）
            image_process_result = self._get_or_process_image(
                trigger_timestamp, 
                context.config  # 传递事件特有配置
            )
            if image_process_result:
                original_path, processed_path, image_timestamp, cdn_url = image_process_result
                logger.info(f"业务逻辑事件 {event_id} 图片处理成功: {original_path} -> CDN: {cdn_url}")
                
                # 更新事件信息，添加图片信息
                context.current_event.update({
                    'original_image_path': original_path,
                    'processed_image_path': processed_path,
                    'image_timestamp': image_timestamp,
                    'cdn_url': cdn_url
                })
            else:
                logger.warning(f"业务逻辑事件 {event_id} 图片处理失败，告警将不包含图片")
            
            # 检查详情信息并进行业务逻辑判断
            if self._evaluate_business_logic(event_id, context, judge_field, judge_condition, judge_value):
                # 条件满足，准备告警
                logger.info(f"业务逻辑事件 {event_id} 判断条件满足，准备告警")
                with context.state_lock:
                    context.state = EventState.ALARM_READY
                    # 创建虚拟AI结果用于告警处理
                    context.current_event['ai_result'] = {
                        'business_logic_result': True,
                        'judge_field': judge_field,
                        'judge_value': judge_value,
                        'confidence': 1.0
                    }
                    # 预先准备告警消息（现在包含图片URL）
                    self._prepare_alarm_message(event_id, context)
            else:
                # 条件不满足，返回IDLE
                logger.info(f"业务逻辑事件 {event_id} 判断条件不满足，返回空闲状态")
                with context.state_lock:
                    context.state = EventState.IDLE
                    context.current_event = None
                    context.excluded = False
            
        except Exception as e:
            logger.error(f"业务逻辑事件 {event_id} 处理异常: {e}", exc_info=True)
            self._abort_event(event_id)
    
    def _evaluate_business_logic(self, event_id: str, context: EventContext, judge_field: str, 
                                judge_condition: str, judge_value: str) -> bool:
        """
        评估业务逻辑条件
        
        Args:
            event_id: 事件ID
            context: 事件上下文
            judge_field: 判断字段
            judge_condition: 判断条件（contains/equals/exists）
            judge_value: 期望值
            
        Returns:
            bool: 是否满足条件
        """
        try:
            # 获取详情信息
            detail_info = self.alarm_notifier.detail_info_cache.copy()
            
            if not detail_info:
                logger.info(f"事件 {event_id} 详情信息为空，条件不满足")
                return False
            
            # 检查字段是否存在
            if judge_field not in detail_info:
                logger.info(f"事件 {event_id} 详情信息中缺少字段 {judge_field}，条件不满足")
                return False
            
            field_value = str(detail_info.get(judge_field, '')).strip()
            logger.info(f"事件 {event_id} 字段 {judge_field} 的值: '{field_value}'")
            
            # 根据条件类型进行判断
            if judge_condition == 'contains':
                # 支持多值匹配，用|分隔
                values_to_check = [v.strip() for v in judge_value.split('|')]
                result = any(value in field_value for value in values_to_check if value)
                logger.info(f"事件 {event_id} contains判断: '{field_value}' 包含 {values_to_check} 中任一值: {result}")
                return result
                
            elif judge_condition == 'equals':
                result = field_value == judge_value.strip()
                logger.info(f"事件 {event_id} equals判断: '{field_value}' == '{judge_value}': {result}")
                return result
                
            elif judge_condition == 'exists':
                result = bool(field_value)
                logger.info(f"事件 {event_id} exists判断: '{field_value}' 非空: {result}")
                return result
            
            else:
                logger.warning(f"事件 {event_id} 不支持的判断条件: {judge_condition}")
                return False
        
        except Exception as e:
            logger.error(f"事件 {event_id} 业务逻辑评估异常: {e}", exc_info=True)
            return False
    
    def _prepare_alarm_message(self, event_id: str, context: EventContext):
        """预先准备告警消息，但不立即发送"""
        try:
            if not context.current_event:
                logger.error(f"事件 {event_id} 当前事件为空，无法准备告警")
                return

            # 构建图片URL列表
            image_urls = []
            cdn_url = context.current_event.get('cdn_url')
            if cdn_url:
                image_urls.append(cdn_url)
                logger.info(f"事件 {event_id} 使用CDN URL: {cdn_url}")
            else:
                logger.warning(f"事件 {event_id} 未获取到CDN URL，告警将不包含图片")

            # 预先构建告警消息
            ai_result = context.current_event.get('ai_result')
            alarm_message = self.alarm_notifier._build_alarm_message(
                image_urls, 
                ai_result, 
                context.config
            )
            
            # 存储预构建的告警消息
            context.current_event['prepared_alarm'] = alarm_message
            logger.info(f"事件 {event_id} 告警消息已预先准备完成")
            
            # 计算剩余超时时间（仅用于日志显示）
            start_time = context.current_event.get('start_time', time.time())
            elapsed_time = time.time() - start_time
            remaining_time = max(0, context.holding_timeout - elapsed_time)
            logger.info(f"事件 {event_id} 距离超时还有 {remaining_time:.1f}秒（从触发后已过{elapsed_time:.1f}秒）")

        except Exception as e:
            logger.error(f"事件 {event_id} 准备告警消息异常: {e}", exc_info=True)
    
    
    def _send_alarm_notification(self, event_id: str, context: EventContext):
        """发送告警通知"""
        try:
            if not context.current_event:
                logger.error(f"事件 {event_id} 当前事件为空，无法发送告警")
                return

            # 构建图片URL列表
            image_urls = []
            cdn_url = context.current_event.get('cdn_url')
            if cdn_url:
                image_urls.append(cdn_url)
                logger.info(f"事件 {event_id} 使用CDN URL: {cdn_url}")
            else:
                logger.warning(f"事件 {event_id} 未获取到CDN URL，告警将不包含图片")

            # 发送告警（使用事件特有配置）
            ai_result = context.current_event.get('ai_result')
            logger.debug(f"事件 {event_id} 准备发送告警，配置中的EVENT_ID: {context.config.get('EVENT_ID', '未找到')}")
            success = self.alarm_notifier.send_alarm(
                image_urls, 
                ai_result, 
                context.config  # 传递事件特有配置
            )
            
            if success:
                logger.info(f"事件 {event_id} 告警通知发送成功")
            else:
                logger.error(f"事件 {event_id} 告警通知发送失败")
            
            # 清理临时文件
            processed_path = context.current_event.get('processed_image_path')
            if processed_path:
                self.image_handler.cleanup_temp_files(processed_path)
            
            # 重置状态
            with context.state_lock:
                context.state = EventState.IDLE
                context.current_event = None
                context.excluded = False  # 重置排除标志，允许下次触发
            
            # 多事件并发模式：无需清除全局活跃状态
            logger.info(f"事件 {event_id} 告警发送完成，状态已重置，等待下一次触发")
            
            # 检查是否所有事件都已空闲，如果是则清理图片缓存
            self._check_and_clear_image_cache()
            
        except Exception as e:
            logger.error(f"事件 {event_id} 发送告警通知异常: {e}", exc_info=True)
            self._abort_event(event_id)
    
    def _handle_timeout(self, event_id: str, context: EventContext):
        """处理超时事件 - 立即发送告警，不可逆转"""
        with context.state_lock:
            if context.state == EventState.IDLE:
                logger.debug(f"事件 {event_id} 已处于空闲状态，跳过超时处理")
                return
            
            # 检查是否已经被解除
            if context.current_event and context.current_event.get('released', False):
                logger.info(f"事件 {event_id} 在超时前已被解除，不发送告警")
                # 重置状态
                context.reset_state()
                with self.global_lock:
                    if self.active_event_id == event_id:
                        self.active_event_id = None
                return
            
            logger.warning(f"事件 {event_id} 达到滞留超时时间，立即强制发送告警")
            
            # 标记为超时状态，不再响应解除信号
            if context.current_event:
                context.current_event['timeout_reached'] = True
                context.current_event['timeout_time'] = time.time()
            
            # 不管当前状态如何，立即发送告警
            context.state = EventState.ALARM_READY
            
            # 立即发送超时告警（同步执行）
            self._send_timeout_alarm(event_id, context)
    
    def _send_timeout_alarm(self, event_id: str, context: EventContext):
        """超时时发送消息 - 统一使用AlarmNotifier"""
        try:
            logger.info(f"事件 {event_id} 达到超时条件，发送消息")
            
            # 取消所有定时器，防止干扰
            context.cancel_timers()
            
            # 构建图片URL列表
            image_urls = []
            if context.current_event:
                cdn_url = context.current_event.get('cdn_url')
                if cdn_url:
                    image_urls.append(cdn_url)
                    logger.info(f"事件 {event_id} 使用图片URL: {cdn_url}")
                else:
                    logger.info(f"事件 {event_id} 无图片URL")
            
            # 获取AI结果（如果有的话）
            ai_result = None
            if context.current_event:
                ai_result = context.current_event.get('ai_result')
                if ai_result:
                    logger.info(f"事件 {event_id} 使用AI分析结果")
                else:
                    logger.info(f"事件 {event_id} 无AI分析结果")
            
            # 统一使用AlarmNotifier发送消息（严格按配置构建）
            success = self.alarm_notifier.send_alarm(
                image_urls, 
                ai_result,  # 传递AI结果（如果有）
                context.config  # 使用事件配置
            )
            
            if success:
                logger.info(f"事件 {event_id} 消息发送成功")
            else:
                logger.error(f"事件 {event_id} 消息发送失败")
            
            # 清理临时文件
            if context.current_event:
                processed_path = context.current_event.get('processed_image_path')
                if processed_path:
                    try:
                        self.image_handler.cleanup_temp_files(processed_path)
                    except Exception as cleanup_e:
                        logger.warning(f"事件 {event_id} 清理临时文件失败: {cleanup_e}")
            
            # 完成处理
            with context.state_lock:
                context.state = EventState.IDLE
                context.current_event = None
                context.excluded = False  # 重置排除标志，允许下次触发
            
            # 多事件并发模式：无需清除全局活跃状态
            logger.warning(f"事件 {event_id} 超时处理完成，等待下一次触发")
            
            # 检查是否所有事件都已空闲，如果是则清理图片缓存
            self._check_and_clear_image_cache()
            
        except Exception as e:
            logger.error(f"事件 {event_id} 发送超时告警异常: {e}", exc_info=True)
            self._abort_event(event_id)
    
    def _abort_event(self, event_id: str):
        """中止指定事件"""
        if event_id not in self.event_contexts:
            return
        
        context = self.event_contexts[event_id]
        
        # 重入保护：检查是否正在中止
        with context.state_lock:
            if getattr(context, 'aborting', False):
                logger.debug(f"事件 {event_id} 正在中止中，跳过重复调用")
                return
            context.aborting = True
        
        logger.info(f"开始中止事件 {event_id}...")

        # 注意：新架构下AI服务是外部独立服务，无法直接取消请求
        # 如需要可以发送取消控制消息（根据需求决定是否实现）
        logger.debug(f"事件 {event_id} 中止，外部AI服务将自行处理超时")

        # 清理临时文件
        processed_image_path = None
        with context.state_lock:
            if context.current_event and context.current_event.get('processed_image_path'):
                processed_image_path = context.current_event['processed_image_path']

        if processed_image_path:
            self.image_handler.cleanup_temp_files(processed_image_path)

        # 重置事件上下文
        context.reset_state()
        
        # 清除中止标志
        with context.state_lock:
            context.aborting = False
        
        # 多事件并发模式：每个事件独立处理
        logger.debug(f"事件 {event_id} 中止操作完成")

        logger.info(f"事件 {event_id} 已中止，等待下一次触发")
    
    def get_status(self) -> Dict[str, Any]:
        """获取所有事件的状态 - 多事件并发模式"""
        # 统计各状态事件数量
        status_counts = {}
        active_events = []
        
        for event_id, context in self.event_contexts.items():
            event_status = context.get_status()
            state = event_status['state']
            
            # 统计状态
            status_counts[state] = status_counts.get(state, 0) + 1
            
            # 记录活跃事件
            if state != 'idle':
                active_events.append({
                    'event_id': event_id,
                    'state': state,
                    'duration': event_status.get('event_duration', 0)
                })
        
        with self.global_lock:
            status = {
                'processing_mode': 'concurrent_multi_event',  # 并发多事件模式
                'mqtt_connected': self.mqtt_client.is_connected(),
                'total_events': len(self.event_contexts),
                'active_events_count': len(active_events),
                'status_summary': status_counts,
                'active_events': active_events,
                'all_events': {}
            }
            
            # 完整事件状态详情
            for event_id, context in self.event_contexts.items():
                status['all_events'][event_id] = context.get_status()
            
            return status
    
    def get_active_events(self) -> List[Dict[str, Any]]:
        """获取当前活跃事件列表"""
        active_events = []
        for event_id, context in self.event_contexts.items():
            if context.state != EventState.IDLE:
                event_info = {
                    'event_id': event_id,
                    'state': context.state.value,
                    'start_time': context.current_event.get('start_time') if context.current_event else None,
                    'holding_timeout': context.holding_timeout,
                    'excluded': getattr(context, 'excluded', False),
                    'timeout_reached': context.current_event.get('timeout_reached', False) if context.current_event else False
                }
                if event_info['start_time']:
                    event_info['elapsed_time'] = time.time() - event_info['start_time']
                    event_info['remaining_time'] = max(0, event_info['holding_timeout'] - event_info['elapsed_time'])
                active_events.append(event_info)
        return active_events
    
    def get_event_status(self, event_id: str) -> Optional[Dict[str, Any]]:
        """获取指定事件的状态"""
        if event_id in self.event_contexts:
            return self.event_contexts[event_id].get_status()
        return None
    
    
    # ========== V3功能集成方法 ==========
    
    # V2架构方法 - 已废弃，V3使用简化的排除匹配逻辑
    def _enqueue_exclusion_message(self, event_id: str, topic: str, message: Dict[str, Any]):
        """V2架构方法 - 已废弃，V3使用简化的排除匹配逻辑"""
        logger.debug(f"V2方法_enqueue_exclusion_message已废弃，V3使用_check_event_exclusion")
    
    # V2架构方法 - 已废弃，V3使用简化的排除匹配逻辑
    def _check_exclusion_matching(self, topic: str, message: Dict[str, Any]) -> bool:
        """V2架构方法 - 已废弃，V3使用简化的排除匹配逻辑"""
        logger.debug(f"V2方法_check_exclusion_matching已废弃，V3使用_check_event_exclusion")
        return False
    
    
    
    
    
    

    # V3架构：移除了渐进式收集和聚合AI分析相关方法
    # 直接跳转到图片处理工具方法

    def _check_and_clear_image_cache(self):
        """检查所有事件状态，如果都空闲则清理图片缓存"""
        try:
            # 检查是否所有事件都处于空闲状态
            all_idle = True
            for context in self.event_contexts.values():
                if context.state != EventState.IDLE:
                    all_idle = False
                    break
            
            # 如果所有事件都空闲，清理图片缓存
            if all_idle:
                self._clear_image_cache()
        except Exception as e:
            logger.error(f"检查并清理图片缓存时发生异常: {e}", exc_info=True)
    
    def _clear_image_cache(self):
        """清理图片处理缓存"""
        with self.image_cache_lock:
            cache_size = len(self.image_processing_cache)
            progress_size = len(self.image_processing_in_progress)
            if cache_size > 0 or progress_size > 0:
                self.image_processing_cache.clear()
                self.image_processing_in_progress.clear()
                logger.debug(f"已清理图片处理缓存，清理了 {cache_size} 个条目和 {progress_size} 个处理中标记")

    # V3架构：保留核心图片处理方法
    def _get_or_process_image(self, trigger_timestamp: int, config: Dict[str, Any]):
        """
        获取或处理图片（使用缓存避免重复处理）
        
        Args:
            trigger_timestamp: 触发时间戳
            config: 事件配置
            
        Returns:
            Tuple[str, str, int, str] | None: (原始路径, 处理后路径, 图片时间戳, CDN URL) 或 None
        """
        try:
            # 获取图片目录（从全局配置获取，用于缓存键）
            global_config = self.config_manager.get_global_config()
            image_dir = global_config.get('EP_PV_BIND_IPC_IMAGE_DIR', '')
            if not image_dir:
                logger.warning("全局图片目录配置为空，无法处理图片")
                return None
            
            # 生成缓存键（基于触发时间戳和图片目录）
            cache_key = (trigger_timestamp, image_dir)
            
            # 检查缓存
            with self.image_cache_lock:
                if cache_key in self.image_processing_cache:
                    cached_result = self.image_processing_cache[cache_key]
                    logger.info(f"使用缓存的图片处理结果: {cached_result[0]} -> CDN: {cached_result[3]}")
                    return cached_result
                
                # 检查是否正在处理中
                if cache_key in self.image_processing_in_progress:
                    logger.info(f"图片正在处理中，等待其他事件完成: 时间戳={trigger_timestamp}")
                    # 等待处理完成
                    for _ in range(30):
                        time.sleep(0.1)
                        if cache_key in self.image_processing_cache:
                            cached_result = self.image_processing_cache[cache_key]
                            logger.info(f"等待完成，使用缓存结果: {cached_result[0]} -> CDN: {cached_result[3]}")
                            return cached_result
                    
                    # 超时仍未完成，继续自己处理
                    logger.warning(f"等待图片处理超时，自行处理: 时间戳={trigger_timestamp}")
                
                # 标记为正在处理
                self.image_processing_in_progress.add(cache_key)
            
            try:
                # 缓存未命中，执行实际处理
                logger.debug(f"缓存未命中，开始处理图片: 时间戳={trigger_timestamp}, 目录={image_dir}")
                image_process_result = self.image_handler.find_and_process_image(
                    trigger_timestamp, 
                    config
                )
                
                # 将结果加入缓存
                if image_process_result:
                    with self.image_cache_lock:
                        self.image_processing_cache[cache_key] = image_process_result
                        logger.debug(f"图片处理结果已缓存: {cache_key}")
                        
            finally:
                # 清除处理中标记
                with self.image_cache_lock:
                    self.image_processing_in_progress.discard(cache_key)
            
            return image_process_result
            
        except Exception as e:
            logger.error(f"获取或处理图片时发生异常: {e}", exc_info=True)
            return None
