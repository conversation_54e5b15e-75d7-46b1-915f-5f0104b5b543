#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI日志处理器

专门记录所有的AI提示词和AI返回结果的日志处理器。
提供独立的日志文件记录AI交互的详细信息。
"""

import os
import json
import time
import logging
from datetime import datetime
from typing import Dict, Any, Optional
from threading import Lock


class AILogger:
    """
    AI日志处理器
    
    专门记录AI提示词和返回结果，使用独立的日志文件。
    日志文件格式：log_AI_提示词和结果.log
    """
    
    def __init__(self, log_dir: str = "logs", max_file_size: int = 50 * 1024 * 1024):
        """
        初始化AI日志处理器
        
        Args:
            log_dir: 日志目录路径
            max_file_size: 最大文件大小（字节），默认50MB
        """
        self.log_dir = log_dir
        self.max_file_size = max_file_size
        self.lock = Lock()
        
        # 确保日志目录存在
        os.makedirs(self.log_dir, exist_ok=True)
        
        # 日志文件路径
        self.log_file_path = os.path.join(self.log_dir, "log_AI_提示词和结果.log")
        
        # 设置AI专用logger
        self.logger = self._setup_ai_logger()
        
        # 记录初始化信息
        self.logger.info("AI日志处理器初始化完成")
        self.logger.info(f"日志文件路径: {self.log_file_path}")
        self.logger.info(f"最大文件大小: {self.max_file_size / 1024 / 1024:.1f}MB")
    
    def _setup_ai_logger(self) -> logging.Logger:
        """
        设置AI专用的logger
        
        Returns:
            配置好的logger实例
        """
        # 创建专用的logger
        ai_logger = logging.getLogger('ai_interaction')
        ai_logger.setLevel(logging.INFO)
        
        # 清除现有的handlers，避免重复
        ai_logger.handlers.clear()
        
        # 创建文件handler
        file_handler = logging.FileHandler(
            self.log_file_path, 
            mode='a', 
            encoding='utf-8'
        )
        file_handler.setLevel(logging.INFO)
        
        # 设置格式
        formatter = logging.Formatter(
            '%(asctime)s - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        file_handler.setFormatter(formatter)
        
        # 添加handler
        ai_logger.addHandler(file_handler)
        
        # 防止向上级logger传播
        ai_logger.propagate = False
        
        return ai_logger
    
    def log_ai_request(self, 
                      request_id: str,
                      event_id: str,
                      prompt: str,
                      image_path: str,
                      model_name: str,
                      additional_info: Optional[Dict[str, Any]] = None):
        """
        记录AI请求信息
        
        Args:
            request_id: 请求ID
            event_id: 事件ID
            prompt: AI提示词
            image_path: 图片路径
            model_name: 模型名称
            additional_info: 额外信息
        """
        with self.lock:
            try:
                # 检查文件大小并轮转
                self._check_and_rotate_log()
                
                # 构建请求日志数据
                request_data = {
                    "type": "AI_REQUEST",
                    "timestamp": datetime.now().isoformat(),
                    "request_id": request_id,
                    "event_id": event_id,
                    "model_name": model_name,
                    "image_path": image_path,
                    "prompt_length": len(prompt),
                    "prompt": prompt,
                    "additional_info": additional_info or {}
                }
                
                # 记录到日志
                self.logger.info(f"[AI请求] {json.dumps(request_data, ensure_ascii=False, indent=2)}")
                
            except Exception as e:
                # 使用标准logger记录错误，避免循环依赖
                logging.getLogger(__name__).error(f"记录AI请求日志失败: {e}")
    
    def log_ai_response(self,
                       request_id: str,
                       event_id: str,
                       success: bool,
                       response_data: Dict[str, Any],
                       response_time: float,
                       error_msg: Optional[str] = None,
                       additional_info: Optional[Dict[str, Any]] = None):
        """
        记录AI响应信息
        
        Args:
            request_id: 请求ID
            event_id: 事件ID
            success: 是否成功
            response_data: 响应数据
            response_time: 响应时间（秒）
            error_msg: 错误信息（如果失败）
            additional_info: 额外信息
        """
        with self.lock:
            try:
                # 检查文件大小并轮转
                self._check_and_rotate_log()
                
                # 构建响应日志数据
                response_log = {
                    "type": "AI_RESPONSE",
                    "timestamp": datetime.now().isoformat(),
                    "request_id": request_id,
                    "event_id": event_id,
                    "success": success,
                    "response_time": round(response_time, 3),
                    "error_msg": error_msg,
                    "additional_info": additional_info or {}
                }
                
                # 添加响应数据（成功时）
                if success and response_data:
                    # 提取关键信息，避免日志过大
                    response_summary = {
                        "ai_result": response_data.get("ai_result"),
                        "confidence": response_data.get("confidence"),
                        "ai_model_name": response_data.get("ai_model_name"),
                        "raw_content_length": len(response_data.get("raw_content", "")),
                        "usage": response_data.get("usage", {})
                    }
                    response_log["response_summary"] = response_summary
                    
                    # 如果需要完整内容，可以选择性记录
                    if len(response_data.get("raw_content", "")) < 1000:
                        response_log["raw_content"] = response_data.get("raw_content")
                    else:
                        response_log["raw_content_truncated"] = response_data.get("raw_content", "")[:500] + "...[截断]"
                
                # 记录到日志
                status = "成功" if success else "失败"
                self.logger.info(f"[AI响应-{status}] {json.dumps(response_log, ensure_ascii=False, indent=2)}")
                
            except Exception as e:
                # 使用标准logger记录错误，避免循环依赖
                logging.getLogger(__name__).error(f"记录AI响应日志失败: {e}")
    
    def log_ai_interaction_summary(self,
                                  request_id: str,
                                  event_id: str,
                                  total_time: float,
                                  final_result: str,
                                  additional_metrics: Optional[Dict[str, Any]] = None):
        """
        记录AI交互总结信息
        
        Args:
            request_id: 请求ID
            event_id: 事件ID
            total_time: 总耗时（秒）
            final_result: 最终结果描述
            additional_metrics: 额外的度量信息
        """
        with self.lock:
            try:
                # 检查文件大小并轮转
                self._check_and_rotate_log()
                
                # 构建总结日志数据
                summary_data = {
                    "type": "AI_INTERACTION_SUMMARY",
                    "timestamp": datetime.now().isoformat(),
                    "request_id": request_id,
                    "event_id": event_id,
                    "total_time": round(total_time, 3),
                    "final_result": final_result,
                    "additional_metrics": additional_metrics or {}
                }
                
                # 记录到日志
                self.logger.info(f"[AI交互总结] {json.dumps(summary_data, ensure_ascii=False, indent=2)}")
                
            except Exception as e:
                # 使用标准logger记录错误，避免循环依赖
                logging.getLogger(__name__).error(f"记录AI交互总结日志失败: {e}")
    
    def _check_and_rotate_log(self):
        """
        检查日志文件大小并进行轮转
        """
        try:
            if os.path.exists(self.log_file_path):
                file_size = os.path.getsize(self.log_file_path)
                if file_size > self.max_file_size:
                    # 轮转日志文件
                    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                    backup_path = os.path.join(
                        self.log_dir, 
                        f"log_AI_提示词和结果_{timestamp}.log"
                    )
                    os.rename(self.log_file_path, backup_path)
                    
                    # 重新设置logger
                    self.logger = self._setup_ai_logger()
                    self.logger.info(f"日志文件已轮转，备份文件: {backup_path}")
                    
        except Exception as e:
            logging.getLogger(__name__).error(f"日志轮转失败: {e}")
    
    def get_log_file_path(self) -> str:
        """
        获取当前日志文件路径
        
        Returns:
            日志文件路径
        """
        return self.log_file_path
    
    def get_log_file_size(self) -> int:
        """
        获取当前日志文件大小
        
        Returns:
            文件大小（字节）
        """
        try:
            if os.path.exists(self.log_file_path):
                return os.path.getsize(self.log_file_path)
            return 0
        except Exception:
            return 0
    
    def close(self):
        """
        关闭AI日志处理器
        """
        with self.lock:
            try:
                self.logger.info("AI日志处理器正在关闭")
                
                # 关闭所有handlers
                for handler in self.logger.handlers:
                    handler.close()
                    self.logger.removeHandler(handler)
                    
            except Exception as e:
                logging.getLogger(__name__).error(f"关闭AI日志处理器失败: {e}")


# 全局AI日志实例
_ai_logger_instance: Optional[AILogger] = None
_ai_logger_lock = Lock()


def get_ai_logger(log_dir: str = "logs") -> AILogger:
    """
    获取全局AI日志实例（单例模式）
    
    Args:
        log_dir: 日志目录路径
        
    Returns:
        AILogger实例
    """
    global _ai_logger_instance
    
    with _ai_logger_lock:
        if _ai_logger_instance is None:
            _ai_logger_instance = AILogger(log_dir)
        return _ai_logger_instance


def close_ai_logger():
    """
    关闭全局AI日志实例
    """
    global _ai_logger_instance
    
    with _ai_logger_lock:
        if _ai_logger_instance is not None:
            _ai_logger_instance.close()
            _ai_logger_instance = None