#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
图片FTP上传工具模块

提供图片上传到FTP服务器的功能。
"""

import os
import re
import logging
import ftplib
from datetime import datetime
from typing import Optional, Dict, Any

logger = logging.getLogger(__name__)


def extract_timestamp_from_filename(filename: str) -> Optional[str]:
    """
    从文件名中提取时间戳
    
    支持的格式：
    1. 裁剪后格式: {原始文件名}-CROPPED.jpg
    2. 原始格式: {前缀}_{标识}_{17位时间戳}_{后缀}.jpg
    """
    
    try:
        # 移除文件扩展名和-CROPPED后缀
        name_without_ext = os.path.splitext(filename)[0]
        if name_without_ext.endswith('-CROPPED'):
            name_without_ext = name_without_ext[:-8]  # 移除-CROPPED
            logger.debug(f"检测到裁剪后文件，移除后缀: {name_without_ext}")
        
        # 查找17位时间戳（以20开头的17位数字）
        timestamp_pattern = r'_(\d{17})_'
        timestamp_match = re.search(timestamp_pattern, name_without_ext)
        
        if timestamp_match:
            timestamp = timestamp_match.group(1)
            
            # 验证时间戳格式
            if timestamp.startswith('20') and len(timestamp) == 17:
                logger.debug(f"成功提取时间戳: {timestamp}")
                return timestamp
        
        logger.warning(f"无法从文件名提取有效的时间戳: {filename}")
        return None
        
    except Exception as e:
        logger.error(f"提取时间戳时发生错误: {e}, 文件名: {filename}")
        return None


def file_upload_ftp_to_cdn(local_path: str, ftp_config: Dict[str, Any], date_directory: str = None, position_id: str = None) -> Optional[str]:
    """
    上传文件到FTP服务器并返回CDN URL
    
    Args:
        local_path: 本地文件路径
        ftp_config: FTP配置信息
        date_directory: 日期目录（可选）
        position_id: 位置ID（可选）
        
    Returns:
        Optional[str]: CDN URL，失败时返回None
    """
    ftp = None
    try:
        # 检查本地文件是否存在
        if not os.path.exists(local_path):
            logger.error(f"本地文件不存在: {local_path}")
            return None
        
        # 从配置中获取FTP连接信息
        ftp_host = ftp_config.get('ftp_host')
        ftp_port = ftp_config.get('ftp_port', 21)
        ftp_username = ftp_config.get('ftp_username')
        ftp_password = ftp_config.get('ftp_password')
        ftp_remote_dir = ftp_config.get('ftp_remote_dir', '')
        ftp_base_url = ftp_config.get('ftp_base_url', '')
        
        if not ftp_host or not ftp_username or not ftp_password:
            logger.error("FTP配置信息不完整")
            return None
        
        logger.info(f"开始上传文件到FTP: {local_path}")
        
        # 提取文件名和时间戳
        filename = os.path.basename(local_path)
        timestamp = extract_timestamp_from_filename(filename)
        if not timestamp:
            logger.error(f"无法从文件名提取时间戳: {filename}")
            return None
        
        # 验证position_id参数
        if not position_id:
            logger.error("position_id参数为空，请从配置中提供")
            return None
        
        # 生成远程路径
        if date_directory:
            remote_path = f"/{ftp_remote_dir}/{date_directory}/{position_id}_{timestamp}.jpg"
        else:
            # 从时间戳计算日期目录
            year_month = timestamp[:6]  # 取前6位: YYYYMM
            remote_path = f"/{ftp_remote_dir}/{year_month}/{position_id}_{timestamp}.jpg"
        
        # 连接FTP服务器
        ftp = ftplib.FTP()
        ftp.connect(ftp_host, ftp_port)
        ftp.login(ftp_username, ftp_password)
        
        logger.debug("FTP连接成功")
        
        # 确保远程目录存在
        remote_dir = os.path.dirname(remote_path).replace('\\', '/')
        if remote_dir.startswith('/'):
            remote_dir = remote_dir[1:]  # 移除开头的斜杠
            
        # 创建目录结构
        if remote_dir:
            dirs = remote_dir.split('/')
            current_dir = ""
            for dir_name in dirs:
                current_dir = f"{current_dir}/{dir_name}" if current_dir else dir_name
                try:
                    ftp.mkd(current_dir)
                    logger.debug(f"创建目录: {current_dir}")
                except ftplib.error_perm as e:
                    # 目录可能已存在，忽略错误
                    if "550" in str(e):  # 目录已存在
                        logger.debug(f"目录已存在: {current_dir}")
                    else:
                        logger.warning(f"创建目录失败: {current_dir}, 错误: {e}")
        
        # 上传文件
        remote_filename = os.path.basename(remote_path)
        if remote_dir:
            ftp.cwd(remote_dir)
            
        with open(local_path, 'rb') as file:
            ftp.storbinary(f'STOR {remote_filename}', file)
            
        logger.info(f"文件上传成功: {remote_path}")
        
        # 生成CDN访问URL
        cdn_url = f"{ftp_base_url}{remote_path}"
        logger.info(f"生成CDN访问URL: {cdn_url}")
        
        return cdn_url
            
    except ftplib.all_errors as e:
        logger.error(f"FTP操作失败: {e}")
        return None
    except Exception as e:
        logger.error(f"上传文件时发生未预期错误: {e}")
        return None
    finally:
        if ftp:
            try:
                ftp.quit()
                logger.debug("FTP连接已关闭")
            except Exception as e:
                logger.warning(f"关闭FTP连接时出错: {e}")

