#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
图片处理工具模块

提供图片裁剪、缩放等处理功能。
"""

import os
import logging
from PIL import Image
from typing import Tuple, Optional

logger = logging.getLogger(__name__)


def crop_image(image_path: str, output_dir: str, crop_coords: Tuple[int, int, int, int]) -> Optional[str]:
    """
    裁剪图片
    
    Args:
        image_path: 原图路径
        output_dir: 输出目录
        crop_coords: 裁剪坐标 (left, top, right, bottom)
        
    Returns:
        Optional[str]: 裁剪后的图片路径，失败时返回None
    """
    try:
        # 检查输入文件是否存在
        if not os.path.exists(image_path):
            logger.error(f"图片文件不存在: {image_path}")
            return None
        
        # 打开图片
        with Image.open(image_path) as img:
            # 获取原图尺寸
            original_width, original_height = img.size
            logger.debug(f"原图尺寸: {original_width}x{original_height}")
            
            # 解析裁剪坐标 (left, top, right, bottom)
            left, top, right, bottom = crop_coords
            
            # 如果坐标为(0,0,0,0)，则不进行裁剪
            if left == 0 and top == 0 and right == 0 and bottom == 0:
                logger.info("裁剪坐标为(0,0,0,0)，跳过裁剪")
                # 对原图进行智能缩放（如果需要）
                final_img = smart_resize_if_needed(img)
                
                # 保存处理后的原图
                filename = os.path.basename(image_path)
                name, ext = os.path.splitext(filename)
                output_path = os.path.join(output_dir, f"{name}-CROPPED{ext}")
                final_img.save(output_path)
                logger.debug(f"原图智能缩放完成: {output_path}，原始尺寸: {img.size}，最终尺寸: {final_img.size}")
                return output_path
            
            # 验证裁剪坐标
            if left < 0 or top < 0 or right <= left or bottom <= top:
                logger.error(f"无效的裁剪坐标: {crop_coords}")
                return None
            
            if right > original_width or bottom > original_height:
                logger.error(f"裁剪区域超出图片范围: {crop_coords}, 图片尺寸: {original_width}x{original_height}")
                return None
            
            # 执行裁剪
            cropped_img = img.crop((left, top, right, bottom))
            
            # 智能缩放：如果裁剪后图片尺寸都大于800x400，则进行缩放
            final_img = smart_resize_if_needed(cropped_img)
            
            # 生成输出文件名
            filename = os.path.basename(image_path)
            name, ext = os.path.splitext(filename)
            output_path = os.path.join(output_dir, f"{name}-CROPPED{ext}")
            
            # 保存处理后的图片
            final_img.save(output_path)
            
            logger.debug(f"图片裁剪完成: {output_path}.裁剪后尺寸: {right - left}x{bottom - top}，最终尺寸: {final_img.size}")
            
            return output_path
            
    except Exception as e:
        logger.error(f"图片裁剪失败: {e}", exc_info=True)
        return None


def smart_resize_if_needed(img: Image.Image, max_width: int = 800, max_height: int = 400) -> Image.Image:
    """
    智能缩放图片：如果图片宽高都大于指定阈值，则进行缩放
    
    缩放规则：
    - 如果宽高都大于800x400，则缩放
    - 优先保持宽度为800（如果高度>400）
    - 或保持高度为400（如果宽度>800）
    - 保持原始宽高比
    
    Args:
        img: PIL Image对象
        max_width: 最大宽度，默认800
        max_height: 最大高度，默认400
        
    Returns:
        Image.Image: 处理后的图片（可能是原图或缩放后的图）
    """
    try:
        original_width, original_height = img.size
        
        # 如果图片宽高都不超过阈值，直接返回原图
        if original_width <= max_width or original_height <= max_height:
            logger.debug(f"图片尺寸无需缩放: {original_width}x{original_height} (阈值: {max_width}x{max_height})")
            return img
        
        # 计算缩放比例
        # 规则：宽800xh(h>400) 或 wx400(w>800)
        scale_by_width = max_width / original_width
        scale_by_height = max_height / original_height
        
        # 选择较小的缩放比例，确保缩放后的图片不超过任何一个维度的限制
        scale_factor = min(scale_by_width, scale_by_height)
        
        # 计算新的尺寸
        new_width = int(original_width * scale_factor)
        new_height = int(original_height * scale_factor)
        
        # 确保新尺寸至少为1像素
        new_width = max(1, new_width)
        new_height = max(1, new_height)
        
        # 执行缩放（使用高质量的重采样算法）
        resized_img = img.resize((new_width, new_height), Image.Resampling.LANCZOS)
        
        logger.info(f"图片智能缩放: {original_width}x{original_height} -> {new_width}x{new_height} (缩放比例: {scale_factor:.3f})")
        
        return resized_img
        
    except Exception as e:
        logger.error(f"图片智能缩放失败: {e}", exc_info=True)
        logger.warning("返回原图")
        return img


def calculate_smart_resize_dimensions(width: int, height: int, max_width: int = 800, max_height: int = 400) -> Tuple[int, int, float]:
    """
    计算智能缩放的目标尺寸（工具函数，用于测试和预览）
    
    Args:
        width: 原始宽度
        height: 原始高度
        max_width: 最大宽度，默认800
        max_height: 最大高度，默认400
        
    Returns:
        Tuple[int, int, float]: (新宽度, 新高度, 缩放比例)
    """
    if width <= max_width or height <= max_height:
        return width, height, 1.0
    
    scale_by_width = max_width / width
    scale_by_height = max_height / height
    scale_factor = min(scale_by_width, scale_by_height)
    
    new_width = max(1, int(width * scale_factor))
    new_height = max(1, int(height * scale_factor))
    
    return new_width, new_height, scale_factor