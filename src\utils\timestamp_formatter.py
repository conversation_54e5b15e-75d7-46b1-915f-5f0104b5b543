"""
时间戳格式化工具模块

提供时间戳格式化功能，用于在日志输出中添加可读的时间格式
"""

from datetime import datetime
from typing import Optional
import logging

logger = logging.getLogger(__name__)


def format_timestamp_for_log(timestamp: int) -> str:
    """
    将时间戳格式化为日志显示格式
    
    Args:
        timestamp: 时间戳（毫秒）
        
    Returns:
        str: 格式化后的时间字符串，格式为 "HH:MM:SS.fff (时间戳=原始值)"
             如果解析失败，返回原始时间戳字符串
    """
    try:
        # 将毫秒时间戳转换为秒
        timestamp_seconds = timestamp / 1000.0
        
        # 转换为datetime对象
        dt = datetime.fromtimestamp(timestamp_seconds)
        
        # 格式化为 HH:MM:SS.fff
        formatted_time = dt.strftime('%H:%M:%S.%f')[:-3]  # 只保留3位毫秒
        
        return f"{formatted_time} (时间戳={timestamp})"
        
    except (ValueError, OSError, OverflowError) as e:
        logger.debug(f"时间戳格式化失败: {timestamp}, 错误: {e}")
        return str(timestamp)


def format_trigger_timestamp_for_log(timestamp: int) -> str:
    """
    将触发时间戳格式化为日志显示格式
    
    Args:
        timestamp: 触发时间戳（毫秒）
        
    Returns:
        str: 格式化后的时间字符串，格式为 "HH:MM:SS.fff (触发时间戳: 原始值)"
             如果解析失败，返回原始时间戳字符串
    """
    try:
        # 将毫秒时间戳转换为秒
        timestamp_seconds = timestamp / 1000.0
        
        # 转换为datetime对象
        dt = datetime.fromtimestamp(timestamp_seconds)
        
        # 格式化为 HH:MM:SS.fff
        formatted_time = dt.strftime('%H:%M:%S.%f')[:-3]  # 只保留3位毫秒
        
        return f"{formatted_time} (触发时间戳: {timestamp})"
        
    except (ValueError, OSError, OverflowError) as e:
        logger.debug(f"触发时间戳格式化失败: {timestamp}, 错误: {e}")
        return f"触发时间戳: {timestamp}"


def format_image_timestamp_for_log(timestamp: int) -> str:
    """
    将图片时间戳格式化为日志显示格式
    
    Args:
        timestamp: 图片时间戳（毫秒）
        
    Returns:
        str: 格式化后的时间字符串，格式为 "HH:MM:SS.fff (时间戳: 原始值)"
             如果解析失败，返回原始时间戳字符串
    """
    try:
        # 将毫秒时间戳转换为秒
        timestamp_seconds = timestamp / 1000.0
        
        # 转换为datetime对象
        dt = datetime.fromtimestamp(timestamp_seconds)
        
        # 格式化为 HH:MM:SS.fff
        formatted_time = dt.strftime('%H:%M:%S.%f')[:-3]  # 只保留3位毫秒
        
        return f"{formatted_time} (时间戳: {timestamp})"
        
    except (ValueError, OSError, OverflowError) as e:
        logger.debug(f"图片时间戳格式化失败: {timestamp}, 错误: {e}")
        return f"时间戳: {timestamp}"
