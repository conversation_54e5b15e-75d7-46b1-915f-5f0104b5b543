# EP_V4.1 配置切换脚本
# 用于在EV001001和EV001002配置之间切换

param(
    [Parameter(Mandatory=$true)]
    [ValidateSet("EV001001", "EV001002")]
    [string]$EventType
)

$serverPath = "\\bdnserver\BDN\EP_V4.1"
$localPath = "C:\Users\<USER>\src\event_processor\ep_v4.1_augment"

Write-Host "=== EP_V4.1 配置切换脚本 ===" -ForegroundColor Green
Write-Host "切换到事件类型: $EventType" -ForegroundColor Yellow

try {
    if ($EventType -eq "EV001001") {
        # 切换到EV001001 (未过期月租车)
        Write-Host "正在切换到 EV001001 - 月租车未过期超时滞留出口..." -ForegroundColor Cyan
        
        # 备份当前配置
        Copy-Item "$serverPath\appsettings.yaml" "$serverPath\appsettings_backup.yaml" -Force
        Write-Host "已备份当前配置到 appsettings_backup.yaml" -ForegroundColor Gray
        
        # 复制EV001001配置
        Copy-Item "$localPath\appsettings.yaml" "$serverPath\appsettings.yaml" -Force
        Write-Host "已应用 EV001001 配置" -ForegroundColor Green
        
        Write-Host ""
        Write-Host "EV001001 配置特征:" -ForegroundColor White
        Write-Host "  - 事件ID: EV001001" -ForegroundColor Gray
        Write-Host "  - 检测条件: Expire=0 (卡未过期)" -ForegroundColor Gray
        Write-Host "  - 告警名称: 月租车超时滞留出口（卡未过期）" -ForegroundColor Gray
        Write-Host "  - 告警等级: 通知" -ForegroundColor Gray
        Write-Host "  - 测试脚本: python test_expire_field.py" -ForegroundColor Gray
    }
    elseif ($EventType -eq "EV001002") {
        # 切换到EV001002 (过期月租车)
        Write-Host "正在切换到 EV001002 - 月租车过期超时滞留出口..." -ForegroundColor Cyan
        
        # 备份当前配置
        Copy-Item "$serverPath\appsettings.yaml" "$serverPath\appsettings_backup.yaml" -Force
        Write-Host "已备份当前配置到 appsettings_backup.yaml" -ForegroundColor Gray
        
        # 复制EV001002配置
        Copy-Item "$serverPath\appsettings_EV001002.yaml" "$serverPath\appsettings.yaml" -Force
        Write-Host "已应用 EV001002 配置" -ForegroundColor Green
        
        Write-Host ""
        Write-Host "EV001002 配置特征:" -ForegroundColor White
        Write-Host "  - 事件ID: EV001002" -ForegroundColor Gray
        Write-Host "  - 检测条件: log_remain_days=0 (卡已过期)" -ForegroundColor Gray
        Write-Host "  - 告警名称: 月租车过期超时滞留出口" -ForegroundColor Gray
        Write-Host "  - 告警等级: 通知" -ForegroundColor Gray
        Write-Host "  - 测试脚本: python test_expire_ev001002.py" -ForegroundColor Gray
    }
    
    Write-Host ""
    Write-Host "配置切换完成！" -ForegroundColor Green
    Write-Host "请重启 EP_V4.1 服务以应用新配置" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "使用方法:" -ForegroundColor White
    Write-Host "  切换到EV001001: .\switch_config.ps1 -EventType EV001001" -ForegroundColor Gray
    Write-Host "  切换到EV001002: .\switch_config.ps1 -EventType EV001002" -ForegroundColor Gray
}
catch {
    Write-Host "配置切换失败: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}
