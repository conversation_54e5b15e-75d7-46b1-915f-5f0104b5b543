#!/usr/bin/env python3
"""
测试Expire字段提取的脚本
发送包含Expire字段的AJB消息到MQTT，验证EP_V4.1是否正确提取
"""

import json
import time
import paho.mqtt.client as mqtt
from datetime import datetime

# MQTT配置
MQTT_CONFIG = {
    'host': 'mq.bangdouni.com',
    'port': 1883,
    'username': 'bdn_ai_process',
    'password': 'Bdn@2024',
    'keepalive': 60
}

def on_connect(client, userdata, flags, rc):
    if rc == 0:
        print(f"[{datetime.now().strftime('%H:%M:%S')}] MQTT连接成功")
    else:
        print(f"[{datetime.now().strftime('%H:%M:%S')}] MQTT连接失败，错误码: {rc}")

def on_publish(client, userdata, mid):
    print(f"[{datetime.now().strftime('%H:%M:%S')}] 消息发布成功，消息ID: {mid}")

def send_test_messages():
    """发送测试消息"""
    client = mqtt.Client()
    client.username_pw_set(MQTT_CONFIG['username'], MQTT_CONFIG['password'])
    client.on_connect = on_connect
    client.on_publish = on_publish
    
    try:
        print(f"[{datetime.now().strftime('%H:%M:%S')}] 连接到MQTT服务器...")
        client.connect(MQTT_CONFIG['host'], MQTT_CONFIG['port'], MQTT_CONFIG['keepalive'])
        client.loop_start()
        
        time.sleep(2)  # 等待连接建立
        
        # 1. 发送设备信号（触发事件） - 使用测试主题
        device_topic = "device/BDN888/event"
        device_payload = {
            "I2": {
                "value": "0",
                "timestamp": int(time.time())
            }
        }
        
        print(f"[{datetime.now().strftime('%H:%M:%S')}] 发送设备信号...")
        client.publish(device_topic, json.dumps(device_payload))
        time.sleep(2)
        
        # 2. 发送包含Expire字段的AJB消息 - 使用测试主题
        ajb_topic = "ajb/101013/out/P088LfyBmOut/time_log"
        ajb_payload = {
            "log_original_timestamp": "15:50:00:000",
            "log_event_type": "查询停车系统返回结果",
            # EV001002关键字段 - 添加到顶层
            "CardType": "月租卡",
            "log_car_no": "粤A88L0J",
            "log_user_name": "李明",
            "log_end_time": "2025-07-30 23:59:59",
            "log_remain_days": "0",  # 关键：过期标志
            "response_payload": {
                "status": True,
                "code": "0000",
                "msg": "查询成功",
                "confirm": "1",
                "data": {
                    "CarInfo": {
                        "CardId": "52",
                        "CardSnId": "52",
                        "CarNo": "粤A88L0J",
                        "CardType": "月租卡",
                        "Intime": "2025-08-04 10:46:52",
                        "PStatus": "入场",
                        "DoorName": "一楼出口",
                        "Balance": 0,
                        "Starttime": "2020-04-01",
                        "Endtime": "2023-12-31",
                        "Name": "李明",
                        "PositionNum": "",
                        "NColor": "蓝色",
                        "ByCarType": "小车",
                        "BindFeeType": "业主临停收费",
                        "UserID": "",
                        "BillId": "",
                        "NoSensePay": "0",
                        "Per_Full": "",
                        "MoreCarNoInfo": "",
                        "IsMoreCarNo": "0",
                        "Acctime": ""
                    },
                    "Charge": {
                        "AllFee": "5",
                        "ParkTime": "21时6分",
                        "FeeTime": "20时56分",
                        "FavMoney": "0",
                        "FavTime": "0",
                        "TotalFee": "5",
                        "CurrFavMoney": "0",
                        "Overtime": "15",
                        "IsFree": "0",
                        "IsTime": "0",
                        "ChargeMoney": "0",
                        "ChargeTime": "",
                        "ChargeType": "",
                        "StartTime": "2025-08-04 10:46:52",
                        "EndTime": "2025-08-05 07:52:57",
                        "ChargingCar": "业主临停收费",
                        "OrderID": "66676CD413B2439ABFD028551ECDADD8",
                        "log_remain_days": "0",  # 关键字段：已过期（EV001002测试）
                        "IsAutoCharge": "0",
                        "lastPaidTime": ""
                    }
                }
            }
        }
        
        print(f"[{datetime.now().strftime('%H:%M:%S')}] 发送AJB消息（包含Expire=0）...")
        client.publish(ajb_topic, json.dumps(ajb_payload, ensure_ascii=False))
        time.sleep(2)
        
        # 3. 发送设备信号（结束事件）
        device_payload["I2"]["value"] = "1"
        device_payload["I2"]["timestamp"] = int(time.time())
        
        print(f"[{datetime.now().strftime('%H:%M:%S')}] 发送设备信号结束...")
        client.publish(device_topic, json.dumps(device_payload))
        time.sleep(2)
        
        print(f"[{datetime.now().strftime('%H:%M:%S')}] 测试消息发送完成")
        
    except Exception as e:
        print(f"[{datetime.now().strftime('%H:%M:%S')}] 错误: {e}")
    finally:
        client.loop_stop()
        client.disconnect()

if __name__ == "__main__":
    print("=== EP_V4.1 Expire字段提取测试 ===")
    print(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    send_test_messages()
    print("测试完成，请检查EP_V4.1日志中的'AJB关键字段'输出")
