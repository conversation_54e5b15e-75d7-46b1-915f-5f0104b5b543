#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
EP_V4.1 EV001002 真实过期月租车测试脚本
使用真实的过期车消息格式进行测试
"""

import json
import time
import paho.mqtt.client as mqtt
from datetime import datetime

# MQTT配置
MQTT_CONFIG = {
    'host': 'mq.bangdouni.com',
    'port': 1883,
    'username': 'bdn_ai_process',
    'password': 'Bdn@2024',
    'keepalive': 60
}

def on_connect(client, userdata, flags, rc):
    if rc == 0:
        print(f"[{datetime.now().strftime('%H:%M:%S')}] MQTT连接成功")
    else:
        print(f"[{datetime.now().strftime('%H:%M:%S')}] MQTT连接失败，错误码: {rc}")

def on_publish(client, userdata, mid):
    print(f"[{datetime.now().strftime('%H:%M:%S')}] 消息发布成功，消息ID: {mid}")

def send_real_expire_test():
    """发送真实格式的EV001002过期测试消息"""
    client = mqtt.Client()
    client.username_pw_set(MQTT_CONFIG['username'], MQTT_CONFIG['password'])
    client.on_connect = on_connect
    client.on_publish = on_publish
    
    print("=== EP_V4.1 EV001002 真实过期月租车测试 ===")
    print(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    try:
        print("[{0}] 连接到MQTT服务器...".format(datetime.now().strftime('%H:%M:%S')))
        client.connect(MQTT_CONFIG['host'], MQTT_CONFIG['port'], MQTT_CONFIG['keepalive'])
        client.loop_start()
        
        time.sleep(2)
        
        # 1. 发送设备信号（触发事件） - 使用测试主题
        device_topic = "device/BDN888/event"
        device_message = {
            "I2": "0",  # 触发值
            "duration": 180,  # 停留时间180秒
            "timestamp": datetime.now().isoformat()
        }
        
        print("[{0}] 发送设备信号...".format(datetime.now().strftime('%H:%M:%S')))
        client.publish(device_topic, json.dumps(device_message), qos=1)
        
        time.sleep(2)
        
        # 2. 发送真实格式的过期AJB消息 - 使用测试主题
        ajb_topic = "ajb/101013/out/P088LfyBmOut/time_log"
        
        # 真实的过期车消息格式
        real_expire_message = {
            "log_original_timestamp": "15:28:55:462",
            "log_event_type": "有效期剩余天数",
            "log_send_day_num": "0",
            "log_remaining_days": "0",  # 关键：剩余天数为0
            "log_reminder_text": "请到管理处缴费续期"
        }
        
        print("[{0}] 发送真实格式的过期AJB消息...".format(datetime.now().strftime('%H:%M:%S')))
        print("消息内容:")
        print(json.dumps(real_expire_message, ensure_ascii=False, indent=2))
        client.publish(ajb_topic, json.dumps(real_expire_message), qos=1)
        
        time.sleep(2)
        
        # 3. 发送设备信号结束
        device_end_message = {
            "I2": "1",  # 结束值
            "timestamp": datetime.now().isoformat()
        }
        
        print("[{0}] 发送设备信号结束...".format(datetime.now().strftime('%H:%M:%S')))
        client.publish(device_topic, json.dumps(device_end_message), qos=1)
        
        time.sleep(2)
        
        print("[{0}] 测试消息发送完成".format(datetime.now().strftime('%H:%M:%S')))
        print("")
        print("=== 测试完成 ===")
        print("请检查EP_V4.1日志中的EV001002告警输出")
        print("")
        print("预期结果:")
        print("1. 业务规则匹配: log_event_type = '有效期剩余天数'")
        print("2. 业务规则匹配: log_remaining_days = '0'")
        print("3. 生成告警消息，data字段应包含:")
        print("   [详情]: [有效期剩余0天][请到管理处缴费续期]")
        print("   [名称]: 月租车过期超时滞留出口")
        print("")
        
    except Exception as e:
        print(f"发送测试消息时出错: {e}")
    finally:
        client.loop_stop()
        client.disconnect()

if __name__ == "__main__":
    send_real_expire_test()
