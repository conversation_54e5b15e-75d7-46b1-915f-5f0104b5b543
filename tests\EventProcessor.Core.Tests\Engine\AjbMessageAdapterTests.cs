using EventProcessor.Core.Engine;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;
using FluentAssertions;

namespace EventProcessor.Core.Tests.Engine;

/// <summary>
/// AjbMessageAdapter 测试类
/// 验证ajb消息适配器的功能，确保能正确处理报告中的问题
/// </summary>
public class AjbMessageAdapterTests
{
    private readonly Mock<ILogger> _mockLogger;

    public AjbMessageAdapterTests()
    {
        _mockLogger = new Mock<ILogger>();
    }

    [Theory]
    [InlineData("ajb/101013/out/P002LfyBmOut/time_log", true)]
    [InlineData("ajb/123456/out/SomePosition/time_log", true)]
    [InlineData("device/101013/out/P002LfyBmOut", false)]
    [InlineData("other/topic", false)]
    [InlineData("ajb/101013/in/P002LfyBmIn/time_log", true)] // 入口也应该支持
    public void IsAjbMessage_WithDifferentTopics_ShouldIdentifyCorrectly(string topic, bool expected)
    {
        // Act
        var result = AjbMessageAdapter.IsAjbMessage(topic);

        // Assert
        result.Should().Be(expected);
    }

    [Fact]
    public void AdaptAjbMessage_WithReportedFailureData_ShouldExtractAllFields()
    {
        // Arrange - 使用报告中导致失败的实际数据结构
        var jsonPayload = """
        {
            "log_original_timestamp": "22:17:28:958",
            "log_event_type": "出场查询系统返回结果",
            "response_payload": {
                "status": true,
                "code": "0000",
                "msg": "操作成功",
                "data": {
                    "CarInfo": {
                        "CarNo": "粤AY8C52",
                        "CardType": "储值卡",
                        "Intime": "2025-08-04 22:14:06",
                        "Name": "蒋燕琼"
                    },
                    "Charge": {
                        "ParkTime": "0时3分",
                        "StartTime": "2025-08-04 22:14:06",
                        "EndTime": "2025-08-04 22:17:28"
                    }
                }
            }
        }
        """;

        // Act
        var result = AjbMessageAdapter.AdaptAjbMessage(jsonPayload, _mockLogger.Object);

        // Assert - 验证所有关键字段都被正确提取
        result.Should().ContainKey("CardType");
        result.Should().ContainKey("log_car_no");
        result.Should().ContainKey("log_user_name");
        result.Should().ContainKey("duration");
        result.Should().ContainKey("ParkTime");

        // 验证字段值的正确性
        result["CardType"].Should().Be("储值卡");
        result["log_car_no"].Should().Be("粤AY8C52");
        result["log_user_name"].Should().Be("蒋燕琼");
        result["duration"].Should().Be(3); // "0时3分" = 3分钟
        result["ParkTime"].Should().Be("0时3分");

        // 验证其他字段
        result.Should().ContainKey("log_original_timestamp");
        result.Should().ContainKey("log_event_type");
        result.Should().ContainKey("response_status");
        result.Should().ContainKey("response_code");
    }

    [Theory]
    [InlineData("2小时30分钟", 150)]
    [InlineData("1小时", 60)]
    [InlineData("45分钟", 45)]
    [InlineData("0时3分", 3)]
    [InlineData("3时45分", 225)]
    [InlineData("无效格式", 0)]
    [InlineData("", 0)]
    [InlineData(null, 0)]
    public void AdaptAjbMessage_WithDifferentParkTimeFormats_ShouldCalculateCorrectDuration(string parkTime, int expectedMinutes)
    {
        // Arrange
        var jsonPayload = $$"""
        {
            "response_payload": {
                "data": {
                    "CarInfo": {
                        "CardType": "月租卡",
                        "CarNo": "粤B12345"
                    },
                    "Charge": {
                        "ParkTime": "{{parkTime}}"
                    }
                }
            }
        }
        """;

        // Act
        var result = AjbMessageAdapter.AdaptAjbMessage(jsonPayload, _mockLogger.Object);

        // Assert
        if (expectedMinutes > 0)
        {
            result.Should().ContainKey("duration");
            result["duration"].Should().Be(expectedMinutes);
        }
        else
        {
            // 对于无效格式，duration应该为0
            result.GetValueOrDefault("duration", 0).Should().Be(0);
        }
    }

    [Fact]
    public void AdaptAjbMessage_WithMissingCarInfo_ShouldHandleGracefully()
    {
        // Arrange - 缺少CarInfo的消息
        var jsonPayload = """
        {
            "response_payload": {
                "status": true,
                "data": {
                    "Charge": {
                        "ParkTime": "1小时30分钟"
                    }
                }
            }
        }
        """;

        // Act
        var result = AjbMessageAdapter.AdaptAjbMessage(jsonPayload, _mockLogger.Object);

        // Assert - 应该至少提取到duration
        result.Should().ContainKey("duration");
        result["duration"].Should().Be(90); // 1小时30分钟 = 90分钟
    }

    [Fact]
    public void AdaptAjbMessage_WithMissingCharge_ShouldHandleGracefully()
    {
        // Arrange - 缺少Charge的消息
        var jsonPayload = """
        {
            "response_payload": {
                "status": true,
                "data": {
                    "CarInfo": {
                        "CardType": "月租卡",
                        "CarNo": "粤B12345"
                    }
                }
            }
        }
        """;

        // Act
        var result = AjbMessageAdapter.AdaptAjbMessage(jsonPayload, _mockLogger.Object);

        // Assert - 应该至少提取到CarInfo字段
        result.Should().ContainKey("CardType");
        result.Should().ContainKey("log_car_no");
        result["CardType"].Should().Be("月租卡");
        result["log_car_no"].Should().Be("粤B12345");
    }

    [Fact]
    public void AdaptAjbMessage_WithInvalidJson_ShouldReturnEmptyDictionary()
    {
        // Arrange
        var invalidJson = "{ invalid json }";

        // Act
        var result = AjbMessageAdapter.AdaptAjbMessage(invalidJson, _mockLogger.Object);

        // Assert
        result.Should().BeEmpty();
    }

    [Fact]
    public void AdaptAjbMessage_WithCompleteEV001001Scenario_ShouldProvideAllRequiredFields()
    {
        // Arrange - 完整的EV001001场景数据
        var jsonPayload = """
        {
            "log_original_timestamp": "22:17:28:958",
            "log_event_type": "出场查询系统返回结果",
            "response_payload": {
                "status": true,
                "code": "0000",
                "msg": "操作成功",
                "data": {
                    "CarInfo": {
                        "CarNo": "粤B88888",
                        "CardType": "月租卡",
                        "Intime": "2025-08-04 20:00:00",
                        "Name": "张三",
                        "RemainDays": 15,
                        "EndTime": "2025-12-31 23:59:59"
                    },
                    "Charge": {
                        "ParkTime": "2小时30分钟",
                        "StartTime": "2025-08-04 20:00:00",
                        "EndTime": "2025-08-04 22:30:00"
                    }
                }
            }
        }
        """;

        // Act
        var result = AjbMessageAdapter.AdaptAjbMessage(jsonPayload, _mockLogger.Object);

        // Assert - 验证EV001001业务规则需要的所有字段都存在
        
        // 卡类型检查
        result.Should().ContainKey("CardType");
        result["CardType"].Should().Be("月租卡");

        // 剩余天数检查
        result.Should().ContainKey("log_remain_days");
        result["log_remain_days"].Should().Be(15L);

        // 停车时长检查
        result.Should().ContainKey("duration");
        result["duration"].Should().Be(150); // 2小时30分钟 = 150分钟，满足 > 20 的条件

        // 其他有用字段
        result.Should().ContainKey("log_car_no");
        result.Should().ContainKey("log_user_name");
        result.Should().ContainKey("log_end_time");
        
        result["log_car_no"].Should().Be("粤B88888");
        result["log_user_name"].Should().Be("张三");
        result["log_end_time"].Should().Be("2025-12-31 23:59:59");
    }

    [Fact]
    public void AdaptAjbMessage_WithNullValues_ShouldHandleGracefully()
    {
        // Arrange - 包含null值的消息
        var jsonPayload = """
        {
            "response_payload": {
                "data": {
                    "CarInfo": {
                        "CardType": "月租卡",
                        "CarNo": "粤B12345",
                        "Name": null,
                        "EndTime": null
                    },
                    "Charge": {
                        "ParkTime": "1小时"
                    }
                }
            }
        }
        """;

        // Act
        var result = AjbMessageAdapter.AdaptAjbMessage(jsonPayload, _mockLogger.Object);

        // Assert - null值应该被正确处理
        result.Should().ContainKey("CardType");
        result.Should().ContainKey("log_car_no");
        result.Should().ContainKey("duration");

        result["CardType"].Should().Be("月租卡");
        result["log_car_no"].Should().Be("粤B12345");
        result["duration"].Should().Be(60);

        // null值字段不应该被包含，或者包含但值为null
        if (result.ContainsKey("log_user_name"))
        {
            result["log_user_name"].Should().BeNull();
        }
    }

    [Fact]
    public void AdaptAjbMessage_WithExpireField_ShouldExtractExpireCorrectly()
    {
        // Arrange - 专门测试Expire字段提取（修复EV001001问题）
        var jsonPayload = """
        {
            "log_original_timestamp": "07:52:59:637",
            "log_event_type": "出场查询系统返回结果",
            "response_payload": {
                "status": true,
                "code": "0000",
                "msg": "操作成功",
                "confirm": "1",
                "data": {
                    "CarInfo": {
                        "CardId": "52",
                        "CarNo": "粤A32L0J",
                        "CardType": "储值卡"
                    },
                    "Charge": {
                        "AllFee": "5",
                        "ParkTime": "21时6分",
                        "TotalFee": "5",
                        "StartTime": "2025-08-04 10:46:52",
                        "EndTime": "2025-08-05 07:52:57",
                        "Expire": "0",
                        "OrderID": "66676CD413B2439ABFD028551ECDADD8"
                    }
                }
            }
        }
        """;

        // Act
        var result = AjbMessageAdapter.AdaptAjbMessage(jsonPayload, _mockLogger.Object);

        // Assert - 验证关键字段都被正确提取
        result.Should().ContainKey("CardType");
        result.Should().ContainKey("Expire");
        result.Should().ContainKey("log_car_no");
        result.Should().ContainKey("duration");
        result.Should().ContainKey("AllFee");
        result.Should().ContainKey("TotalFee");
        result.Should().ContainKey("OrderID");

        result["CardType"].Should().Be("储值卡");
        result["Expire"].Should().Be("0");  // 🔧 这是修复的关键字段
        result["log_car_no"].Should().Be("粤A32L0J");
        result["duration"].Should().Be(1266); // 21时6分 = 1266分钟
        result["AllFee"].Should().Be("5");
        result["TotalFee"].Should().Be("5");
        result["OrderID"].Should().Be("66676CD413B2439ABFD028551ECDADD8");
    }

    [Fact]
    public void AdaptAjbMessage_PerformanceTest_ShouldBeEfficient()
    {
        // Arrange
        var jsonPayload = """
        {
            "response_payload": {
                "data": {
                    "CarInfo": {
                        "CardType": "月租卡",
                        "CarNo": "粤B12345"
                    },
                    "Charge": {
                        "ParkTime": "1小时30分钟"
                    }
                }
            }
        }
        """;

        var stopwatch = System.Diagnostics.Stopwatch.StartNew();

        // Act - 处理1000次
        for (int i = 0; i < 1000; i++)
        {
            var result = AjbMessageAdapter.AdaptAjbMessage(jsonPayload, _mockLogger.Object);
            result.Should().NotBeEmpty();
        }

        stopwatch.Stop();

        // Assert - 性能应该在合理范围内
        stopwatch.ElapsedMilliseconds.Should().BeLessThan(1000, "处理1000条消息应该在1秒内完成");
    }
}
