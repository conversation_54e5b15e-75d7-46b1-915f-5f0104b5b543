using EventProcessor.Core.Engine;
using EventProcessor.Core.Models;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;
using FluentAssertions;

namespace EventProcessor.Core.Tests.Integration;

/// <summary>
/// AJB消息修复验证测试
/// 验证报告 REPORT01-Rule-Engine-Fails-To-Parse-Upstream-Data.md 中的问题是否已解决
/// </summary>
public class AjbMessageFixValidationTests
{
    private readonly Mock<ILogger<BusinessDataAccumulator>> _mockLogger;

    public AjbMessageFixValidationTests()
    {
        _mockLogger = new Mock<ILogger<BusinessDataAccumulator>>();
    }

    [Fact]
    public void BusinessDataAccumulator_WithReportedAjbMessage_ShouldProcessSuccessfully()
    {
        // Arrange - 使用报告中导致失败的实际消息
        var accumulator = new BusinessDataAccumulator(_mockLogger.Object);
        var message = new EventMessage
        {
            Topic = "ajb/101013/out/P002LfyBmOut/time_log",
            Payload = """
            {
                "log_original_timestamp": "22:17:28:958",
                "log_event_type": "出场查询系统返回结果",
                "response_payload": {
                    "status": true,
                    "code": "0000",
                    "msg": "操作成功",
                    "data": {
                        "CarInfo": {
                            "CarNo": "粤AY8C52",
                            "CardType": "储值卡",
                            "Intime": "2025-08-04 22:14:06",
                            "Name": "蒋燕琼",
                            "RemainDays": 5
                        },
                        "Charge": {
                            "ParkTime": "0时25分",
                            "StartTime": "2025-08-04 22:14:06",
                            "EndTime": "2025-08-04 22:17:28"
                        }
                    }
                }
            }
            """,
            Timestamp = DateTime.UtcNow
        };

        // Act
        accumulator.UpdateMessage(message);
        var result = accumulator.GetLatestData();

        // Assert - 验证之前失败的字段现在都能正确获取
        result.Should().ContainKey("CardType", "现在应该能从嵌套路径提取CardType");
        result.Should().ContainKey("log_car_no", "现在应该能从嵌套路径提取车牌号");
        result.Should().ContainKey("log_remain_days", "现在应该能从嵌套路径提取剩余天数");
        result.Should().ContainKey("duration", "现在应该能从ParkTime计算duration");

        result["CardType"].Should().Be("储值卡");
        result["log_car_no"].Should().Be("粤AY8C52");
        result["log_remain_days"].Should().Be(5L);
        result["duration"].Should().Be(25); // "0时25分" = 25分钟
    }

    [Fact]
    public void BusinessDataAccumulator_WithEV001001Scenario_ShouldSatisfyBusinessRules()
    {
        // Arrange - 模拟EV001001场景，确保所有业务规则条件都能满足
        var accumulator = new BusinessDataAccumulator(_mockLogger.Object);
        var message = new EventMessage
        {
            Topic = "ajb/101013/out/P002LfyBmOut/time_log",
            Payload = """
            {
                "response_payload": {
                    "data": {
                        "CarInfo": {
                            "CarNo": "粤B88888",
                            "CardType": "月租卡",
                            "RemainDays": 15
                        },
                        "Charge": {
                            "ParkTime": "2时30分"
                        }
                    }
                }
            }
            """,
            Timestamp = DateTime.UtcNow
        };

        // Act
        accumulator.UpdateMessage(message);
        var data = accumulator.GetLatestData();

        // Assert - 验证EV001001业务规则的所有条件都能满足
        
        // 条件1: CardType in "月租卡|万全卡|贵宾卡|储值卡"
        data.Should().ContainKey("CardType");
        data["CardType"].Should().Be("月租卡");
        var validCardTypes = new[] { "月租卡", "万全卡", "贵宾卡", "储值卡" };
        validCardTypes.Should().Contain(data["CardType"].ToString());

        // 条件2: log_remain_days > 0
        data.Should().ContainKey("log_remain_days");
        data["log_remain_days"].Should().BeOfType<long>().Which.Should().BeGreaterThan(0);

        // 条件3: duration > 20
        data.Should().ContainKey("duration");
        data["duration"].Should().BeOfType<int>().Which.Should().BeGreaterThan(20);
        data["duration"].Should().Be(150); // 2时30分 = 150分钟
    }

    [Theory]
    [InlineData("储值卡", 5, "0时25分", 25, true)]   // 报告中的成功场景
    [InlineData("月租卡", 10, "1时30分", 90, true)]  // 另一个成功场景
    [InlineData("临时卡", 5, "0时25分", 25, false)]  // 卡类型不匹配
    [InlineData("储值卡", 0, "0时25分", 25, false)]  // 剩余天数不满足
    [InlineData("储值卡", 5, "0时15分", 15, false)]  // 停车时长不满足
    public void BusinessRuleEvaluation_WithDifferentAjbScenarios_ShouldEvaluateCorrectly(
        string cardType, int remainDays, string parkTime, int expectedDuration, bool shouldSatisfyRules)
    {
        // Arrange
        var accumulator = new BusinessDataAccumulator(_mockLogger.Object);
        var message = new EventMessage
        {
            Topic = "ajb/101013/out/P002LfyBmOut/time_log",
            Payload = $$"""
            {
                "response_payload": {
                    "data": {
                        "CarInfo": {
                            "CardType": "{{cardType}}",
                            "CarNo": "粤B12345",
                            "RemainDays": {{remainDays}}
                        },
                        "Charge": {
                            "ParkTime": "{{parkTime}}"
                        }
                    }
                }
            }
            """,
            Timestamp = DateTime.UtcNow
        };

        // Act
        accumulator.UpdateMessage(message);
        var data = accumulator.GetLatestData();

        // Assert
        data.Should().ContainKey("duration");
        data["duration"].Should().Be(expectedDuration);

        // 验证业务规则条件
        var validCardTypes = new[] { "月租卡", "万全卡", "贵宾卡", "储值卡" };
        var cardTypeValid = validCardTypes.Contains(cardType);
        var remainDaysValid = remainDays > 0;
        var durationValid = expectedDuration > 20;

        var allConditionsMet = cardTypeValid && remainDaysValid && durationValid;
        allConditionsMet.Should().Be(shouldSatisfyRules);
    }

    [Fact]
    public void BackwardCompatibility_NonAjbMessages_ShouldStillWork()
    {
        // Arrange - 验证非ajb消息的向后兼容性
        var accumulator = new BusinessDataAccumulator(_mockLogger.Object);
        var nonAjbMessage = new EventMessage
        {
            Topic = "other/system/data",
            Payload = """
            {
                "CardType": "月租卡",
                "log_car_no": "粤B12345",
                "duration": 30
            }
            """,
            Timestamp = DateTime.UtcNow
        };

        // Act
        accumulator.UpdateMessage(nonAjbMessage);
        var result = accumulator.GetLatestData();

        // Assert - 非ajb消息应该继续使用原有的处理逻辑
        result.Should().ContainKey("CardType");
        result.Should().ContainKey("log_car_no");
        result.Should().ContainKey("duration");

        result["CardType"].Should().Be("月租卡");
        result["log_car_no"].Should().Be("粤B12345");
        result["duration"].Should().Be(30L); // JSON反序列化的数字类型
    }

    [Fact]
    public void AjbMessageProcessing_WithMissingFields_ShouldHandleGracefully()
    {
        // Arrange - 测试缺少某些字段的ajb消息
        var accumulator = new BusinessDataAccumulator(_mockLogger.Object);
        var message = new EventMessage
        {
            Topic = "ajb/101013/out/P002LfyBmOut/time_log",
            Payload = """
            {
                "response_payload": {
                    "data": {
                        "CarInfo": {
                            "CardType": "月租卡",
                            "CarNo": "粤B12345"
                        }
                    }
                }
            }
            """,
            Timestamp = DateTime.UtcNow
        };

        // Act
        accumulator.UpdateMessage(message);
        var result = accumulator.GetLatestData();

        // Assert - 应该至少提取到可用的字段
        result.Should().ContainKey("CardType");
        result.Should().ContainKey("log_car_no");
        result["CardType"].Should().Be("月租卡");
        result["log_car_no"].Should().Be("粤B12345");

        // 缺少的字段不应该导致异常
        result.Should().NotContainKey("log_remain_days");
        result.Should().NotContainKey("duration");
    }

    [Fact]
    public void AjbMessageProcessing_PerformanceTest_ShouldBeEfficient()
    {
        // Arrange
        var accumulator = new BusinessDataAccumulator(_mockLogger.Object);
        var message = new EventMessage
        {
            Topic = "ajb/101013/out/P002LfyBmOut/time_log",
            Payload = """
            {
                "response_payload": {
                    "data": {
                        "CarInfo": {
                            "CardType": "月租卡",
                            "CarNo": "粤B12345",
                            "RemainDays": 15
                        },
                        "Charge": {
                            "ParkTime": "1时30分"
                        }
                    }
                }
            }
            """,
            Timestamp = DateTime.UtcNow
        };

        var stopwatch = System.Diagnostics.Stopwatch.StartNew();

        // Act - 处理1000条消息
        for (int i = 0; i < 1000; i++)
        {
            accumulator.UpdateMessage(message);
            var result = accumulator.GetLatestData();
            result.Should().NotBeEmpty();
        }

        stopwatch.Stop();

        // Assert - 性能应该在合理范围内
        stopwatch.ElapsedMilliseconds.Should().BeLessThan(2000, "处理1000条AJB消息应该在2秒内完成");
    }
}
