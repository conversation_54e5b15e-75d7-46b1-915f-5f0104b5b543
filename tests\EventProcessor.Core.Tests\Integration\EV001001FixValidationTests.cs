using EventProcessor.Core.Engine;
using EventProcessor.Core.Models;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;
using FluentAssertions;

namespace EventProcessor.Core.Tests.Integration;

/// <summary>
/// EV001001修复验证集成测试
/// 验证报告 REPORT01-Rule-Engine-Fails-To-Parse-Upstream-Data.md 中的问题是否已解决
/// </summary>
public class EV001001FixValidationTests
{
    private readonly Mock<ILogger<BusinessDataAccumulator>> _mockLogger;
    private readonly Mock<ILogger<ConditionEvaluator>> _mockConditionLogger;

    public EV001001FixValidationTests()
    {
        _mockLogger = new Mock<ILogger<BusinessDataAccumulator>>();
        _mockConditionLogger = new Mock<ILogger<ConditionEvaluator>>();
    }

    [Fact]
    public void BusinessDataAccumulator_WithReportedAjbMessage_ShouldProcessSuccessfully()
    {
        // Arrange - 使用报告中导致失败的实际消息
        var accumulator = new BusinessDataAccumulator(_mockLogger.Object);
        var message = new EventMessage
        {
            Topic = "ajb/101013/out/P002LfyBmOut/time_log",
            Payload = """
            {
                "log_original_timestamp": "22:17:28:958",
                "log_event_type": "出场查询系统返回结果",
                "response_payload": {
                    "status": true,
                    "code": "0000",
                    "msg": "操作成功",
                    "data": {
                        "CarInfo": {
                            "CarNo": "粤AY8C52",
                            "CardType": "储值卡",
                            "Intime": "2025-08-04 22:14:06",
                            "Name": "蒋燕琼"
                        },
                        "Charge": {
                            "ParkTime": "0时3分",
                            "StartTime": "2025-08-04 22:14:06",
                            "EndTime": "2025-08-04 22:17:28"
                        }
                    }
                }
            }
            """,
            Timestamp = DateTime.UtcNow
        };

        // Act
        accumulator.UpdateMessage(message);
        var result = accumulator.GetLatestData();

        // Assert - 验证之前失败的字段现在都能正确获取
        result.Should().ContainKey("CardType", "现在应该能从嵌套路径提取CardType");
        result.Should().ContainKey("log_car_no", "现在应该能从嵌套路径提取车牌号");
        result.Should().ContainKey("duration", "现在应该能从ParkTime计算duration");

        result["CardType"].Should().Be("储值卡");
        result["log_car_no"].Should().Be("粤AY8C52");
        result["duration"].Should().Be(3); // "0时3分" = 3分钟
    }

    [Fact]
    public void ConditionEvaluator_WithProcessedAjbData_ShouldEvaluateBusinessRulesCorrectly()
    {
        // Arrange - 模拟处理后的ajb数据
        var evaluator = new ConditionEvaluator(_mockConditionLogger.Object);
        
        // 模拟报告中的业务规则条件
        var cardTypeCondition = new Condition
        {
            FieldName = "CardType",
            DataType = "string",
            Operator = "In",
            Value = "月租卡|万全卡|贵宾卡|储值卡"
        };

        var remainDaysCondition = new Condition
        {
            FieldName = "log_remain_days",
            DataType = "number",
            Operator = "GreaterThan",
            Value = "0"
        };

        var durationCondition = new Condition
        {
            FieldName = "duration",
            DataType = "number",
            Operator = "GreaterThan",
            Value = "20"
        };

        // 模拟经过AjbMessageAdapter处理后的数据
        var processedData = new Dictionary<string, object>
        {
            ["CardType"] = "储值卡",
            ["log_car_no"] = "粤AY8C52",
            ["log_remain_days"] = 15,
            ["duration"] = 25 // 假设停车25分钟，满足 > 20 的条件
        };

        // Act - 评估各个条件
        var cardTypeResult = evaluator.EvaluateCondition(cardTypeCondition, processedData);
        var remainDaysResult = evaluator.EvaluateCondition(remainDaysCondition, processedData);
        var durationResult = evaluator.EvaluateCondition(durationCondition, processedData);

        // Assert - 所有条件现在都应该能正确评估
        cardTypeResult.Should().BeTrue("储值卡应该在允许的卡类型列表中");
        remainDaysResult.Should().BeTrue("剩余天数15应该大于0");
        durationResult.Should().BeTrue("停车时长25分钟应该大于20分钟");
    }

    [Fact]
    public void EndToEndTest_ReportedFailureScenario_ShouldNowSucceed()
    {
        // Arrange - 完整的端到端测试，模拟报告中的失败场景
        var accumulator = new BusinessDataAccumulator(_mockLogger.Object);
        var evaluator = new ConditionEvaluator(_mockConditionLogger.Object);

        // 报告中导致失败的原始消息
        var ajbMessage = new EventMessage
        {
            Topic = "ajb/101013/out/P002LfyBmOut/time_log",
            Payload = """
            {
                "response_payload": {
                    "data": {
                        "CarInfo": {
                            "CarNo": "粤AY8C52",
                            "CardType": "储值卡",
                            "RemainDays": 5
                        },
                        "Charge": {
                            "ParkTime": "0时25分"
                        }
                    }
                }
            }
            """,
            Timestamp = DateTime.UtcNow
        };

        // 报告中的业务规则配置
        var businessRuleGroup = new BusinessRuleGroup
        {
            SourceTopic = "ajb/+/out/+/time_log",
            LogicOperator = "AND",
            Conditions = new[]
            {
                new Condition
                {
                    FieldName = "CardType",
                    DataType = "string",
                    Operator = "In",
                    Value = "月租卡|万全卡|贵宾卡|储值卡"
                },
                new Condition
                {
                    FieldName = "log_remain_days",
                    DataType = "number",
                    Operator = "GreaterThan",
                    Value = "0"
                },
                new Condition
                {
                    FieldName = "duration",
                    DataType = "number",
                    Operator = "GreaterThan",
                    Value = "20"
                }
            }
        };

        // Act - 模拟完整的处理流程
        
        // 1. 数据累积器处理消息
        accumulator.UpdateMessage(ajbMessage);
        var businessData = accumulator.GetLatestData();

        // 2. 规则引擎评估条件
        var conditionResults = new List<bool>();
        foreach (var condition in businessRuleGroup.Conditions)
        {
            var result = evaluator.EvaluateCondition(condition, businessData);
            conditionResults.Add(result);
        }

        // 3. 应用逻辑操作符
        var finalResult = businessRuleGroup.LogicOperator == "AND" 
            ? conditionResults.All(r => r) 
            : conditionResults.Any(r => r);

        // Assert - 验证整个流程现在能正常工作
        businessData.Should().ContainKey("CardType");
        businessData.Should().ContainKey("log_remain_days");
        businessData.Should().ContainKey("duration");

        conditionResults[0].Should().BeTrue("CardType条件应该满足");
        conditionResults[1].Should().BeTrue("log_remain_days条件应该满足");
        conditionResults[2].Should().BeTrue("duration条件应该满足");

        finalResult.Should().BeTrue("所有业务规则条件都满足，应该生成告警");
    }

    [Fact]
    public void BackwardCompatibility_NonAjbMessages_ShouldStillWork()
    {
        // Arrange - 验证非ajb消息的向后兼容性
        var accumulator = new BusinessDataAccumulator(_mockLogger.Object);
        var nonAjbMessage = new EventMessage
        {
            Topic = "other/system/data",
            Payload = """
            {
                "CardType": "月租卡",
                "log_car_no": "粤B12345",
                "duration": 30
            }
            """,
            Timestamp = DateTime.UtcNow
        };

        // Act
        accumulator.UpdateMessage(nonAjbMessage);
        var result = accumulator.GetLatestData();

        // Assert - 非ajb消息应该继续使用原有的处理逻辑
        result.Should().ContainKey("CardType");
        result.Should().ContainKey("log_car_no");
        result.Should().ContainKey("duration");

        result["CardType"].Should().Be("月租卡");
        result["log_car_no"].Should().Be("粤B12345");
        result["duration"].Should().Be(30L); // JSON反序列化的数字类型
    }

    [Theory]
    [InlineData("储值卡", 5, 25, true)]   // 报告中的成功场景
    [InlineData("月租卡", 10, 30, true)]  // 另一个成功场景
    [InlineData("临时卡", 5, 25, false)]  // 卡类型不匹配
    [InlineData("储值卡", 0, 25, false)]  // 剩余天数不满足
    [InlineData("储值卡", 5, 15, false)]  // 停车时长不满足
    public void BusinessRuleEvaluation_WithDifferentAjbScenarios_ShouldEvaluateCorrectly(
        string cardType, int remainDays, int durationMinutes, bool expectedResult)
    {
        // Arrange
        var accumulator = new BusinessDataAccumulator(_mockLogger.Object);
        var evaluator = new ConditionEvaluator(_mockConditionLogger.Object);

        var message = new EventMessage
        {
            Topic = "ajb/101013/out/P002LfyBmOut/time_log",
            Payload = $$"""
            {
                "response_payload": {
                    "data": {
                        "CarInfo": {
                            "CardType": "{{cardType}}",
                            "CarNo": "粤B12345",
                            "RemainDays": {{remainDays}}
                        },
                        "Charge": {
                            "ParkTime": "{{durationMinutes / 60}}时{{durationMinutes % 60}}分"
                        }
                    }
                }
            }
            """,
            Timestamp = DateTime.UtcNow
        };

        var conditions = new[]
        {
            new Condition
            {
                FieldName = "CardType",
                DataType = "string",
                Operator = "In",
                Value = "月租卡|万全卡|贵宾卡|储值卡"
            },
            new Condition
            {
                FieldName = "log_remain_days",
                DataType = "number",
                Operator = "GreaterThan",
                Value = "0"
            },
            new Condition
            {
                FieldName = "duration",
                DataType = "number",
                Operator = "GreaterThan",
                Value = "20"
            }
        };

        // Act
        accumulator.UpdateMessage(message);
        var data = accumulator.GetLatestData();

        var results = conditions.Select(c => evaluator.EvaluateCondition(c, data)).ToArray();
        var finalResult = results.All(r => r);

        // Assert
        finalResult.Should().Be(expectedResult);
    }
}
