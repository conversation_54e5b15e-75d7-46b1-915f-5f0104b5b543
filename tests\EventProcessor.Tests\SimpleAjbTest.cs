using EventProcessor.Core.Engine;
using EventProcessor.Core.Models;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;

namespace EventProcessor.Tests;

/// <summary>
/// 简单的AJB消息修复验证测试
/// </summary>
public class SimpleAjbTest
{
    [Fact]
    public void BusinessDataAccumulator_WithAjbMessage_ShouldExtractNestedFields()
    {
        // Arrange
        var mockLogger = new Mock<ILogger<BusinessDataAccumulator>>();
        var accumulator = new BusinessDataAccumulator(mockLogger.Object);
        
        var message = new EventMessage
        {
            Topic = "ajb/101013/out/P002LfyBmOut/time_log",
            Payload = """
            {
                "response_payload": {
                    "data": {
                        "CarInfo": {
                            "CardType": "储值卡",
                            "CarNo": "粤AY8C52",
                            "RemainDays": 5
                        },
                        "Charge": {
                            "ParkTime": "0时25分"
                        }
                    }
                }
            }
            """,
            Timestamp = DateTime.UtcNow
        };

        // Act
        accumulator.UpdateMessage(message);
        var result = accumulator.GetLatestData();

        // Assert
        Assert.True(result.ContainsKey("CardType"));
        Assert.True(result.ContainsKey("log_car_no"));
        Assert.True(result.ContainsKey("duration"));
        
        Assert.Equal("储值卡", result["CardType"]);
        Assert.Equal("粤AY8C52", result["log_car_no"]);
        Assert.Equal(25, result["duration"]); // 0时25分 = 25分钟
    }

    [Fact]
    public void BusinessDataAccumulator_WithNonAjbMessage_ShouldUseOriginalLogic()
    {
        // Arrange
        var mockLogger = new Mock<ILogger<BusinessDataAccumulator>>();
        var accumulator = new BusinessDataAccumulator(mockLogger.Object);
        
        var message = new EventMessage
        {
            Topic = "other/system/data",
            Payload = """
            {
                "CardType": "月租卡",
                "log_car_no": "粤B12345",
                "duration": 30
            }
            """,
            Timestamp = DateTime.UtcNow
        };

        // Act
        accumulator.UpdateMessage(message);
        var result = accumulator.GetLatestData();

        // Assert
        Assert.True(result.ContainsKey("CardType"));
        Assert.True(result.ContainsKey("log_car_no"));
        Assert.True(result.ContainsKey("duration"));
        
        Assert.Equal("月租卡", result["CardType"]);
        Assert.Equal("粤B12345", result["log_car_no"]);
        Assert.Equal(30L, result["duration"]); // JSON反序列化的数字类型
    }
}
