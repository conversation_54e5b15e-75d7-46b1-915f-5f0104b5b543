# utils/EV001001Replayer.py
import argparse
import os
import sys
import time
import logging
import json
from datetime import datetime, timedelta
import yaml
try:
    import paho.mqtt.client as mqtt
except ImportError:
    print("Error: The 'paho-mqtt' library is not installed.", file=sys.stderr)
    print("Please install it using: pip install paho-mqtt", file=sys.stderr)
    sys.exit(1)

# --- MQTT Configuration ---
MQTT_CONFIG = {
    'host': 'mq.bangdouni.com',
    'port': 1883,
    'client_id': 'EV001001Replayer',
    'username': 'bdn_ai_process',
    'password': 'Bdn@2024'
}

def setup_logging(scene_path):
    """Configures logging to a dedicated file in the scene directory."""
    log_file_path = os.path.join(scene_path, "replay.log")
    
    # Overwrite the log file for each new run
    if os.path.exists(log_file_path):
        os.remove(log_file_path)
        
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s [%(levelname)s] - %(message)s',
        handlers=[
            logging.FileHandler(log_file_path),
            logging.StreamHandler(sys.stdout) # Also print to console
        ]
    )

def on_connect(client, userdata, flags, rc):
    if rc == 0:
        logging.info("MQTT Broker connected successfully.")
    else:
        logging.error(f"Failed to connect to MQTT Broker, return code {rc}")
        sys.exit(1)

def load_scene(scene_yaml_path):
    """Loads the scene.yaml file."""
    logging.info(f"Loading scene from: {scene_yaml_path}")
    if not os.path.exists(scene_yaml_path):
        logging.error(f"Error: Scene file not found at '{scene_yaml_path}'")
        sys.exit(1)
    
    with open(scene_yaml_path, 'r', encoding='utf-8') as f:
        return yaml.safe_load(f)

def replay_scene(scene_data, non_interactive=False):
    """Connects to MQTT and replays the event sequence."""
    client = mqtt.Client(MQTT_CONFIG['client_id'])
    client.username_pw_set(MQTT_CONFIG['username'], MQTT_CONFIG['password'])
    client.on_connect = on_connect
    
    logging.info(f"Connecting to MQTT Broker at {MQTT_CONFIG['host']}:{MQTT_CONFIG['port']}...")
    try:
        client.connect(MQTT_CONFIG['host'], MQTT_CONFIG['port'], 60)
        client.loop_start()
    except Exception as e:
        logging.error(f"Error connecting to MQTT: {e}")
        sys.exit(1)

    metadata = scene_data['metadata']
    logging.info("\n--- Scene Details ---\n" 
                 f"  ID:          {metadata['scene_id']}\n" 
                 f"  Description: {metadata['description']}\n" 
                 f"  Duration:    {metadata['original_duration_sec']:.3f} seconds\n" 
                 "-----------------------")
    
    if not non_interactive:
        try:
            input("Press Enter to start replay...")
        except KeyboardInterrupt:
            logging.info("\nReplay cancelled by user.")
            client.loop_stop()
            sys.exit(0)

    logging.info("Starting replay...")
    replay_start_time = datetime.now()
    
    for event in scene_data['events']:
        offset_ms = event['offset_ms']
        scheduled_time = replay_start_time + timedelta(milliseconds=offset_ms)
        
        sleep_duration = (scheduled_time - datetime.now()).total_seconds()
        if sleep_duration > 0:
            time.sleep(sleep_duration)
            
        topic = event['topic']
        payload = event['payload']
        
        # Log the action
        current_offset = (datetime.now() - replay_start_time).total_seconds() * 1000
        logging.info(f"--> [Offset: {int(current_offset)}ms] Publishing to topic: {topic}")
        try:
            payload_json = json.loads(payload)
            pretty_payload = json.dumps(payload_json, indent=2, ensure_ascii=False)
            logging.info(f"Payload:\n{pretty_payload}")
        except (json.JSONDecodeError, TypeError):
            logging.info(f"Payload (raw): {payload}")

        # Publish the message
        client.publish(topic, payload, qos=1)

    logging.info("Scene replay finished.")
    client.loop_stop()

def main():
    parser = argparse.ArgumentParser(description="EV001001 Scene Replay Tool")
    parser.add_argument('--scene', required=True, help="The Scene ID to replay (e.g., EV001001-20250805075258707)")
    parser.add_argument('--play', required=True, help="The YAML script file to play from the scene directory (e.g., EV001001.yaml)")
    parser.add_argument('-y', '--non-interactive', action='store_true', help="Run without waiting for user confirmation")
    args = parser.parse_args()

    scene_dir = os.path.join("utils", "scenes", args.scene)
    scene_yaml_path = os.path.join(scene_dir, args.play)

    if not os.path.isdir(scene_dir):
        print(f"Error: Scene directory not found at '{scene_dir}'", file=sys.stderr) # Can't log yet
        sys.exit(1)

    # Setup logging inside the scene directory
    setup_logging(scene_dir)

    scene_data = load_scene(scene_yaml_path)
    replay_scene(scene_data, args.non_interactive)

if __name__ == "__main__":
    main()
