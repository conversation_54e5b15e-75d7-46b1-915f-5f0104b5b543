
# utils/EV001001YAMLGenerator.py
import argparse
import csv
import json
import os
from datetime import datetime
import yaml

def generate_scene_yaml(scene_id):
    """
    Reads the raw_events.csv and generates a scene.yaml specific to EV001001 logic.
    """
    scenes_dir = os.path.join("utils", "scenes")
    scene_path = os.path.join(scenes_dir, scene_id)
    raw_csv_path = os.path.join(scene_path, "raw_events.csv")

    if not os.path.exists(raw_csv_path):
        print(f"Error: 'raw_events.csv' not found in '{scene_path}'", file=sys.stderr)
        sys.exit(1)

    print(f"Generating scene.yaml for {scene_id}...")

    with open(raw_csv_path, 'r', encoding='utf-8') as f:
        reader = csv.DictReader(f)
        messages = list(reader)

    if not messages:
        print("Error: raw_events.csv is empty.", file=sys.stderr)
        sys.exit(1)

    # The first message's timestamp is our baseline
    start_time_str = messages[0]['received_timestamp']
    start_time = datetime.fromisoformat(start_time_str)
    end_time_str = messages[-1]['received_timestamp']
    end_time = datetime.fromisoformat(end_time_str)

    scene_data = {
        'metadata': {
            'scene_id': scene_id,
            'event_id': "EV001001",
            'description': f"Reproduction of EV001001 from {start_time.isoformat()}",
            'capture_time': datetime.now().isoformat(),
            'original_start_time': start_time.isoformat(),
            'original_end_time': end_time.isoformat(),
            'original_duration_sec': (end_time - start_time).total_seconds()
        },
        'events': []
    }

    for msg in messages:
        current_time = datetime.fromisoformat(msg['received_timestamp'])
        offset = current_time - start_time
        
        # EV001001 specific logic: We don't need to modify any timestamps inside the payload
        # as this event logic does not depend on them. We just pass the payload through.
        
        scene_data['events'].append({
            'offset_ms': int(offset.total_seconds() * 1000),
            'topic': msg['topic'],
            'payload': msg['payload']
        })

    # Write scene.yaml
    scene_yaml_path = os.path.join(scene_path, 'EV001001.yaml')
    with open(scene_yaml_path, 'w', encoding='utf-8') as f:
        yaml.dump(scene_data, f, allow_unicode=True, sort_keys=False)
    
    print(f"Successfully created scene file: {scene_yaml_path}")

def main():
    parser = argparse.ArgumentParser(description="EV001001 Scene YAML Generator")
    parser.add_argument('--scene', required=True, help="The Scene ID to process (e.g., EV001001-20250805075258707)")
    args = parser.parse_args()
    
    generate_scene_yaml(args.scene)

if __name__ == "__main__":
    main()
