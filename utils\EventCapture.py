
# utils/EventCapture.py
import argparse
import csv
import os
import sys
from datetime import datetime, timedelta
import yaml
try:
    import mysql.connector
except ImportError:
    print("Error: The 'mysql-connector-python' library is not installed.", file=sys.stderr)
    sys.exit(1)

# --- Database Configuration ---
DB_CONFIG = {
    'user': 'bdn',
    'password': 'Bdn@2024',
    'host': '***********',
    'port': 3307,
    'database': 'mq-log-db',
    'charset': 'utf8mb4',
    'collation': 'utf8mb4_general_ci'
}

def load_ep_config(config_path):
    """Loads the EP configuration YAML."""
    print(f"Loading configuration from: {config_path}")
    if not os.path.exists(config_path):
        print(f"Error: Config file not found at '{config_path}'", file=sys.stderr)
        sys.exit(1)
    with open(config_path, 'r', encoding='utf-8') as f:
        return yaml.safe_load(f)

def find_event_window(cursor, config, trigger_time_str):
    """Finds the start and end timestamps of the event."""
    print("Searching for event window...")
    trigger_time = datetime.strptime(trigger_time_str, '%Y-%m-%d %H:%M:%S.%f')
    
    ds_config = config['EventProcessor']['DeviceSignal']
    trigger_topic = ds_config['Topics'][0]
    trigger_field = ds_config['TriggerField']
    trigger_value = ds_config['TriggerValues']['true']
    contact_value = ds_config['TriggerValues']['false']

    # Find the precise trigger event
    query_trigger = """
        SELECT received_timestamp FROM mqtt_messages 
        WHERE topic = %s AND received_timestamp BETWEEN %s AND %s 
        AND JSON_EXTRACT(payload, %s) = %s
        ORDER BY received_timestamp ASC LIMIT 1
    """
    search_start = trigger_time - timedelta(seconds=2)
    search_end = trigger_time + timedelta(seconds=2)
    cursor.execute(query_trigger, (trigger_topic, search_start, search_end, f'$.{trigger_field}.value', str(trigger_value)))
    trigger_event = cursor.fetchone()

    if not trigger_event:
        print(f"Error: Could not find a matching trigger event around '{trigger_time_str}'", file=sys.stderr)
        return None, None

    start_time = trigger_event['received_timestamp']
    print(f"Found trigger event (start_time): {start_time.strftime('%Y-%m-%d %H:%M:%S.%f')}")

    # Find the subsequent contact event
    query_contact = """
        SELECT received_timestamp FROM mqtt_messages 
        WHERE topic = %s AND received_timestamp > %s
        AND JSON_EXTRACT(payload, %s) = %s
        ORDER BY received_timestamp ASC LIMIT 1
    """
    cursor.execute(query_contact, (trigger_topic, start_time, f'$.{trigger_field}.value', str(contact_value)))
    contact_event = cursor.fetchone()

    if not contact_event:
        print(f"Warning: No contact event found. Using a 60-second window.", file=sys.stderr)
        end_time = start_time + timedelta(seconds=60)
    else:
        end_time = contact_event['received_timestamp']
        print(f"Found contact event (end_time): {end_time.strftime('%Y-%m-%d %H:%M:%S.%f')}")

    return start_time, end_time

def export_to_csv(cursor, config, start_time, end_time):
    """Fetches all related messages and exports them to a raw CSV file."""
    print("Fetching related data for export...")
    topics = set(config['EventProcessor']['DeviceSignal']['Topics'])
    for rule in config['EventProcessor']['RuleConfiguration']['BusinessRules']:
        topics.add(rule['SourceTopic'])
    
    query = "SELECT * FROM mqtt_messages WHERE topic IN ({}) AND received_timestamp BETWEEN %s AND %s ORDER BY received_timestamp ASC".format(
        ', '.join(['%s'] * len(topics))
    )
    params = list(topics) + [start_time, end_time]
    cursor.execute(query, params)
    messages = cursor.fetchall()

    if not messages:
        print("No messages found in the specified window to export.")
        return

    event_id = config['EventProcessor']['EventId']
    scene_id = f"{event_id}-{start_time.strftime('%Y%m%d%H%M%S%f')[:-3]}"
    
    # Create scenes directory
    scenes_dir = os.path.join("utils", "scenes")
    if not os.path.exists(scenes_dir):
        os.makedirs(scenes_dir)
        
    # Create scene-specific directory
    scene_path = os.path.join(scenes_dir, scene_id)
    if not os.path.exists(scene_path):
        os.makedirs(scene_path)

    output_filename = os.path.join(scene_path, "raw_events.csv")
    
    with open(output_filename, 'w', newline='', encoding='utf-8') as f:
        writer = csv.writer(f)
        writer.writerow(cursor.column_names)
        for row in messages:
            writer.writerow(row.values())
            
    print(f"Successfully exported {len(messages)} records to: {output_filename}")

def main():
    parser = argparse.ArgumentParser(description="Universal Event Capture Tool")
    parser.add_argument('--config', required=True, help="Path to the EP config file (e.g., EV001001.yaml)")
    parser.add_argument('--trigger-time', required=True, help="Precise trigger timestamp (e.g., '2025-08-05 07:52:58.707')")
    args = parser.parse_args()

    config = load_ep_config(args.config)
    
    try:
        cnx = mysql.connector.connect(**DB_CONFIG)
        cursor = cnx.cursor(dictionary=True)
        
        start_time, end_time = find_event_window(cursor, config, args.trigger_time)
        if not start_time:
            sys.exit(1)
            
        export_to_csv(cursor, config, start_time, end_time)

    except mysql.connector.Error as err:
        print(f"Database Error: {err}", file=sys.stderr)
        sys.exit(1)
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'cnx' in locals() and cnx.is_connected():
            cnx.close()

if __name__ == "__main__":
    main()
