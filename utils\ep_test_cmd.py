#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
EP Test 命令行版本
简化版自动化设备事件模拟测试功能

使用示例:
python ep_test_cmd.py -i x:\kdc.jpg -d Y:\LFY-IN01 -p P001LfyBmIn -m device/BDN8888/event -t 16 --mqtt-host mq.bangdouni.com --mqtt-port 1883

排除测试示例:
python ep_test_cmd.py -i x:\kdc.jpg -d Y:\LFY-IN01 -p P001LfyBmIn -m device/BDN8888/event -t 16 --exclude EV001008

时序说明:
1. I1=0 (车辆进入检测区域)
2. 等待1秒 (车牌识别系统处理时间)
3. 发送ajb消息 (车牌识别结果，说明是正常车辆而非三轮车)
4. 等待剩余时间
5. I1=1 (车辆离开检测区域)

"""

import os
import sys
import json
import time
import shutil
import random
import string
import argparse
import logging
import threading
import socket
import errno
from pathlib import Path
from datetime import datetime
from typing import Dict, Any, Optional, List
from json import JSONDecodeError

# MQTT客户端
try:
    import paho.mqtt.client as mqtt
except ImportError:
    print("错误: 缺少paho-mqtt依赖，请运行: pip install paho-mqtt")
    sys.exit(1)

# 加载环境变量
try:
    from dotenv import load_dotenv
except ImportError:
    print("警告: 缺少python-dotenv依赖，无法加载.env文件")
    load_dotenv = None

# 配置日志
def setup_logging(log_level: str = "INFO") -> None:
    """设置日志输出"""
    logging.basicConfig(
        level=getattr(logging, log_level.upper()),
        format='%(asctime)s - %(levelname)s - %(message)s - %(name)s:%(lineno)d',
        datefmt='%H:%M:%S'
    )

setup_logging()
logger = logging.getLogger(__name__)


class MQTTConnectionState:
    """MQTT连接状态"""
    DISCONNECTED = "disconnected"
    CONNECTING = "connecting"
    CONNECTED = "connected"
    ERROR = "error"


class ExcludeInfoGenerator:
    """排除信息生成器"""
    
    def __init__(self, env_file: str = ".env"):
        """初始化排除信息生成器"""
        self.env_file = env_file
        self.env_config = {}
        self._load_env_config()
    
    def _load_env_config(self):
        """加载环境变量配置"""
        try:
            if load_dotenv and os.path.exists(self.env_file):
                load_dotenv(self.env_file)
                logger.info(f"已加载环境变量文件: {self.env_file}")
            
            # 加载所有环境变量
            self.env_config = dict(os.environ)
            
        except Exception as e:
            logger.warning(f"加载环境变量失败: {e}")
            self.env_config = {}
    
    def get_exclude_config(self, event_id: str) -> Optional[Dict[str, Any]]:
        """获取指定事件的排除配置"""
        try:
            prefix = f"EVENT_ID_{event_id}_EP_PV_EXCLUDE_"
            
            # 检查是否有排除配置
            source_type = self.env_config.get(f"{prefix}INFO_SOURCE_TYPE")
            if not source_type:
                logger.warning(f"事件 {event_id} 没有配置排除信息")
                return None
            
            # 获取配置项
            source = self.env_config.get(f"{prefix}INFO_SOURCE")
            fields_str = self.env_config.get(f"{prefix}INFO_FIELDS")
            logic = self.env_config.get(f"{prefix}LOGIC", "ANY")
            
            if not source or not fields_str:
                logger.warning(f"事件 {event_id} 排除配置不完整")
                return None
            
            # 解析字段配置
            try:
                fields = json.loads(fields_str)
            except json.JSONDecodeError as e:
                logger.error(f"解析排除字段配置失败: {e}")
                return None
            
            return {
                "source_type": source_type,
                "source": source,
                "fields": fields,
                "logic": logic
            }
            
        except Exception as e:
            logger.error(f"获取排除配置失败: {e}")
            return None
    
    def generate_exclude_message(self, event_id: str) -> Optional[Dict[str, Any]]:
        """生成排除信息 MQTT 消息"""
        try:
            config = self.get_exclude_config(event_id)
            if not config:
                return None
            
            # 构建排除消息
            exclude_message = {}
            
            # 根据字段配置生成消息内容
            for field_config in config["fields"]:
                field_name = field_config.get("field_name")
                field_keyword = field_config.get("field_keyword") 
                condition = field_config.get("exclude_condition", "exists")
                
                if not field_keyword:
                    continue
                
                # 根据条件类型生成不同的值
                if condition == "exists":
                    # 车牌识别场景：提供一个真实的车牌号码
                    if field_keyword == "log_car_no":
                        exclude_message[field_keyword] = "粤A67UJ5"
                    else:
                        exclude_message[field_keyword] = "test_value"
                elif condition == "equals":
                    exclude_message[field_keyword] = field_config.get("exclude_value", "test_value")
                elif condition == "contains":
                    exclude_message[field_keyword] = f"包含{field_config.get('exclude_value', 'test_value')}的文本"
                elif condition == "regex":
                    exclude_message[field_keyword] = "匹配正则表达式的文本"
                else:
                    exclude_message[field_keyword] = "test_value"
            
            # 添加车牌识别系统的真实字段格式
            current_time = datetime.now()
            exclude_message["log_original_timestamp"] = current_time.strftime("%H:%M:%S:%f")[:-3]  # HH:MM:SS:mmm
            exclude_message["log_event_type"] = "车牌识别"
            exclude_message["log_plate_color"] = "蓝色"
            exclude_message["log_recognition_time"] = current_time.strftime("%Y-%m-%d %H:%M:%S")
            
            logger.info(f"生成排除信息消息: {exclude_message}")
            return exclude_message
            
        except Exception as e:
            logger.error(f"生成排除信息消息失败: {e}")
            return None
    
    def get_exclude_topic(self, event_id: str) -> Optional[str]:
        """获取排除信息的 MQTT 主题"""
        try:
            config = self.get_exclude_config(event_id)
            if not config:
                return None
            
            return config["source"]
            
        except Exception as e:
            logger.error(f"获取排除信息主题失败: {e}")
            return None


class SimpleMQTTManager:
    """简化版MQTT管理器"""
    
    def __init__(self):
        self.client = None
        self.connection_state = MQTTConnectionState.DISCONNECTED
        self.connection_event = threading.Event()
        self.connection_result = False
        
        # 连接配置
        self.broker_host = "mq.bangdouni.com"
        self.broker_port = 1883
        self.username = "bdn_ai_process"
        self.password = "Bdn@2024"
        # 8位随机字符作为clientid的后缀
        random_suffix = ''.join(random.choices(string.ascii_letters + string.digits, k=8))
        self.client_id = f"ep_test_tool_{random_suffix}"
        
        logger.debug("简化版MQTT管理器初始化完成")
    
    def set_connection_config(self, host: str="mq.bangdouni.com", port: int = 1883, 
                            username: str = "bdn_ai_process", password: str = "Bdn@2024",
                            client_id_prefix: str = "ep_test_cmd") -> None:
        """设置连接配置"""
        self.broker_host = host
        self.broker_port = port
        self.username = username
        self.password = password
        
        # 生成随机客户端ID
        random_suffix = ''.join(random.choices(string.ascii_letters + string.digits, k=8))
        self.client_id = f"{client_id_prefix}_{random_suffix}"
        
        logger.info(f"MQTT连接配置已设置: {host}:{port}, 客户端ID: {self.client_id}")
    
    def _on_connect(self, client, userdata, flags, rc):
        """连接回调"""
        if rc == 0:
            logger.info("MQTT连接成功")
            self.connection_state = MQTTConnectionState.CONNECTED
            self.connection_result = True
        else:
            logger.error(f"MQTT连接失败，返回码: {rc}")
            self.connection_state = MQTTConnectionState.ERROR
            self.connection_result = False
        
        self.connection_event.set()
    
    def _on_disconnect(self, client, userdata, rc):
        """断开连接回调"""
        if rc != 0:
            logger.warning(f"MQTT意外断开连接，错误代码: {rc}")
        else:
            logger.info("MQTT连接已断开")
        self.connection_state = MQTTConnectionState.DISCONNECTED
    
    def _on_publish(self, client, userdata, mid):
        """发布回调"""
        logger.debug(f"消息发布成功: mid={mid}")
    
    def connect(self, timeout: int = 10) -> bool:
        """连接MQTT服务器"""
        try:
            if not self.broker_host:
                logger.error("MQTT服务器地址未设置")
                return False

            logger.info(f"正在连接MQTT服务器: {self.broker_host}:{self.broker_port}")

            # 创建客户端
            self.client = mqtt.Client(client_id=self.client_id)
            
            # 设置回调
            self.client.on_connect = self._on_connect
            self.client.on_disconnect = self._on_disconnect
            self.client.on_publish = self._on_publish

            # 设置用户名密码
            if self.username:
                self.client.username_pw_set(self.username, self.password)
                logger.debug(f"MQTT认证信息已设置，用户名: {self.username}")

            # 重置连接事件
            self.connection_event.clear()
            self.connection_result = False
            self.connection_state = MQTTConnectionState.CONNECTING

            # 连接服务器
            self.client.connect_async(self.broker_host, self.broker_port, 60)
            self.client.loop_start()

            # 等待连接结果
            if self.connection_event.wait(timeout):
                if self.connection_result:
                    logger.info("MQTT连接建立成功")
                    return True
                else:
                    logger.error("MQTT连接失败，服务器返回错误")
                    return False
            else:
                logger.error(f"MQTT连接超时（{timeout}秒）")
                self.connection_state = MQTTConnectionState.ERROR
                return False

        except Exception as e:
            logger.error(f"MQTT连接过程异常: {e}")
            self.connection_state = MQTTConnectionState.ERROR
            return False
    
    def disconnect(self) -> None:
        """断开MQTT连接"""
        try:
            if self.client:
                logger.info("正在断开MQTT连接")
                self.client.loop_stop()
                self.client.disconnect()
                self.client = None
                self.connection_state = MQTTConnectionState.DISCONNECTED
                logger.info("MQTT连接已断开")
        except Exception as e:
            logger.error(f"断开MQTT连接过程异常: {e}")
            self.connection_state = MQTTConnectionState.DISCONNECTED
            self.client = None
    
    def publish(self, topic: str, message: Dict[str, Any], qos: int = 0, retain: bool = False) -> bool:
        """发布MQTT消息"""
        try:
            if self.connection_state != MQTTConnectionState.CONNECTED:
                logger.error("MQTT未连接，无法发布消息")
                return False

            if not self.client:
                logger.error("MQTT客户端对象不存在")
                return False

            # 转换为JSON字符串
            try:
                message_json = json.dumps(message, ensure_ascii=False)
            except (TypeError, ValueError) as e:
                logger.error(f"消息JSON序列化失败: {e}")
                return False

            # 发布消息
            result = self.client.publish(topic, message_json, qos, retain)
            
            if result.rc == mqtt.MQTT_ERR_SUCCESS:
                logger.info(f"MQTT消息发布成功: {topic}")
                logger.debug(f"消息内容: {message_json}")
                return True
            else:
                logger.error(f"MQTT消息发布失败，错误码: {result.rc}")
                return False

        except Exception as e:
            logger.error(f"发布MQTT消息异常: {e}")
            return False


class SimpleDeviceEventSimulator:
    """简化版设备事件模拟器"""
    
    def __init__(self, env_file: str = ".env"):
        self.mqtt_manager = SimpleMQTTManager()
        self.config = {}
        self.exclude_generator = ExcludeInfoGenerator(env_file)
        
        logger.debug("简化版设备事件模拟器初始化完成")
    
    def set_mqtt_config(self, host: str, port: int, username: str, password: str) -> None:
        """设置MQTT配置"""
        self.config = {
            'MQTT_BROKER_HOST': host,
            'MQTT_BROKER_PORT': str(port),
            'MQTT_USERNAME': username,
            'MQTT_PASSWORD': password
        }
        logger.info(f"MQTT配置已设置: {host}:{port}")
    
    def initialize_mqtt(self) -> bool:
        """初始化MQTT连接"""
        try:
            # 从配置中获取MQTT参数
            host = self.config.get('MQTT_BROKER_HOST', '')
            port = int(self.config.get('MQTT_BROKER_PORT', '1883'))
            username = self.config.get('MQTT_USERNAME', '')
            password = self.config.get('MQTT_PASSWORD', '')
            
            if not host:
                logger.error("配置文件中缺少MQTT_BROKER_HOST")
                return False
            
            # 设置连接配置
            self.mqtt_manager.set_connection_config(host, port, username, password)
            
            # 建立连接
            return self.mqtt_manager.connect()
            
        except Exception as e:
            logger.error(f"初始化MQTT连接失败: {e}")
            return False
    
    def run_test_cycle(self, image_path: str, device_dir: str, position: str, 
                      mqtt_topic: str, timeout: int, exclude_event_id: Optional[str] = None) -> bool:
        """运行测试循环"""
        try:
            # 1. 复制图片到设备目录
            if not self._copy_image_to_device(image_path, device_dir, position):
                return False
            
            # 2. 发送第一条消息 (I1=0) - 车辆进入检测区域
            timestamp = int(time.time())
            message1 = {
                "I1": {
                    "value": "0",
                    "timestamp": timestamp
                }
            }
            
            logger.info("发送第一条消息 (I1=0)")
            if not self.mqtt_manager.publish(mqtt_topic, message1):
                logger.error("发送第一条消息失败")
                return False
            
            # 3. 等待1秒，模拟车牌识别系统处理时间
            logger.info("等待1秒，模拟车牌识别系统处理时间...")
            time.sleep(1)
            
            # 4. 发送排除信息消息（如果指定了事件ID）- 车牌识别系统识别到车牌
            if exclude_event_id:
                logger.info("发送车牌识别信息（ajb系统消息）...")
                if not self._send_exclude_info_message(exclude_event_id):
                    logger.warning("发送排除信息消息失败，但继续测试流程")
            
            # 5. 等待剩余时间
            remaining_time = timeout - 1
            if remaining_time > 0:
                logger.info(f"等待剩余 {remaining_time} 秒...")
                time.sleep(remaining_time)
            
            # 6. 发送第二条消息 (I1=1) - 车辆离开检测区域
            timestamp = int(time.time())
            message2 = {
                "I1": {
                    "value": "1",
                    "timestamp": timestamp
                }
            }
            
            logger.info("发送第二条消息 (I1=1)")
            if not self.mqtt_manager.publish(mqtt_topic, message2):
                logger.error("发送第二条消息失败")
                return False
            
            logger.info("测试循环完成")
            return True
            
        except Exception as e:
            logger.error(f"运行测试循环异常: {e}")
            return False
    
    def _send_exclude_info_message(self, event_id: str) -> bool:
        """发送排除信息消息"""
        try:
            logger.info(f"正在为事件 {event_id} 生成排除信息消息...")
            
            # 获取排除信息主题
            exclude_topic = self.exclude_generator.get_exclude_topic(event_id)
            if not exclude_topic:
                logger.error(f"无法获取事件 {event_id} 的排除信息主题")
                return False
            
            # 生成排除信息消息
            exclude_message = self.exclude_generator.generate_exclude_message(event_id)
            if not exclude_message:
                logger.error(f"无法生成事件 {event_id} 的排除信息消息")
                return False
            
            # 发送排除信息消息
            logger.info(f"发送排除信息消息到主题: {exclude_topic}")
            if not self.mqtt_manager.publish(exclude_topic, exclude_message):
                logger.error("发送排除信息消息失败")
                return False
            
            logger.info("车牌识别信息发送成功（模拟ajb系统）")
            
            # 等待短暂时间，确保消息被Event Processor处理
            time.sleep(0.5)
            
            return True
            
        except Exception as e:
            logger.error(f"发送排除信息消息异常: {e}")
            return False
    
    def _copy_image_to_device(self, image_path: str, device_dir: str, position: str) -> bool:
        """复制图片到设备目录"""
        try:
            # 创建目标目录（按日期）
            str_today = datetime.now().strftime('%Y_%m_%d')
            str_today_dir = f"{str_today}-{str_today}"
            target_dir = Path(device_dir) / str_today_dir
            target_dir.mkdir(parents=True, exist_ok=True)
            
            # 生成目标文件名 - 按AI队列要求的格式: {comm_id}_{position_id}_{captured_datetime}_{captured_timestamp}.jpg
            now = datetime.now()
            captured_datetime = now.strftime('%Y%m%d%H%M%S%f')[:17]  # 17位时间戳 (YYYYMMDDHHMMSSfff)
            captured_timestamp = str(int(now.timestamp() * 1000))  # 13位毫秒时间戳
            
            # 使用固定值作为comm_id
            comm_id = "BDN8888"
            
            image_ext = Path(image_path).suffix
            target_filename = f"{comm_id}_{position}_{captured_datetime}_{captured_timestamp}{image_ext}"
            target_path = target_dir / target_filename
            
            # 复制文件
            shutil.copy2(image_path, target_path)
            
            logger.info(f"图片已复制到: {target_path}")
            return True
            
        except Exception as e:
            logger.error(f"复制图片失败: {e}")
            return False
    
    def cleanup(self) -> bool:
        """清理资源"""
        try:
            self.mqtt_manager.disconnect()
            logger.info("资源清理完成")
            return True
        except Exception as e:
            logger.error(f"清理资源异常: {e}")
            return False


def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(
        description="EP Test 命令行版本 - 简化版自动化设备事件模拟测试",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  python ep_test_cmd.py -i x:\\kdc.jpg -d Y:\\LFY-IN01 -p P001LfyBmIn \\
                        -m device/BDN8888/event -t 16 \\
                        --mqtt-host mq.bangdouni.com --mqtt-port 1883 \\
                        --mqtt-username bdn_ai_process --mqtt-password Bdn@2024

排除测试示例:
  python ep_test_cmd.py -i x:\\kdc.jpg -d Y:\\LFY-IN01 -p P001LfyBmIn \\
                        -m device/BDN8888/event -t 16 --exclude EV001008
        """
    )

    parser.add_argument('-i', '--image', required=True,
                       help='指定测试图片文件路径，例如：x:\\kdc.jpg')

    parser.add_argument('-d', '--device-dir', required=True,
                       help='指定设备目录路径，例如：Y:\\LFY-IN01')

    parser.add_argument('-p', '--position', required=True,
                       help='指定位置ID，例如：P001LfyBmIn')

    parser.add_argument('-m', '--mqtt-topic', required=True,
                       help='指定MQTT主题，例如：device/BDN8888/event')

    parser.add_argument('-t', '--timeout', type=int, required=True,
                       help='指定两次消息发送间隔秒数，例如：16')

    parser.add_argument('--mqtt-host', default='mq.bangdouni.com',
                       help='MQTT服务器地址 (默认: mq.bangdouni.com)')

    parser.add_argument('--mqtt-port', type=int, default=1883,
                       help='MQTT服务器端口 (默认: 1883)')

    parser.add_argument('--mqtt-username', default='bdn_ai_process',
                       help='MQTT用户名 (默认: bdn_ai_process)')

    parser.add_argument('--mqtt-password', default='Bdn@2024',
                       help='MQTT密码 (默认: Bdn@2024)')

    parser.add_argument('-l', '--log-level',
                       choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
                       default='DEBUG',
                       help='设置日志级别 (默认: DEBUG)')

    parser.add_argument('--cycles', type=int, default=1,
                       help='指定测试循环次数 (默认: 1)')

    parser.add_argument('--exclude', type=str, 
                       help='指定要生成排除信息的事件ID (例如: EV001008)')

    parser.add_argument('--env-file', default='.env',
                       help='指定环境变量文件路径 (默认: .env)')

    return parser.parse_args()


def validate_arguments(args):
    """验证命令行参数"""
    errors = []

    # 验证图片文件
    if not Path(args.image).exists():
        errors.append(f"图片文件不存在: {args.image}")
    elif not Path(args.image).is_file():
        errors.append(f"图片路径不是文件: {args.image}")

    # 验证设备目录
    if not Path(args.device_dir).exists():
        errors.append(f"设备目录不存在: {args.device_dir}")
    elif not Path(args.device_dir).is_dir():
        errors.append(f"设备路径不是目录: {args.device_dir}")

    # 验证MQTT端口
    if args.mqtt_port <= 0 or args.mqtt_port > 65535:
        errors.append(f"MQTT端口必须在1-65535范围内: {args.mqtt_port}")

    # 验证超时时间
    if args.timeout <= 0:
        errors.append(f"超时时间必须大于0: {args.timeout}")

    # 验证循环次数
    if args.cycles <= 0:
        errors.append(f"循环次数必须大于0: {args.cycles}")

    return errors


def main():
    """主函数"""
    simulator = None
    
    try:
        # 解析参数
        args = parse_arguments()
        
        # 设置日志级别
        setup_logging(args.log_level)
        logger = logging.getLogger(__name__)
        
        logger.info("EP Test 命令行版本启动")
        logger.info(f"当前进程PID: {os.getpid()}")
        logger.info(f"日志级别: {args.log_level}")
        
        # 验证参数
        validation_errors = validate_arguments(args)
        if validation_errors:
            logger.error("参数验证失败:")
            for error in validation_errors:
                logger.error(f"  - {error}")
            return 1
        
        logger.debug("参数验证通过")
        
        # 创建设备事件模拟器
        simulator = SimpleDeviceEventSimulator(args.env_file)
        logger.debug("设备事件模拟器创建成功")
        
        # 设置MQTT配置
        logger.info("正在设置MQTT配置...")
        simulator.set_mqtt_config(
            args.mqtt_host,
            args.mqtt_port,
            args.mqtt_username,
            args.mqtt_password
        )
        
        # 初始化MQTT连接
        logger.info("正在初始化MQTT连接...")
        if not simulator.initialize_mqtt():
            logger.error("MQTT连接初始化失败")
            return 2
        
        logger.info("初始化完成，开始测试")
        
        # 执行测试循环
        for cycle in range(args.cycles):
            if args.cycles > 1:
                logger.info(f"\n开始第 {cycle + 1}/{args.cycles} 轮测试")
            
            success = simulator.run_test_cycle(
                args.image,
                args.device_dir,
                args.position,
                args.mqtt_topic,
                args.timeout,
                args.exclude
            )
            
            if not success:
                logger.error(f"第 {cycle + 1} 轮测试失败")
                return 3
            
            if args.cycles > 1 and cycle < args.cycles - 1:
                logger.info("等待5秒后开始下一轮测试...")
                time.sleep(5)
        
        logger.info("所有测试完成")
        return 0
        
    except KeyboardInterrupt:
        logger.info("\n用户中断，正在退出...")
        return 0
    except Exception as e:
        logger.error(f"程序运行异常: {e}")
        return 4
    finally:
        # 清理资源
        if simulator:
            simulator.cleanup()


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)