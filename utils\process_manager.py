#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
进程管理工具
用于精准管理Event Processor相关进程

遵循调试约束：
- 不能用 taskkill /F /IM python.exe 结束当前进程，因为系统中有别的python进程
- 需要结束进程时，在代码中增加pid的日志输出再精准的结束单一进程
"""

import os
import sys
import subprocess
import argparse
import logging
import time
from typing import List, Dict, Optional

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s - %(name)s:%(lineno)d',
    datefmt='%H:%M:%S'
)
logger = logging.getLogger(__name__)


class ProcessManager:
    """进程管理器"""
    
    def __init__(self):
        self.current_pid = os.getpid()
        logger.info(f"进程管理器启动，当前PID: {self.current_pid}")
    
    def list_python_processes(self) -> List[Dict[str, str]]:
        """列出所有Python进程"""
        try:
            # 使用tasklist命令获取所有python.exe进程
            result = subprocess.run(
                ['tasklist', '/FI', 'IMAGENAME eq python.exe', '/FO', 'CSV'],
                capture_output=True,
                text=True,
                encoding='gbk'
            )
            
            if result.returncode != 0:
                logger.error(f"获取进程列表失败: {result.stderr}")
                return []
            
            processes = []
            lines = result.stdout.strip().split('\n')
            
            # 跳过标题行
            for line in lines[1:]:
                if line.strip():
                    parts = [part.strip('"') for part in line.split('","')]
                    if len(parts) >= 2:
                        processes.append({
                            'name': parts[0],
                            'pid': parts[1],
                            'session': parts[2] if len(parts) > 2 else '',
                            'mem_usage': parts[4] if len(parts) > 4 else ''
                        })
            
            return processes
            
        except Exception as e:
            logger.error(f"获取进程列表异常: {e}")
            return []
    
    def get_process_command_line(self, pid: str) -> Optional[str]:
        """获取进程的命令行参数"""
        try:
            result = subprocess.run(
                ['wmic', 'process', 'where', f'processid={pid}', 'get', 'commandline', '/value'],
                capture_output=True,
                text=True,
                encoding='gbk'
            )
            
            if result.returncode == 0:
                lines = result.stdout.strip().split('\n')
                for line in lines:
                    if line.startswith('CommandLine='):
                        return line.split('=', 1)[1].strip()
            
            return None
            
        except Exception as e:
            logger.error(f"获取进程命令行失败 (PID: {pid}): {e}")
            return None
    
    def find_event_processor_processes(self) -> List[Dict[str, str]]:
        """查找Event Processor相关进程"""
        python_processes = self.list_python_processes()
        ep_processes = []
        
        for proc in python_processes:
            pid = proc['pid']
            cmd_line = self.get_process_command_line(pid)
            
            if cmd_line:
                # 检查是否为Event Processor相关进程
                if any(keyword in cmd_line.lower() for keyword in [
                    'main.py', 'event_processor', 'ep_test_cmd.py'
                ]):
                    proc['command_line'] = cmd_line
                    ep_processes.append(proc)
        
        return ep_processes
    
    def kill_process_by_pid(self, pid: str, force: bool = False) -> bool:
        """通过PID精准终止进程"""
        try:
            if pid == str(self.current_pid):
                logger.warning(f"不能终止当前进程管理器进程 (PID: {pid})")
                return False
            
            # 使用taskkill命令终止指定PID的进程
            cmd = ['taskkill', '/PID', pid]
            if force:
                cmd.append('/F')
            
            result = subprocess.run(cmd, capture_output=True, text=True, encoding='gbk')
            
            if result.returncode == 0:
                logger.info(f"成功终止进程 PID: {pid}")
                return True
            else:
                logger.error(f"终止进程失败 (PID: {pid}): {result.stderr}")
                return False
                
        except Exception as e:
            logger.error(f"终止进程异常 (PID: {pid}): {e}")
            return False
    
    def stop_event_processor(self, confirm: bool = False) -> bool:
        """停止Event Processor进程"""
        ep_processes = self.find_event_processor_processes()
        
        if not ep_processes:
            logger.info("未找到Event Processor相关进程")
            return True
        
        logger.info(f"找到 {len(ep_processes)} 个Event Processor相关进程:")
        for i, proc in enumerate(ep_processes, 1):
            logger.info(f"{i}. PID: {proc['pid']}, 内存: {proc['mem_usage']}")
            logger.info(f"  命令行: {proc.get('command_line', 'N/A')}")
        
        if not confirm:
            response = input("\n是否确认终止这些进程？(y/N): ")
            if response.lower() not in ['y', 'yes']:
                logger.info("操作已取消")
                return False
        
        success_count = 0
        for proc in ep_processes:
            if self.kill_process_by_pid(proc['pid']):
                success_count += 1
                time.sleep(0.5)  # 等待进程终止
        
        logger.info(f"成功终止 {success_count}/{len(ep_processes)} 个进程")
        return success_count == len(ep_processes)


def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(
        description="Event Processor 进程管理工具",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  python process_manager.py --list          # 列出所有Event Processor进程
  python process_manager.py --stop          # 停止Event Processor进程
  python process_manager.py --kill 1234     # 终止指定PID的进程
  python process_manager.py --stop --force  # 强制停止进程
        """
    )
    
    parser.add_argument('--list', action='store_true',
                       help='列出所有Event Processor相关进程')
    
    parser.add_argument('--stop', action='store_true',
                       help='停止Event Processor进程')
    
    parser.add_argument('--kill', type=str, metavar='PID',
                       help='终止指定PID的进程')
    
    parser.add_argument('--force', action='store_true',
                       help='强制终止进程')
    
    parser.add_argument('--yes', action='store_true',
                       help='自动确认所有操作')
    
    return parser.parse_args()


def main():
    """主函数"""
    args = parse_arguments()
    manager = ProcessManager()
    
    if args.list:
        # 列出进程
        processes = manager.find_event_processor_processes()
        if processes:
            print(f"\n找到 {len(processes)} 个Event Processor相关进程:")
            for i, proc in enumerate(processes, 1):
                print(f"  {i}. PID: {proc['pid']}, 内存: {proc['mem_usage']}")
                print(f"     命令行: {proc.get('command_line', 'N/A')}")
        else:
            print("未找到Event Processor相关进程")
    
    elif args.stop:
        # 停止进程
        manager.stop_event_processor(confirm=args.yes)
    
    elif args.kill:
        # 终止指定PID
        manager.kill_process_by_pid(args.kill, force=args.force)
    
    else:
        # 默认列出进程
        processes = manager.find_event_processor_processes()
        if processes:
            print(f"\n找到 {len(processes)} 个Event Processor相关进程:")
            for i, proc in enumerate(processes, 1):
                print(f"  {i}. PID: {proc['pid']}, 内存: {proc['mem_usage']}")
                print(f"     命令行: {proc.get('command_line', 'N/A')}")
        else:
            print("未找到Event Processor相关进程")


if __name__ == "__main__":
    main()