metadata:
  scene_id: EV001001-20250805075258707
  event_id: EV001001
  description: Reproduction of EV001001 from 2025-08-05T07:52:58.707000
  capture_time: '2025-08-05T10:54:14.294038'
  original_start_time: '2025-08-05T07:52:58.707000'
  original_end_time: '2025-08-05T07:53:30.824000'
  original_duration_sec: 32.117
events:
- offset_ms: 0
  topic: device/BDN861290073715232/event
  payload: '{"I2":{"value":"0","timestamp":1754351578}}'
- offset_ms: 1988
  topic: ajb/101013/out/P002LfyBmOut/time_log
  payload: '{"log_original_timestamp": "07:52:59:448", "log_event_type": "等待支付中",
    "log_car_no": "粤A32L0J", "log_card_id": "52", "log_have_car": "0", "log_n_type":
    "0"}'
- offset_ms: 2050
  topic: ajb/101013/out/P002LfyBmOut/time_log
  payload: '{"log_original_timestamp": "07:52:59:637", "log_event_type": "出场查询系统返回结果",
    "response_payload": {"status": true, "code": "0000", "msg": "操作成功", "confirm":
    "1", "data": {"CarInfo": {"CardId": "52", "CardSnId": "52", "CarNo": "粤A32L0J",
    "CardType": "储值卡", "Intime": "2025-08-04 10:46:52", "PStatus": "离场", "DoorName":
    "一楼入口", "Balance": 0, "Starttime": "2020-04-01", "Endtime": "2023-12-31", "Name":
    "张桂彬", "PositionNum": "", "NColor": "蓝色", "ByCarType": "汽车", "BindFeeType": "业主临停车辆",
    "UserID": "", "BillId": "", "NoSensePay": "0", "Per_Full": "", "MoreCarNoInfo":
    "", "IsMoreCarNo": "0", "Acctime": ""}, "Charge": {"AllFee": "5", "ParkTime":
    "21时6分", "FeeTime": "20时56分", "FavMoney": "0", "FavTime": "0", "TotalFee": "5",
    "CurrFavMoney": "0", "Overtime": "15", "IsFree": "0", "IsTime": "0", "ChargeMoney":
    "0", "ChargeTime": "", "ChargeType": "", "StartTime": "2025-08-04 10:46:52", "EndTime":
    "2025-08-05 07:52:57", "ChargingCar": "业主临停车辆", "OrderID": "66676CD413B2439ABFD028551ECDADD8",
    "Expire": "0", "IsAutoCharge": "0", "lastPaidTime": ""}}}}'
- offset_ms: 23780
  topic: ajb/101013/out/P002LfyBmOut/time_log
  payload: '{"log_original_timestamp": "07:53:21:760", "log_event_type": "等待缴费", "log_charge_type":
    "业主临停车辆"}'
- offset_ms: 25869
  topic: ajb/101013/out/P002LfyBmOut/time_log
  payload: '{"log_original_timestamp": "07:53:23:266", "log_event_type": "等待支付中",
    "log_car_no": "粤A32L0J", "log_card_id": "52", "log_have_car": "0", "log_n_type":
    "0"}'
- offset_ms: 25876
  topic: ajb/101013/out/P002LfyBmOut/time_log
  payload: '{"log_original_timestamp": "07:53:23:475", "log_event_type": "出场查询系统返回结果",
    "response_payload": {"status": true, "code": "0000", "msg": "已缴费,未超时", "confirm":
    "0", "data": {"CarInfo": {"CardId": "52", "CardSnId": "52", "CarNo": "粤A32L0J",
    "CardType": "储值卡", "Intime": "2025-08-04 10:46:52", "PStatus": "离场", "DoorName":
    "一楼入口", "Balance": 0, "Starttime": "2020-04-01", "Endtime": "2023-12-31", "Name":
    "张桂彬", "PositionNum": "", "NColor": "蓝色", "ByCarType": "汽车", "BindFeeType": "业主临停车辆",
    "UserID": "", "BillId": "", "NoSensePay": "0", "Per_Full": "", "MoreCarNoInfo":
    "", "IsMoreCarNo": "0", "Acctime": ""}, "Charge": {"AllFee": "0", "ParkTime":
    "21时6分", "FeeTime": "0时0分", "FavMoney": "0", "FavTime": "0", "TotalFee": "0",
    "CurrFavMoney": "0", "Overtime": "15", "IsFree": "0", "IsTime": "0", "ChargeMoney":
    "5", "ChargeTime": "2025-08-05 07:53:21", "ChargeType": "B", "StartTime": "2025-08-04
    10:46:52", "EndTime": "2025-08-05 07:52:57", "ChargingCar": "业主临停车辆", "OrderID":
    "66676CD413B2439ABFD028551ECDADD8", "Expire": "0", "IsAutoCharge": "0", "lastPaidTime":
    ""}}}}'
- offset_ms: 25882
  topic: ajb/101013/out/P002LfyBmOut/time_log
  payload: '{"log_original_timestamp": "07:53:23:484", "log_event_type": "出场开闸控制指令",
    "log_card_id": "52", "log_car_no": "粤A32L0J", "log_request_duration": "207毫秒",
    "log_event_result": "离场起杆完成。"}'
- offset_ms: 25884
  topic: ajb/101013/out/P002LfyBmOut/time_log
  payload: '{"log_original_timestamp": "07:53:23:561", "log_event_type": "查询返回场内剩余车位和场内车辆数量",
    "response_payload": {"status": true, "code": "0000", "msg": "操作成功", "data": {"surplusNum":
    8486, "presenceNum": 1514, "systemTime": "2025-08-05 07:53:23", "online": "1",
    "mac": "200510700120001"}}}'
- offset_ms: 25889
  topic: ajb/101013/out/P002LfyBmOut/time_log
  payload: '{"log_original_timestamp": "07:53:23:685", "log_event_type": "等待缴费", "log_charge_type":
    "业主临停车辆"}'
- offset_ms: 32116
  topic: device/BDN861290073715232/event
  payload: '{"I2":{"value":"1","timestamp":1754351610}}'
