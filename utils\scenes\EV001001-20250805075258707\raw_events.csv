id,topic,payload,payload_size,message_timestamp,received_timestamp,stored_timestamp
16801,device/BDN861290073715232/event,"{""I2"":{""value"":""0"",""timestamp"":1754351578}}",43,,2025-08-05 07:52:58.707000,2025-08-05 07:52:58.707000
16802,ajb/101013/out/P002LfyBmOut/time_log,"{""log_original_timestamp"": ""07:52:59:448"", ""log_event_type"": ""等待支付中"", ""log_car_no"": ""粤A32L0J"", ""log_card_id"": ""52"", ""log_have_car"": ""0"", ""log_n_type"": ""0""}",167,,2025-08-05 07:53:00.695000,2025-08-05 07:53:00.695000
16803,ajb/101013/out/P002LfyBmOut/time_log,"{""log_original_timestamp"": ""07:52:59:637"", ""log_event_type"": ""出场查询系统返回结果"", ""response_payload"": {""status"": true, ""code"": ""0000"", ""msg"": ""操作成功"", ""confirm"": ""1"", ""data"": {""CarInfo"": {""CardId"": ""52"", ""CardSnId"": ""52"", ""CarNo"": ""粤A32L0J"", ""CardType"": ""储值卡"", ""Intime"": ""2025-08-04 10:46:52"", ""PStatus"": ""离场"", ""DoorName"": ""一楼入口"", ""Balance"": 0, ""Starttime"": ""2020-04-01"", ""Endtime"": ""2023-12-31"", ""Name"": ""张桂彬"", ""PositionNum"": """", ""NColor"": ""蓝色"", ""ByCarType"": ""汽车"", ""BindFeeType"": ""业主临停车辆"", ""UserID"": """", ""BillId"": """", ""NoSensePay"": ""0"", ""Per_Full"": """", ""MoreCarNoInfo"": """", ""IsMoreCarNo"": ""0"", ""Acctime"": """"}, ""Charge"": {""AllFee"": ""5"", ""ParkTime"": ""21时6分"", ""FeeTime"": ""20时56分"", ""FavMoney"": ""0"", ""FavTime"": ""0"", ""TotalFee"": ""5"", ""CurrFavMoney"": ""0"", ""Overtime"": ""15"", ""IsFree"": ""0"", ""IsTime"": ""0"", ""ChargeMoney"": ""0"", ""ChargeTime"": """", ""ChargeType"": """", ""StartTime"": ""2025-08-04 10:46:52"", ""EndTime"": ""2025-08-05 07:52:57"", ""ChargingCar"": ""业主临停车辆"", ""OrderID"": ""66676CD413B2439ABFD028551ECDADD8"", ""Expire"": ""0"", ""IsAutoCharge"": ""0"", ""lastPaidTime"": """"}}}}",1140,,2025-08-05 07:53:00.757000,2025-08-05 07:53:00.757000
16804,ajb/101013/out/P002LfyBmOut/time_log,"{""log_original_timestamp"": ""07:52:59:645"", ""log_event_type"": ""等待缴费"", ""log_charge_type"": ""业主临停车辆""}",117,,2025-08-05 07:53:00.759000,2025-08-05 07:53:00.759000
16805,ajb/101013/out/P002LfyBmOut/time_log,"{""log_original_timestamp"": ""07:53:01:140"", ""log_event_type"": ""等待支付中"", ""log_car_no"": ""粤A32L0J"", ""log_card_id"": ""52"", ""log_have_car"": ""0"", ""log_n_type"": ""0""}",167,,2025-08-05 07:53:01.743000,2025-08-05 07:53:01.743000
16806,ajb/101013/out/P002LfyBmOut/time_log,"{""log_original_timestamp"": ""07:53:01:315"", ""log_event_type"": ""出场查询系统返回结果"", ""response_payload"": {""status"": true, ""code"": ""0000"", ""msg"": ""操作成功"", ""confirm"": ""1"", ""data"": {""CarInfo"": {""CardId"": ""52"", ""CardSnId"": ""52"", ""CarNo"": ""粤A32L0J"", ""CardType"": ""储值卡"", ""Intime"": ""2025-08-04 10:46:52"", ""PStatus"": ""离场"", ""DoorName"": ""一楼入口"", ""Balance"": 0, ""Starttime"": ""2020-04-01"", ""Endtime"": ""2023-12-31"", ""Name"": ""张桂彬"", ""PositionNum"": """", ""NColor"": ""蓝色"", ""ByCarType"": ""汽车"", ""BindFeeType"": ""业主临停车辆"", ""UserID"": """", ""BillId"": """", ""NoSensePay"": ""0"", ""Per_Full"": """", ""MoreCarNoInfo"": """", ""IsMoreCarNo"": ""0"", ""Acctime"": """"}, ""Charge"": {""AllFee"": ""5"", ""ParkTime"": ""21时6分"", ""FeeTime"": ""20时56分"", ""FavMoney"": ""0"", ""FavTime"": ""0"", ""TotalFee"": ""5"", ""CurrFavMoney"": ""0"", ""Overtime"": ""15"", ""IsFree"": ""0"", ""IsTime"": ""0"", ""ChargeMoney"": ""0"", ""ChargeTime"": """", ""ChargeType"": """", ""StartTime"": ""2025-08-04 10:46:52"", ""EndTime"": ""2025-08-05 07:52:57"", ""ChargingCar"": ""业主临停车辆"", ""OrderID"": ""66676CD413B2439ABFD028551ECDADD8"", ""Expire"": ""0"", ""IsAutoCharge"": ""0"", ""lastPaidTime"": """"}}}}",1140,,2025-08-05 07:53:01.751000,2025-08-05 07:53:01.751000
16807,ajb/101013/out/P002LfyBmOut/time_log,"{""log_original_timestamp"": ""07:53:01:323"", ""log_event_type"": ""等待缴费"", ""log_charge_type"": ""业主临停车辆""}",117,,2025-08-05 07:53:01.753000,2025-08-05 07:53:01.753000
16809,ajb/101013/out/P002LfyBmOut/time_log,"{""log_original_timestamp"": ""07:53:02:818"", ""log_event_type"": ""等待支付中"", ""log_car_no"": ""粤A32L0J"", ""log_card_id"": ""52"", ""log_have_car"": ""0"", ""log_n_type"": ""0""}",167,,2025-08-05 07:53:03.804000,2025-08-05 07:53:03.804000
16810,ajb/101013/out/P002LfyBmOut/time_log,"{""log_original_timestamp"": ""07:53:03:008"", ""log_event_type"": ""出场查询系统返回结果"", ""response_payload"": {""status"": true, ""code"": ""0000"", ""msg"": ""操作成功"", ""confirm"": ""1"", ""data"": {""CarInfo"": {""CardId"": ""52"", ""CardSnId"": ""52"", ""CarNo"": ""粤A32L0J"", ""CardType"": ""储值卡"", ""Intime"": ""2025-08-04 10:46:52"", ""PStatus"": ""离场"", ""DoorName"": ""一楼入口"", ""Balance"": 0, ""Starttime"": ""2020-04-01"", ""Endtime"": ""2023-12-31"", ""Name"": ""张桂彬"", ""PositionNum"": """", ""NColor"": ""蓝色"", ""ByCarType"": ""汽车"", ""BindFeeType"": ""业主临停车辆"", ""UserID"": """", ""BillId"": """", ""NoSensePay"": ""0"", ""Per_Full"": """", ""MoreCarNoInfo"": """", ""IsMoreCarNo"": ""0"", ""Acctime"": """"}, ""Charge"": {""AllFee"": ""5"", ""ParkTime"": ""21时6分"", ""FeeTime"": ""20时56分"", ""FavMoney"": ""0"", ""FavTime"": ""0"", ""TotalFee"": ""5"", ""CurrFavMoney"": ""0"", ""Overtime"": ""15"", ""IsFree"": ""0"", ""IsTime"": ""0"", ""ChargeMoney"": ""0"", ""ChargeTime"": """", ""ChargeType"": """", ""StartTime"": ""2025-08-04 10:46:52"", ""EndTime"": ""2025-08-05 07:52:57"", ""ChargingCar"": ""业主临停车辆"", ""OrderID"": ""66676CD413B2439ABFD028551ECDADD8"", ""Expire"": ""0"", ""IsAutoCharge"": ""0"", ""lastPaidTime"": """"}}}}",1140,,2025-08-05 07:53:03.811000,2025-08-05 07:53:03.811000
16811,ajb/101013/out/P002LfyBmOut/time_log,"{""log_original_timestamp"": ""07:53:03:016"", ""log_event_type"": ""等待缴费"", ""log_charge_type"": ""业主临停车辆""}",117,,2025-08-05 07:53:03.817000,2025-08-05 07:53:03.817000
16812,ajb/101013/out/P002LfyBmOut/time_log,"{""log_original_timestamp"": ""07:53:04:517"", ""log_event_type"": ""等待支付中"", ""log_car_no"": ""粤A32L0J"", ""log_card_id"": ""52"", ""log_have_car"": ""0"", ""log_n_type"": ""0""}",167,,2025-08-05 07:53:05.878000,2025-08-05 07:53:05.878000
16813,ajb/101013/out/P002LfyBmOut/time_log,"{""log_original_timestamp"": ""07:53:04:704"", ""log_event_type"": ""出场查询系统返回结果"", ""response_payload"": {""status"": true, ""code"": ""0000"", ""msg"": ""操作成功"", ""confirm"": ""1"", ""data"": {""CarInfo"": {""CardId"": ""52"", ""CardSnId"": ""52"", ""CarNo"": ""粤A32L0J"", ""CardType"": ""储值卡"", ""Intime"": ""2025-08-04 10:46:52"", ""PStatus"": ""离场"", ""DoorName"": ""一楼入口"", ""Balance"": 0, ""Starttime"": ""2020-04-01"", ""Endtime"": ""2023-12-31"", ""Name"": ""张桂彬"", ""PositionNum"": """", ""NColor"": ""蓝色"", ""ByCarType"": ""汽车"", ""BindFeeType"": ""业主临停车辆"", ""UserID"": """", ""BillId"": """", ""NoSensePay"": ""0"", ""Per_Full"": """", ""MoreCarNoInfo"": """", ""IsMoreCarNo"": ""0"", ""Acctime"": """"}, ""Charge"": {""AllFee"": ""5"", ""ParkTime"": ""21时6分"", ""FeeTime"": ""20时56分"", ""FavMoney"": ""0"", ""FavTime"": ""0"", ""TotalFee"": ""5"", ""CurrFavMoney"": ""0"", ""Overtime"": ""15"", ""IsFree"": ""0"", ""IsTime"": ""0"", ""ChargeMoney"": ""0"", ""ChargeTime"": """", ""ChargeType"": """", ""StartTime"": ""2025-08-04 10:46:52"", ""EndTime"": ""2025-08-05 07:52:57"", ""ChargingCar"": ""业主临停车辆"", ""OrderID"": ""66676CD413B2439ABFD028551ECDADD8"", ""Expire"": ""0"", ""IsAutoCharge"": ""0"", ""lastPaidTime"": """"}}}}",1140,,2025-08-05 07:53:05.885000,2025-08-05 07:53:05.885000
16814,ajb/101013/out/P002LfyBmOut/time_log,"{""log_original_timestamp"": ""07:53:04:713"", ""log_event_type"": ""等待缴费"", ""log_charge_type"": ""业主临停车辆""}",117,,2025-08-05 07:53:05.887000,2025-08-05 07:53:05.887000
16815,ajb/101013/out/P002LfyBmOut/time_log,"{""log_original_timestamp"": ""07:53:06:212"", ""log_event_type"": ""等待支付中"", ""log_car_no"": ""粤A32L0J"", ""log_card_id"": ""52"", ""log_have_car"": ""0"", ""log_n_type"": ""0""}",167,,2025-08-05 07:53:06.912000,2025-08-05 07:53:06.912000
16816,ajb/101013/out/P002LfyBmOut/time_log,"{""log_original_timestamp"": ""07:53:06:434"", ""log_event_type"": ""出场查询系统返回结果"", ""response_payload"": {""status"": true, ""code"": ""0000"", ""msg"": ""操作成功"", ""confirm"": ""1"", ""data"": {""CarInfo"": {""CardId"": ""52"", ""CardSnId"": ""52"", ""CarNo"": ""粤A32L0J"", ""CardType"": ""储值卡"", ""Intime"": ""2025-08-04 10:46:52"", ""PStatus"": ""离场"", ""DoorName"": ""一楼入口"", ""Balance"": 0, ""Starttime"": ""2020-04-01"", ""Endtime"": ""2023-12-31"", ""Name"": ""张桂彬"", ""PositionNum"": """", ""NColor"": ""蓝色"", ""ByCarType"": ""汽车"", ""BindFeeType"": ""业主临停车辆"", ""UserID"": """", ""BillId"": """", ""NoSensePay"": ""0"", ""Per_Full"": """", ""MoreCarNoInfo"": """", ""IsMoreCarNo"": ""0"", ""Acctime"": """"}, ""Charge"": {""AllFee"": ""5"", ""ParkTime"": ""21时6分"", ""FeeTime"": ""20时56分"", ""FavMoney"": ""0"", ""FavTime"": ""0"", ""TotalFee"": ""5"", ""CurrFavMoney"": ""0"", ""Overtime"": ""15"", ""IsFree"": ""0"", ""IsTime"": ""0"", ""ChargeMoney"": ""0"", ""ChargeTime"": """", ""ChargeType"": """", ""StartTime"": ""2025-08-04 10:46:52"", ""EndTime"": ""2025-08-05 07:52:57"", ""ChargingCar"": ""业主临停车辆"", ""OrderID"": ""66676CD413B2439ABFD028551ECDADD8"", ""Expire"": ""0"", ""IsAutoCharge"": ""0"", ""lastPaidTime"": """"}}}}",1140,,2025-08-05 07:53:06.919000,2025-08-05 07:53:06.919000
16817,ajb/101013/out/P002LfyBmOut/time_log,"{""log_original_timestamp"": ""07:53:06:449"", ""log_event_type"": ""等待缴费"", ""log_charge_type"": ""业主临停车辆""}",117,,2025-08-05 07:53:06.925000,2025-08-05 07:53:06.925000
16818,ajb/101013/out/P002LfyBmOut/time_log,"{""log_original_timestamp"": ""07:53:07:945"", ""log_event_type"": ""等待支付中"", ""log_car_no"": ""粤A32L0J"", ""log_card_id"": ""52"", ""log_have_car"": ""0"", ""log_n_type"": ""0""}",167,,2025-08-05 07:53:08.999000,2025-08-05 07:53:08.999000
16819,ajb/101013/out/P002LfyBmOut/time_log,"{""log_original_timestamp"": ""07:53:08:136"", ""log_event_type"": ""出场查询系统返回结果"", ""response_payload"": {""status"": true, ""code"": ""0000"", ""msg"": ""操作成功"", ""confirm"": ""1"", ""data"": {""CarInfo"": {""CardId"": ""52"", ""CardSnId"": ""52"", ""CarNo"": ""粤A32L0J"", ""CardType"": ""储值卡"", ""Intime"": ""2025-08-04 10:46:52"", ""PStatus"": ""离场"", ""DoorName"": ""一楼入口"", ""Balance"": 0, ""Starttime"": ""2020-04-01"", ""Endtime"": ""2023-12-31"", ""Name"": ""张桂彬"", ""PositionNum"": """", ""NColor"": ""蓝色"", ""ByCarType"": ""汽车"", ""BindFeeType"": ""业主临停车辆"", ""UserID"": """", ""BillId"": """", ""NoSensePay"": ""0"", ""Per_Full"": """", ""MoreCarNoInfo"": """", ""IsMoreCarNo"": ""0"", ""Acctime"": """"}, ""Charge"": {""AllFee"": ""5"", ""ParkTime"": ""21时6分"", ""FeeTime"": ""20时56分"", ""FavMoney"": ""0"", ""FavTime"": ""0"", ""TotalFee"": ""5"", ""CurrFavMoney"": ""0"", ""Overtime"": ""15"", ""IsFree"": ""0"", ""IsTime"": ""0"", ""ChargeMoney"": ""0"", ""ChargeTime"": """", ""ChargeType"": """", ""StartTime"": ""2025-08-04 10:46:52"", ""EndTime"": ""2025-08-05 07:52:57"", ""ChargingCar"": ""业主临停车辆"", ""OrderID"": ""66676CD413B2439ABFD028551ECDADD8"", ""Expire"": ""0"", ""IsAutoCharge"": ""0"", ""lastPaidTime"": """"}}}}",1140,,2025-08-05 07:53:09.006000,2025-08-05 07:53:09.006000
16820,ajb/101013/out/P002LfyBmOut/time_log,"{""log_original_timestamp"": ""07:53:08:144"", ""log_event_type"": ""等待缴费"", ""log_charge_type"": ""业主临停车辆""}",117,,2025-08-05 07:53:09.012000,2025-08-05 07:53:09.012000
16821,ajb/101013/out/P002LfyBmOut/time_log,"{""log_original_timestamp"": ""07:53:09:645"", ""log_event_type"": ""等待支付中"", ""log_car_no"": ""粤A32L0J"", ""log_card_id"": ""52"", ""log_have_car"": ""0"", ""log_n_type"": ""0""}",167,,2025-08-05 07:53:11.071000,2025-08-05 07:53:11.071000
16822,ajb/101013/out/P002LfyBmOut/time_log,"{""log_original_timestamp"": ""07:53:09:822"", ""log_event_type"": ""出场查询系统返回结果"", ""response_payload"": {""status"": true, ""code"": ""0000"", ""msg"": ""操作成功"", ""confirm"": ""1"", ""data"": {""CarInfo"": {""CardId"": ""52"", ""CardSnId"": ""52"", ""CarNo"": ""粤A32L0J"", ""CardType"": ""储值卡"", ""Intime"": ""2025-08-04 10:46:52"", ""PStatus"": ""离场"", ""DoorName"": ""一楼入口"", ""Balance"": 0, ""Starttime"": ""2020-04-01"", ""Endtime"": ""2023-12-31"", ""Name"": ""张桂彬"", ""PositionNum"": """", ""NColor"": ""蓝色"", ""ByCarType"": ""汽车"", ""BindFeeType"": ""业主临停车辆"", ""UserID"": """", ""BillId"": """", ""NoSensePay"": ""0"", ""Per_Full"": """", ""MoreCarNoInfo"": """", ""IsMoreCarNo"": ""0"", ""Acctime"": """"}, ""Charge"": {""AllFee"": ""5"", ""ParkTime"": ""21时6分"", ""FeeTime"": ""20时56分"", ""FavMoney"": ""0"", ""FavTime"": ""0"", ""TotalFee"": ""5"", ""CurrFavMoney"": ""0"", ""Overtime"": ""15"", ""IsFree"": ""0"", ""IsTime"": ""0"", ""ChargeMoney"": ""0"", ""ChargeTime"": """", ""ChargeType"": """", ""StartTime"": ""2025-08-04 10:46:52"", ""EndTime"": ""2025-08-05 07:52:57"", ""ChargingCar"": ""业主临停车辆"", ""OrderID"": ""66676CD413B2439ABFD028551ECDADD8"", ""Expire"": ""0"", ""IsAutoCharge"": ""0"", ""lastPaidTime"": """"}}}}",1140,,2025-08-05 07:53:11.078000,2025-08-05 07:53:11.078000
16823,ajb/101013/out/P002LfyBmOut/time_log,"{""log_original_timestamp"": ""07:53:09:832"", ""log_event_type"": ""等待缴费"", ""log_charge_type"": ""业主临停车辆""}",117,,2025-08-05 07:53:11.084000,2025-08-05 07:53:11.084000
16824,ajb/101013/out/P002LfyBmOut/time_log,"{""log_original_timestamp"": ""07:53:11:329"", ""log_event_type"": ""等待支付中"", ""log_car_no"": ""粤A32L0J"", ""log_card_id"": ""52"", ""log_have_car"": ""0"", ""log_n_type"": ""0""}",167,,2025-08-05 07:53:12.108000,2025-08-05 07:53:12.108000
16825,ajb/101013/out/P002LfyBmOut/time_log,"{""log_original_timestamp"": ""07:53:11:517"", ""log_event_type"": ""出场查询系统返回结果"", ""response_payload"": {""status"": true, ""code"": ""0000"", ""msg"": ""操作成功"", ""confirm"": ""1"", ""data"": {""CarInfo"": {""CardId"": ""52"", ""CardSnId"": ""52"", ""CarNo"": ""粤A32L0J"", ""CardType"": ""储值卡"", ""Intime"": ""2025-08-04 10:46:52"", ""PStatus"": ""离场"", ""DoorName"": ""一楼入口"", ""Balance"": 0, ""Starttime"": ""2020-04-01"", ""Endtime"": ""2023-12-31"", ""Name"": ""张桂彬"", ""PositionNum"": """", ""NColor"": ""蓝色"", ""ByCarType"": ""汽车"", ""BindFeeType"": ""业主临停车辆"", ""UserID"": """", ""BillId"": """", ""NoSensePay"": ""0"", ""Per_Full"": """", ""MoreCarNoInfo"": """", ""IsMoreCarNo"": ""0"", ""Acctime"": """"}, ""Charge"": {""AllFee"": ""5"", ""ParkTime"": ""21时6分"", ""FeeTime"": ""20时56分"", ""FavMoney"": ""0"", ""FavTime"": ""0"", ""TotalFee"": ""5"", ""CurrFavMoney"": ""0"", ""Overtime"": ""15"", ""IsFree"": ""0"", ""IsTime"": ""0"", ""ChargeMoney"": ""0"", ""ChargeTime"": """", ""ChargeType"": """", ""StartTime"": ""2025-08-04 10:46:52"", ""EndTime"": ""2025-08-05 07:52:57"", ""ChargingCar"": ""业主临停车辆"", ""OrderID"": ""66676CD413B2439ABFD028551ECDADD8"", ""Expire"": ""0"", ""IsAutoCharge"": ""0"", ""lastPaidTime"": """"}}}}",1140,,2025-08-05 07:53:12.115000,2025-08-05 07:53:12.115000
16826,ajb/101013/out/P002LfyBmOut/time_log,"{""log_original_timestamp"": ""07:53:11:525"", ""log_event_type"": ""等待缴费"", ""log_charge_type"": ""业主临停车辆""}",117,,2025-08-05 07:53:12.121000,2025-08-05 07:53:12.121000
16827,ajb/101013/out/P002LfyBmOut/time_log,"{""log_original_timestamp"": ""07:53:13:019"", ""log_event_type"": ""等待支付中"", ""log_car_no"": ""粤A32L0J"", ""log_card_id"": ""52"", ""log_have_car"": ""0"", ""log_n_type"": ""0""}",167,,2025-08-05 07:53:14.189000,2025-08-05 07:53:14.189000
16828,ajb/101013/out/P002LfyBmOut/time_log,"{""log_original_timestamp"": ""07:53:13:207"", ""log_event_type"": ""出场查询系统返回结果"", ""response_payload"": {""status"": true, ""code"": ""0000"", ""msg"": ""操作成功"", ""confirm"": ""1"", ""data"": {""CarInfo"": {""CardId"": ""52"", ""CardSnId"": ""52"", ""CarNo"": ""粤A32L0J"", ""CardType"": ""储值卡"", ""Intime"": ""2025-08-04 10:46:52"", ""PStatus"": ""离场"", ""DoorName"": ""一楼入口"", ""Balance"": 0, ""Starttime"": ""2020-04-01"", ""Endtime"": ""2023-12-31"", ""Name"": ""张桂彬"", ""PositionNum"": """", ""NColor"": ""蓝色"", ""ByCarType"": ""汽车"", ""BindFeeType"": ""业主临停车辆"", ""UserID"": """", ""BillId"": """", ""NoSensePay"": ""0"", ""Per_Full"": """", ""MoreCarNoInfo"": """", ""IsMoreCarNo"": ""0"", ""Acctime"": """"}, ""Charge"": {""AllFee"": ""5"", ""ParkTime"": ""21时6分"", ""FeeTime"": ""20时56分"", ""FavMoney"": ""0"", ""FavTime"": ""0"", ""TotalFee"": ""5"", ""CurrFavMoney"": ""0"", ""Overtime"": ""15"", ""IsFree"": ""0"", ""IsTime"": ""0"", ""ChargeMoney"": ""0"", ""ChargeTime"": """", ""ChargeType"": """", ""StartTime"": ""2025-08-04 10:46:52"", ""EndTime"": ""2025-08-05 07:52:57"", ""ChargingCar"": ""业主临停车辆"", ""OrderID"": ""66676CD413B2439ABFD028551ECDADD8"", ""Expire"": ""0"", ""IsAutoCharge"": ""0"", ""lastPaidTime"": """"}}}}",1140,,2025-08-05 07:53:14.196000,2025-08-05 07:53:14.196000
16829,ajb/101013/out/P002LfyBmOut/time_log,"{""log_original_timestamp"": ""07:53:13:215"", ""log_event_type"": ""等待缴费"", ""log_charge_type"": ""业主临停车辆""}",117,,2025-08-05 07:53:14.202000,2025-08-05 07:53:14.202000
16830,ajb/101013/out/P002LfyBmOut/time_log,"{""log_original_timestamp"": ""07:53:14:714"", ""log_event_type"": ""等待支付中"", ""log_car_no"": ""粤A32L0J"", ""log_card_id"": ""52"", ""log_have_car"": ""0"", ""log_n_type"": ""0""}",167,,2025-08-05 07:53:15.228000,2025-08-05 07:53:15.228000
16831,ajb/101013/out/P002LfyBmOut/time_log,"{""log_original_timestamp"": ""07:53:14:927"", ""log_event_type"": ""出场查询系统返回结果"", ""response_payload"": {""status"": true, ""code"": ""0000"", ""msg"": ""操作成功"", ""confirm"": ""1"", ""data"": {""CarInfo"": {""CardId"": ""52"", ""CardSnId"": ""52"", ""CarNo"": ""粤A32L0J"", ""CardType"": ""储值卡"", ""Intime"": ""2025-08-04 10:46:52"", ""PStatus"": ""离场"", ""DoorName"": ""一楼入口"", ""Balance"": 0, ""Starttime"": ""2020-04-01"", ""Endtime"": ""2023-12-31"", ""Name"": ""张桂彬"", ""PositionNum"": """", ""NColor"": ""蓝色"", ""ByCarType"": ""汽车"", ""BindFeeType"": ""业主临停车辆"", ""UserID"": """", ""BillId"": """", ""NoSensePay"": ""0"", ""Per_Full"": """", ""MoreCarNoInfo"": """", ""IsMoreCarNo"": ""0"", ""Acctime"": """"}, ""Charge"": {""AllFee"": ""5"", ""ParkTime"": ""21时6分"", ""FeeTime"": ""20时56分"", ""FavMoney"": ""0"", ""FavTime"": ""0"", ""TotalFee"": ""5"", ""CurrFavMoney"": ""0"", ""Overtime"": ""15"", ""IsFree"": ""0"", ""IsTime"": ""0"", ""ChargeMoney"": ""0"", ""ChargeTime"": """", ""ChargeType"": """", ""StartTime"": ""2025-08-04 10:46:52"", ""EndTime"": ""2025-08-05 07:52:57"", ""ChargingCar"": ""业主临停车辆"", ""OrderID"": ""66676CD413B2439ABFD028551ECDADD8"", ""Expire"": ""0"", ""IsAutoCharge"": ""0"", ""lastPaidTime"": """"}}}}",1140,,2025-08-05 07:53:16.255000,2025-08-05 07:53:16.255000
16832,ajb/101013/out/P002LfyBmOut/time_log,"{""log_original_timestamp"": ""07:53:14:935"", ""log_event_type"": ""等待缴费"", ""log_charge_type"": ""业主临停车辆""}",117,,2025-08-05 07:53:16.260000,2025-08-05 07:53:16.260000
16833,ajb/101013/out/P002LfyBmOut/time_log,"{""log_original_timestamp"": ""07:53:16:452"", ""log_event_type"": ""等待支付中"", ""log_car_no"": ""粤A32L0J"", ""log_card_id"": ""52"", ""log_have_car"": ""0"", ""log_n_type"": ""0""}",167,,2025-08-05 07:53:17.281000,2025-08-05 07:53:17.281000
16834,ajb/101013/out/P002LfyBmOut/time_log,"{""log_original_timestamp"": ""07:53:16:644"", ""log_event_type"": ""出场查询系统返回结果"", ""response_payload"": {""status"": true, ""code"": ""0000"", ""msg"": ""操作成功"", ""confirm"": ""1"", ""data"": {""CarInfo"": {""CardId"": ""52"", ""CardSnId"": ""52"", ""CarNo"": ""粤A32L0J"", ""CardType"": ""储值卡"", ""Intime"": ""2025-08-04 10:46:52"", ""PStatus"": ""离场"", ""DoorName"": ""一楼入口"", ""Balance"": 0, ""Starttime"": ""2020-04-01"", ""Endtime"": ""2023-12-31"", ""Name"": ""张桂彬"", ""PositionNum"": """", ""NColor"": ""蓝色"", ""ByCarType"": ""汽车"", ""BindFeeType"": ""业主临停车辆"", ""UserID"": """", ""BillId"": """", ""NoSensePay"": ""0"", ""Per_Full"": """", ""MoreCarNoInfo"": """", ""IsMoreCarNo"": ""0"", ""Acctime"": """"}, ""Charge"": {""AllFee"": ""5"", ""ParkTime"": ""21时6分"", ""FeeTime"": ""20时56分"", ""FavMoney"": ""0"", ""FavTime"": ""0"", ""TotalFee"": ""5"", ""CurrFavMoney"": ""0"", ""Overtime"": ""15"", ""IsFree"": ""0"", ""IsTime"": ""0"", ""ChargeMoney"": ""0"", ""ChargeTime"": """", ""ChargeType"": """", ""StartTime"": ""2025-08-04 10:46:52"", ""EndTime"": ""2025-08-05 07:52:57"", ""ChargingCar"": ""业主临停车辆"", ""OrderID"": ""66676CD413B2439ABFD028551ECDADD8"", ""Expire"": ""0"", ""IsAutoCharge"": ""0"", ""lastPaidTime"": """"}}}}",1140,,2025-08-05 07:53:17.289000,2025-08-05 07:53:17.289000
16835,ajb/101013/out/P002LfyBmOut/time_log,"{""log_original_timestamp"": ""07:53:16:652"", ""log_event_type"": ""等待缴费"", ""log_charge_type"": ""业主临停车辆""}",117,,2025-08-05 07:53:17.291000,2025-08-05 07:53:17.291000
16837,ajb/101013/out/P002LfyBmOut/time_log,"{""log_original_timestamp"": ""07:53:18:148"", ""log_event_type"": ""等待支付中"", ""log_car_no"": ""粤A32L0J"", ""log_card_id"": ""52"", ""log_have_car"": ""0"", ""log_n_type"": ""0""}",167,,2025-08-05 07:53:19.345000,2025-08-05 07:53:19.345000
16838,ajb/101013/out/P002LfyBmOut/time_log,"{""log_original_timestamp"": ""07:53:18:350"", ""log_event_type"": ""出场查询系统返回结果"", ""response_payload"": {""status"": true, ""code"": ""0000"", ""msg"": ""操作成功"", ""confirm"": ""1"", ""data"": {""CarInfo"": {""CardId"": ""52"", ""CardSnId"": ""52"", ""CarNo"": ""粤A32L0J"", ""CardType"": ""储值卡"", ""Intime"": ""2025-08-04 10:46:52"", ""PStatus"": ""离场"", ""DoorName"": ""一楼入口"", ""Balance"": 0, ""Starttime"": ""2020-04-01"", ""Endtime"": ""2023-12-31"", ""Name"": ""张桂彬"", ""PositionNum"": """", ""NColor"": ""蓝色"", ""ByCarType"": ""汽车"", ""BindFeeType"": ""业主临停车辆"", ""UserID"": """", ""BillId"": """", ""NoSensePay"": ""0"", ""Per_Full"": """", ""MoreCarNoInfo"": """", ""IsMoreCarNo"": ""0"", ""Acctime"": """"}, ""Charge"": {""AllFee"": ""5"", ""ParkTime"": ""21时6分"", ""FeeTime"": ""20时56分"", ""FavMoney"": ""0"", ""FavTime"": ""0"", ""TotalFee"": ""5"", ""CurrFavMoney"": ""0"", ""Overtime"": ""15"", ""IsFree"": ""0"", ""IsTime"": ""0"", ""ChargeMoney"": ""0"", ""ChargeTime"": """", ""ChargeType"": """", ""StartTime"": ""2025-08-04 10:46:52"", ""EndTime"": ""2025-08-05 07:52:57"", ""ChargingCar"": ""业主临停车辆"", ""OrderID"": ""66676CD413B2439ABFD028551ECDADD8"", ""Expire"": ""0"", ""IsAutoCharge"": ""0"", ""lastPaidTime"": """"}}}}",1140,,2025-08-05 07:53:19.351000,2025-08-05 07:53:19.351000
16839,ajb/101013/out/P002LfyBmOut/time_log,"{""log_original_timestamp"": ""07:53:18:359"", ""log_event_type"": ""等待缴费"", ""log_charge_type"": ""业主临停车辆""}",117,,2025-08-05 07:53:19.357000,2025-08-05 07:53:19.357000
16840,ajb/101013/out/P002LfyBmOut/time_log,"{""log_original_timestamp"": ""07:53:19:861"", ""log_event_type"": ""等待支付中"", ""log_car_no"": ""粤A32L0J"", ""log_card_id"": ""52"", ""log_have_car"": ""0"", ""log_n_type"": ""0""}",167,,2025-08-05 07:53:20.381000,2025-08-05 07:53:20.381000
16841,ajb/101013/out/P002LfyBmOut/time_log,"{""log_original_timestamp"": ""07:53:20:053"", ""log_event_type"": ""出场查询系统返回结果"", ""response_payload"": {""status"": true, ""code"": ""0000"", ""msg"": ""操作成功"", ""confirm"": ""1"", ""data"": {""CarInfo"": {""CardId"": ""52"", ""CardSnId"": ""52"", ""CarNo"": ""粤A32L0J"", ""CardType"": ""储值卡"", ""Intime"": ""2025-08-04 10:46:52"", ""PStatus"": ""离场"", ""DoorName"": ""一楼入口"", ""Balance"": 0, ""Starttime"": ""2020-04-01"", ""Endtime"": ""2023-12-31"", ""Name"": ""张桂彬"", ""PositionNum"": """", ""NColor"": ""蓝色"", ""ByCarType"": ""汽车"", ""BindFeeType"": ""业主临停车辆"", ""UserID"": """", ""BillId"": """", ""NoSensePay"": ""0"", ""Per_Full"": """", ""MoreCarNoInfo"": """", ""IsMoreCarNo"": ""0"", ""Acctime"": """"}, ""Charge"": {""AllFee"": ""5"", ""ParkTime"": ""21时6分"", ""FeeTime"": ""20时56分"", ""FavMoney"": ""0"", ""FavTime"": ""0"", ""TotalFee"": ""5"", ""CurrFavMoney"": ""0"", ""Overtime"": ""15"", ""IsFree"": ""0"", ""IsTime"": ""0"", ""ChargeMoney"": ""0"", ""ChargeTime"": """", ""ChargeType"": """", ""StartTime"": ""2025-08-04 10:46:52"", ""EndTime"": ""2025-08-05 07:52:57"", ""ChargingCar"": ""业主临停车辆"", ""OrderID"": ""66676CD413B2439ABFD028551ECDADD8"", ""Expire"": ""0"", ""IsAutoCharge"": ""0"", ""lastPaidTime"": """"}}}}",1140,,2025-08-05 07:53:21.415000,2025-08-05 07:53:21.415000
16842,ajb/101013/out/P002LfyBmOut/time_log,"{""log_original_timestamp"": ""07:53:20:062"", ""log_event_type"": ""等待缴费"", ""log_charge_type"": ""业主临停车辆""}",117,,2025-08-05 07:53:21.421000,2025-08-05 07:53:21.421000
16843,ajb/101013/out/P002LfyBmOut/time_log,"{""log_original_timestamp"": ""07:53:21:564"", ""log_event_type"": ""等待支付中"", ""log_car_no"": ""粤A32L0J"", ""log_card_id"": ""52"", ""log_have_car"": ""0"", ""log_n_type"": ""0""}",167,,2025-08-05 07:53:22.455000,2025-08-05 07:53:22.455000
16844,ajb/101013/out/P002LfyBmOut/time_log,"{""log_original_timestamp"": ""07:53:21:752"", ""log_event_type"": ""出场查询系统返回结果"", ""response_payload"": {""status"": true, ""code"": ""0000"", ""msg"": ""操作成功"", ""confirm"": ""1"", ""data"": {""CarInfo"": {""CardId"": ""52"", ""CardSnId"": ""52"", ""CarNo"": ""粤A32L0J"", ""CardType"": ""储值卡"", ""Intime"": ""2025-08-04 10:46:52"", ""PStatus"": ""离场"", ""DoorName"": ""一楼入口"", ""Balance"": 0, ""Starttime"": ""2020-04-01"", ""Endtime"": ""2023-12-31"", ""Name"": ""张桂彬"", ""PositionNum"": """", ""NColor"": ""蓝色"", ""ByCarType"": ""汽车"", ""BindFeeType"": ""业主临停车辆"", ""UserID"": """", ""BillId"": """", ""NoSensePay"": ""0"", ""Per_Full"": """", ""MoreCarNoInfo"": """", ""IsMoreCarNo"": ""0"", ""Acctime"": """"}, ""Charge"": {""AllFee"": ""5"", ""ParkTime"": ""21时6分"", ""FeeTime"": ""20时56分"", ""FavMoney"": ""0"", ""FavTime"": ""0"", ""TotalFee"": ""5"", ""CurrFavMoney"": ""0"", ""Overtime"": ""15"", ""IsFree"": ""0"", ""IsTime"": ""0"", ""ChargeMoney"": ""0"", ""ChargeTime"": """", ""ChargeType"": """", ""StartTime"": ""2025-08-04 10:46:52"", ""EndTime"": ""2025-08-05 07:52:57"", ""ChargingCar"": ""业主临停车辆"", ""OrderID"": ""66676CD413B2439ABFD028551ECDADD8"", ""Expire"": ""0"", ""IsAutoCharge"": ""0"", ""lastPaidTime"": """"}}}}",1140,,2025-08-05 07:53:22.467000,2025-08-05 07:53:22.467000
16845,ajb/101013/out/P002LfyBmOut/time_log,"{""log_original_timestamp"": ""07:53:21:760"", ""log_event_type"": ""等待缴费"", ""log_charge_type"": ""业主临停车辆""}",117,,2025-08-05 07:53:22.487000,2025-08-05 07:53:22.487000
16846,ajb/101013/out/P002LfyBmOut/time_log,"{""log_original_timestamp"": ""07:53:23:266"", ""log_event_type"": ""等待支付中"", ""log_car_no"": ""粤A32L0J"", ""log_card_id"": ""52"", ""log_have_car"": ""0"", ""log_n_type"": ""0""}",167,,2025-08-05 07:53:24.576000,2025-08-05 07:53:24.576000
16847,ajb/101013/out/P002LfyBmOut/time_log,"{""log_original_timestamp"": ""07:53:23:475"", ""log_event_type"": ""出场查询系统返回结果"", ""response_payload"": {""status"": true, ""code"": ""0000"", ""msg"": ""已缴费,未超时"", ""confirm"": ""0"", ""data"": {""CarInfo"": {""CardId"": ""52"", ""CardSnId"": ""52"", ""CarNo"": ""粤A32L0J"", ""CardType"": ""储值卡"", ""Intime"": ""2025-08-04 10:46:52"", ""PStatus"": ""离场"", ""DoorName"": ""一楼入口"", ""Balance"": 0, ""Starttime"": ""2020-04-01"", ""Endtime"": ""2023-12-31"", ""Name"": ""张桂彬"", ""PositionNum"": """", ""NColor"": ""蓝色"", ""ByCarType"": ""汽车"", ""BindFeeType"": ""业主临停车辆"", ""UserID"": """", ""BillId"": """", ""NoSensePay"": ""0"", ""Per_Full"": """", ""MoreCarNoInfo"": """", ""IsMoreCarNo"": ""0"", ""Acctime"": """"}, ""Charge"": {""AllFee"": ""0"", ""ParkTime"": ""21时6分"", ""FeeTime"": ""0时0分"", ""FavMoney"": ""0"", ""FavTime"": ""0"", ""TotalFee"": ""0"", ""CurrFavMoney"": ""0"", ""Overtime"": ""15"", ""IsFree"": ""0"", ""IsTime"": ""0"", ""ChargeMoney"": ""5"", ""ChargeTime"": ""2025-08-05 07:53:21"", ""ChargeType"": ""B"", ""StartTime"": ""2025-08-04 10:46:52"", ""EndTime"": ""2025-08-05 07:52:57"", ""ChargingCar"": ""业主临停车辆"", ""OrderID"": ""66676CD413B2439ABFD028551ECDADD8"", ""Expire"": ""0"", ""IsAutoCharge"": ""0"", ""lastPaidTime"": """"}}}}",1165,,2025-08-05 07:53:24.583000,2025-08-05 07:53:24.583000
16848,ajb/101013/out/P002LfyBmOut/time_log,"{""log_original_timestamp"": ""07:53:23:484"", ""log_event_type"": ""出场开闸控制指令"", ""log_card_id"": ""52"", ""log_car_no"": ""粤A32L0J"", ""log_request_duration"": ""207毫秒"", ""log_event_result"": ""离场起杆完成。""}",218,,2025-08-05 07:53:24.589000,2025-08-05 07:53:24.589000
16849,ajb/101013/out/P002LfyBmOut/time_log,"{""log_original_timestamp"": ""07:53:23:561"", ""log_event_type"": ""查询返回场内剩余车位和场内车辆数量"", ""response_payload"": {""status"": true, ""code"": ""0000"", ""msg"": ""操作成功"", ""data"": {""surplusNum"": 8486, ""presenceNum"": 1514, ""systemTime"": ""2025-08-05 07:53:23"", ""online"": ""1"", ""mac"": ""200510700120001""}}}",321,,2025-08-05 07:53:24.591000,2025-08-05 07:53:24.591000
16850,ajb/101013/out/P002LfyBmOut/time_log,"{""log_original_timestamp"": ""07:53:23:685"", ""log_event_type"": ""等待缴费"", ""log_charge_type"": ""业主临停车辆""}",117,,2025-08-05 07:53:24.596000,2025-08-05 07:53:24.596000
16852,device/BDN861290073715232/event,"{""I2"":{""value"":""1"",""timestamp"":1754351610}}",43,,2025-08-05 07:53:30.824000,2025-08-05 07:53:30.824000
